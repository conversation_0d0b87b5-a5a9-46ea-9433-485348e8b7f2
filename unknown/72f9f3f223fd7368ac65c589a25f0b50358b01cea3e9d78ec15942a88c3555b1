/yourapp
├── /cmd
│   └── /yourapp                (main package entrypoint)
│       └── main.go
├── /internal
│   ├── /domain                 (Domain layer (entities, value objects, interfaces))
│   │   └── /user
│   │       ├── entity.go
│   │       ├── repository.go   (interface)
│   │       └── service.go      (business rules)
│   ├── /application            (Application layer (use cases))
│   │   └── /user
│   │       └── service.go
│   ├── /infrastructure         (Infrastructure layer (DB, third-party))
│   │   └── /postgres
│   │       └── user_repository.go
│   └── /interfaces             (Interface layer (HTTP handlers, GraphQL, gRPC, etc))
│       └── /http
│           └── user_handler.go
├── go.mod
└── .env
