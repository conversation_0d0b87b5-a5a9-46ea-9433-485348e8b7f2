package notification

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"

	"github.com/gorilla/mux"
	"github.com/thongsoi/biomassx/database"
)

type Handler struct {
	service Service
	repo    Repository
}

func <PERSON>Handler(service Service, repo Repository) *Handler {
	return &Handler{
		service: service,
		repo:    repo,
	}
}

func SendRegistrationNotification(db *sql.DB, userID int64, email, firstName, lastName, username, organizationName, phone, language string) error {
	log.Printf("Starting registration notification for user ID: %d, email: %s, language: %s", userID, email, language)
	repo := NewPostgresRepository(db)
	emailConfig := NewEmailConfig(
		os.Getenv("SMTP_HOST"),
		os.<PERSON>env("SMTP_PORT"),
		os.<PERSON>env("SMTP_USERNAME"),
		os.<PERSON>env("FASTMAIL_APP_PASSWORD"),
		os.<PERSON>env("SMTP_SENDER_EMAIL"),
		"BiomassX Platform",
	)
	service := NewEmailService(repo, emailConfig)

	notificationData := &RegistrationNotification{
		UserID:           userID,
		Email:            email,
		FirstName:        firstName,
		LastName:         lastName,
		Username:         username,
		OrganizationName: organizationName,
		Phone:            phone,
		Language:         language, // Set the user's language preference
	}

	err := service.SendRegistrationEmail(notificationData)
	if err != nil {
		log.Printf("Error sending registration notification: %v", err)
		return err // Changed to return the error instead of nil
	}

	log.Printf("Registration notification sent successfully to %s in language %s", email, language)
	return nil
}

func SendPasswordResetNotification(db *sql.DB, userID int64, email, firstName, lastName, username, resetLink string, language string) error {
	log.Printf("Starting password reset notification for user ID: %d, email: %s, language: %s", userID, email, language)
	repo := NewPostgresRepository(db)
	emailConfig := NewEmailConfig(
		os.Getenv("SMTP_HOST"),
		os.Getenv("SMTP_PORT"),
		os.Getenv("SMTP_USERNAME"),
		os.Getenv("FASTMAIL_APP_PASSWORD"),
		os.Getenv("SMTP_SENDER_EMAIL"),
		"BiomassX Platform",
	)
	service := NewEmailService(repo, emailConfig)

	// If language is not provided, default to English
	if language == "" {
		language = "en"
	}

	notificationData := &PasswordResetNotification{
		UserID:    userID,
		Email:     email,
		FirstName: firstName,
		LastName:  lastName,
		Username:  username,
		ResetLink: resetLink,
		Language:  language,
	}

	err := service.SendPasswordResetEmail(notificationData)
	if err != nil {
		log.Printf("Error sending password reset notification: %v", err)
		return err
	}

	log.Printf("Password reset notification sent successfully to %s in language %s", email, language)
	return nil
}

func SendPasswordResetSuccessNotification(db *sql.DB, userID int64, email, firstName, lastName, username string, language string) error {
	log.Printf("Starting password reset success notification for user ID: %d, email: %s, language: %s", userID, email, language)
	repo := NewPostgresRepository(db)
	emailConfig := NewEmailConfig(
		os.Getenv("SMTP_HOST"),
		os.Getenv("SMTP_PORT"),
		os.Getenv("SMTP_USERNAME"),
		os.Getenv("FASTMAIL_APP_PASSWORD"),
		os.Getenv("SMTP_SENDER_EMAIL"),
		"BiomassX Platform",
	)
	service := NewEmailService(repo, emailConfig)

	// If language is not provided, default to English
	if language == "" {
		language = "en"
	}

	notificationData := &PasswordResetSuccessNotification{
		UserID:    userID,
		Email:     email,
		FirstName: firstName,
		LastName:  lastName,
		Username:  username,
		Language:  language,
	}

	err := service.SendPasswordResetSuccessEmail(notificationData)
	if err != nil {
		log.Printf("Error sending password reset success notification: %v", err)
		return err
	}

	log.Printf("Password reset success notification sent successfully to %s in language %s", email, language)
	return nil
}

func (h *Handler) CheckProfileCompleteness(userID int64, email, firstName, lastName, username, language string) error {
	log.Printf("Checking profile completeness for user ID: %d, email: %s, language: %s", userID, email, language)

	completeness, err := h.repo.GetUserProfileCompleteness(userID)
	if err != nil {
		log.Printf("Error getting profile completeness for user %d: %v", userID, err)
		return err
	}

	if completeness.CompletionPercentage == 100 {
		log.Printf("User %d has complete profile", userID)
		return nil
	}

	// Send notification only once after registration
	missingFields := append(completeness.MissingBasicFields, completeness.MissingAddressFields...)
	missingCategories := make(map[string][]string)
	if len(completeness.MissingBasicFields) > 0 {
		missingCategories["Basic Profile Information"] = completeness.MissingBasicFields
	}
	if len(completeness.MissingAddressFields) > 0 {
		missingCategories["Address Information"] = completeness.MissingAddressFields
	}

	baseURL := os.Getenv("BASE_URL") // Use environment variable for base URL
	notificationData := &ProfileIncompleteNotification{
		UserID:                      userID,
		Email:                       email,
		FirstName:                   firstName,
		LastName:                    lastName,
		Username:                    username,
		MissingBasicFields:          completeness.MissingBasicFields,
		MissingAddressFields:        completeness.MissingAddressFields,
		MissingFields:               missingFields,
		MissingCategories:           missingCategories,
		ProfileCompletionPercentage: completeness.CompletionPercentage,
		ProfileURL:                  baseURL + "/profile?lang=" + language,
		BasicInfoURL:                baseURL + "/profile?section=basic&lang=" + language,
		AddressURL:                  baseURL + "/profile?section=address&lang=" + language,
		Language:                    language, // Set the user's language preference
	}

	err = h.service.SendProfileIncompleteNotification(notificationData)
	if err != nil {
		log.Printf("Error sending profile completion notification: %v", err)
		return err
	}

	log.Printf("Profile completion notification sent to user %d in language %s", userID, language)
	return nil
}

func (h *Handler) StartScheduledNotificationsHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	h.service.StartScheduledNotifications()

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Scheduled notifications service started",
	})
}

func (h *Handler) GetUserNotificationsHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	userIDStr := r.URL.Query().Get("user_id")
	if userIDStr == "" {
		http.Error(w, "User ID is required", http.StatusBadRequest)
		return
	}

	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid user ID format", http.StatusBadRequest)
		return
	}

	notifications, err := h.repo.GetNotificationsByUserID(userID)
	if err != nil {
		log.Printf("Error getting notifications for user %d: %v", userID, err)
		http.Error(w, "Failed to get notifications", http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":       true,
		"notifications": notifications,
	})
}

func (h *Handler) ProcessPendingNotificationsHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	err := h.service.ProcessPendingNotifications()
	if err != nil {
		log.Printf("Error processing pending notifications: %v", err)
		http.Error(w, "Failed to process notifications", http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Pending notifications processed",
	})
}

func Initialize(db *sql.DB) (*Handler, error) {
	repo := NewPostgresRepository(db)

	emailConfig := NewEmailConfig(
		os.Getenv("SMTP_HOST"),
		os.Getenv("SMTP_PORT"),
		os.Getenv("SMTP_USERNAME"),
		os.Getenv("FASTMAIL_APP_PASSWORD"),
		os.Getenv("SMTP_SENDER_EMAIL"),
		"BiomassX Platform",
	)
	service := NewEmailService(repo, emailConfig)

	notificationTypes := []string{
		string(NotificationTypeEmail),
		string(NotificationTypeSMS),
		string(NotificationTypePasswordReset),
		string(NotificationTypePasswordResetSuccess),
		string(NotificationTypeRegistration),
		string(NotificationTypeProfileAndAddress),
		string(NotificationTypeOrderSubmission),
		string(NotificationTypeMatching),
		string(NotificationTypeContractConfirmation),
		string(NotificationTypeContractCancellation),
		string(NotificationTypeContractStatusUpdate),
		string(NotificationTypeMatchExpiryReminder),
		string(NotificationTypeMatchExpiryReminderConfirmed),
	}

	for _, typeName := range notificationTypes {
		typeInfo, err := repo.GetNotificationTypeByName(typeName)
		if err != nil || typeInfo == nil {
			_, err = repo.CreateNotificationType(typeName)
			if err != nil {
				log.Printf("Error creating notification type %s: %v", typeName, err)
				return nil, err
			}
			log.Printf("Created notification type: %s", typeName)
		}
	}

	handler := NewHandler(service, repo)

	service.StartScheduledNotifications()
	log.Println("Scheduled notification service started")

	log.Println("Notification system initialized successfully")
	return handler, nil
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	router.HandleFunc("/api/notifications/user", h.GetUserNotificationsHandler).Methods("GET")
	router.HandleFunc("/api/notifications/process", h.ProcessPendingNotificationsHandler).Methods("GET")
	router.HandleFunc("/api/notifications/start-scheduled", h.StartScheduledNotificationsHandler).Methods("GET")
}

// SendOrderSubmissionNotification sends a notification when a user submits an order
func SendOrderSubmissionNotification(data interface{}) error {
	orderData, ok := data.(*OrderSubmissionNotification)
	if !ok {
		return fmt.Errorf("invalid data type for order submission notification")
	}

	log.Printf("Starting order submission notification for user ID: %d, email: %s, language: %s", orderData.UserID, orderData.Email, orderData.Language)

	// Get database connection
	db := database.GetDB()
	repo := NewPostgresRepository(db)
	emailConfig := NewEmailConfig(
		os.Getenv("SMTP_HOST"),
		os.Getenv("SMTP_PORT"),
		os.Getenv("SMTP_USERNAME"),
		os.Getenv("FASTMAIL_APP_PASSWORD"),
		os.Getenv("SMTP_SENDER_EMAIL"),
		"BiomassX Platform",
	)
	service := NewEmailService(repo, emailConfig)

	// If language is not provided, default to English
	if orderData.Language == "" {
		orderData.Language = "en"
	}

	// Get base URL from environment
	if orderData.BaseURL == "" {
		orderData.BaseURL = os.Getenv("BASE_URL")
		if orderData.BaseURL == "" {
			log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
			// No fallback, rely only on the environment variable
		}
	}

	// Create order details URL if not provided
	if orderData.OrderDetailsURL == "" {
		// Include language parameter in URL for proper language persistence
		orderData.OrderDetailsURL = fmt.Sprintf("%s/orders?lang=%s", orderData.BaseURL, orderData.Language)
	}

	// Send notification
	err := service.SendOrderSubmissionNotification(orderData)
	if err != nil {
		log.Printf("Error sending order submission notification: %v", err)
		return err
	}

	log.Printf("Successfully sent order submission notification to user ID: %d, email: %s", orderData.UserID, orderData.Email)
	return nil
}

// SendMatchingNotification sends a notification when orders are matched
func SendMatchingNotification(data interface{}) error {
	matchData, ok := data.(*MatchingNotification)
	if !ok {
		return fmt.Errorf("invalid data type for matching notification")
	}

	log.Printf("Starting matching notification for user ID: %d, email: %s, language: %s", matchData.UserID, matchData.Email, matchData.Language)

	// Get database connection
	db := database.GetDB()
	repo := NewPostgresRepository(db)
	emailConfig := NewEmailConfig(
		os.Getenv("SMTP_HOST"),
		os.Getenv("SMTP_PORT"),
		os.Getenv("SMTP_USERNAME"),
		os.Getenv("FASTMAIL_APP_PASSWORD"),
		os.Getenv("SMTP_SENDER_EMAIL"),
		"BiomassX Platform",
	)
	service := NewEmailService(repo, emailConfig)

	// If language is not provided, default to English
	if matchData.Language == "" {
		matchData.Language = "en"
	}

	// Get base URL from environment
	if matchData.BaseURL == "" {
		matchData.BaseURL = os.Getenv("BASE_URL")
		if matchData.BaseURL == "" {
			log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
			// No fallback, rely only on the environment variable
		}
	}

	// Create contract details URL if not provided
	if matchData.ContractDetailsURL == "" {
		// Include language parameter in URL for proper language persistence
		matchData.ContractDetailsURL = fmt.Sprintf("%s/contract/%d?lang=%s", matchData.BaseURL, matchData.ContractID, matchData.Language)
	}

	// Send notification
	err := service.SendMatchingNotification(matchData)
	if err != nil {
		log.Printf("Error sending matching notification: %v", err)
		return err
	}

	log.Printf("Successfully sent matching notification to user ID: %d, email: %s", matchData.UserID, matchData.Email)
	return nil
}

// SendContractConfirmationNotification sends a notification when a contract is confirmed
func SendContractConfirmationNotification(data interface{}) error {
	confirmData, ok := data.(*ContractConfirmationNotification)
	if !ok {
		return fmt.Errorf("invalid data type for contract confirmation notification")
	}

	log.Printf("Starting contract confirmation notification for user ID: %d, email: %s, language: %s", confirmData.UserID, confirmData.Email, confirmData.Language)

	// Get database connection
	db := database.GetDB()
	repo := NewPostgresRepository(db)
	emailConfig := NewEmailConfig(
		os.Getenv("SMTP_HOST"),
		os.Getenv("SMTP_PORT"),
		os.Getenv("SMTP_USERNAME"),
		os.Getenv("FASTMAIL_APP_PASSWORD"),
		os.Getenv("SMTP_SENDER_EMAIL"),
		"BiomassX Platform",
	)
	service := NewEmailService(repo, emailConfig)

	// If language is not provided, default to English
	if confirmData.Language == "" {
		confirmData.Language = "en"
	}

	// Get base URL from environment
	if confirmData.BaseURL == "" {
		confirmData.BaseURL = os.Getenv("BASE_URL")
		if confirmData.BaseURL == "" {
			log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
			// No fallback, rely only on the environment variable
		}
	}

	// Create contract details URL if not provided
	if confirmData.ContractDetailsURL == "" {
		// Include language parameter in URL for proper language persistence
		confirmData.ContractDetailsURL = fmt.Sprintf("%s/contract/%d?lang=%s", confirmData.BaseURL, confirmData.ContractID, confirmData.Language)
	}

	// Send notification
	err := service.SendContractConfirmationNotification(confirmData)
	if err != nil {
		log.Printf("Error sending contract confirmation notification: %v", err)
		return err
	}

	log.Printf("Successfully sent contract confirmation notification to user ID: %d, email: %s", confirmData.UserID, confirmData.Email)
	return nil
}

// SendContractCancellationNotification sends a notification when a contract is cancelled
func SendContractCancellationNotification(data interface{}) error {
	cancelData, ok := data.(*ContractCancellationNotification)
	if !ok {
		return fmt.Errorf("invalid data type for contract cancellation notification")
	}

	log.Printf("Starting contract cancellation notification for user ID: %d, email: %s, language: %s", cancelData.UserID, cancelData.Email, cancelData.Language)

	// Get database connection
	db := database.GetDB()
	repo := NewPostgresRepository(db)
	emailConfig := NewEmailConfig(
		os.Getenv("SMTP_HOST"),
		os.Getenv("SMTP_PORT"),
		os.Getenv("SMTP_USERNAME"),
		os.Getenv("FASTMAIL_APP_PASSWORD"),
		os.Getenv("SMTP_SENDER_EMAIL"),
		"BiomassX Platform",
	)
	service := NewEmailService(repo, emailConfig)

	// If language is not provided, default to English
	if cancelData.Language == "" {
		cancelData.Language = "en"
	}

	// Get base URL from environment
	if cancelData.BaseURL == "" {
		cancelData.BaseURL = os.Getenv("BASE_URL")
		if cancelData.BaseURL == "" {
			log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
			// No fallback, rely only on the environment variable
		}
	}

	// Create contract details URL if not provided
	if cancelData.ContractDetailsURL == "" {
		// Include language parameter in URL for proper language persistence
		cancelData.ContractDetailsURL = fmt.Sprintf("%s/contract/%d?lang=%s", cancelData.BaseURL, cancelData.ContractID, cancelData.Language)
	}

	// Send notification
	err := service.SendContractCancellationNotification(cancelData)
	if err != nil {
		log.Printf("Error sending contract cancellation notification: %v", err)
		return err
	}

	log.Printf("Successfully sent contract cancellation notification to user ID: %d, email: %s", cancelData.UserID, cancelData.Email)
	return nil
}

// SendContractStatusUpdateNotification sends a notification when a contract status is updated
func SendContractStatusUpdateNotification(data interface{}) error {
	statusData, ok := data.(*ContractStatusUpdateNotification)
	if !ok {
		return fmt.Errorf("invalid data type for contract status update notification")
	}

	log.Printf("Starting contract status update notification for user ID: %d, email: %s, language: %s", statusData.UserID, statusData.Email, statusData.Language)

	// Get database connection
	db := database.GetDB()
	repo := NewPostgresRepository(db)
	emailConfig := NewEmailConfig(
		os.Getenv("SMTP_HOST"),
		os.Getenv("SMTP_PORT"),
		os.Getenv("SMTP_USERNAME"),
		os.Getenv("FASTMAIL_APP_PASSWORD"),
		os.Getenv("SMTP_SENDER_EMAIL"),
		"BiomassX Platform",
	)
	service := NewEmailService(repo, emailConfig)

	// If language is not provided, default to English
	if statusData.Language == "" {
		statusData.Language = "en"
	}

	// Get base URL from environment
	if statusData.BaseURL == "" {
		statusData.BaseURL = os.Getenv("BASE_URL")
		if statusData.BaseURL == "" {
			log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
			// No fallback, rely only on the environment variable
		}
	}

	// Create contract details URL if not provided
	if statusData.ContractDetailsURL == "" {
		// Include language parameter in URL for proper language persistence
		statusData.ContractDetailsURL = fmt.Sprintf("%s/contract/%d?lang=%s", statusData.BaseURL, statusData.ContractID, statusData.Language)
	}

	// Send notification
	err := service.SendContractStatusUpdateNotification(statusData)
	if err != nil {
		log.Printf("Error sending contract status update notification: %v", err)
		return err
	}

	log.Printf("Successfully sent contract status update notification to user ID: %d, email: %s", statusData.UserID, statusData.Email)
	return nil
}
