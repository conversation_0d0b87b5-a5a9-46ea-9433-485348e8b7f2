/biomassx.com
├── /cmd
│   └── /web                
│       └── main.go
├── /internal
│   ├── /user                 
│   │   ├── handler.go          (http layer)
│   │   ├── repository.go       (Infrastructure adapter)
│   │   ├── service.go          (Application / business logic layer)
│   │   └── model.go            (Domain entities)
│   │       
│   ├── /order
│   │   ├── handler.go
│   │   ├── repository.go
│   │   ├── service.go
│   │   └── model.go
│   │  
│   ├── /delivery
│   │   ├── handler.go
│   │   ├── repository.go
│   │   ├── service.go
│   │   └── model.go
│   │       
│   └── /view       
│       ├──/css
│       |  └── styles.css
│       └──/templates
│           └── index.html
│   
├── go.sum  
├── go.mod
├── .env
└── README.md
