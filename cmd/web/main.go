package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"time"

	"github.com/gorilla/mux"
	"github.com/joho/godotenv"
	"github.com/thongsoi/biomassx/database"
	"github.com/thongsoi/biomassx/internal/contract"
	"github.com/thongsoi/biomassx/internal/currency"
	"github.com/thongsoi/biomassx/internal/index"
	"github.com/thongsoi/biomassx/internal/invoice"
	"github.com/thongsoi/biomassx/internal/markets"
	"github.com/thongsoi/biomassx/internal/middleware"
	"github.com/thongsoi/biomassx/internal/notification"
	"github.com/thongsoi/biomassx/internal/order"
	"github.com/thongsoi/biomassx/internal/user"
)

func init() {
	// Load .env file
	err := godotenv.Load()
	if err != nil {
		log.Printf("Warning: Could not load .env file: %v", err)
	}
}

func main() {

	// Load .env file
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found")
	}

	// Initialize the database
	if err := database.InitDB(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer func() {
		if err := database.CloseDB(); err != nil {
			log.Println("Failed to close the database:", err)
		}
	}()

	// Get database connection
	db := database.GetDB()

	// Initialize currency service
	currencyService := currency.NewCurrencyService()
	// Test the currency service
	rate, err := currencyService.GetRate("USD", "THB")
	if err != nil {
		log.Printf("Warning: Failed to get initial exchange rate: %v", err)
	} else {
		log.Printf("Current USD/THB rate: %.4f", rate)
	}

	// Initialize notification system
	notificationHandler, err := notification.Initialize(database.DB)
	if err != nil {
		log.Printf("Warning: Failed to initialize notification system: %v", err)
		// Continue anyway as this is not critical for the application to run
	} else {
		log.Println("Notification system initialized successfully")
	}

	r := mux.NewRouter()

	// Add notification routes
	if notificationHandler != nil {
		// Register notification routes using the handler's RegisterRoutes method
		notificationHandler.RegisterRoutes(r)
	}

	// Middleware to log requests
	r.Use(loggingMiddleware)

	// Static files
	r.PathPrefix("/static/").Handler(http.StripPrefix("/static/", http.FileServer(http.Dir("static"))))

	// Payment slip files
	r.PathPrefix("/payment_slip/").Handler(http.StripPrefix("/payment_slip/", http.FileServer(http.Dir("payment_slip"))))

	// Add to your route setup

	// Index
	r.HandleFunc("/api/recent-market-orders", index.RecentMarketOrdersHandler).Methods("GET")
	r.HandleFunc("/api/recent-market-orders-global", index.RecentMarketOrdersGlobalHandler).Methods("GET")
	r.HandleFunc("/api/top-product-name", func(w http.ResponseWriter, r *http.Request) {
		index.TopProductNameHandler(w, r, currencyService)
	})
	r.HandleFunc("/api/product-prices", func(w http.ResponseWriter, r *http.Request) {
		index.ProductPricesHandler(w, r, currencyService)
	})
	r.HandleFunc("/api/market-volume", func(w http.ResponseWriter, r *http.Request) {
		index.MarketVolumeHandler(w, r, currencyService)
	}).Methods("GET")
	r.HandleFunc("/api/active-orders", index.ActiveOrdersHandler).Methods("GET")
	// Add to your route setup
	r.HandleFunc("/api/co2-saved", index.CO2EmissionsSavedHandler).Methods("GET")
	/*----------------------------*/

	// Auth routes
	r.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		middleware.IndexHandler(w, r, currencyService)
	})
	r.HandleFunc("/about", middleware.AboutHandler).Methods("GET")
	r.HandleFunc("/faqs", middleware.FaqsHandler).Methods("GET")
	r.HandleFunc("/services", middleware.ServiceHandler).Methods("GET")
	r.HandleFunc("/productspage", middleware.ProductHandler).Methods("GET")
	r.HandleFunc("/markets", middleware.MarketHandler).Methods("GET")
	r.HandleFunc("/contact", middleware.ContactHandler).Methods("GET")
	r.HandleFunc("/login", middleware.LoginHandler).Methods("GET", "POST")
	r.HandleFunc("/logout", middleware.LogoutHandler).Methods("GET", "POST")
	r.HandleFunc("/register", user.RegisterHandler).Methods("GET", "POST")
	r.HandleFunc("/api/delete-address", user.HandleDeleteAddress).Methods("GET", "POST")

	r.HandleFunc("/api/update-profile", user.UpdateProfileHandler(db)).Methods("POST")
	// Register address-related routes

	// Added by pie
	r.HandleFunc("/forgot-password", user.ForgotPasswordHandler(db)).Methods("GET", "POST")
	r.HandleFunc("/reset-password", user.ResetPasswordHandler(db)).Methods("GET", "POST")
	/*----------------------------*/

	//profile
	r.HandleFunc("/api/add-address", user.AddAddressHandler).Methods("GET", "POST")
	r.HandleFunc("/api/address-types", user.GetAddressTypesHandler).Methods("GET", "POST")
	r.HandleFunc("/api/countries", user.GetCountriesHandler).Methods("GET", "POST")
	r.HandleFunc("/api/provinces", user.GetProvincesHandler).Methods("GET", "POST")
	r.HandleFunc("/api/districts", user.GetDistrictsHandler).Methods("GET", "POST")
	r.HandleFunc("/api/subdistricts", user.GetSubdistrictsHandler).Methods("GET", "POST")
	r.HandleFunc("/api/segments-subsegments", user.GetSegmentsSubsegmentsHandler).Methods("GET")
	r.HandleFunc("/api/user-roles", user.GetUserRolesHandler).Methods("GET")
	r.HandleFunc("/api/update-user-roles", user.UpdateUserRolesHandler).Methods("POST")

	//api orderpage handler
	r.HandleFunc("/submarkets", order.SubmarketHandler).Methods("GET")
	r.HandleFunc("/products", order.ProductHandler).Methods("GET")
	r.HandleFunc("/qualities", order.QualityHandler).Methods("GET")
	r.HandleFunc("/district", order.DistrictHandler).Methods("GET")
	r.HandleFunc("/subdistrict", order.SubdistrictHandler).Methods("GET")
	r.HandleFunc("/payment", order.PaymentTermHandler).Methods("GET")
	r.HandleFunc("/delivery", order.DeliveryHandler).Methods("GET")
	r.HandleFunc("/api/validate-dates", order.ValidateDatesHandler).Methods("GET")
	r.HandleFunc("/order", order.SubmitOrderHandler).Methods("POST")

	//api contract
	r.HandleFunc("/download-pdfEn/{id}", contract.DownloadContractPDFHandlerEn).Methods("GET")
	r.HandleFunc("/download-pdfTh/{id}", contract.DownloadContractPDFHandlerTh).Methods("GET")
	r.HandleFunc("/api/update-address", user.UpdateAddressHandler).Methods("GET", "POST")
	r.HandleFunc("/api/get-address", user.GetAddressHandler).Methods("GET", "POST")

	// Initialize market services
	marketRepo := markets.NewRepository(db)
	marketService := markets.NewService(marketRepo)
	marketHandler := markets.NewHandler(marketService)
	r.HandleFunc("/api/search-products", marketHandler.SearchProductsHandler).Methods("GET")
	r.HandleFunc("/api/products/{id}/qualities", marketHandler.GetProductQualities).Methods("GET")
	r.HandleFunc("/api/delivery-terms", marketHandler.GetDeliveryTerms).Methods("GET")
	r.HandleFunc("/api/markets", marketHandler.GetMarkets).Methods("GET")
	r.HandleFunc("/api/contract-types", marketHandler.GetContractTypes).Methods("GET")
	r.HandleFunc("/api/payment-terms", marketHandler.GetPaymentTerms).Methods("GET")
	r.HandleFunc("/api/marketspaces", marketHandler.GetMarketspaces).Methods("GET")
	r.HandleFunc("/api/market-data/filtered", marketHandler.GetFilteredMarketData).Methods("GET")
	r.HandleFunc("/api/market-futures", marketHandler.GetMarketFutures).Methods("GET")
	r.HandleFunc("/api/price-history", marketHandler.PriceHistoryHandler).Methods("GET")
	r.HandleFunc("/api/price-history-realtime", marketHandler.PriceHistoryRealtimeHandler).Methods("GET")

	// Protected routes
	r.Handle("/dashboard", middleware.AuthMiddleware(http.HandlerFunc(middleware.DashboardHandler))).Methods("GET")
	r.Handle("/order", middleware.AuthMiddleware(http.HandlerFunc(order.OrderHandler))).Methods("GET", "POST")
	r.Handle("/order_book", middleware.AuthMiddleware(http.HandlerFunc(order.OrderBookHandler))).Methods("GET", "POST")
	r.Handle("/api/order/{id:[0-9]+}", middleware.AuthMiddleware(http.HandlerFunc(order.OrderAPIHandler))).Methods("GET")
	r.Handle("/contract", middleware.AuthMiddleware(http.HandlerFunc(contract.ContractHandler))).Methods("GET")
	r.Handle("/contract/{id:[0-9]+}", middleware.AuthMiddleware(http.HandlerFunc(contract.ContractDetailHandler))).Methods("GET")
	r.Handle("/sign", middleware.AuthMiddleware(http.HandlerFunc(contract.UpdateContractStatusHandler))).Methods("POST")
	r.Handle("/reject", middleware.AuthMiddleware(http.HandlerFunc(contract.RejectContractHandler))).Methods("POST")
	r.Handle("/profile", middleware.AuthMiddleware(http.HandlerFunc(user.ProfileHandler))).Methods("GET", "POST")
	r.Handle("/assets", middleware.AuthMiddleware(http.HandlerFunc(middleware.AssetsHandler))).Methods("GET")

	// Invoice routes
	r.Handle("/invoice", middleware.AuthMiddleware(http.HandlerFunc(invoice.InvoiceHandler))).Methods("GET")
	r.Handle("/invoice/{id:[0-9]+}", middleware.AuthMiddleware(http.HandlerFunc(invoice.InvoiceDetailHandler))).Methods("GET")
	r.Handle("/invoice/{id:[0-9]+}/download", middleware.AuthMiddleware(http.HandlerFunc(invoice.DownloadInvoicePDFHandler))).Methods("GET")
	r.Handle("/invoice/upload-payment-proof", middleware.AuthMiddleware(http.HandlerFunc(invoice.UploadPaymentProofHandler))).Methods("POST")
	r.Handle("/api/invoice/status", middleware.AuthMiddleware(http.HandlerFunc(invoice.UpdateInvoiceStatusHandler))).Methods("POST")
	r.Handle("/api/invoice/generate", middleware.AuthMiddleware(http.HandlerFunc(invoice.GenerateInvoicesForContractHandler))).Methods("POST")
	r.Handle("/api/invoice/generate-all", middleware.AuthMiddleware(http.HandlerFunc(invoice.GenerateInvoicesForSignedContractsHandler))).Methods("POST")
	r.HandleFunc("/api/invoice/check-overdue", invoice.CheckOverdueInvoicesHandler).Methods("GET")

	// HTTP Server configuration
	srv := &http.Server{
		Handler:      r,
		Addr:         ":8000",
		WriteTimeout: 15 * time.Second,
		ReadTimeout:  15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Channel for interrupt signal
	idleConnsClosed := make(chan struct{})

	// Graceful shutdown
	go func() {
		sigint := make(chan os.Signal, 1)
		signal.Notify(sigint, os.Interrupt)
		<-sigint

		// Gracefully shutdown the server
		if err := srv.Shutdown(context.Background()); err != nil {
			log.Printf("HTTP server Shutdown: %v", err)
		}
		close(idleConnsClosed)
	}()

	log.Println("Server started on :8000")
	if err := srv.ListenAndServe(); err != http.ErrServerClosed {
		log.Fatalf("ListenAndServe(): %v", err)
	}

	<-idleConnsClosed
	log.Println("Server shutdown completed")
}

// Simple logging middleware
func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Printf("%s %s %s", r.Method, r.RequestURI, r.RemoteAddr)
		next.ServeHTTP(w, r)
	})
}
