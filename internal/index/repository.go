package index

import (
	"database/sql"
	"fmt"
	"log"
	"sort"

	"github.com/thongsoi/biomassx/internal/currency"
)

// Update function signature to accept language parameter
func GetLatestOrders(db *sql.DB, language string) ([]matchings, error) {
	var query string

	switch language {
	case "th":
		query = `
SELECT
    CASE
        WHEN m.order_type_id = 1 THEN 'ซื้อ'
        WHEN m.order_type_id = 2 THEN 'ขาย'
    END AS "Order",
    p.th_name AS "Product",
    CONCAT(CAST(ROUND(m.quantity) AS int)::text, ' ', u.th_name) AS "Quantity_Unit",
    CONCAT(s.th_name, ' ', '(', g.th_name, ')') AS "Quality",
    pk.th_name AS "Packaging",
    mr.th_name AS "Market",
    s2.th_name as "subMarket",
    ct.th_name AS "Contract_Type",
    dt.th_name AS "Delivery_Term",
    c.th_name AS "Country",
    COALESCE(pv.th_name, '') AS "Province_State",
    pt.th_name AS "Payment_term"
FROM matchings m
LEFT JOIN products p ON m.product_id = p.id
LEFT JOIN uoms u ON m.uom_id = u.id
LEFT JOIN qualities q ON m.quality_id = q.id
LEFT JOIN standards s ON q.standard_id = s.id
LEFT JOIN grades g ON q.grade_id = g.id
LEFT JOIN packings pk ON m.packing_id = pk.id
LEFT JOIN markets mr ON m.market_id = mr.id
LEFT JOIN submarkets s2 ON m.submarket_id = s2.id
LEFT JOIN contract_types ct ON m.contract_type_id = ct.id
LEFT JOIN delivery_terms_marketspaces dtm ON m.marketspace_id = dtm.marketspace_id AND m.delivery_term_id = dtm.delivery_term_id  
LEFT JOIN delivery_terms dt ON m.delivery_term_id = dt.id
LEFT JOIN countries c ON m.country_id = c.id
LEFT JOIN provinces pv ON m.province_id = pv.id
LEFT JOIN payment_terms pt ON m.payment_term_id = pt.id
WHERE m.marketspace_id = 1
  AND m.quantity <> 0
ORDER BY m.created_at DESC
LIMIT 10;`

	case "en":
		query = `
SELECT
    CASE
        WHEN m.order_type_id = 1 THEN 'Buy'
        WHEN m.order_type_id = 2 THEN 'Sell'
    END AS "Order",
    p.en_name AS "Product",
    CONCAT(CAST(ROUND(m.quantity) AS int)::text, ' ', u.en_name) AS "Quantity_Unit",
    CONCAT(s.th_name, ' ', '(', g.en_name, ')') AS "Quality",
    pk.en_name AS "Packaging",
    mr.en_name AS "Market",
    s2.en_name as "subMarket",
    ct.en_name AS "Contract_Type",
    dt.en_name AS "Delivery_Term",
    c.en_name AS "Country",
    COALESCE(pv.en_name, '') AS "Province_State",
    pt.en_name AS "Payment_term"
FROM matchings m
LEFT JOIN products p ON m.product_id = p.id
LEFT JOIN uoms u ON m.uom_id = u.id
LEFT JOIN qualities q ON m.quality_id = q.id
LEFT JOIN standards s ON q.standard_id = s.id
LEFT JOIN grades g ON q.grade_id = g.id
LEFT JOIN packings pk ON m.packing_id = pk.id
LEFT JOIN markets mr ON m.market_id = mr.id
LEFT JOIN submarkets s2 ON m.submarket_id = s2.id
LEFT JOIN contract_types ct ON m.contract_type_id = ct.id
LEFT JOIN delivery_terms_marketspaces dtm ON m.marketspace_id = dtm.marketspace_id AND m.delivery_term_id = dtm.delivery_term_id  
LEFT JOIN delivery_terms dt ON m.delivery_term_id = dt.id
LEFT JOIN countries c ON m.country_id = c.id
LEFT JOIN provinces pv ON m.province_id = pv.id
LEFT JOIN payment_terms pt ON m.payment_term_id = pt.id
WHERE m.marketspace_id = 1
  AND m.quantity <> 0
ORDER BY m.created_at DESC
LIMIT 10;
`

	default:
		// Default to English if language is not specified
		language = "en"
		return GetLatestOrders(db, "en")
	}

	var matching []matchings
	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var m matchings
		err := rows.Scan(
			&m.Order,
			&m.Product,
			&m.QuantityUnit,
			&m.Quality,
			&m.Packaging,
			&m.Market,
			&m.SubMarket,
			&m.ContractType,
			&m.DeliveryTerm,
			&m.Country,
			&m.ProvinceState,
			&m.PaymentTerm,
		)
		if err != nil {
			return nil, err
		}
		matching = append(matching, m)
	}
	return matching, nil
}

func GetLatestGlobalOrders(db *sql.DB, language string) ([]GlobalMatchings, error) {
	var query string

	switch language {
	case "th":
		query = `
        SELECT
    CASE
        WHEN m.order_type_id = 1 THEN 'ซื้อ'
        WHEN m.order_type_id = 2 THEN 'ขาย'
    END AS "Order",
    p.th_name AS "Product",
    CONCAT(CAST(ROUND(m.quantity) AS int)::text, ' ', u.th_name) AS "Quantity_Unit",
    CONCAT(s.th_name, ' ', '(', g.th_name, ')') AS "Quality",
    pk.th_name AS "Packaging",
    mr.th_name AS "Market",
    s2.th_name AS "subMarket",
    ct.th_name AS "Contract_Type",
    dt.th_name AS "Delivery_Term",
    c.region AS "Region",
    c.th_name AS "Country",
    COALESCE(pol.th_name, '') AS "port_of_loading",
    COALESCE(pod.th_name, '') AS "port_of_discharge",
    pt.th_name AS "Payment_term"
FROM matchings m
LEFT JOIN products p ON m.product_id = p.id
LEFT JOIN uoms u ON m.uom_id = u.id
LEFT JOIN qualities q ON m.quality_id = q.id
LEFT JOIN standards s ON q.standard_id = s.id
LEFT JOIN grades g ON q.grade_id = g.id
LEFT JOIN packings pk ON m.packing_id = pk.id
LEFT JOIN markets mr ON m.market_id = mr.id
LEFT JOIN submarkets s2 ON m.submarket_id = s2.id
LEFT JOIN contract_types ct ON m.contract_type_id = ct.id
LEFT JOIN delivery_terms_marketspaces dtm ON m.marketspace_id = dtm.marketspace_id AND m.delivery_term_id = dtm.delivery_term_id
LEFT JOIN delivery_terms dt ON m.delivery_term_id = dt.id
LEFT JOIN countries c ON m.country_id = c.id
LEFT JOIN ports pol ON m.port_of_loading_id = pol.id
LEFT JOIN ports pod ON m.port_of_discharge_id = pod.id
LEFT JOIN payment_terms pt ON m.payment_term_id = pt.id
WHERE m.marketspace_id = 2
  AND m.quantity <> 0
ORDER BY m.created_at DESC
LIMIT 10;`

	default:
		query = `
        SELECT
    CASE
        WHEN m.order_type_id = 1 THEN 'Buy'
        WHEN m.order_type_id = 2 THEN 'Sell'
    END AS "Order",
    p.en_name AS "Product",
    CONCAT(CAST(ROUND(m.quantity) AS int)::text, ' ', u.en_name) AS "Quantity_Unit",
    CONCAT(s.en_name, ' ', '(', g.en_name, ')') AS "Quality",
    pk.en_name AS "Packaging",
    mr.en_name AS "Market",
    s2.en_name AS "subMarket",
    ct.en_name AS "Contract_Type",
    dt.en_name AS "Delivery_Term",
    c.region AS "Region",
    c.en_name AS "Country",
    COALESCE(pol.en_name, '') AS "port_of_loading",
    COALESCE(pod.en_name, '') AS "port_of_discharge",
    pt.en_name AS "Payment_term"
FROM matchings m
LEFT JOIN products p ON m.product_id = p.id
LEFT JOIN uoms u ON m.uom_id = u.id
LEFT JOIN qualities q ON m.quality_id = q.id
LEFT JOIN standards s ON q.standard_id = s.id
LEFT JOIN grades g ON q.grade_id = g.id
LEFT JOIN packings pk ON m.packing_id = pk.id
LEFT JOIN markets mr ON m.market_id = mr.id
LEFT JOIN submarkets s2 ON m.submarket_id = s2.id
LEFT JOIN contract_types ct ON m.contract_type_id = ct.id
LEFT JOIN delivery_terms_marketspaces dtm ON m.marketspace_id = dtm.marketspace_id AND m.delivery_term_id = dtm.delivery_term_id
LEFT JOIN delivery_terms dt ON m.delivery_term_id = dt.id
LEFT JOIN countries c ON m.country_id = c.id
LEFT JOIN ports pol ON m.port_of_loading_id = pol.id
LEFT JOIN ports pod ON m.port_of_discharge_id = pod.id
LEFT JOIN payment_terms pt ON m.payment_term_id = pt.id
WHERE m.marketspace_id = 2
  AND m.quantity <> 0
ORDER BY m.created_at DESC
LIMIT 10;
`
	}

	var globalMatchings []GlobalMatchings
	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var gm GlobalMatchings
		err := rows.Scan(
			&gm.Order,
			&gm.Product,
			&gm.QuantityUnit,
			&gm.Quality,
			&gm.Packaging,
			&gm.Market,
			&gm.SubMarket,
			&gm.ContractType,
			&gm.DeliveryTerm,
			&gm.Region,
			&gm.Country,
			&gm.PortOfLoading,
			&gm.PortOfDischarge,
			&gm.PaymentTerm,
		)
		if err != nil {
			return nil, err
		}
		globalMatchings = append(globalMatchings, gm)
	}
	return globalMatchings, nil
}

// First, update the function signature to accept the currency service
func GetTopTradingProduct(db *sql.DB, language string, currencyService *currency.CurrencyService) (*ProductIndex, error) {
	// Start a transaction to ensure consistent reads
	tx, err := db.Begin()
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %v", err)
	}
	defer tx.Rollback()

	rate, err := currencyService.GetRate("USD", "THB")
	if err != nil {
		return nil, fmt.Errorf("failed to get exchange rate: %v", err)
	}

	log.Printf("Current exchange rate USD/THB: %.4f", rate)

	// Modify your queries to use DECIMAL instead of floating-point
	var query string
	switch language {
	case "th":
		query = `
        WITH raw_volumes AS (
            SELECT 
                p.id,
                p.th_name,
                SUM(CASE WHEN c.code = 'THB' THEN m.quantity * m.price ELSE 0 END)::DECIMAL(32,2) as raw_thb,
                SUM(CASE WHEN c.code = 'USD' THEN m.quantity * m.price ELSE 0 END)::DECIMAL(32,2) as raw_usd
            FROM matchings m
            JOIN products p ON m.product_id = p.id
            JOIN statuses s ON m.status_id = s.id
            JOIN currencies c ON m.currency_id = c.id
            WHERE s.en_name IN ('open', 'partially matched', 'amended','confirmed')
            GROUP BY p.id, p.th_name
        ),
        trading_volume AS (
            SELECT 
                th_name as product_name,
                raw_thb as thb_volume,
                raw_usd as usd_volume,
                ((raw_thb + (raw_usd * $1::DECIMAL(32,2)))::DECIMAL(32,2)) as market_value_thb,
                'THB' as currency
            FROM raw_volumes
            ORDER BY market_value_thb DESC
            LIMIT 1
        )
        SELECT * FROM trading_volume`

	case "en":
		query = `
        WITH raw_volumes AS (
            SELECT 
                p.id,
                p.en_name,
                SUM(CASE WHEN c.code = 'THB' THEN m.quantity * m.price ELSE 0 END)::DECIMAL(32,2) as raw_thb,
                SUM(CASE WHEN c.code = 'USD' THEN m.quantity * m.price ELSE 0 END)::DECIMAL(32,2) as raw_usd
            FROM matchings m
            JOIN products p ON m.product_id = p.id
            JOIN statuses s ON m.status_id = s.id
            JOIN currencies c ON m.currency_id = c.id
            WHERE s.en_name IN ('open', 'partially matched', 'amended','confirmed')
            GROUP BY p.id, p.en_name
        ),
        trading_volume AS (
            SELECT 
                en_name as product_name,
                raw_thb as thb_volume,
                raw_usd as usd_volume,
                ((raw_usd + (raw_thb / $1::DECIMAL(32,2)))::DECIMAL(32,2)) as market_value_usd,
                'USD' as currency
            FROM raw_volumes
            ORDER BY market_value_usd DESC
            LIMIT 1
        )
        SELECT * FROM trading_volume`

	default:
		language = "en"
		return GetTopTradingProduct(db, "en", currencyService)
	}

	// Use the transaction for querying
	productIndex := &ProductIndex{}
	err = tx.QueryRow(query, rate).Scan(
		&productIndex.ProductName,
		&productIndex.OriginalTHBVolume,
		&productIndex.OriginalUSDVolume,
		&productIndex.TotalMarketValue,
		&productIndex.Currency,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			currency := "USD"
			if language == "th" {
				currency = "THB"
			}
			return &ProductIndex{
				ProductName:       "No Active Products",
				OriginalTHBVolume: 0,
				OriginalUSDVolume: 0,
				TotalMarketValue:  0,
				Currency:          currency,
			}, nil
		}
		return nil, fmt.Errorf("query error: %v", err)
	}

	// Commit the transaction
	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %v", err)
	}

	// Log the exact values for debugging
	log.Printf("Raw values - THB: %.2f, USD: %.2f, Total: %.2f %s",
		productIndex.OriginalTHBVolume,
		productIndex.OriginalUSDVolume,
		productIndex.TotalMarketValue,
		productIndex.Currency)

	return productIndex, nil
}

// GetProductPricesByDeliveryTerm returns buy and sell prices for the top product by delivery term
func GetProductPricesByDeliveryTerm(db *sql.DB, language string, currencyService *currency.CurrencyService) ([]DeliveryTermPrice, error) {
	// Get the current exchange rate
	rate, err := currencyService.GetRate("USD", "THB")
	if err != nil {
		return nil, fmt.Errorf("failed to get exchange rate: %v", err)
	}

	// Use a transaction to ensure consistent reads
	tx, err := db.Begin()
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// SQL query to get prices by delivery term for the top product
	query := `
	WITH raw_volumes AS (
		SELECT 
			p.id AS product_id,
			p.en_name,
			p.th_name,
			SUM(CASE WHEN c.code = 'THB' THEN m.quantity * m.price ELSE 0 END)::DECIMAL(32,6) as raw_thb,
			SUM(CASE WHEN c.code = 'USD' THEN m.quantity * m.price ELSE 0 END)::DECIMAL(32,6) as raw_usd
		FROM matchings m
		JOIN products p ON m.product_id = p.id
		JOIN statuses s ON m.status_id = s.id
		JOIN currencies c ON m.currency_id = c.id
		WHERE s.en_name IN ('open', 'partially matched', 'amended','confirmed')
		GROUP BY p.id, p.en_name, p.th_name
	),
	top_product AS (
		SELECT 
			product_id,
			en_name as product_name_en,
			th_name as product_name_th,
			raw_thb as thb_volume,
			raw_usd as usd_volume,
			(raw_thb + (raw_usd * $1::DECIMAL(32,6)))::DECIMAL(32,6) as market_value_thb,
			(raw_usd + (raw_thb / $1::DECIMAL(32,6)))::DECIMAL(32,6) as market_value_usd,
			'THB' as currency_th,
			'USD' as currency_en
		FROM raw_volumes
		ORDER BY market_value_thb DESC
		LIMIT 1
	),
	buy_prices AS (
		SELECT 
			CASE WHEN $2 = 'en' THEN tp.product_name_en ELSE tp.product_name_th END as product_name,
			CASE WHEN $2 = 'en' THEN dt.en_name ELSE dt.th_name END AS delivery_term,
			1 as order_type_id,
			TRUNC(
				(
					SUM(CASE 
						WHEN $2 = 'en' THEN
							CASE
								WHEN c.code = 'USD' THEN m.quantity * m.price 
								WHEN c.code = 'THB' THEN m.quantity * (m.price / $1::DECIMAL(32,6))
								ELSE 0 
							END
						ELSE
							CASE
								WHEN c.code = 'THB' THEN m.quantity * m.price 
								WHEN c.code = 'USD' THEN m.quantity * (m.price * $1::DECIMAL(32,6))
								ELSE 0 
							END
					END)::DECIMAL(32,6) / 
					NULLIF(SUM(m.quantity), 0)::DECIMAL(32,6)
				), 
				2
			) AS weighted_avg_price,
			CASE WHEN $2 = 'en' THEN tp.currency_en ELSE tp.currency_th END as currency
		FROM matchings m
		JOIN products p ON m.product_id = p.id
		JOIN delivery_terms dt ON m.delivery_term_id = dt.id
		JOIN currencies c ON m.currency_id = c.id
		JOIN top_product tp ON p.id = tp.product_id
		WHERE m.order_type_id = 1 -- Buy orders
		AND m.status_id IN (SELECT s.id FROM statuses s WHERE s.en_name IN ('open', 'partially matched', 'amended','confirmed'))
		GROUP BY product_name, delivery_term, currency
		ORDER BY delivery_term
	),
	sell_prices AS (
		SELECT 
			CASE WHEN $2 = 'en' THEN tp.product_name_en ELSE tp.product_name_th END as product_name,
			CASE WHEN $2 = 'en' THEN dt.en_name ELSE dt.th_name END AS delivery_term,
			2 as order_type_id,
			TRUNC(
				(
					SUM(CASE 
						WHEN $2 = 'en' THEN
							CASE
								WHEN c.code = 'USD' THEN m.quantity * m.price 
								WHEN c.code = 'THB' THEN m.quantity * (m.price / $1::DECIMAL(32,6))
								ELSE 0 
							END
						ELSE
							CASE
								WHEN c.code = 'THB' THEN m.quantity * m.price 
								WHEN c.code = 'USD' THEN m.quantity * (m.price * $1::DECIMAL(32,6))
								ELSE 0 
							END
					END)::DECIMAL(32,6) / 
					NULLIF(SUM(m.quantity), 0)::DECIMAL(32,6)
				),
				2
			) AS weighted_avg_price,
			CASE WHEN $2 = 'en' THEN tp.currency_en ELSE tp.currency_th END as currency
		FROM matchings m
		JOIN products p ON m.product_id = p.id
		JOIN delivery_terms dt ON m.delivery_term_id = dt.id
		JOIN currencies c ON m.currency_id = c.id
		JOIN top_product tp ON p.id = tp.product_id
		WHERE m.order_type_id = 2 -- Sell orders
		AND m.status_id IN (SELECT s.id FROM statuses s WHERE s.en_name IN ('open', 'partially matched', 'amended','confirmed'))
		GROUP BY product_name, delivery_term, currency
		ORDER BY delivery_term
	)
	SELECT product_name, delivery_term, order_type_id, weighted_avg_price, currency
	FROM buy_prices
	UNION ALL
	SELECT product_name, delivery_term, order_type_id, weighted_avg_price, currency
	FROM sell_prices
	ORDER BY delivery_term, order_type_id
	`

	// Execute the query within the transaction
	rows, err := tx.Query(query, rate, language)
	if err != nil {
		return nil, fmt.Errorf("query error: %v", err)
	}
	defer rows.Close()

	// Map to store prices by delivery term
	priceMap := make(map[string]DeliveryTermPrice)
	var productName string

	// Process the results
	for rows.Next() {
		var deliveryTerm, currency string
		var orderTypeID int
		var price float64

		err := rows.Scan(&productName, &deliveryTerm, &orderTypeID, &price, &currency)
		if err != nil {
			return nil, fmt.Errorf("scan error: %v", err)
		}

		// Get or create the price entry for this delivery term
		priceEntry, exists := priceMap[deliveryTerm]
		if !exists {
			priceEntry = DeliveryTermPrice{
				ProductName:  productName,
				DeliveryTerm: deliveryTerm,
				Currency:     currency,
			}
		}

		// Set buy or sell price based on order type
		if orderTypeID == 1 {
			priceEntry.BuyPrice = price
		} else if orderTypeID == 2 {
			priceEntry.SellPrice = price
		}

		// Update the map
		priceMap[deliveryTerm] = priceEntry
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("rows error: %v", err)
	}

	// Commit the transaction
	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %v", err)
	}

	// Convert map to slice and maintain consistent order
	var result []DeliveryTermPrice
	var deliveryTerms []string
	for term := range priceMap {
		deliveryTerms = append(deliveryTerms, term)
	}
	sort.Strings(deliveryTerms)

	for _, term := range deliveryTerms {
		result = append(result, priceMap[term])
	}

	return result, nil
}

// GetMarketVolumeByDeliveryTerm returns the trading volume for each delivery term with currency conversion
func GetMarketVolumeByDeliveryTerm(db *sql.DB, language string, currencyService *currency.CurrencyService) ([]DeliveryTermVolume, error) {
	log.Printf("GetMarketVolumeByDeliveryTerm called with language: %s", language)

	// Get the current exchange rate
	rate, err := currencyService.GetRate("USD", "THB")
	if err != nil {
		log.Printf("Error getting exchange rate: %v", err)
		return nil, fmt.Errorf("failed to get exchange rate: %v", err)
	}

	log.Printf("Current exchange rate USD/THB: %.4f", rate)

	// Use a transaction to ensure consistent reads
	tx, err := db.Begin()
	if err != nil {
		log.Printf("Error beginning transaction: %v", err)
		return nil, fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// SQL query to get market volume by delivery term
	query := `
	WITH volume_data AS (
		SELECT 
			CASE WHEN $1 = 'en' THEN dt.en_name ELSE dt.th_name END AS delivery_term,
			m.order_type_id,
			m.price * m.quantity AS volume,
			c.code as currency_code,
			MAX(m.created_at) as latest_created_at
		FROM matchings m
		JOIN delivery_terms_marketspaces dtm ON m.marketspace_id = dtm.marketspace_id AND m.delivery_term_id = dtm.delivery_term_id
		JOIN delivery_terms dt ON m.delivery_term_id = dt.id
		JOIN statuses s ON m.status_id = s.id
		JOIN currencies c ON m.currency_id = c.id
		WHERE s.en_name IN ('open', 'partially matched', 'amended', 'confirmed')
		GROUP BY 
			delivery_term,
			m.order_type_id,
			m.price,
			m.quantity,
			c.code
	),
	converted_volumes AS (
		SELECT 
			delivery_term,
			order_type_id,
			CASE 
				WHEN $1 = 'en' THEN
					CASE
						WHEN currency_code = 'USD' THEN volume
						WHEN currency_code = 'THB' THEN volume / $2::DECIMAL(32,6)
						ELSE 0 
					END
				ELSE
					CASE
						WHEN currency_code = 'THB' THEN volume
						WHEN currency_code = 'USD' THEN volume * $2::DECIMAL(32,6)
						ELSE 0 
					END
			END as converted_volume,
			latest_created_at
		FROM volume_data
	),
	aggregated_volumes AS (
		SELECT 
			delivery_term,
			SUM(CASE WHEN order_type_id = 1 THEN converted_volume ELSE 0 END) as buy_volume,
			SUM(CASE WHEN order_type_id = 2 THEN converted_volume ELSE 0 END) as sell_volume,
			MAX(latest_created_at) as latest_created_at
		FROM converted_volumes
		GROUP BY delivery_term
	)
	SELECT 
		delivery_term,
		ROUND(buy_volume::numeric, 2) as buy_volume,
		ROUND(sell_volume::numeric, 2) as sell_volume
	FROM aggregated_volumes
	ORDER BY latest_created_at DESC
	LIMIT 12
	`

	log.Printf("Executing query with language: %s and rate: %.4f", language, rate)

	// Execute the query within the transaction
	rows, err := tx.Query(query, language, rate)
	if err != nil {
		log.Printf("Error executing query: %v", err)
		return nil, fmt.Errorf("query error: %v", err)
	}
	defer rows.Close()

	// Process the results
	var volumes []DeliveryTermVolume
	for rows.Next() {
		var volume DeliveryTermVolume
		err := rows.Scan(
			&volume.DeliveryTerm,
			&volume.BuyVolume,
			&volume.SellVolume,
		)
		if err != nil {
			log.Printf("Error scanning row: %v", err)
			return nil, fmt.Errorf("scan error: %v", err)
		}

		// Set the currency based on language, but don't include it in the values
		if language == "th" {
			volume.Currency = "THB"
		} else {
			volume.Currency = "USD"
		}

		volumes = append(volumes, volume)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error after iterating rows: %v", err)
		return nil, fmt.Errorf("rows error: %v", err)
	}

	// Commit the transaction
	if err := tx.Commit(); err != nil {
		log.Printf("Error committing transaction: %v", err)
		return nil, fmt.Errorf("failed to commit transaction: %v", err)
	}

	log.Printf("GetMarketVolumeByDeliveryTerm returning %d volumes", len(volumes))
	return volumes, nil
}

// GetActiveOrderSummary returns a summary of active orders by market and delivery term
func GetActiveOrderSummary(db *sql.DB, language string) ([]ActiveOrderSummary, error) {
	log.Printf("GetActiveOrderSummary called with language: %s", language)

	// Use a transaction to ensure consistent reads
	tx, err := db.Begin()
	if err != nil {
		log.Printf("Error beginning transaction: %v", err)
		return nil, fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// Single query with language parameter for consistent results
	query := `
	WITH active_orders AS (
		SELECT 
			dt.id AS delivery_term_id,
			CASE WHEN $1 = 'en' THEN dt.en_name ELSE dt.th_name END AS delivery_term_name,
			m.order_type_id,
			mk.id AS market_id,
			CASE WHEN $1 = 'en' THEN mk.en_name ELSE mk.th_name END AS market_name,
			ms.id AS marketspace_id,
			CASE WHEN $1 = 'en' THEN ms.en_name ELSE ms.th_name END AS marketspace_name,
			COUNT(*) AS order_count
		FROM 
			matchings m
		JOIN 
			marketspaces ms ON m.marketspace_id = ms.id
		JOIN 
			markets mk ON m.market_id = mk.id
		JOIN 
			delivery_terms dt ON m.delivery_term_id = dt.id
		JOIN 
			statuses s ON m.status_id = s.id
		WHERE 
			s.en_name IN ('open', 'partially matched', 'amended', 'confirmed')
		GROUP BY 
			dt.id, delivery_term_name,
			m.order_type_id,
			mk.id, market_name,
			ms.id, marketspace_name
	)
	SELECT 
		delivery_term_name AS "Delivery-Term",
		SUM(CASE WHEN order_type_id = 1 THEN order_count ELSE 0 END) AS "Buy",
		SUM(CASE WHEN order_type_id = 2 THEN order_count ELSE 0 END) AS "Sell",
		market_name AS "Market",
		marketspace_name AS "Market Type"
	FROM 
		active_orders
	GROUP BY 
		delivery_term_id, delivery_term_name,
		market_id, market_name,
		marketspace_id, marketspace_name
	ORDER BY 
		marketspace_id,
		market_id,
		delivery_term_id
	LIMIT 12;
	`

	log.Printf("Executing active orders query with language: %s", language)

	// Execute the query within the transaction, passing language as parameter
	rows, err := tx.Query(query, language)
	if err != nil {
		log.Printf("Error executing query: %v", err)
		return nil, fmt.Errorf("query error: %v", err)
	}
	defer rows.Close()

	// Process the results
	var summaries []ActiveOrderSummary
	for rows.Next() {
		var summary ActiveOrderSummary
		err := rows.Scan(
			&summary.DeliveryTerm,
			&summary.BuyCount,
			&summary.SellCount,
			&summary.Market,
			&summary.MarketType,
		)
		if err != nil {
			log.Printf("Error scanning row: %v", err)
			return nil, fmt.Errorf("scan error: %v", err)
		}

		summaries = append(summaries, summary)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error after iterating rows: %v", err)
		return nil, fmt.Errorf("rows error: %v", err)
	}

	// Commit the transaction
	if err := tx.Commit(); err != nil {
		log.Printf("Error committing transaction: %v", err)
		return nil, fmt.Errorf("failed to commit transaction: %v", err)
	}

	log.Printf("GetActiveOrderSummary returning %d summaries", len(summaries))
	return summaries, nil
}

// GetCO2EmissionsSaved calculates CO2 emissions saved with language support
func GetCO2EmissionsSaved(db *sql.DB, language string) (*CO2EmissionsSaved, error) {
	log.Printf("GetCO2EmissionsSaved called with language: %s", language)

	// Use a transaction to ensure consistent reads
	tx, err := db.Begin()
	if err != nil {
		log.Printf("Error beginning transaction: %v", err)
		return nil, fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// SQL query to calculate total CO2 emissions saved (all time)
	totalQuery := `
    SELECT 
        ROUND(COALESCE(SUM(((94.0/1000.0) * p.carbon_reduction_factor * c.quantity)), 0)::numeric, 2) as total_co2_saved_tco2e
    FROM 
        contracts c
    JOIN 
        products p ON c.product_id = p.id
    `

	// SQL query to calculate yearly CO2 emissions saved
	yearlyQuery := `
    SELECT 
        ROUND(COALESCE(SUM(((94.0/1000.0) * p.carbon_reduction_factor * c.quantity)), 0)::numeric, 2) as yearly_co2_saved_tco2e
    FROM 
        contracts c
    JOIN 
        products p ON c.product_id = p.id
    WHERE 
        c.contract_date >= NOW() - INTERVAL '1 year'
    `

	// SQL query to calculate monthly CO2 emissions saved
	monthlyQuery := `
    SELECT 
        ROUND(COALESCE(SUM(((94.0/1000.0) * p.carbon_reduction_factor * c.quantity)), 0)::numeric, 2) as monthly_co2_saved_tco2e
    FROM 
        contracts c
    JOIN 
        products p ON c.product_id = p.id
    WHERE 
        c.contract_date >= NOW() - INTERVAL '1 month'
    `

	log.Printf("Executing CO2 emissions saved queries")

	// Execute the queries within the transaction
	var totalCO2Saved, yearlyCO2Saved, monthlyCO2Saved float64

	err = tx.QueryRow(totalQuery).Scan(&totalCO2Saved)
	if err != nil {
		log.Printf("Error executing total query: %v", err)
		return nil, fmt.Errorf("total query error: %v", err)
	}

	err = tx.QueryRow(yearlyQuery).Scan(&yearlyCO2Saved)
	if err != nil {
		log.Printf("Error executing yearly query: %v", err)
		return nil, fmt.Errorf("yearly query error: %v", err)
	}

	err = tx.QueryRow(monthlyQuery).Scan(&monthlyCO2Saved)
	if err != nil {
		log.Printf("Error executing monthly query: %v", err)
		return nil, fmt.Errorf("monthly query error: %v", err)
	}

	// Commit the transaction
	if err := tx.Commit(); err != nil {
		log.Printf("Error committing transaction: %v", err)
		return nil, fmt.Errorf("failed to commit transaction: %v", err)
	}

	result := &CO2EmissionsSaved{
		Total:   totalCO2Saved,
		Yearly:  yearlyCO2Saved,
		Monthly: monthlyCO2Saved,
	}

	log.Printf("GetCO2EmissionsSaved returning Total: %.2f, Yearly: %.2f, Monthly: %.2f tCO2e",
		totalCO2Saved, yearlyCO2Saved, monthlyCO2Saved)

	return result, nil
}
