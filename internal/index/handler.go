package index

import (
	"fmt"
	"html/template"
	"log"
	"net/http"

	"github.com/thongsoi/biomassx/database" // Import the database package
	"github.com/thongsoi/biomassx/internal/currency"
)

func RecentMarketOrdersHandler(w http.ResponseWriter, r *http.Request) {
	db := database.GetDB()
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en"
	}

	matching, err := GetLatestOrders(db, language)
	if err != nil {
		http.Error(w, "Error fetching orders", http.StatusInternalServerError)
		return
	}

	tmpl := template.Must(template.New("marketRecentOrders").Parse(`
        {{ range . }}
        <tr>
            <td>{{ .Order }}</td>
            <td>{{ .Product }}</td>
            <td>{{ .QuantityUnit }}</td>
            <td>{{ .Quality }}</td>
            <td>{{ .Packaging }}</td>
            <td>{{ .Market }}</td>
			<td>{{ .SubMarket }}</td>
            <td>{{ .ContractType }}</td>
            <td>{{ .DeliveryTerm }}</td>
            <td>{{ .Country }}</td>
            <td>{{ .ProvinceState }}</td>
            <td>{{ .PaymentTerm }}</td>
        </tr>
        {{ end }}
    `))

	w.Header().Set("Content-Type", "text/html")
	err = tmpl.Execute(w, matching)
	if err != nil {
		http.Error(w, "Error rendering template", http.StatusInternalServerError)
	}
}

func RecentMarketOrdersGlobalHandler(w http.ResponseWriter, r *http.Request) {
	db := database.GetDB()
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en"
	}

	var orders []GlobalMatchings
	orders, err := GetLatestGlobalOrders(db, language)
	if err != nil {
		http.Error(w, "Error fetching orders", http.StatusInternalServerError)
		return
	}

	tmpl := template.Must(template.New("marketGlobalOrders").Parse(`
        {{ range . }}
        <tr>
            <td>{{ .Order }}</td>
            <td>{{ .Product }}</td>
            <td>{{ .QuantityUnit }}</td>
            <td>{{ .Quality }}</td>
            <td>{{ .Packaging }}</td>
            <td>{{ .Market }}</td>
            <td>{{ .SubMarket }}</td>
            <td>{{ .ContractType }}</td>
            <td>{{ .DeliveryTerm }}</td>
			 <td>{{ .Region }}</td>
            <td>{{ .Country }}</td>
            <td>{{ .PortOfLoading }}</td>
            <td>{{ .PortOfDischarge }}</td>
            <td>{{ .PaymentTerm }}</td>
        </tr>
        {{ end }}
    `))

	w.Header().Set("Content-Type", "text/html")
	err = tmpl.Execute(w, orders)
	if err != nil {
		http.Error(w, "Error rendering template", http.StatusInternalServerError)
	}
}

func TopProductHandler(w http.ResponseWriter, r *http.Request, currencyService *currency.CurrencyService) {
	db := database.GetDB()
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en"
	}

	topProduct, err := GetTopTradingProduct(db, language, currencyService)
	if err != nil {
		http.Error(w, "Error fetching data", http.StatusInternalServerError)
		return
	}

	tmpl := template.Must(template.ParseFiles("views/partials/top_product.html"))
	tmpl.Execute(w, topProduct)
}

func TopProductNameHandler(w http.ResponseWriter, r *http.Request, currencyService *currency.CurrencyService) {
	db := database.GetDB()
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en"
	}

	topProduct, err := GetTopTradingProduct(db, language, currencyService)
	if err != nil {
		http.Error(w, "Error fetching data", http.StatusInternalServerError)
		return
	}

	// Return formatted string
	if language == "th" {
		fmt.Fprintf(w, "ดัชนีราคา %s ", topProduct.ProductName)
	} else {
		fmt.Fprintf(w, "%s price index", topProduct.ProductName)
	}
}

// ProductPricesHandler returns the buy and sell prices by delivery term as HTML
func ProductPricesHandler(w http.ResponseWriter, r *http.Request, currencyService *currency.CurrencyService) {
	db := database.GetDB()
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en"
	}

	// Get the prices by delivery term
	prices, err := GetProductPricesByDeliveryTerm(db, language, currencyService)
	if err != nil {
		log.Printf("Error fetching product prices: %v", err)
		http.Error(w, "Error fetching data", http.StatusInternalServerError)
		return
	}

	// Create HTML template for the table rows
	tmpl := template.New("priceTable").Funcs(template.FuncMap{
		"formatPrice": func(price float64) string {
			if price <= 0 {
				return "-"
			}
			// Format to exactly 2 decimal places without rounding
			return fmt.Sprintf("%.2f", price)
		},
	})

	tmpl, err = tmpl.Parse(`
	{{ range . }}
	<tr>
		<td>{{ .DeliveryTerm }}</td>
		<td>{{ formatPrice .BuyPrice }}{{ if gt .BuyPrice 0.0 }} {{ end }}</td>
		<td>{{ formatPrice .SellPrice }}{{ if gt .SellPrice 0.0 }} {{ end }}</td>
	</tr>
	{{ end }}
	`)

	if err != nil {
		log.Printf("Error parsing template: %v", err)
		http.Error(w, "Error rendering template", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/html")
	err = tmpl.Execute(w, prices)
	if err != nil {
		log.Printf("Error executing template: %v", err)
		http.Error(w, "Error rendering template", http.StatusInternalServerError)
	}
}

// MarketVolumeHandler returns the market volume data as HTML for real-time updates
func MarketVolumeHandler(w http.ResponseWriter, r *http.Request, currencyService *currency.CurrencyService) {
	log.Printf("MarketVolumeHandler called with lang: %s", r.URL.Query().Get("lang"))

	db := database.GetDB()
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en"
	}

	// Get the market volume data with currency conversion
	volumes, err := GetMarketVolumeByDeliveryTerm(db, language, currencyService)
	if err != nil {
		log.Printf("Error fetching market volume: %v", err)
		http.Error(w, "Error fetching data", http.StatusInternalServerError)
		return
	}

	log.Printf("Found %d volume records", len(volumes))

	// If no volumes found, return a message
	if len(volumes) == 0 {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte("<tr><td colspan='3'>No market volume data available</td></tr>"))
		return
	}

	// Create a template for the table rows - removed currency from display
	tmplStr := `
	{{ range . }}
	<tr>
		<td>{{ .DeliveryTerm }}</td>
		<td>{{ if gt .BuyVolume 0.0 }}{{ printf "%.2f" .BuyVolume }}{{ else }}-{{ end }}</td>
		<td>{{ if gt .SellVolume 0.0 }}{{ printf "%.2f" .SellVolume }}{{ else }}-{{ end }}</td>
	</tr>
	{{ end }}
	`

	tmpl, err := template.New("volumeTable").Parse(tmplStr)
	if err != nil {
		log.Printf("Error parsing template: %v", err)
		http.Error(w, "Error rendering template", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/html")
	err = tmpl.Execute(w, volumes)
	if err != nil {
		log.Printf("Error executing template: %v", err)
		http.Error(w, "Error rendering template", http.StatusInternalServerError)
	}

	log.Printf("MarketVolumeHandler completed successfully")
}

// ActiveOrdersHandler returns the active orders data as HTML
func ActiveOrdersHandler(w http.ResponseWriter, r *http.Request) {
	// Get language from query parameter
	db := database.GetDB()
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en"
	}

	log.Printf("ActiveOrdersHandler called with language: %s", language)

	// Get the active orders data
	summaries, err := GetActiveOrderSummary(db, language)
	if err != nil {
		log.Printf("Error fetching active orders: %v", err)
		http.Error(w, "Error fetching data", http.StatusInternalServerError)
		return
	}

	log.Printf("Found %d active order summaries", len(summaries))

	// If no summaries found, return a message
	if len(summaries) == 0 {
		w.Header().Set("Content-Type", "text/html")
		if language == "th" {
			w.Write([]byte("<tr><td colspan='5'>ไม่<lemma<lemma>ซื้อขาย<lemma>ใช้งานอยู่</td></tr>"))
		} else {
			w.Write([]byte("<tr><td colspan='5'>No active orders available</td></tr>"))
		}
		return
	}

	// Create a template for the table rows
	tmplStr := `
    {{ range . }}
    <tr>
        <td>{{ .DeliveryTerm }}</td>
        <td>{{ .BuyCount }}</td>
        <td>{{ .SellCount }}</td>
		<td>{{ .MarketType }}</td>
        <td>{{ .Market }}</td>
    </tr>
    {{ end }}
    `

	tmpl, err := template.New("activeOrdersTable").Parse(tmplStr)
	if err != nil {
		log.Printf("Error parsing template: %v", err)
		http.Error(w, "Error rendering template", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/html")
	err = tmpl.Execute(w, summaries)
	if err != nil {
		log.Printf("Error executing template: %v", err)
		http.Error(w, "Error rendering template", http.StatusInternalServerError)
	}

	log.Printf("ActiveOrdersHandler completed successfully")
}

// CO2EmissionsSavedHandler returns CO2 emissions saved with language support
func CO2EmissionsSavedHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("CO2EmissionsSavedHandler called with lang: %s", r.URL.Query().Get("lang"))

	// Get language parameter
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en" // Default language
	}

	db := database.GetDB()

	// Get CO2 emissions saved
	co2Data, err := GetCO2EmissionsSaved(db, language)
	if err != nil {
		log.Printf("Error calculating CO2 emissions saved: %v", err)
		http.Error(w, "Error calculating data", http.StatusInternalServerError)
		return
	}

	// Format the numbers
	formattedTotal := fmt.Sprintf("%.2f", co2Data.Total)
	formattedYearly := fmt.Sprintf("%.2f", co2Data.Yearly)
	formattedMonthly := fmt.Sprintf("%.2f", co2Data.Monthly)

	// Return the formatted number based on the requested type
	dataType := r.URL.Query().Get("type")

	switch dataType {
	case "total":
		w.Header().Set("Content-Type", "text/plain")
		w.Write([]byte(formattedTotal))
	case "yearly":
		w.Header().Set("Content-Type", "text/plain")
		w.Write([]byte(formattedYearly))
	case "monthly":
		w.Header().Set("Content-Type", "text/plain")
		w.Write([]byte(formattedMonthly))
	default:
		// Return all values as HTML
		var html string

		log.Printf("Using language for CO2 display: %s", language)

		if language == "th" {
			html = fmt.Sprintf(`
			<div class="co2-stat">
				<div class="co2-label">รายเดือน</div>
				<div class="co2-value">%s TCO2eq</div>
			</div>
			<div class="co2-stat">
				<div class="co2-label">รายปี</div>
				<div class="co2-value">%s TCO2eq</div>
			</div>
			<div class="co2-stat">
				<div class="co2-label">ทั้งหมด</div>
				<div class="co2-value">%s TCO2eq</div>
			</div>
			`, formattedMonthly, formattedYearly, formattedTotal)
		} else {
			html = fmt.Sprintf(`
			<div class="co2-stat">
				<div class="co2-label">Monthly:</div>
				<div class="co2-value">%s TCO2eq</div>
			</div>
			<div class="co2-stat">
				<div class="co2-label">Yearly:</div>
				<div class="co2-value">%s TCO2eq</div>
			</div>
			<div class="co2-stat">
				<div class="co2-label">Total:</div>
				<div class="co2-value">%s TCO2eq</div>
			</div>
			`, formattedMonthly, formattedYearly, formattedTotal)
		}

		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(html))
	}

	log.Printf("CO2EmissionsSavedHandler completed successfully")
}
