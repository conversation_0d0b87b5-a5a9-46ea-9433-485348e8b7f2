package index

type matchings struct {
	Order         string
	Product       string
	QuantityUnit  string
	Quality       string
	Packaging     string
	Market        string
	SubMarket     string
	ContractType  string
	DeliveryTerm  string
	Country       string
	ProvinceState string
	PaymentTerm   string
}

type GlobalMatchings struct {
	Order           string
	Product         string
	QuantityUnit    string
	Quality         string
	Packaging       string
	Market          string
	SubMarket       string
	ContractType    string
	DeliveryTerm    string
	Region          string
	Country         string
	PortOfLoading   string
	PortOfDischarge string
	PaymentTerm     string
}

type ProductIndex struct {
	ProductName       string
	OriginalTHBVolume float64
	OriginalUSDVolume float64
	TotalMarketValue  float64
	Currency          string
}

type DeliveryTermPrice struct {
	ProductName  string
	DeliveryTerm string
	BuyPrice     float64
	SellPrice    float64
	Currency     string
}

// DeliveryTermVolume represents the trading volume by delivery term
type DeliveryTermVolume struct {
	DeliveryTerm string
	BuyVolume    float64
	SellVolume   float64
	Currency     string
}
type ActiveOrderSummary struct {
	MarketType   string
	Market       string
	DeliveryTerm string
	BuyCount     int
	SellCount    int
}

// CO2EmissionsSaved represents CO2 emissions saved data
type CO2EmissionsSaved struct {
	Total   float64
	Yearly  float64
	Monthly float64
}
