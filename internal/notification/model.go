package notification

import (
	"time"
)

// NotificationType represents the type of notification
type NotificationType string

const (
	// NotificationTypeEmail represents an email notification
	NotificationTypeEmail NotificationType = "email"
	// NotificationTypeSMS represents an SMS notification
	NotificationTypeSMS NotificationType = "sms"
	// NotificationTypePasswordReset represents a password reset notification
	NotificationTypePasswordReset NotificationType = "password_reset"
	// NotificationTypePasswordResetSuccess represents a successful password reset notification
	NotificationTypePasswordResetSuccess NotificationType = "password_reset_success"
	// NotificationTypeRegistration represents a registration notification
	NotificationTypeRegistration NotificationType = "registration"
	// NotificationTypeProfileAndAddress represents a profile and address completion reminder notification
	NotificationTypeProfileAndAddress NotificationType = "profile_and_address"
	// NotificationTypeOrderSubmission represents an order submission notification
	NotificationTypeOrderSubmission NotificationType = "order_submission"
	// Add new notification types
	NotificationTypeMatching             NotificationType = "matching"
	NotificationTypeContractConfirmation NotificationType = "contract_confirmation"
	NotificationTypeContractCancellation NotificationType = "contract_cancellation"
	// NotificationTypeContractStatusUpdate represents a contract status update notification
	NotificationTypeContractStatusUpdate NotificationType = "contract_status_update"
	// NotificationTypeMatchExpiryReminder represents a reminder notification for unconfirmed users on day 3
	NotificationTypeMatchExpiryReminder NotificationType = "match_expiry_reminder"
	// NotificationTypeMatchExpiryReminderConfirmed represents a reminder notification for users who have confirmed but their counterparty hasn't
	NotificationTypeMatchExpiryReminderConfirmed NotificationType = "match_expiry_reminder_confirmed"
	// NotificationTypeInvoicePayment represents a notification for invoice payment
	NotificationTypeInvoicePayment NotificationType = "invoice_payment"
)

// NotificationStatus represents the status of a notification
type NotificationStatus string

const (
	// NotificationStatusPending means the notification is waiting to be sent
	NotificationStatusPending NotificationStatus = "pending"
	// NotificationStatusSent means the notification was sent successfully
	NotificationStatusSent NotificationStatus = "sent"
	// NotificationStatusFailed means the notification failed to send
	NotificationStatusFailed NotificationStatus = "failed"

	// NotificationSequenceDelay is the delay between sequential notifications
	NotificationSequenceDelay = 3 * time.Second
)

// For compatibility with string-based code
const (
	NotificationTypeEmailStr                        = "email"
	NotificationTypeSMSStr                          = "sms"
	NotificationTypePasswordResetStr                = "password_reset"
	NotificationTypePasswordResetSuccessStr         = "password_reset_success"
	NotificationTypeRegistrationStr                 = "registration"
	NotificationTypeProfileAndAddressStr            = "profile_and_address"
	NotificationTypeOrderSubmissionStr              = "order_submission"
	NotificationTypeMatchingStr                     = "matching"
	NotificationTypeContractConfirmationStr         = "contract_confirmation"
	NotificationTypeContractCancellationStr         = "contract_cancellation"
	NotificationTypeContractStatusUpdateStr         = "contract_status_update"
	NotificationTypeMatchExpiryReminderStr          = "match_expiry_reminder"
	NotificationTypeMatchExpiryReminderConfirmedStr = "match_expiry_reminder_confirmed"
	NotificationTypeInvoicePaymentStr               = "invoice_payment"
	NotificationStatusPendingStr                    = "pending"
	NotificationStatusSentStr                       = "sent"
	NotificationStatusFailedStr                     = "failed"
)

// NotificationTypeInfo represents a notification type record in the database
type NotificationTypeInfo struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

// Notification represents a notification record in the database
type Notification struct {
	ID                   int64                  `json:"id"`
	UserID               int64                  `json:"user_id"`
	Type                 string                 `json:"type,omitempty"` // Kept for backward compatibility
	NotificationTypeID   int64                  `json:"notification_type_id"`
	NotificationTypeName string                 `json:"notification_type_name,omitempty"` // Added for convenience
	Email                string                 `json:"email,omitempty"`
	Phone                string                 `json:"phone,omitempty"`
	Subject              string                 `json:"subject"`
	Body                 string                 `json:"body"`
	Status               string                 `json:"status"`
	CreatedAt            time.Time              `json:"created_at"`
	SentAt               time.Time              `json:"sent_at,omitempty"`
	Data                 map[string]interface{} `json:"data,omitempty"` // Additional data for the notification
}

// RegistrationNotification contains data for a registration notification
type RegistrationNotification struct {
	UserID           int64  `json:"user_id"`
	Email            string `json:"email"`
	FirstName        string `json:"first_name"`
	LastName         string `json:"last_name"`
	Username         string `json:"username"`
	OrganizationName string `json:"organization_name,omitempty"`
	Phone            string `json:"phone,omitempty"`
	Language         string `json:"language"` // Added to support language preference
	BaseURL          string `json:"base_url"` // Added to support configurable URLs
}

// PasswordResetNotification contains data for a password reset notification
type PasswordResetNotification struct {
	UserID    int64  `json:"user_id"`
	Email     string `json:"email"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Username  string `json:"username"`
	ResetLink string `json:"reset_link"`
	Language  string `json:"language"` // Added to support language preference
	BaseURL   string `json:"base_url"` // Added to support configurable URLs
}

// PasswordResetSuccessNotification contains data for a successful password reset notification
type PasswordResetSuccessNotification struct {
	UserID    int64  `json:"user_id"`
	Email     string `json:"email"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Username  string `json:"username"`
	Language  string `json:"language"` // Added to support language preference
	BaseURL   string `json:"base_url"` // Added to support configurable URLs
}

// ProfileIncompleteNotification contains data for profile completion reminders
type ProfileIncompleteNotification struct {
	UserID                      int64               `json:"user_id"`
	Email                       string              `json:"email"`
	FirstName                   string              `json:"first_name"`
	LastName                    string              `json:"last_name"`
	Username                    string              `json:"username"`
	MissingBasicFields          []string            `json:"missing_basic_fields"`
	MissingAddressFields        []string            `json:"missing_address_fields"`
	MissingFields               []string            `json:"missing_fields"`
	MissingCategories           map[string][]string `json:"missing_categories"`
	ProfileCompletionPercentage int                 `json:"profile_completion_percentage"`
	FieldImportance             map[string]string   `json:"field_importance"`
	ProfileURL                  string              `json:"profile_url"`
	BasicInfoURL                string              `json:"basic_info_url"`
	AddressURL                  string              `json:"address_url"`
	Language                    string              `json:"language"` // Added to support language preference
	BaseURL                     string              `json:"base_url"` // Added to support configurable URLs
}

// OrderSubmissionNotification contains data for order submission notifications
type OrderSubmissionNotification struct {
	UserID              int64   `json:"user_id"`
	Email               string  `json:"email"`
	FirstName           string  `json:"first_name"`
	LastName            string  `json:"last_name"`
	Username            string  `json:"username"`
	OrderID             int64   `json:"order_id"`
	OrderType           string  `json:"order_type"` // Buy or Sell
	ProductName         string  `json:"product_name"`
	Quantity            float64 `json:"quantity"`
	UnitOfMeasure       string  `json:"unit_of_measure"`
	Price               float64 `json:"price"`
	Currency            string  `json:"currency"`
	DeliveryTerms       string  `json:"delivery_terms"`
	DeliveryTermID      int     `json:"delivery_term_id"` // Added to help with conditional display
	FirstDeliveryDate   string  `json:"first_delivery_date"`
	LastDeliveryDate    string  `json:"last_delivery_date"`
	MarketspaceName     string  `json:"marketspace_name"`
	MarketspaceID       int     `json:"marketspace_id"` // Added to help with conditional display
	MarketName          string  `json:"market_name"`
	SubmarketName       string  `json:"submarket_name"`
	ContractTypeName    string  `json:"contract_type_name"`
	QualityName         string  `json:"quality_name"`
	PackingName         string  `json:"packing_name"`
	PaymentTermName     string  `json:"payment_term_name"`
	CountryName         string  `json:"country_name"`
	PortOfLoadingName   string  `json:"port_of_loading_name"`
	PortOfDischargeName string  `json:"port_of_discharge_name"`
	ProvinceName        string  `json:"province_name"`
	DistrictName        string  `json:"district_name"`
	SubdistrictName     string  `json:"subdistrict_name"`
	Remark              string  `json:"remark"`
	OrderDetailsURL     string  `json:"order_details_url"`
	Language            string  `json:"language"` // Added to support language preference
	BaseURL             string  `json:"base_url"` // Added to support configurable URLs
}

// MatchingNotification contains data for match notifications
type MatchingNotification struct {
	UserID                  int64   `json:"user_id"`
	Email                   string  `json:"email"`
	FirstName               string  `json:"first_name"`
	LastName                string  `json:"last_name"`
	Username                string  `json:"username"`
	ContractID              int64   `json:"contract_id"`
	Role                    string  `json:"role"` // "buyer" or "seller"
	CounterpartyName        string  `json:"counterparty_name"`
	CounterpartyCompany     string  `json:"counterparty_company"`
	CounterpartyPhone       string  `json:"counterparty_phone"`
	CounterpartyEmail       string  `json:"counterparty_email"`
	CounterpartyCountry     string  `json:"counterparty_country"`
	CounterpartyProvince    string  `json:"counterparty_province"`
	CounterpartyDistrict    string  `json:"counterparty_district"`
	CounterpartySubdistrict string  `json:"counterparty_subdistrict"`
	CounterpartyLocation    string  `json:"counterparty_location"`
	ProductName             string  `json:"product_name"`
	Quantity                float64 `json:"quantity"`
	UnitOfMeasure           string  `json:"unit_of_measure"`
	Price                   float64 `json:"price"`
	Currency                string  `json:"currency"`
	TotalValue              float64 `json:"total_value"`        // Pre-calculated total value (Price * Quantity)
	IsPartialMatch          bool    `json:"is_partial_match"`   // Whether this is a partial match of the original order
	OriginalQuantity        float64 `json:"original_quantity"`  // Original order quantity before matching
	RemainingQuantity       float64 `json:"remaining_quantity"` // Remaining quantity after matching
	DeliveryTerms           string  `json:"delivery_terms"`
	DeliveryLocation        string  `json:"delivery_location"`
	FirstDeliveryDate       string  `json:"first_delivery_date"`
	LastDeliveryDate        string  `json:"last_delivery_date"`
	DeadlineDate            string  `json:"deadline_date"` // 3 days from match
	ContractDetailsURL      string  `json:"contract_details_url"`
	Language                string  `json:"language"`
	BaseURL                 string  `json:"base_url"`
}

// ContractConfirmationNotification contains data for contract confirmation notifications
type ContractConfirmationNotification struct {
	UserID               int64   `json:"user_id"`
	Email                string  `json:"email"`
	FirstName            string  `json:"first_name"`
	LastName             string  `json:"last_name"`
	Username             string  `json:"username"`
	Phone                string  `json:"phone"`
	ContractID           int64   `json:"contract_id"`
	Role                 string  `json:"role"` // "buyer" or "seller"
	CounterpartyName     string  `json:"counterparty_name"`
	CounterpartyCompany  string  `json:"counterparty_company"`
	CounterpartyPhone    string  `json:"counterparty_phone"`
	CounterpartyEmail    string  `json:"counterparty_email"`
	CounterpartyLocation string  `json:"counterparty_location"`
	ProductName          string  `json:"product_name"`
	Quantity             float64 `json:"quantity"`
	UnitOfMeasure        string  `json:"unit_of_measure"`
	Price                float64 `json:"price"`
	Currency             string  `json:"currency"`
	TotalValue           float64 `json:"total_value"` // Pre-calculated total value (Price * Quantity)
	DeliveryTerms        string  `json:"delivery_terms"`
	DeliveryLocation     string  `json:"delivery_location"`
	FirstDeliveryDate    string  `json:"first_delivery_date"`
	LastDeliveryDate     string  `json:"last_delivery_date"`
	ContractDetailsURL   string  `json:"contract_details_url"`
	Language             string  `json:"language"`
	BaseURL              string  `json:"base_url"`
	IsFirstConfirmation  bool    `json:"is_first_confirmation"` // Indicates if this is the first confirmation notification
}

// ContractCancellationNotification contains data for contract cancellation notifications
type ContractCancellationNotification struct {
	UserID              int64   `json:"user_id"`
	Email               string  `json:"email"`
	FirstName           string  `json:"first_name"`
	LastName            string  `json:"last_name"`
	Username            string  `json:"username"`
	ContractID          int64   `json:"contract_id"`
	Role                string  `json:"role"` // "buyer" or "seller"
	CounterpartyName    string  `json:"counterparty_name"`
	CounterpartyCompany string  `json:"counterparty_company"`
	ProductName         string  `json:"product_name"`
	Quantity            float64 `json:"quantity"`
	UnitOfMeasure       string  `json:"unit_of_measure"`
	Price               float64 `json:"price"`
	Currency            string  `json:"currency"`
	TotalValue          float64 `json:"total_value"` // Pre-calculated total value (Price * Quantity)
	DeliveryTerms       string  `json:"delivery_terms"`
	CancellationReason  string  `json:"cancellation_reason"`
	ContractDetailsURL  string  `json:"contract_details_url"`
	Language            string  `json:"language"`
	BaseURL             string  `json:"base_url"`
}

// ContractStatusUpdateNotification contains data for contract status update notifications
type ContractStatusUpdateNotification struct {
	UserID              int64   `json:"user_id"`
	Email               string  `json:"email"`
	FirstName           string  `json:"first_name"`
	LastName            string  `json:"last_name"`
	Username            string  `json:"username"`
	ContractID          int64   `json:"contract_id"`
	Role                string  `json:"role"` // "buyer" or "seller"
	CounterpartyName    string  `json:"counterparty_name"`
	CounterpartyEmail   string  `json:"counterparty_email"`   // Added for deadline reminder notifications
	CounterpartyCompany string  `json:"counterparty_company"` // Added for rejection notifications
	ProductName         string  `json:"product_name"`
	Quantity            float64 `json:"quantity"`
	UnitOfMeasure       string  `json:"unit_of_measure"`
	Price               float64 `json:"price"`
	Currency            string  `json:"currency"`
	TotalValue          float64 `json:"total_value"` // Pre-calculated total value (Price * Quantity)
	DeliveryTerms       string  `json:"delivery_terms"`
	ActionTaken         string  `json:"action_taken"`    // "confirmed", "rejected", "deadline", "waiting", etc.
	IsActionTaker       bool    `json:"is_action_taker"` // true if this user took the action, false if they're receiving notification
	ContractDetailsURL  string  `json:"contract_details_url"`
	Language            string  `json:"language"`
	BaseURL             string  `json:"base_url"`
}

// MatchExpiryReminderNotification contains data for match expiry reminder notifications
type MatchExpiryReminderNotification struct {
	UserID              int64   `json:"user_id"`
	Email               string  `json:"email"`
	FirstName           string  `json:"first_name"`
	LastName            string  `json:"last_name"`
	Username            string  `json:"username"`
	ContractID          int64   `json:"contract_id"`
	Role                string  `json:"role"` // "buyer" or "seller"
	CounterpartyName    string  `json:"counterparty_name"`
	CounterpartyEmail   string  `json:"counterparty_email"`
	CounterpartyCompany string  `json:"counterparty_company"`
	ProductName         string  `json:"product_name"`
	Quantity            float64 `json:"quantity"`
	UnitOfMeasure       string  `json:"unit_of_measure"`
	Price               float64 `json:"price"`
	Currency            string  `json:"currency"`
	TotalValue          float64 `json:"total_value"` // Pre-calculated total value (Price * Quantity)
	DeliveryTerms       string  `json:"delivery_terms"`
	ContractDetailsURL  string  `json:"contract_details_url"`
	Language            string  `json:"language"`
	BaseURL             string  `json:"base_url"`
}

// InvoicePaymentNotification contains data for invoice payment notifications
type InvoicePaymentNotification struct {
	UserID            int64   `json:"user_id"`
	Email             string  `json:"email"`
	FirstName         string  `json:"first_name"`
	LastName          string  `json:"last_name"`
	Username          string  `json:"username"`
	InvoiceID         int64   `json:"invoice_id"`
	InvoiceNumber     string  `json:"invoice_number"`
	ContractID        int64   `json:"contract_id"`
	ContractNumber    string  `json:"contract_number"`
	Role              string  `json:"role"` // "buyer" or "seller"
	Amount            float64 `json:"amount"`
	Currency          string  `json:"currency"`
	DueDate           string  `json:"due_date"`
	ProductName       string  `json:"product_name"`
	InvoiceDetailsURL string  `json:"invoice_details_url"`
	Language          string  `json:"language"`
	BaseURL           string  `json:"base_url"`
}

// EmailConfig contains SMTP server configuration
type EmailConfig struct {
	Host     string
	Port     string
	Username string
	Password string
	From     string
	FromName string
}

// NewEmailConfig creates a new email configuration
func NewEmailConfig(host, port, username, password, from, fromName string) *EmailConfig {
	return &EmailConfig{
		Host:     host,
		Port:     port,
		Username: username,
		Password: password,
		From:     from,
		FromName: fromName,
	}
}

// User represents a user in the system
type User struct {
	ID               int64  `json:"id"`
	Email            string `json:"email"`
	FirstName        string `json:"first_name"`
	LastName         string `json:"last_name"`
	Username         string `json:"username"`
	Phone            string `json:"phone,omitempty"`
	OrganizationName string `json:"organization_name,omitempty"`
	UserType         string `json:"user_type,omitempty"`
	IsSystemUser     bool   `json:"is_system_user,omitempty"`
	IsActive         bool   `json:"is_active,omitempty"`
}

// Address represents a user's address
type Address struct {
	ID             int64  `json:"id"`
	UserID         int64  `json:"user_id"`
	AddressType    string `json:"address_type"`
	Country        string `json:"country"`
	Province       string `json:"province"`
	District       string `json:"district"`
	Subdistrict    string `json:"subdistrict"`
	AddressDetails string `json:"address_details"`
	PostalCode     string `json:"postal_code"`
	IsPrimary      bool   `json:"is_primary"`
}

// ProfileCompleteness represents how complete a user's profile is
type ProfileCompleteness struct {
	HasBasicInfo            bool              `json:"has_basic_info"`
	HasAddressInfo          bool              `json:"has_address_info"`
	MissingBasicFields      []string          `json:"missing_basic_fields"`
	MissingAddressFields    []string          `json:"missing_address_fields"`
	CompletionPercentage    int               `json:"completion_percentage"`
	UserID                  int64             `json:"user_id,omitempty"`
	Email                   string            `json:"email,omitempty"`
	FirstName               string            `json:"first_name,omitempty"`
	LastName                string            `json:"last_name,omitempty"`
	OrganizationName        string            `json:"organization_name,omitempty"`
	Phone                   string            `json:"phone,omitempty"`
	BasicFieldsImportance   map[string]string `json:"basic_fields_importance,omitempty"`
	AddressFieldsImportance map[string]string `json:"address_fields_importance,omitempty"`
}

// EmailMessage represents an email to be sent
type EmailMessage struct {
	To      string
	Subject string
	Body    string
	IsHTML  bool
}

// GetNotificationTypeID returns the ID for a given notification type name
func GetNotificationTypeID(typeName string) int64 {
	// This is a placeholder. In a real implementation, you would look up the ID
	// from the database or maintain a mapping.
	switch typeName {
	case string(NotificationTypeEmail):
		return 1
	case string(NotificationTypeSMS):
		return 2
	case string(NotificationTypePasswordReset):
		return 3
	case string(NotificationTypePasswordResetSuccess):
		return 4
	case string(NotificationTypeRegistration):
		return 5
	case string(NotificationTypeProfileAndAddress):
		return 6
	case string(NotificationTypeOrderSubmission):
		return 7
	case string(NotificationTypeMatching):
		return 8
	case string(NotificationTypeContractConfirmation):
		return 9
	case string(NotificationTypeContractCancellation):
		return 10
	case string(NotificationTypeContractStatusUpdate):
		return 11
	case string(NotificationTypeMatchExpiryReminder):
		return 12
	case string(NotificationTypeMatchExpiryReminderConfirmed):
		return 13
	case string(NotificationTypeInvoicePayment):
		return 14
	default:
		return 0
	}
}
