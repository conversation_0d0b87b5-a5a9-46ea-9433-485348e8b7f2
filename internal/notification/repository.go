package notification

import (
	"database/sql"
	"log"
	"time"
)

// Repository defines the interface for notification data operations
type Repository interface {
	CreateNotification(notification *Notification) error
	GetNotificationByID(id int64) (*Notification, error)
	GetNotificationsByUserID(userID int64) ([]*Notification, error)
	GetPendingNotifications() ([]*Notification, error)
	MarkNotificationAsSent(id int64) error
	MarkNotificationAsFailed(id int64, errorMsg string) error
	UpdateNotificationStatus(id int64, status string, errorMsg string) error
	// Methods for notification types
	GetNotificationTypeByName(name string) (*NotificationTypeInfo, error)
	GetNotificationTypeByID(id int64) (*NotificationTypeInfo, error)
	CreateNotificationType(name string) (*NotificationTypeInfo, error)
	GetAllNotificationTypes() ([]*NotificationTypeInfo, error)
	// Method to check if a notification exists
	NotificationExists(userID int64, notificationTypeID int64, invoiceID int64) (bool, error)
	// Method to check if user has addresses
	UserHasAddresses(userID int64) (bool, error)
	// Methods for user data
	GetAllUsers() ([]*User, error)
	GetActiveUsers() ([]*User, error) // Kept for backward compatibility, use GetAllUsers instead
	GetUserByID(userID int64) (*User, error)
	GetUserAddresses(userID int64) ([]*Address, error)
	GetUserProfileCompleteness(userID int64) (*ProfileCompleteness, error)
}

// PostgresRepository implements Repository interface for PostgreSQL
type PostgresRepository struct {
	db *sql.DB
}

// NewPostgresRepository creates a new PostgreSQL repository
func NewPostgresRepository(db *sql.DB) *PostgresRepository {
	return &PostgresRepository{
		db: db,
	}
}

// NewRepository creates a new repository instance
func NewRepository(db *sql.DB) Repository {
	return NewPostgresRepository(db)
}

// UserHasAddresses checks if a user has any addresses in the system
func (r *PostgresRepository) UserHasAddresses(userID int64) (bool, error) {
	query := `
        SELECT EXISTS(
            SELECT 1 FROM addresses
            WHERE user_id = $1
        )
    `
	var hasAddresses bool
	err := r.db.QueryRow(query, userID).Scan(&hasAddresses)
	if err != nil {
		log.Printf("Error checking if user %d has addresses: %v", userID, err)
		return false, err
	}
	return hasAddresses, nil
}

// GetNotificationTypeByName retrieves a notification type by name
func (r *PostgresRepository) GetNotificationTypeByName(name string) (*NotificationTypeInfo, error) {
	query := `
        SELECT id, name
        FROM notification_types
        WHERE name = $1
    `
	notificationType := &NotificationTypeInfo{}
	err := r.db.QueryRow(query, name).Scan(
		&notificationType.ID,
		&notificationType.Name,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return notificationType, nil
}

// GetNotificationTypeByID retrieves a notification type by ID
func (r *PostgresRepository) GetNotificationTypeByID(id int64) (*NotificationTypeInfo, error) {
	query := `
        SELECT id, name
        FROM notification_types
        WHERE id = $1
    `
	notificationType := &NotificationTypeInfo{}
	err := r.db.QueryRow(query, id).Scan(
		&notificationType.ID,
		&notificationType.Name,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return notificationType, nil
}

// GetAllNotificationTypes retrieves all notification types
func (r *PostgresRepository) GetAllNotificationTypes() ([]*NotificationTypeInfo, error) {
	query := `
        SELECT id, name
        FROM notification_types
        ORDER BY name
    `
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var types []*NotificationTypeInfo
	for rows.Next() {
		notificationType := &NotificationTypeInfo{}
		err := rows.Scan(
			&notificationType.ID,
			&notificationType.Name,
		)
		if err != nil {
			return nil, err
		}
		types = append(types, notificationType)
	}
	if err = rows.Err(); err != nil {
		return nil, err
	}
	return types, nil
}

// CreateNotificationType creates a new notification type
func (r *PostgresRepository) CreateNotificationType(name string) (*NotificationTypeInfo, error) {
	query := `
        INSERT INTO notification_types (name)
        VALUES ($1)
        RETURNING id, name
    `
	notificationType := &NotificationTypeInfo{}
	err := r.db.QueryRow(query, name).Scan(
		&notificationType.ID,
		&notificationType.Name,
	)
	if err != nil {
		return nil, err
	}
	return notificationType, nil
}

// CreateNotification creates a new notification record
func (r *PostgresRepository) CreateNotification(notification *Notification) error {
	// First, ensure we have a notification type ID
	if notification.NotificationTypeID == 0 && notification.Type != "" {
		// Look up the notification type by name
		notificationType, err := r.GetNotificationTypeByName(notification.Type)
		if err != nil {
			return err
		}
		// If notification type doesn't exist, create it
		if notificationType == nil {
			notificationType, err = r.CreateNotificationType(notification.Type)
			if err != nil {
				return err
			}
		}
		notification.NotificationTypeID = notificationType.ID
	}
	query := `
        INSERT INTO notifications (user_id, notification_type_id, email, phone, subject, body, status, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id
    `
	err := r.db.QueryRow(
		query,
		notification.UserID,
		notification.NotificationTypeID,
		notification.Email,
		notification.Phone,
		notification.Subject,
		notification.Body,
		notification.Status,
		notification.CreatedAt,
	).Scan(&notification.ID)
	if err != nil {
		log.Printf("Error creating notification: %v", err)
		return err
	}
	return nil
}

// GetNotificationByID retrieves a notification by ID
func (r *PostgresRepository) GetNotificationByID(id int64) (*Notification, error) {
	query := `
        SELECT n.id, n.user_id, nt.name, n.notification_type_id, n.email, n.phone, n.subject, n.body, n.status, n.created_at, n.sent_at
        FROM notifications n
        JOIN notification_types nt ON n.notification_type_id = nt.id
        WHERE n.id = $1
    `
	notification := &Notification{}
	err := r.db.QueryRow(query, id).Scan(
		&notification.ID,
		&notification.UserID,
		&notification.Type,
		&notification.NotificationTypeID,
		&notification.Email,
		&notification.Phone,
		&notification.Subject,
		&notification.Body,
		&notification.Status,
		&notification.CreatedAt,
		&notification.SentAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return notification, nil
}

// GetNotificationsByUserID retrieves notifications for a user
func (r *PostgresRepository) GetNotificationsByUserID(userID int64) ([]*Notification, error) {
	query := `
        SELECT n.id, n.user_id, nt.name, n.notification_type_id, n.email, n.phone, n.subject, n.body, n.status, n.created_at, n.sent_at
        FROM notifications n
        JOIN notification_types nt ON n.notification_type_id = nt.id
        WHERE n.user_id = $1
        ORDER BY n.created_at DESC
    `
	rows, err := r.db.Query(query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var notifications []*Notification
	for rows.Next() {
		notification := &Notification{}
		err := rows.Scan(
			&notification.ID,
			&notification.UserID,
			&notification.Type,
			&notification.NotificationTypeID,
			&notification.Email,
			&notification.Phone,
			&notification.Subject,
			&notification.Body,
			&notification.Status,
			&notification.CreatedAt,
			&notification.SentAt,
		)
		if err != nil {
			return nil, err
		}
		notifications = append(notifications, notification)
	}
	if err = rows.Err(); err != nil {
		return nil, err
	}
	return notifications, nil
}

// GetPendingNotifications retrieves all pending notifications
func (r *PostgresRepository) GetPendingNotifications() ([]*Notification, error) {
	query := `
        SELECT n.id, n.user_id, nt.name, n.notification_type_id, n.email, n.phone, n.subject, n.body, n.status, n.created_at
        FROM notifications n
        JOIN notification_types nt ON n.notification_type_id = nt.id
        WHERE n.status = $1
        ORDER BY n.created_at ASC
    `
	rows, err := r.db.Query(query, string(NotificationStatusPending))
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var notifications []*Notification
	for rows.Next() {
		notification := &Notification{}
		err := rows.Scan(
			&notification.ID,
			&notification.UserID,
			&notification.Type,
			&notification.NotificationTypeID,
			&notification.Email,
			&notification.Phone,
			&notification.Subject,
			&notification.Body,
			&notification.Status,
			&notification.CreatedAt,
		)
		if err != nil {
			return nil, err
		}
		notifications = append(notifications, notification)
	}
	if err = rows.Err(); err != nil {
		return nil, err
	}
	return notifications, nil
}

// MarkNotificationAsSent marks a notification as sent
func (r *PostgresRepository) MarkNotificationAsSent(id int64) error {
	now := time.Now()
	query := `
        UPDATE notifications
        SET status = $1, sent_at = $2
        WHERE id = $3
    `
	_, err := r.db.Exec(query, string(NotificationStatusSent), now, id)
	return err
}

// MarkNotificationAsFailed marks a notification as failed
func (r *PostgresRepository) MarkNotificationAsFailed(id int64, errorMsg string) error {
	query := `
        UPDATE notifications
        SET status = $1
        WHERE id = $2
    `
	_, err := r.db.Exec(query, string(NotificationStatusFailed), id)
	return err
}

// UpdateNotificationStatus updates a notification's status
func (r *PostgresRepository) UpdateNotificationStatus(id int64, status string, errorMsg string) error {
	var query string
	var args []interface{}
	if status == string(NotificationStatusSent) {
		now := time.Now()
		query = `
            UPDATE notifications
            SET status = $1, sent_at = $2
            WHERE id = $3
        `
		args = []interface{}{status, now, id}
	} else {
		query = `
            UPDATE notifications
            SET status = $1
            WHERE id = $2
        `
		args = []interface{}{status, id}
	}
	_, err := r.db.Exec(query, args...)
	return err
}

// GetAllUsers retrieves all users from the database
func (r *PostgresRepository) GetAllUsers() ([]*User, error) {
	query := `
        SELECT id, email, first_name, last_name, username, phone, organization_name
        FROM users
        ORDER BY id
    `
	rows, err := r.db.Query(query)
	if err != nil {
		log.Printf("Error getting all users: %v", err)
		return nil, err
	}
	defer rows.Close()

	var users []*User
	for rows.Next() {
		user := &User{}
		err := rows.Scan(
			&user.ID,
			&user.Email,
			&user.FirstName,
			&user.LastName,
			&user.Username,
			&user.Phone,
			&user.OrganizationName,
		)
		if err != nil {
			log.Printf("Error scanning user row: %v", err)
			return nil, err
		}
		users = append(users, user)
	}

	if err = rows.Err(); err != nil {
		log.Printf("Error iterating user rows: %v", err)
		return nil, err
	}

	return users, nil
}

// GetActiveUsers retrieves all users from the database
// Note: This function is named GetActiveUsers for backward compatibility,
// but it actually returns all users since there is no is_active column in the users table.
// We're keeping this function for backward compatibility, but new code should use GetAllUsers instead.
func (r *PostgresRepository) GetActiveUsers() ([]*User, error) {
	// Since there's no is_active column, we're just returning all users
	return r.GetAllUsers()
}

// GetUserByID retrieves a user by their ID
func (r *PostgresRepository) GetUserByID(userID int64) (*User, error) {
	query := `
        SELECT id, email, first_name, last_name, username, phone, organization_name
        FROM users
        WHERE id = $1
    `
	user := &User{}
	err := r.db.QueryRow(query, userID).Scan(
		&user.ID,
		&user.Email,
		&user.FirstName,
		&user.LastName,
		&user.Username,
		&user.Phone,
		&user.OrganizationName,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		log.Printf("Error getting user by ID %d: %v", userID, err)
		return nil, err
	}

	return user, nil
}

// GetUserAddresses retrieves all addresses for a user
func (r *PostgresRepository) GetUserAddresses(userID int64) ([]*Address, error) {
	query := `
        SELECT id, user_id, address_type, country, province, district,
               subdistrict, address_details, postal_code, is_primary
        FROM addresses
        WHERE user_id = $1
        ORDER BY is_primary DESC, id
    `
	rows, err := r.db.Query(query, userID)
	if err != nil {
		log.Printf("Error getting addresses for user %d: %v", userID, err)
		return nil, err
	}
	defer rows.Close()

	var addresses []*Address
	for rows.Next() {
		address := &Address{}
		err := rows.Scan(
			&address.ID,
			&address.UserID,
			&address.AddressType,
			&address.Country,
			&address.Province,
			&address.District,
			&address.Subdistrict,
			&address.AddressDetails,
			&address.PostalCode,
			&address.IsPrimary,
		)
		if err != nil {
			log.Printf("Error scanning address row: %v", err)
			return nil, err
		}
		addresses = append(addresses, address)
	}

	if err = rows.Err(); err != nil {
		log.Printf("Error iterating address rows: %v", err)
		return nil, err
	}

	return addresses, nil
}

// NotificationExists checks if a notification exists for a user, notification type, and invoice ID
func (r *PostgresRepository) NotificationExists(userID int64, notificationTypeID int64, invoiceID int64) (bool, error) {
	query := `
        SELECT EXISTS(
            SELECT 1 FROM notifications
            WHERE user_id = $1
            AND notification_type_id = $2
            AND status = 'pending'
            AND created_at > NOW() - INTERVAL '1 day'
        )
    `
	var exists bool
	err := r.db.QueryRow(query, userID, notificationTypeID).Scan(&exists)
	if err != nil {
		log.Printf("Error checking if notification exists: %v", err)
		return false, err
	}
	return exists, nil
}

// GetUserProfileCompleteness calculates how complete a user's profile is
func (r *PostgresRepository) GetUserProfileCompleteness(userID int64) (*ProfileCompleteness, error) {
	// Get the user first
	user, err := r.GetUserByID(userID)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, sql.ErrNoRows
	}

	// Check if user has addresses
	hasAddresses, err := r.UserHasAddresses(userID)
	if err != nil {
		return nil, err
	}

	// Initialize the completeness object
	completeness := &ProfileCompleteness{
		HasBasicInfo:   true,
		HasAddressInfo: hasAddresses,
	}

	// Check for missing basic fields
	if user.FirstName == "" {
		completeness.HasBasicInfo = false
		completeness.MissingBasicFields = append(completeness.MissingBasicFields, "First Name")
	}

	if user.LastName == "" {
		completeness.HasBasicInfo = false
		completeness.MissingBasicFields = append(completeness.MissingBasicFields, "Last Name")
	}

	if user.Phone == "" {
		completeness.HasBasicInfo = false
		completeness.MissingBasicFields = append(completeness.MissingBasicFields, "Phone")
	}

	if user.OrganizationName == "" && (user.UserType == "business" || user.UserType == "organization") {
		completeness.HasBasicInfo = false
		completeness.MissingBasicFields = append(completeness.MissingBasicFields, "Organization Name")
	}

	// If user doesn't have addresses, add missing address fields
	if !hasAddresses {
		completeness.MissingAddressFields = append(
			completeness.MissingAddressFields,
			"Country", "Province", "District", "Subdistrict", "Address Details", "Postal Code",
		)
	}

	// Calculate completion percentage
	totalFields := 4 // Basic fields: first_name, last_name, phone, organization_name (if applicable)
	if user.UserType != "business" && user.UserType != "organization" {
		totalFields = 3 // Don't count organization_name for individual users
	}
	totalFields += 6 // Address fields

	completedFields := totalFields - (len(completeness.MissingBasicFields) + len(completeness.MissingAddressFields))
	completeness.CompletionPercentage = (completedFields * 100) / totalFields

	return completeness, nil
}
