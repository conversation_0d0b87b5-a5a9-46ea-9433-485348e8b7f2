package notification

import (
	"bytes"
	"crypto/tls"
	"database/sql"
	"fmt"
	"html/template"
	"log"
	"math/rand"
	"net/smtp"
	"os"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/thongsoi/biomassx/database"
)

// Service defines the interface for notification operations
type Service interface {
	SendRegistrationEmail(data interface{}) error
	SendPasswordResetEmail(data interface{}) error
	SendPasswordResetSuccessEmail(data interface{}) error
	SendProfileIncompleteNotification(data interface{}) error
	SendOrderSubmissionNotification(data interface{}) error
	SendMatchingNotification(data interface{}) error
	SendContractConfirmationNotification(data interface{}) error
	SendContractCancellationNotification(data interface{}) error
	SendContractStatusUpdateNotification(data interface{}) error
	SendInvoicePaymentNotification(data interface{}) error
	ProcessPendingNotifications() error
	StartScheduledNotifications()
	SendEmail(message *EmailMessage) error
	CheckAddressForNewUser(userID int64) error
	ScheduleProfileCompletionReminders() error
	CheckExpiredContractConfirmations() error
}

// EmailService implements the Service interface for email notifications
type EmailService struct {
	repo                 Repository
	config               *EmailConfig
	mu                   sync.Mutex // Mutex to synchronize access to shared resources
	notificationChannels sync.Map   // Map to store notification completion channels
}

// Email templates for registration (English and Thai)
const (
	registrationTemplateEN = `<!DOCTYPE html><html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Successful</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
            width: 100%;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #b2ff59;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
            border-color: #ff6d00;
        }
        .footer a {
            color: #b2ff59;
            text-decoration: none;
        }
        .footer a:hover {
            color: #ff6d00;
        }
        @media only screen and (max-width: 600px) {
            .content {
                padding: 10px;
            }
            .card {
                padding: 15px;
            }
            .header {
                padding: 15px;
                font-size: 20px;
            }
        }
        .account-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .account-details ul {
            list-style-type: none;
            padding-left: 5px;
        }
        .account-details li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .account-details li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Registration Successful
    </div>
    <div class="content">
        <h2>Welcome to BiomassX!</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>
        <div class="card">
            <p>Thank you for registering with BiomassX! Your account has been created successfully.</p>
            <div class="account-details">
                <p><strong>Account Details:</strong></p>
                <ul>
                    <li><strong>Username:</strong> {{.Username}}</li>
                    <li><strong>Email:</strong> {{.Email}}</li>
                    {{if .Phone}}<li><strong>Phone:</strong> {{.Phone}}</li>{{end}}
                    {{if .OrganizationName}}<li><strong>Organization:</strong> {{.OrganizationName}}</li>{{end}}
                </ul>
            </div>
            <p>You can now log in to your account and start using our services.</p>
            <div style="text-align: center;">
                <a href="{{.BaseURL}}/login" class="button">Login to Your Account</a>
            </div>
        </div>
        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}">Visit Our Website</a></p>
    </div>
</body></html>`

	registrationTemplateTH = `<!DOCTYPE html><html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>การลงทะเบียนสำเร็จ</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
            width: 100%;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #b2ff59;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
            border-color: #ff6d00;
        }
        .footer a {
            color: #b2ff59;
            text-decoration: none;
        }
        .footer a:hover {
            color: #ff6d00;
        }
        @media only screen and (max-width: 600px) {
            .content {
                padding: 10px;
            }
            .card {
                padding: 15px;
            }
            .header {
                padding: 15px;
                font-size: 20px;
            }
        }
        .account-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .account-details ul {
            list-style-type: none;
            padding-left: 5px;
        }
        .account-details li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .account-details li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        การลงทะเบียนสำเร็จ
    </div>
    <div class="content">
        <h2>ยินดีต้อนรับสู่ BiomassX!</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>
        <div class="card">
            <p>ขอบคุณที่ลงทะเบียนกับ BiomassX! บัญชีของคุณถูกสร้างเรียบร้อยแล้ว</p>
            <div class="account-details">
                <p><strong>รายละเอียดบัญชี:</strong></p>
                <ul>
                    <li><strong>ชื่อผู้ใช้:</strong> {{.Username}}</li>
                    <li><strong>อีเมล:</strong> {{.Email}}</li>
                    {{if .Phone}}<li><strong>โทรศัพท์:</strong> {{.Phone}}</li>{{end}}
                    {{if .OrganizationName}}<li><strong>องค์กร:</strong> {{.OrganizationName}}</li>{{end}}
                </ul>
            </div>
            <p>คุณสามารถเข้าสู่ระบบบัญชีของคุณและเริ่มใช้งานบริการของเราได้แล้ว</p>
            <div style="text-align: center;">
                <a href="{{.BaseURL}}/login" class="button">เข้าสู่ระบบบัญชีของคุณ</a>
            </div>
        </div>
        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body></html>`
)

// Email templates for password reset (English and Thai)
const (
	passwordResetTemplateEN = `<!DOCTYPE html><html><head>
    <meta charset="UTF-8">
    <title>Password Reset</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
        }
        .footer a {
            color: #b2ff59;
        }
        .footer a:hover {
            color: #ff6d00;
        }
        @media only screen and (max-width: 600px) {
            .content {
                padding: 10px;
            }
            .card {
                padding: 15px;
            }
            .header {
                padding: 15px;
            }
        }
        .account-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
        }
        .account-details ul {
            list-style-type: none;
        }
        .account-details li {
            padding: 5px 0;
        }
        .account-details li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Password reset request
    </div>
    <div class="content">
        <h2>Password Reset</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>
        <div class="card">
            <p>We received a request to reset your password for your BiomassX account. If you didn't make this request, you can safely ignore this email.</p>
            <p>To reset your password, please click the button below:</p>
            <div style="text-align: center;">
                <a href="{{.ResetLink}}" class="button">Reset Password</a>
            </div>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all;">{{.ResetLink}}</p>
            <p>This link will expire in 1 hour for security reasons.</p>
        </div>
        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}">Visit Our Website</a></p>
    </div>
</body></html>`

	passwordResetTemplateTH = `<!DOCTYPE html><html><head>
    <meta charset="UTF-8">
    <title>รีเซ็ตรหัสผ่าน</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
        }
        .footer a {
            color: #b2ff59;
        }
        .footer a:hover {
            color: #ff6d00;
        }
        @media only screen and (max-width: 600px) {
            .content {
                padding: 10px;
            }
            .card {
                padding: 15px;
            }
            .header {
                padding: 15px;
            }
        }
        .account-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .account-details ul {
            list-style-type: none;
        }
        .account-details li {
            padding: 5px 0;
        }
        .account-details li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        คำขอรีเซ็ตรหัสผ่าน
    </div>
    <div class="content">
        <h2>รีเซ็ตรหัสผ่าน</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>
        <div class="card">
            <p>เราได้รับคำขอให้รีเซ็ตรหัสผ่านสำหรับบัญชี BiomassX ของคุณ หากคุณไม่ได้ทำคำขอนี้ คุณสามารถเพิกเฉยต่ออีเมลนี้ได้อย่างปลอดภัย</p>
            <p>เพื่อรีเซ็ตรหัสผ่านของคุณ โปรดคลิกปุ่มด้านล่าง:</p>
            <div style="text-align: center;">
                <a href="{{.ResetLink}}" class="button">รีเซ็ตรหัสผ่าน</a>
            </div>
            <p>หรือคัดลอกและวางลิงก์นี้ลงในเบราว์เซอร์ของคุณ:</p>
            <p style="word-break: break-all;">{{.ResetLink}}</p>
            <p>ลิงก์นี้จะหมดอายุใน 1 ชั่วโมงเพื่อความปลอดภัย</p>
        </div>
        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body></html>`
)

// Email templates for password reset success (English and Thai)
const (
	passwordResetSuccessTemplateEN = `<!DOCTYPE html><html><head>
        <meta charset="UTF-8">
        <title>Password Reset Successful</title>
        <style>
            body {
                background-color: #ffffff;
                font-family: 'Verdana', 'Geneva', sans-serif;
                color: #333;
                margin: 0;
                padding: 0;
                line-height: 1.6;
            }
            .header {
                background-color: #ff6d00;
                color: white;
                padding: 20px;
                text-align: center;
                font-size: 24px;
                font-weight: bold;
            }
            .nav-bar {
                background-color: #b2ff59;
                padding: 15px;
                text-align: center;
                box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            }
            .content {
                padding: 30px;
                max-width: 600px;
                margin: 0 auto;
                background-color: white;
            }
            .footer {
                background-color: #333;
                color: white;
                text-align: center;
                padding: 20px;
                width: 100%;
            }
            h1, h2 {
                color: black;
            }
            .card {
                background-color: #f9f9f9;
                border-radius: 8px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                padding: 20px;
                margin-bottom: 20px;
                width: 100%;
                box-sizing: border-box;
            }
            .button {
                display: inline-block;
                background-color: #b2ff59;
                color: #333;
                padding: 10px 20px;
                text-decoration: none;
                border-radius: 4px;
                font-size: 16px;
                margin-top: 20px;
                text-align: center;
                font-weight: bold;
                border: 2px solid #b2ff59;
            }
            .button:hover {
                background-color: #ff6d00;
                color: white;
                border-color: #ff6d00;
            }
            .footer a {
                color: #b2ff59;
                text-decoration: none;
            }
            .footer a:hover {
                color: #ff6d00;
            }
            @media only screen and (max-width: 600px) {
                .content {
                    padding: 10px;
                }
                .card {
                    padding: 15px;
                }
                .header {
                    padding: 15px;
                    font-size: 20px;
                }
            }
            .account-details {
                background-color: #fff;
                border-left: 4px solid #b2ff59;
                padding: 10px 15px;
            }
            .account-details ul {
                list-style-type: none;
            }
            .account-details li {
                padding: 5px 0;
            }
            .account-details li:last-child {
                border-bottom: none;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>BiomassX</h1>
        </div>
        <div class="nav-bar">
            Password reset confirmation
        </div>
        <div class="content">
            <h2>Password reset confirmation</h2>
            <p>Hello {{.FirstName}} {{.LastName}},</p>
            <div class="card">
                <p>Your password for your BiomassX account has been successfully reset.</p>
                <p>If you did not make this change, please contact our customer service team immediately as your account may have been compromised.</p>
                <div style="text-align: center;">
                    <a href="{{.BaseURL}}/login" class="button">Login to Your Account</a>
                </div>
            </div>
            <p>Best regards,<br>
            The BiomassX Team</p>
        </div>
        <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
            <p><a href="{{.BaseURL}}">Visit Our Website</a></p>
        </div>
    </body></html>`

	passwordResetSuccessTemplateTH = `<!DOCTYPE html><html><head>
        <meta charset="UTF-8">
        <title>การรีเซ็ตรหัสผ่านสำเร็จ</title>
        <style>
            body {
                background-color: #ffffff;
                font-family: 'Verdana', 'Geneva', sans-serif;
                color: #333;
                margin: 0;
                padding: 0;
                line-height: 1.6;
            }
            .header {
                background-color: #ff6d00;
                color: white;
                padding: 20px;
                text-align: center;
                font-size: 24px;
                font-weight: bold;
            }
            .nav-bar {
                background-color: #b2ff59;
                padding: 15px;
                text-align: center;
                box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            }
            .content {
                padding: 30px;
                max-width: 600px;
                margin: 0 auto;
                background-color: white;
            }
            .footer {
                background-color: #333;
                color: white;
                text-align: center;
                padding: 20px;
                width: 100%;
            }
            h1, h2 {
                color: black;
            }
            .card {
                background-color: #f9f9f9;
                border-radius: 8px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                padding: 20px;
                margin-bottom: 20px;
                width: 100%;
            }
            .button {
                display: inline-block;
                background-color: #b2ff59;
                color: #333;
                padding: 10px 20px;
                text-decoration: none;
                border-radius: 4px;
                font-size: 16px;
                margin-top: 20px;
                text-align: center;
                font-weight: bold;
                border: 2px solid #b2ff59;
            }
            .button:hover {
                background-color: #ff6d00;
                color: white;
                border-color: #ff6d00;
            }
            .footer a {
                color: #b2ff59;
                text-decoration: none;
            }
            .footer a:hover {
                color: #ff6d00;
            }
            @media only screen and (max-width: 600px) {
                .content {
                    padding: 10px;
                }
                .card {
                    padding: 15px;
                }
                .header {
                    padding: 15px;
                    font-size: 20px;
                }
            }
            .account-details {
                background-color: #fff;
                border-left: 4px solid #b2ff59;
                padding: 10px 15px;
                margin: 15px 0;
            }
            .account-details ul {
                list-style-type: none;
            }
            .account-details li {
                padding: 5px 0;
            }
            .account-details li:last-child {
                border-bottom: none;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>BiomassX</h1>
        </div>
        <div class="nav-bar">
            การยืนยันการรีเซ็ตรหัสผ่าน
        </div>
        <div class="content">
            <h2>การยืนยันการรีเซ็ตรหัสผ่าน</h2>
            <p>สวัสดี {{.FirstName}} {{.LastName}},</p>
            <div class="card">
                <p>รหัสผ่านสำหรับบัญชี BiomassX ของคุณถูกรีเซ็ตเรียบร้อยแล้ว</p>
                <p>หากคุณไม่ได้ทำการเปลี่ยนแปลงนี้ กรุณาติดต่อทีมบริการลูกค้าของเราโดยทันที เนื่องจากบัญชีของคุณอาจถูกบุกรุก</p>
                <div style="text-align: center;">
                    <a href="{{.BaseURL}}/login" class="button">เข้าสู่ระบบบัญชีของคุณ</a>
                </div>
            </div>
            <p>ด้วยความเคารพ,<br>
            ทีมงาน BiomassX</p>
        </div>
        <div class="footer">
            <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
            <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
            <p><a href="{{.BaseURL}}">เยี่ยมชมเว็บไซต์ของเรา</a></p>
        </div>
    </body></html>`
)

// Email templates for order submission notification (English and Thai)
const (
	orderSubmissionTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Order Submission Confirmation</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
        }
        .footer a {
            color: #b2ff59;
            text-decoration: none;
        }
        .footer a:hover {
            color: #ff6d00;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .order-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Order Submission Confirmation
    </div>
    <div class="content">
        <h2>Order Submission Confirmation</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>
        <div class="card">
            <p>Thank you for submitting your order on BiomassX. Your order has been received and is being processed.</p>

            <div class="order-details">
                <h3>Order Details:</h3>
                <table>
                    <tr>
                        <th>Order Type:</th>
                        <td>{{.OrderType}}</td>
                    </tr>
                    <tr>
                        <th>Marketspace:</th>
                        <td>{{.MarketspaceName}}</td>
                    </tr>
                    <tr>
                        <th>Market:</th>
                        <td>{{.MarketName}}</td>
                    </tr>
                    <tr>
                        <th>Submarket:</th>
                        <td>{{.SubmarketName}}</td>
                    </tr>
                    <tr>
                        <th>Contract Type:</th>
                        <td>{{.ContractTypeName}}</td>
                    </tr>
                    <tr>
                        <th>Product:</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>Quality:</th>
                        <td>{{.QualityName}}</td>
                    </tr>
                    <tr>
                        <th>Quantity:</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>Packing:</th>
                        <td>{{.PackingName}}</td>
                    </tr>
                    <tr>
                        <th>Price:</th>
                        <td>{{.Price}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>Payment Terms:</th>
                        <td>{{.PaymentTermName}}</td>
                    </tr>
                    <tr>
                        <th>Delivery Terms:</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                    <tr>
                        <th>Delivery Period:</th>
                        <td>{{.FirstDeliveryDate}} to {{.LastDeliveryDate}}</td>
                    </tr>
                    <tr>
                        <th>Country:</th>
                        <td>{{.CountryName}}</td>
                    </tr>
                    {{if eq .MarketspaceID 1}}
                    <!-- Local Market: Show Province, District, Subdistrict -->
                    {{if .ProvinceName}}
                    <tr>
                        <th>Province:</th>
                        <td>{{.ProvinceName}}</td>
                    </tr>
                    {{end}}
                    {{if .DistrictName}}
                    <tr>
                        <th>District:</th>
                        <td>{{.DistrictName}}</td>
                    </tr>
                    {{end}}
                    {{if .SubdistrictName}}
                    <tr>
                        <th>Subdistrict:</th>
                        <td>{{.SubdistrictName}}</td>
                    </tr>
                    {{end}}
                    {{else}}
                    <!-- Global Market: Show Ports -->
                    {{if .PortOfLoadingName}}
                    <tr>
                        <th>Port of Loading:</th>
                        <td>{{.PortOfLoadingName}}</td>
                    </tr>
                    {{end}}
                    {{if .PortOfDischargeName}}
                    <tr>
                        <th>Port of Discharge:</th>
                        <td>{{.PortOfDischargeName}}</td>
                    </tr>
                    {{end}}
                    {{end}}
                    {{if .Remark}}
                    <tr>
                        <th>Remark:</th>
                        <td>{{.Remark}}</td>
                    </tr>
                    {{end}}
                </table>
            </div>

        </div>
        <p>If you have any questions or need assistance, please contact our support team.</p>
        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}">Visit Our Website</a></p>
    </div>
</body>
</html>`

	orderSubmissionTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>ยืนยันการส่งคำสั่งซื้อ</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
        }
        .footer a {
            color: #b2ff59;
            text-decoration: none;
        }
        .footer a:hover {
            color: #ff6d00;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .order-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        ยืนยันการส่งคำสั่งซื้อ
    </div>
    <div class="content">
        <h2>ยืนยันการส่งคำสั่งซื้อ</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>
        <div class="card">
            <p>ขอบคุณสำหรับการส่งคำสั่งซื้อบน BiomassX ระบบได้รับคำสั่งซื้อของคุณแล้วและกำลังดำเนินการ</p>

            <div class="order-details">
                <h3>รายละเอียดคำสั่งซื้อ:</h3>
                <table>
                    <tr>
                        <th>ประเภทคำสั่งซื้อ:</th>
                        <td>{{.OrderType}}</td>
                    </tr>
                    <tr>
                        <th>ตลาดกลาง:</th>
                        <td>{{.MarketspaceName}}</td>
                    </tr>
                    <tr>
                        <th>ตลาด:</th>
                        <td>{{.MarketName}}</td>
                    </tr>
                    <tr>
                        <th>ตลาดย่อย:</th>
                        <td>{{.SubmarketName}}</td>
                    </tr>
                    <tr>
                        <th>ประเภทสัญญา:</th>
                        <td>{{.ContractTypeName}}</td>
                    </tr>
                    <tr>
                        <th>สินค้า:</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>คุณภาพ:</th>
                        <td>{{.QualityName}}</td>
                    </tr>
                    <tr>
                        <th>ปริมาณ:</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>บรรจุภัณฑ์:</th>
                        <td>{{.PackingName}}</td>
                    </tr>
                    <tr>
                        <th>ราคา:</th>
                        <td>{{.Price}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>เงื่อนไขการชำระเงิน:</th>
                        <td>{{.PaymentTermName}}</td>
                    </tr>
                    <tr>
                        <th>เงื่อนไขการส่งมอบ:</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                    <tr>
                        <th>ช่วงเวลาการส่งมอบ:</th>
                        <td>{{.FirstDeliveryDate}} ถึง {{.LastDeliveryDate}}</td>
                    </tr>
                    <tr>
                        <th>ประเทศ:</th>
                        <td>{{.CountryName}}</td>
                    </tr>
                    {{if eq .MarketspaceID 1}}
                    <!-- Local Market: Show Province, District, Subdistrict -->
                    {{if .ProvinceName}}
                    <tr>
                        <th>จังหวัด:</th>
                        <td>{{.ProvinceName}}</td>
                    </tr>
                    {{end}}
                    {{if .DistrictName}}
                    <tr>
                        <th>อำเภอ:</th>
                        <td>{{.DistrictName}}</td>
                    </tr>
                    {{end}}
                    {{if .SubdistrictName}}
                    <tr>
                        <th>ตำบล:</th>
                        <td>{{.SubdistrictName}}</td>
                    </tr>
                    {{end}}
                    {{else}}
                    <!-- Global Market: Show Ports -->
                    {{if .PortOfLoadingName}}
                    <tr>
                        <th>ท่าเรือต้นทาง:</th>
                        <td>{{.PortOfLoadingName}}</td>
                    </tr>
                    {{end}}
                    {{if .PortOfDischargeName}}
                    <tr>
                        <th>ท่าเรือปลายทาง:</th>
                        <td>{{.PortOfDischargeName}}</td>
                    </tr>
                    {{end}}
                    {{end}}
                    {{if .Remark}}
                    <tr>
                        <th>หมายเหตุ:</th>
                        <td>{{.Remark}}</td>
                    </tr>
                    {{end}}
                </table>
            </div>

        </div>
        <p>หากคุณมีคำถามหรือต้องการความช่วยเหลือ โปรดติดต่อทีมสนับสนุนของเรา</p>
        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body>
</html>`
)

// Email templates for matching notifications (English and Thai)
const (
	matchingTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Order Match Found</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .highlight {
            color: #ff6d00;
            font-weight: bold;
        }
        .deadline {
            color: #f44336;
            font-weight: bold;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px;
            text-decoration: none;
            border-radius: 4px;
        }
        .button.reject {
            background-color: #f44336;
        }
        .order-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Order Match Found
    </div>
    <div class="content">
        <h2>Order Match Found</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>

        <div class="card">
            <p>We're pleased to inform you that your <span class="highlight">{{if eq .Role "buyer"}}buy{{else}}sell{{end}}</span> order has been matched with a counterparty.</p>

            <div class="order-details">
                <h3>Match Details:</h3>
                <table>
                    <tr>
                        <th>Contract ID</th>
                        <td>{{.ContractID}}</td>
                    </tr>
                    <tr>
                        <th>Your Role</th>
                        <td>{{if eq .Role "buyer"}}Buyer{{else}}Seller{{end}}</td>
                    </tr>
                    <tr>
                        <th colspan="2" style="background-color: #e0f2f1; color: #00796b;">Counterparty Information</th>
                    </tr>
                    <tr>
                        <th>Name</th>
                        <td>{{.CounterpartyName}}</td>
                    </tr>
                    {{if .CounterpartyCompany}}
                    <tr>
                        <th>Company</th>
                        <td>{{.CounterpartyCompany}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartyPhone}}
                    <tr>
                        <th>Phone</th>
                        <td>{{.CounterpartyPhone}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartyEmail}}
                    <tr>
                        <th>Email</th>
                        <td>{{.CounterpartyEmail}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartyLocation}}
                    <tr>
                        <th>Location</th>
                        <td>{{.CounterpartyLocation}}</td>
                    </tr>
                    {{else}}
                    {{if .CounterpartyCountry}}
                    <tr>
                        <th>Country</th>
                        <td>{{.CounterpartyCountry}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartyProvince}}
                    <tr>
                        <th>Province</th>
                        <td>{{.CounterpartyProvince}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartyDistrict}}
                    <tr>
                        <th>District</th>
                        <td>{{.CounterpartyDistrict}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartySubdistrict}}
                    <tr>
                        <th>Subdistrict</th>
                        <td>{{.CounterpartySubdistrict}}</td>
                    </tr>
                    {{end}}
                    {{end}}
                    <tr>
                        <th colspan="2" style="background-color: #e0f2f1; color: #00796b;">Product Information</th>
                    </tr>
                    <tr>
                        <th>Product</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>Quantity</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    {{if .IsPartialMatch}}
                    <tr>
                        <th colspan="2" style="background-color: #e3f2fd; color: #0d47a1;">Partial Match Information</th>
                    </tr>
                    <tr>
                        <td colspan="2" style="background-color: #f5f9ff; padding: 15px;">
                            <div style="border-left: 4px solid #2196f3; padding-left: 15px;">
                                <p style="margin-top: 0;"><strong>This is a partial match of your order.</strong></p>
                                <ul style="margin-bottom: 0;">
                                    <li>Your original order: <strong>{{.OriginalQuantity}} {{.UnitOfMeasure}}</strong></li>
                                    <li>Matched in this contract: <strong>{{.Quantity}} {{.UnitOfMeasure}}</strong></li>
                                    <li>Remaining in the market: <strong>{{.RemainingQuantity}} {{.UnitOfMeasure}}</strong></li>
                                </ul>
                                <p style="margin-bottom: 0;">The remaining quantity will continue to be available in the market for future matching.</p>
                            </div>
                        </td>
                    </tr>
                    {{end}}
                    <tr>
                        <th>Price</th>
                        <td>{{.Price}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>Total Value</th>
                        <td>{{.Price}} × {{.Quantity}} = {{.TotalValue}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th colspan="2" style="background-color: #e0f2f1; color: #00796b;">Delivery Information</th>
                    </tr>
                    <tr>
                        <th>Delivery Terms</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                    {{if .DeliveryLocation}}
                    <tr>
                        <th>Delivery Location</th>
                        <td>{{.DeliveryLocation}}</td>
                    </tr>
                    {{end}}
                    {{if .FirstDeliveryDate}}
                    <tr>
                        <th>First Delivery Date</th>
                        <td>{{.FirstDeliveryDate}}</td>
                    </tr>
                    {{end}}
                    {{if .LastDeliveryDate}}
                    <tr>
                        <th>Last Delivery Date</th>
                        <td>{{.LastDeliveryDate}}</td>
                    </tr>
                    {{end}}
                </table>
            </div>

            <p class="deadline">Important: You have 3 days until {{.DeadlineDate}} to confirm this match. If not confirmed within this timeframe, the match will be automatically canceled.</p>

            <div style="background-color: #fff8e1; border-left: 4px solid #ffc107; padding: 15px; margin: 15px 0;">
                <h3 style="color: #ff6d00; margin-top: 0;">Why should you confirm or reject this match?</h3>
                <p>Confirming a match means you agree to proceed with the transaction with the counterparty under the terms specified above. This will create a binding contract between you and the counterparty.</p>
                <p>You should confirm this match if:</p>
                <ul>
                    <li>The product, quantity, and price meet your requirements</li>
                    <li>The delivery terms and location are acceptable</li>
                    <li>You are comfortable doing business with the counterparty</li>
                    <li>You are ready to fulfill your obligations as a {{if eq .Role "buyer"}}buyer{{else}}seller{{end}}</li>
                </ul>
                <p>You should reject this match if:</p>
                <ul>
                    <li>Any of the terms do not meet your requirements</li>
                    <li>You are not ready to proceed with the transaction</li>
                    <li>You have concerns about the counterparty</li>
                </ul>
                <p>Take your time to review all details carefully before making your decision.</p>
            </div>

            <p>You can also view the full contract details here: <a href="{{.ContractDetailsURL}}">View Contract Details</a></p>
        </div>

        <p>If you have any questions or need assistance, please contact our support team.</p>
        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}" style="color: white;">Visit Our Website</a></p>
    </div>
</body>
</html>`

	matchingTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>พบการจับคู่คำสั่งซื้อขาย</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .highlight {
            color: #ff6d00;
            font-weight: bold;
        }
        .deadline {
            color: #f44336;
            font-weight: bold;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px;
            text-decoration: none;
            border-radius: 4px;
        }
        .button.reject {
            background-color: #f44336;
        }
        .order-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        พบการจับคู่คำสั่งซื้อขาย
    </div>
    <div class="content">
        <h2>พบการจับคู่คำสั่งซื้อขาย</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>

        <div class="card">
            <p>เรายินดีที่จะแจ้งให้คุณทราบว่าคำสั่ง<span class="highlight">{{if eq .Role "buyer"}}ซื้อ{{else}}ขาย{{end}}</span>ของคุณได้รับการจับคู่กับคู่ค้าแล้ว</p>

            <div class="order-details">
                <h3>รายละเอียดการจับคู่:</h3>
                <table>
                    <tr>
                        <th>รหัสสัญญา</th>
                        <td>{{.ContractID}}</td>
                    </tr>
                    <tr>
                        <th>บทบาทของคุณ</th>
                        <td>{{if eq .Role "buyer"}}ผู้ซื้อ{{else}}ผู้ขาย{{end}}</td>
                    </tr>
                    <tr>
                        <th colspan="2" style="background-color: #e0f2f1; color: #00796b;">ข้อมูลคู่ค้า</th>
                    </tr>
                    <tr>
                        <th>ชื่อ</th>
                        <td>{{.CounterpartyName}}</td>
                    </tr>
                    {{if .CounterpartyCompany}}
                    <tr>
                        <th>บริษัท</th>
                        <td>{{.CounterpartyCompany}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartyPhone}}
                    <tr>
                        <th>เบอร์โทรศัพท์</th>
                        <td>{{.CounterpartyPhone}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartyEmail}}
                    <tr>
                        <th>อีเมล</th>
                        <td>{{.CounterpartyEmail}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartyLocation}}
                    <tr>
                        <th>ที่อยู่</th>
                        <td>{{.CounterpartyLocation}}</td>
                    </tr>
                    {{else}}
                    {{if .CounterpartyCountry}}
                    <tr>
                        <th>ประเทศ</th>
                        <td>{{.CounterpartyCountry}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartyProvince}}
                    <tr>
                        <th>จังหวัด</th>
                        <td>{{.CounterpartyProvince}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartyDistrict}}
                    <tr>
                        <th>อำเภอ</th>
                        <td>{{.CounterpartyDistrict}}</td>
                    </tr>
                    {{end}}
                    {{if .CounterpartySubdistrict}}
                    <tr>
                        <th>ตำบล</th>
                        <td>{{.CounterpartySubdistrict}}</td>
                    </tr>
                    {{end}}
                    {{end}}
                    <tr>
                        <th colspan="2" style="background-color: #e0f2f1; color: #00796b;">ข้อมูลสินค้า</th>
                    </tr>
                    <tr>
                        <th>สินค้า</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>ปริมาณ</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    {{if .IsPartialMatch}}
                    <tr>
                        <th colspan="2" style="background-color: #e3f2fd; color: #0d47a1;">ข้อมูลการจับคู่บางส่วน</th>
                    </tr>
                    <tr>
                        <td colspan="2" style="background-color: #f5f9ff; padding: 15px;">
                            <div style="border-left: 4px solid #2196f3; padding-left: 15px;">
                                <p style="margin-top: 0;"><strong>นี่เป็นการจับคู่บางส่วนของคำสั่งของคุณ</strong></p>
                                <ul style="margin-bottom: 0;">
                                    <li>คำสั่งเดิมของคุณ: <strong>{{.OriginalQuantity}} {{.UnitOfMeasure}}</strong></li>
                                    <li>จับคู่ในสัญญานี้: <strong>{{.Quantity}} {{.UnitOfMeasure}}</strong></li>
                                    <li>คงเหลือในตลาด: <strong>{{.RemainingQuantity}} {{.UnitOfMeasure}}</strong></li>
                                </ul>
                                <p style="margin-bottom: 0;">ปริมาณที่เหลือจะยังคงมีอยู่ในตลาดเพื่อรอการจับคู่ในอนาคต</p>
                            </div>
                        </td>
                    </tr>
                    {{end}}
                    <tr>
                        <th>ราคา</th>
                        <td>{{.Price}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>มูลค่ารวม</th>
                        <td>{{.Price}} × {{.Quantity}} = {{.TotalValue}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th colspan="2" style="background-color: #e0f2f1; color: #00796b;">ข้อมูลการส่งมอบ</th>
                    </tr>
                    <tr>
                        <th>เงื่อนไขการส่งมอบ</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                    {{if .DeliveryLocation}}
                    <tr>
                        <th>สถานที่ส่งมอบ</th>
                        <td>{{.DeliveryLocation}}</td>
                    </tr>
                    {{end}}
                    {{if .FirstDeliveryDate}}
                    <tr>
                        <th>วันที่ส่งมอบแรก</th>
                        <td>{{.FirstDeliveryDate}}</td>
                    </tr>
                    {{end}}
                    {{if .LastDeliveryDate}}
                    <tr>
                        <th>วันที่ส่งมอบสุดท้าย</th>
                        <td>{{.LastDeliveryDate}}</td>
                    </tr>
                    {{end}}
                </table>
            </div>

            <p class="deadline">สำคัญ: คุณมีเวลา 3 วันจนถึงวันที่ {{.DeadlineDate}} ในการยืนยันการจับคู่นี้ หากไม่ได้รับการยืนยันภายในระยะเวลาดังกล่าว การจับคู่จะถูกยกเลิกโดยอัตโนมัติ</p>

            <div style="background-color: #fff8e1; border-left: 4px solid #ffc107; padding: 15px; margin: 15px 0;">
                <h3 style="color: #ff6d00; margin-top: 0;">ทำไมคุณควรยืนยันหรือปฏิเสธการจับคู่นี้?</h3>
                <p>การยืนยันการจับคู่หมายความว่าคุณตกลงที่จะดำเนินการธุรกรรมกับคู่ค้าภายใต้เงื่อนไขที่ระบุไว้ข้างต้น ซึ่งจะสร้างสัญญาที่มีผลผูกพันระหว่างคุณและคู่ค้า</p>
                <p>คุณควรยืนยันการจับคู่นี้หาก:</p>
                <ul>
                    <li>สินค้า ปริมาณ และราคาตรงตามความต้องการของคุณ</li>
                    <li>เงื่อนไขการส่งมอบและสถานที่เป็นที่ยอมรับได้</li>
                    <li>คุณรู้สึกสบายใจที่จะทำธุรกิจกับคู่ค้า</li>
                    <li>คุณพร้อมที่จะปฏิบัติตามข้อผูกพันในฐานะ{{if eq .Role "buyer"}}ผู้ซื้อ{{else}}ผู้ขาย{{end}}</li>
                </ul>
                <p>คุณควรปฏิเสธการจับคู่นี้หาก:</p>
                <ul>
                    <li>เงื่อนไขใดๆ ไม่ตรงตามความต้องการของคุณ</li>
                    <li>คุณยังไม่พร้อมที่จะดำเนินการธุรกรรม</li>
                    <li>คุณมีข้อกังวลเกี่ยวกับคู่ค้า</li>
                </ul>
                <p>ใช้เวลาตรวจสอบรายละเอียดทั้งหมดอย่างรอบคอบก่อนตัดสินใจ</p>
            </div>

            <p>คุณสามารถดูรายละเอียดสัญญาทั้งหมดได้ที่นี่: <a href="{{.ContractDetailsURL}}">ดูรายละเอียดสัญญา</a></p>
        </div>

        <p>หากคุณมีคำถามหรือต้องการความช่วยเหลือ โปรดติดต่อทีมสนับสนุนของเรา</p>
        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}" style="color: white;">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body>
</html>`
)

// Email templates for contract confirmation notification (English and Thai)
const (
	contractConfirmationTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Contract Confirmed</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px;
            text-decoration: none;
            border-radius: 4px;
        }
        .order-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .highlight-box {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .next-steps {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .next-steps h3 {
            color: #ff6d00;
            margin-top: 0;
        }
        .next-steps ul {
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        {{if .IsFirstConfirmation}}Contract Successfully Confirmed{{else}}Contract Confirmation Reminder{{end}}
    </div>
    <div class="content">
        <h2>{{if .IsFirstConfirmation}}Contract Successfully Confirmed{{else}}Contract Confirmation Reminder{{end}}</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>

        <div class="card">
            <div class="highlight-box">
                {{if .IsFirstConfirmation}}
                <p class="success" style="font-size: 18px;">🎉 Congratulations! Your contract has been confirmed</p>
                <p>This contract has been confirmed by both parties and is now officially active.</p>
                <p>You can now contact your counterparty to proceed with the transaction according to the terms.</p>
                <p>The system has recorded the confirmation from both parties as evidence.</p>
                <p><strong style="color: #4CAF50;">Contract Status: Confirmed ✓</strong></p>
                {{else}}
                <p class="success" style="font-size: 18px;">🎉 Congratulations! Your contract has been confirmed</p>
                <p>This contract has been confirmed by both parties and is now officially active.</p>
                <p>You can now contact your counterparty to proceed with the transaction according to the terms.</p>
                <p>The system has recorded the confirmation from both parties as evidence.</p>
                <p><strong style="color: #4CAF50;">Contract Status: Confirmed ✓</strong></p>
                {{end}}
            </div>

            <div class="order-details">
                <h3>Contract Details:</h3>
                <table>
                    <tr>
                        <th>Contract ID</th>
                        <td colspan="2">{{.ContractID}}</td>
                    </tr>
                </table>

                <h3 style="margin-top: 20px;">Parties Involved:</h3>
                <div style="display: flex; margin-bottom: 20px;">
                    <div style="flex: 1; background-color: #e8f5e9; border-radius: 8px; padding: 15px; margin-right: 10px;">
                        <h4 style="color: #2e7d32; margin-top: 0; text-align: center; border-bottom: 1px solid #2e7d32; padding-bottom: 5px;">Your Information</h4>
                        <table style="width: 100%;">
                            <tr>
                                <th style="width: 40%;">Role</th>
                                <td>{{if eq .Role "buyer"}}Buyer{{else}}Seller{{end}}</td>
                            </tr>
                            <tr>
                                <th>Name</th>
                                <td>{{.FirstName}} {{.LastName}}</td>
                            </tr>
                            <tr>
                                <th>Email</th>
                                <td>{{.Email}}</td>
                            </tr>
                            {{if .Phone}}
                            <tr>
                                <th>Phone</th>
                                <td>{{.Phone}}</td>
                            </tr>
                            {{end}}
                        </table>
                    </div>

                    <div style="flex: 1; background-color: #fff8e1; border-radius: 8px; padding: 15px; margin-left: 10px;">
                        <h4 style="color: #ff6d00; margin-top: 0; text-align: center; border-bottom: 1px solid #ff6d00; padding-bottom: 5px;">Counterparty Information</h4>
                        <table style="width: 100%;">
                            <tr>
                                <th style="width: 40%;">Role</th>
                                <td>{{if eq .Role "buyer"}}Seller{{else}}Buyer{{end}}</td>
                            </tr>
                            <tr>
                                <th>Name</th>
                                <td>{{.CounterpartyName}}</td>
                            </tr>
                            {{if .CounterpartyEmail}}
                            <tr>
                                <th>Email</th>
                                <td>{{.CounterpartyEmail}}</td>
                            </tr>
                            {{end}}
                            {{if .CounterpartyPhone}}
                            <tr>
                                <th>Phone</th>
                                <td>{{.CounterpartyPhone}}</td>
                            </tr>
                            {{end}}
                        </table>
                    </div>
                </div>

                <h3 style="margin-top: 20px;">Product & Transaction Details:</h3>
                <table>
                    <tr>
                        <th>Product</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>Quantity</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>Price</th>
                        <td>{{.Price}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>Total Value</th>
                        <td>{{.Price}} × {{.Quantity}} = {{.TotalValue}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>Delivery Terms</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                    {{if .DeliveryLocation}}
                    <tr>
                        <th>Delivery Location</th>
                        <td>{{.DeliveryLocation}}</td>
                    </tr>
                    {{end}}
                    {{if .FirstDeliveryDate}}
                    <tr>
                        <th>First Delivery Date</th>
                        <td>{{.FirstDeliveryDate}}</td>
                    </tr>
                    {{end}}
                    {{if .LastDeliveryDate}}
                    <tr>
                        <th>Last Delivery Date</th>
                        <td>{{.LastDeliveryDate}}</td>
                    </tr>
                    {{end}}
                </table>
            </div>

            <div class="next-steps">
                <h3>Next Steps for Contract Execution:</h3>
                <ul>
                    <li><strong>Coordination:</strong> Contact your counterparty directly to confirm delivery details and payment</li>
                    <li><strong>Document Preparation:</strong> Prepare necessary documents for the transaction, such as invoices, delivery notes, or shipping documents</li>
                    <li><strong>Quality Verification:</strong> Ensure the product meets the quality specifications stated in the contract</li>
                    <li><strong>Product Delivery:</strong> Proceed with product delivery according to the delivery terms specified in the contract</li>
                    <li><strong>Payment:</strong> Process payment according to the terms agreed in the contract</li>
                    <li><strong>Issue Resolution:</strong> If any issues arise during contract execution, contact your counterparty to find a solution together</li>
                    <li><strong>Support:</strong> If you have questions or need assistance, our support team is available at any time</li>
                </ul>
                <p><strong>Important Note:</strong> Strict adherence to the contract terms will help build credibility and trust for future business transactions</p>
            </div>

            <p>You can view the full contract details here:</p>
            <div style="text-align: center;">
                <a href="{{.ContractDetailsURL}}" class="button">View Contract Details</a>
            </div>

            <p>Please contact your counterparty to proceed with the transaction according to the terms specified in the contract.</p>
        </div>

        <p>If you have any questions or need assistance, please contact our support team.</p>
        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}" style="color: white;">Visit Our Website</a></p>
    </div>
</body>
</html>`

	contractConfirmationTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>สัญญาได้รับการยืนยันเรียบร้อยแล้ว</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px;
            text-decoration: none;
            border-radius: 4px;
        }
        .order-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .highlight-box {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .next-steps {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .next-steps h3 {
            color: #ff6d00;
            margin-top: 0;
        }
        .next-steps ul {
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        {{if .IsFirstConfirmation}}สัญญาได้รับการยืนยันเรียบร้อยแล้ว{{else}}แจ้งเตือนการยืนยันสัญญา{{end}}
    </div>
    <div class="content">
        <h2>{{if .IsFirstConfirmation}}สัญญาได้รับการยืนยันเรียบร้อยแล้ว{{else}}แจ้งเตือนการยืนยันสัญญา{{end}}</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>

        <div class="card">
            <div class="highlight-box">
                {{if .IsFirstConfirmation}}
                <p class="success" style="font-size: 18px;">🎉 ยินดีด้วย! สัญญาของคุณได้รับการยืนยันเรียบร้อยแล้ว</p>
                <p>สัญญาซื้อขายนี้ได้รับการยืนยันจากทั้งสองฝ่ายและมีผลบังคับใช้อย่างเป็นทางการแล้ว</p>
                <p>ขณะนี้คุณสามารถติดต่อกับคู่ค้าของคุณเพื่อดำเนินการตามข้อตกลงในสัญญาได้ทันที</p>
                <p>ระบบได้บันทึกข้อมูลการยืนยันสัญญาของทั้งสองฝ่ายไว้เป็นหลักฐานเรียบร้อยแล้ว</p>
                <p><strong style="color: #4CAF50;">สถานะสัญญา: ยืนยันแล้ว ✓</strong></p>
                {{else}}
                <p class="success" style="font-size: 18px;">🎉 ยินดีด้วย! สัญญาของคุณได้รับการยืนยันเรียบร้อยแล้ว</p>
                <p>สัญญาซื้อขายนี้ได้รับการยืนยันจากทั้งสองฝ่ายและมีผลบังคับใช้อย่างเป็นทางการแล้ว</p>
                <p>ขณะนี้คุณสามารถติดต่อกับคู่ค้าของคุณเพื่อดำเนินการตามข้อตกลงในสัญญาได้ทันที</p>
                <p>ระบบได้บันทึกข้อมูลการยืนยันสัญญาของทั้งสองฝ่ายไว้เป็นหลักฐานเรียบร้อยแล้ว</p>
                <p><strong style="color: #4CAF50;">สถานะสัญญา: ยืนยันแล้ว ✓</strong></p>
                {{end}}
            </div>

            <div class="order-details">
                <h3>รายละเอียดสัญญา:</h3>
                <table>
                    <tr>
                        <th>รหัสสัญญา</th>
                        <td colspan="2">{{.ContractID}}</td>
                    </tr>
                </table>

                <h3 style="margin-top: 20px;">คู่สัญญา:</h3>
                <div style="display: flex; margin-bottom: 20px;">
                    <div style="flex: 1; background-color: #e8f5e9; border-radius: 8px; padding: 15px; margin-right: 10px;">
                        <h4 style="color: #2e7d32; margin-top: 0; text-align: center; border-bottom: 1px solid #2e7d32; padding-bottom: 5px;">ข้อมูลของคุณ</h4>
                        <table style="width: 100%;">
                            <tr>
                                <th style="width: 40%;">บทบาท</th>
                                <td>{{if eq .Role "buyer"}}ผู้ซื้อ{{else}}ผู้ขาย{{end}}</td>
                            </tr>
                            <tr>
                                <th>ชื่อ</th>
                                <td>{{.FirstName}} {{.LastName}}</td>
                            </tr>
                            <tr>
                                <th>อีเมล</th>
                                <td>{{.Email}}</td>
                            </tr>
                            {{if .Phone}}
                            <tr>
                                <th>เบอร์โทรศัพท์</th>
                                <td>{{.Phone}}</td>
                            </tr>
                            {{end}}
                        </table>
                    </div>

                    <div style="flex: 1; background-color: #fff8e1; border-radius: 8px; padding: 15px; margin-left: 10px;">
                        <h4 style="color: #ff6d00; margin-top: 0; text-align: center; border-bottom: 1px solid #ff6d00; padding-bottom: 5px;">ข้อมูลคู่ค้า</h4>
                        <table style="width: 100%;">
                            <tr>
                                <th style="width: 40%;">บทบาท</th>
                                <td>{{if eq .Role "buyer"}}ผู้ขาย{{else}}ผู้ซื้อ{{end}}</td>
                            </tr>
                            <tr>
                                <th>ชื่อ</th>
                                <td>{{.CounterpartyName}}</td>
                            </tr>
                            {{if .CounterpartyEmail}}
                            <tr>
                                <th>อีเมล</th>
                                <td>{{.CounterpartyEmail}}</td>
                            </tr>
                            {{end}}
                            {{if .CounterpartyPhone}}
                            <tr>
                                <th>เบอร์โทรศัพท์</th>
                                <td>{{.CounterpartyPhone}}</td>
                            </tr>
                            {{end}}
                        </table>
                    </div>
                </div>

                <h3 style="margin-top: 20px;">รายละเอียดสินค้าและการซื้อขาย:</h3>
                <table>
                    <tr>
                        <th>สินค้า</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>ปริมาณ</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>ราคา</th>
                        <td>{{.Price}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>มูลค่ารวม</th>
                        <td>{{.Price}} × {{.Quantity}} = {{.TotalValue}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>เงื่อนไขการส่งมอบ</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                    {{if .DeliveryLocation}}
                    <tr>
                        <th>สถานที่ส่งมอบ</th>
                        <td>{{.DeliveryLocation}}</td>
                    </tr>
                    {{end}}
                    {{if .FirstDeliveryDate}}
                    <tr>
                        <th>วันที่ส่งมอบแรก</th>
                        <td>{{.FirstDeliveryDate}}</td>
                    </tr>
                    {{end}}
                    {{if .LastDeliveryDate}}
                    <tr>
                        <th>วันที่ส่งมอบสุดท้าย</th>
                        <td>{{.LastDeliveryDate}}</td>
                    </tr>
                    {{end}}
                </table>
            </div>

            <div class="next-steps">
                <h3>ขั้นตอนต่อไปในการดำเนินการตามสัญญา:</h3>
                <ul>
                    <li><strong>การติดต่อประสานงาน:</strong> ติดต่อคู่ค้าของคุณโดยตรงเพื่อยืนยันรายละเอียดการส่งมอบสินค้าและการชำระเงิน</li>
                    <li><strong>การเตรียมเอกสาร:</strong> เตรียมเอกสารที่จำเป็นสำหรับการซื้อขาย เช่น ใบกำกับภาษี ใบส่งของ หรือเอกสารการขนส่ง</li>
                    <li><strong>การตรวจสอบคุณภาพ:</strong> ตรวจสอบให้แน่ใจว่าสินค้ามีคุณภาพตรงตามที่ระบุในสัญญา</li>
                    <li><strong>การส่งมอบสินค้า:</strong> ดำเนินการส่งมอบสินค้าตามเงื่อนไขการส่งมอบที่ระบุในสัญญา</li>
                    <li><strong>การชำระเงิน:</strong> ดำเนินการชำระเงินตามเงื่อนไขที่ตกลงกันในสัญญา</li>
                    <li><strong>การแก้ไขปัญหา:</strong> หากพบปัญหาในระหว่างการดำเนินการตามสัญญา ให้ติดต่อคู่ค้าเพื่อหาทางแก้ไขร่วมกัน</li>
                    <li><strong>การขอความช่วยเหลือ:</strong> หากมีข้อสงสัยหรือต้องการความช่วยเหลือ สามารถติดต่อทีมสนับสนุนของเราได้ตลอดเวลา</li>
                </ul>
                <p><strong>หมายเหตุสำคัญ:</strong> การปฏิบัติตามเงื่อนไขในสัญญาอย่างเคร่งครัดจะช่วยสร้างความน่าเชื่อถือและความไว้วางใจในการทำธุรกิจร่วมกันในอนาคต</p>
            </div>

            <p>คุณสามารถดูรายละเอียดสัญญาทั้งหมดได้ที่นี่:</p>
            <div style="text-align: center;">
                <a href="{{.ContractDetailsURL}}" class="button">ดูรายละเอียดสัญญา</a>
            </div>

            <p>กรุณาติดต่อคู่ค้าของคุณเพื่อดำเนินการตามข้อกำหนดที่ระบุในสัญญา</p>
        </div>

        <p>หากคุณมีคำถามหรือต้องการความช่วยเหลือ โปรดติดต่อทีมสนับสนุนของเรา</p>
        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}" style="color: white;">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body>
</html>`
)

// Email templates for contract deadline reminder notification (English and Thai)
const (
	contractDeadlineReminderTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>URGENT: Contract Match Will Expire Today</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #ff5252;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            color: white;
            font-weight: bold;
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px;
            text-decoration: none;
            border-radius: 4px;
        }
        .order-details {
            background-color: #fff;
            border-left: 4px solid #ff5252;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .action-required {
            color: #f44336;
            font-weight: bold;
        }
        .deadline {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 15px 0;
            font-weight: bold;
            color: #d32f2f;
        }
        .contact-info {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
        }
        @media only screen and (max-width: 600px) {
            .content {
                padding: 10px;
            }
            .card {
                padding: 15px;
            }
            .header {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        URGENT: Your Contract Match Will Expire Today
    </div>
    <div class="content">
        <h2>Final Reminder: Your Contract Match Will Expire Today</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>

        <div class="deadline">
            <p>⚠️ IMPORTANT: Your contract match will expire today if not confirmed. After expiration, the match will be automatically canceled and both orders will return to the marketplace.</p>
        </div>

        <div class="card">
            <p>This is a final reminder that you have not yet confirmed the following contract match:</p>

            <div class="order-details">
                <table>
                    <tr>
                        <th colspan="2" style="background-color: #e3f2fd; color: #1976d2;">Order Information</th>
                    </tr>
                    <tr>
                        <th>Product</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>Quantity</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>Price</th>
                        <td>{{.Price}} {{.Currency}} per {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>Total Value</th>
                        <td>{{.TotalValue}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>Delivery Terms</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                    <tr>
                        <th>Your Role</th>
                        <td>{{if eq .Role "buyer"}}Buyer{{else}}Seller{{end}}</td>
                    </tr>
                    <tr>
                        <th>Counterparty</th>
                        <td>{{.CounterpartyName}}</td>
                    </tr>
                </table>
            </div>

            <p class="action-required">Action Required: Please visit the contract page to confirm or reject this match before the end of today.</p>

            <div style="text-align: center;">
                <a href="{{.ContractDetailsURL}}" class="button">View Contract Details</a>
            </div>
        </div>

        <p>If you have any questions or concerns, please contact our support team.</p>

        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}" style="color: #b2ff59;">Visit Our Website</a></p>
    </div>
</body>
</html>`

	contractDeadlineReminderTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>ด่วน: สัญญาของคุณจะหมดอายุวันนี้</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #ff5252;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            color: white;
            font-weight: bold;
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px;
            text-decoration: none;
            border-radius: 4px;
        }
        .order-details {
            background-color: #fff;
            border-left: 4px solid #ff5252;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .action-required {
            color: #f44336;
            font-weight: bold;
        }
        .deadline {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 15px 0;
            font-weight: bold;
            color: #d32f2f;
        }
        .contact-info {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
        }
        @media only screen and (max-width: 600px) {
            .content {
                padding: 10px;
            }
            .card {
                padding: 15px;
            }
            .header {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        ด่วน: สัญญาของคุณจะหมดอายุวันนี้
    </div>
    <div class="content">
        <h2>แจ้งเตือนครั้งสุดท้าย: สัญญาของคุณจะหมดอายุวันนี้</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>

        <div class="deadline">
            <p>⚠️ สำคัญ: สัญญาของคุณจะหมดอายุวันนี้หากไม่ได้รับการยืนยัน หลังจากหมดอายุ การจับคู่จะถูกยกเลิกโดยอัตโนมัติและคำสั่งซื้อทั้งสองจะกลับไปยังตลาด</p>
        </div>

        <div class="card">
            <p>นี่คือการแจ้งเตือนครั้งสุดท้ายว่าคุณยังไม่ได้ยืนยันการจับคู่สัญญาต่อไปนี้:</p>

            <div class="order-details">
                <table>
                    <tr>
                        <th colspan="2" style="background-color: #e3f2fd; color: #1976d2;">ข้อมูลคำสั่งซื้อ</th>
                    </tr>
                    <tr>
                        <th>สินค้า</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>ปริมาณ</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>ราคา</th>
                        <td>{{.Price}} {{.Currency}} ต่อ {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>มูลค่ารวม</th>
                        <td>{{.TotalValue}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>เงื่อนไขการส่งมอบ</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                    <tr>
                        <th>บทบาทของคุณ</th>
                        <td>{{if eq .Role "buyer"}}ผู้ซื้อ{{else}}ผู้ขาย{{end}}</td>
                    </tr>
                    <tr>
                        <th>คู่สัญญา</th>
                        <td>{{.CounterpartyName}}</td>
                    </tr>
                </table>
            </div>

            <p class="action-required">ต้องดำเนินการ: กรุณาเข้าชมหน้าสัญญาเพื่อยืนยันหรือปฏิเสธการจับคู่นี้ก่อนสิ้นวันนี้</p>

            <div style="text-align: center;">
                <a href="{{.ContractDetailsURL}}" class="button">ดูรายละเอียดสัญญา</a>
            </div>
        </div>

        <p>หากคุณมีคำถามหรือข้อสงสัย โปรดติดต่อทีมสนับสนุนของเรา</p>

        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}" style="color: #b2ff59;">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body>
</html>`
)

// Email templates for contract waiting notification (English and Thai)
const (
	contractWaitingNotificationTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Your Counterparty Hasn't Confirmed the Contract Yet</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #ffa726;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            color: white;
            font-weight: bold;
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px;
            text-decoration: none;
            border-radius: 4px;
        }
        .order-details {
            background-color: #fff;
            border-left: 4px solid #ffa726;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .action-required {
            color: #f44336;
            font-weight: bold;
        }
        .deadline {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            font-weight: bold;
            color: #ff6f00;
        }
        .contact-info {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
        }
        @media only screen and (max-width: 600px) {
            .content {
                padding: 10px;
            }
            .card {
                padding: 15px;
            }
            .header {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Your Counterparty Hasn't Confirmed the Contract Yet
    </div>
    <div class="content">
        <h2>Reminder: Your Contract Match Needs Counterparty Confirmation</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>

        <div class="deadline">
            <p>⚠️ IMPORTANT: You have already confirmed this contract, but your counterparty has not yet confirmed. The match will expire today if not confirmed by both parties.</p>
        </div>

        <div class="card">
            <p>Thank you for confirming the following contract match:</p>

            <div class="order-details">
                <table>
                    <tr>
                        <th colspan="2" style="background-color: #e3f2fd; color: #1976d2;">Order Information</th>
                    </tr>
                    <tr>
                        <th>Product</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>Quantity</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>Price</th>
                        <td>{{.Price}} {{.Currency}} per {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>Total Value</th>
                        <td>{{.TotalValue}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>Delivery Terms</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                    <tr>
                        <th>Your Role</th>
                        <td>{{if eq .Role "buyer"}}Buyer{{else}}Seller{{end}}</td>
                    </tr>
                    <tr>
                        <th>Counterparty</th>
                        <td>{{.CounterpartyName}}</td>
                    </tr>
                    <tr>
                        <th>Counterparty Company</th>
                        <td>{{if ne .CounterpartyCompany ""}}{{.CounterpartyCompany}}{{else}}Not specified{{end}}</td>
                    </tr>
                </table>
            </div>

            <div class="contact-info">
                <p>You may want to contact your counterparty to remind them to confirm the contract before it expires:</p>
                <p><strong>Counterparty:</strong> {{.CounterpartyName}}</p>
                <p><strong>Company:</strong> {{.CounterpartyCompany}}</p>
                <p><strong>Email:</strong> {{.CounterpartyEmail}}</p>
            </div>

            <p>You can view the full contract details by clicking the button below:</p>

            <div style="text-align: center;">
                <a href="{{.ContractDetailsURL}}" class="button">View Contract Details</a>
            </div>
        </div>

        <p>If you have any questions or concerns, please contact our support team.</p>

        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}" style="color: #b2ff59;">Visit Our Website</a></p>
    </div>
</body>
</html>`

	contractWaitingNotificationTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>คู่สัญญาของคุณยังไม่ได้ยืนยันสัญญา</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #ffa726;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            color: white;
            font-weight: bold;
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            margin: 10px;
            text-decoration: none;
            border-radius: 4px;
        }
        .order-details {
            background-color: #fff;
            border-left: 4px solid #ffa726;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .action-required {
            color: #f44336;
            font-weight: bold;
        }
        .deadline {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            font-weight: bold;
            color: #ff6f00;
        }
        .contact-info {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
        }
        @media only screen and (max-width: 600px) {
            .content {
                padding: 10px;
            }
            .card {
                padding: 15px;
            }
            .header {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        คู่สัญญาของคุณยังไม่ได้ยืนยันสัญญา
    </div>
    <div class="content">
        <h2>แจ้งเตือน: สัญญาของคุณต้องการการยืนยันจากคู่สัญญา</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>

        <div class="deadline">
            <p>⚠️ สำคัญ: คุณได้ยืนยันสัญญานี้แล้ว แต่คู่สัญญาของคุณยังไม่ได้ยืนยัน การจับคู่จะหมดอายุวันนี้หากไม่ได้รับการยืนยันจากทั้งสองฝ่าย</p>
        </div>

        <div class="card">
            <p>ขอบคุณที่ยืนยันการจับคู่สัญญาต่อไปนี้:</p>

            <div class="order-details">
                <table>
                    <tr>
                        <th colspan="2" style="background-color: #e3f2fd; color: #1976d2;">ข้อมูลคำสั่งซื้อ</th>
                    </tr>
                    <tr>
                        <th>สินค้า</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>ปริมาณ</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>ราคา</th>
                        <td>{{.Price}} {{.Currency}} ต่อ {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>มูลค่ารวม</th>
                        <td>{{.TotalValue}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>เงื่อนไขการส่งมอบ</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                    <tr>
                        <th>บทบาทของคุณ</th>
                        <td>{{if eq .Role "buyer"}}ผู้ซื้อ{{else}}ผู้ขาย{{end}}</td>
                    </tr>
                    <tr>
                        <th>คู่สัญญา</th>
                        <td>{{.CounterpartyName}}</td>
                    </tr>
                    <tr>
                        <th>บริษัทคู่สัญญา</th>
                        <td>{{if ne .CounterpartyCompany ""}}{{.CounterpartyCompany}}{{else}}ไม่ระบุชื่อองค์กร{{end}}</td>
                    </tr>
                </table>
            </div>

            <div class="contact-info">
                <p>คุณอาจต้องการติดต่อคู่สัญญาของคุณเพื่อเตือนให้ยืนยันสัญญาก่อนที่จะหมดอายุ:</p>
                <p><strong>คู่สัญญา:</strong> {{.CounterpartyName}}</p>
                <p><strong>บริษัท:</strong> {{.CounterpartyCompany}}</p>
                <p><strong>อีเมล:</strong> {{.CounterpartyEmail}}</p>
            </div>

            <p>คุณสามารถดูรายละเอียดสัญญาทั้งหมดได้โดยคลิกปุ่มด้านล่าง:</p>

            <div style="text-align: center;">
                <a href="{{.ContractDetailsURL}}" class="button">ดูรายละเอียดสัญญา</a>
            </div>
        </div>

        <p>หากคุณมีคำถามหรือข้อสงสัย โปรดติดต่อทีมสนับสนุนของเรา</p>

        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}" style="color: #b2ff59;">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body>
</html>`
)

// Email templates for contract status update notification (English and Thai)
const (
	contractStatusUpdateTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Contract Status Update</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
        }
        .order-details {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .order-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .order-details th, .order-details td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .order-details th {
            background-color: #e0e0e0;
        }
        .action-required {
            color: #ff6600;
            font-weight: bold;
        }
        .status-box {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .status-confirmed {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
        }
        .status-pending {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
        }
        .status-rejected {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .next-steps {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .next-steps h3 {
            color: #0d47a1;
            margin-top: 0;
        }
        .next-steps ul {
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Contract Status Update
    </div>
    <div class="content">
        <h2>Contract Status Update</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>

        <div class="card">
            {{if .IsActionTaker}}
            <div class="status-box status-confirmed">
                <h3 style="color: #2e7d32; margin-top: 0;">Your Action Confirmed</h3>
                <p>You have successfully <strong>{{.ActionTaken}}</strong> the contract for the following order.</p>
                {{if eq .ActionTaken "confirmed"}}
                <p>The system is now waiting for confirmation from your counterparty. Once both parties confirm, the contract will be effective and you can proceed with the transaction immediately.</p>
                <p>The system has recorded your confirmation as evidence.</p>
                <p><strong style="color: #4CAF50;">Your Action Status: Confirmed ✓</strong></p>
                {{else}}
                <div style="background-color: #ffebee; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                    <p style="font-size: 18px; color: #d32f2f; margin-top: 0;"><strong>⚠️ You have rejected this contract</strong></p>
                    <p>This contract has been rejected and will not be effective. Your order will be returned to the market to be matched with other orders.</p>
                </div>
                <p>You can create a new order or wait for the system to automatically match your order with other counterparties.</p>
                <p>The system has recorded your rejection as evidence.</p>
                <p><strong style="color: #d32f2f;">Your Action Status: Rejected ✗</strong></p>
                {{end}}
            </div>
            {{else}}
            <div class="status-box {{if eq .ActionTaken "confirmed"}}status-pending{{else}}status-rejected{{end}}">
                <h3 style="color: {{if eq .ActionTaken "confirmed"}}#ff6d00{{else}}#d32f2f{{end}}; margin-top: 0;">Counterparty Action</h3>
                <p><strong>{{.CounterpartyName}}</strong> has <strong>{{.ActionTaken}}</strong> the contract for the following order.</p>
                {{if eq .ActionTaken "confirmed"}}
                <p>Please review and confirm this contract to proceed with the transaction. Your confirmation is the final step in making the contract effective.</p>
                <p><strong>Caution:</strong> If you do not take action within 3 days, the contract will be automatically canceled and both parties' orders will return to the market.</p>
                <p>After confirming the contract, you should contact your counterparty directly to arrange delivery details and payment.</p>
                <p><strong style="color: #ff6d00;">Contract Status: Waiting for your confirmation ⏳</strong></p>
                {{else}}
                <div style="background-color: #ffebee; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                    <p style="font-size: 18px; color: #d32f2f; margin-top: 0;"><strong>⚠️ Contract has been rejected by your counterparty</strong></p>
                    <p>Your counterparty has decided not to proceed with this contract. You may contact them for more information or create a new order.</p>
                </div>
                <p>Your order will be returned to the market to be matched with other orders. The system will notify you when a new match is found.</p>
                <p>The system has recorded the rejection as evidence.</p>
                <p><strong style="color: #d32f2f;">Contract Status: Rejected ✗</strong></p>
                {{end}}
            </div>
            {{end}}

            <div class="order-details">
                <h3>Contract Details:</h3>
                <table>
                    <tr>
                        <th>Contract ID:</th>
                        <td>{{.ContractID}}</td>
                    </tr>
                    <tr>
                        <th>Your Role:</th>
                        <td>{{if eq .Role "buyer"}}Buyer{{else}}Seller{{end}}</td>
                    </tr>
                    <tr>
                        <th>Counterparty:</th>
                        <td>{{.CounterpartyName}}</td>
                    </tr>
                    <tr>
                        <th>Counterparty Company:</th>
                        <td>{{if ne .CounterpartyCompany ""}}{{.CounterpartyCompany}}{{else}}Not specified{{end}}</td>
                    </tr>
                    <tr>
                        <th>Product:</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>Quantity:</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>Price:</th>
                        <td>{{.Price}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>Total Value:</th>
                        <td>{{.TotalValue}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>Delivery Terms:</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                </table>
            </div>

            {{if not .IsActionTaker}}
            {{if eq .ActionTaken "confirmed"}}
            <div class="next-steps">
                <h3>Next Steps:</h3>
                <ul>
                    <li>Review the contract details thoroughly</li>
                    <li>Confirm the contract if you agree with all terms</li>
                    <li>If you have questions, you can contact your counterparty directly before deciding</li>
                    <li>Please note that the contract will expire in 3 days if not confirmed by both parties</li>
                </ul>
            </div>
            <p class="action-required">Action Required: Please review and confirm this contract to complete the agreement.</p>
            {{end}}
            {{end}}

            <p>You can view the full contract details{{if not .IsActionTaker}}{{if eq .ActionTaken "confirmed"}} and take action{{end}}{{end}} by clicking the button below:</p>

            <div style="text-align: center;">
                <a href="{{.ContractDetailsURL}}" class="button">View Contract Details</a>
            </div>
        </div>

        <p>If you have any questions or need assistance, please contact our support team.</p>
        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}" style="color: white;">Visit Our Website</a></p>
    </div>
</body>
</html>`

	contractStatusUpdateTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>การอัปเดตสถานะสัญญา</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
        }
        .order-details {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .order-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .order-details th, .order-details td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .order-details th {
            background-color: #e0e0e0;
        }
        .action-required {
            color: #ff6600;
            font-weight: bold;
        }
        .status-box {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .status-confirmed {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
        }
        .status-pending {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
        }
        .status-rejected {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .next-steps {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .next-steps h3 {
            color: #0d47a1;
            margin-top: 0;
        }
        .next-steps ul {
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        การอัปเดตสถานะสัญญา
    </div>
    <div class="content">
        <h2>การอัปเดตสถานะสัญญา</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>

        <div class="card">
            {{if .IsActionTaker}}
            <div class="status-box status-confirmed">
                <h3 style="color: #2e7d32; margin-top: 0;">การดำเนินการของคุณได้รับการยืนยัน</h3>
                <p>คุณได้{{if eq .ActionTaken "ยืนยัน"}}<strong>ยืนยัน</strong>{{else}}<strong>ปฏิเสธ</strong>{{end}}สัญญาสำหรับคำสั่งซื้อขายต่อไปนี้เรียบร้อยแล้ว</p>
                {{if eq .ActionTaken "ยืนยัน"}}
                <p>ขณะนี้ระบบกำลังรอการยืนยันจากคู่ค้าของคุณ เมื่อทั้งสองฝ่ายยืนยันแล้ว สัญญาจะมีผลบังคับใช้และคุณสามารถดำเนินการซื้อขายได้ทันที</p>
                <p>ระบบได้บันทึกข้อมูลการยืนยันของคุณไว้เป็นหลักฐานเรียบร้อยแล้ว</p>
                <p><strong style="color: #4CAF50;">สถานะการดำเนินการของคุณ: ยืนยันแล้ว ✓</strong></p>
                {{else}}
                <div style="background-color: #ffebee; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                    <p style="font-size: 18px; color: #d32f2f; margin-top: 0;"><strong>⚠️ คุณได้ปฏิเสธสัญญานี้</strong></p>
                    <p>สัญญานี้ได้ถูกปฏิเสธและจะไม่มีผลบังคับใช้ คำสั่งซื้อขายของคุณจะถูกส่งกลับไปยังตลาดเพื่อจับคู่กับคำสั่งอื่นต่อไป</p>
                </div>
                <p>คุณสามารถสร้างคำสั่งซื้อขายใหม่หรือรอให้ระบบจับคู่คำสั่งของคุณกับคู่ค้ารายอื่นโดยอัตโนมัติ</p>
                <p>ระบบได้บันทึกข้อมูลการปฏิเสธของคุณไว้เป็นหลักฐานเรียบร้อยแล้ว</p>
                <p><strong style="color: #d32f2f;">สถานะการดำเนินการของคุณ: ปฏิเสธแล้ว ✗</strong></p>
                {{end}}
            </div>
            {{else}}
            <div class="status-box {{if eq .ActionTaken "ยืนยัน"}}status-pending{{else}}status-rejected{{end}}">
                <h3 style="color: {{if eq .ActionTaken "ยืนยัน"}}#ff6d00{{else}}#d32f2f{{end}}; margin-top: 0;">การดำเนินการของคู่ค้า</h3>
                <p><strong>{{.CounterpartyName}}</strong> ได้{{if eq .ActionTaken "ยืนยัน"}}<strong>ยืนยัน</strong>{{else}}<strong>ปฏิเสธ</strong>{{end}}สัญญาสำหรับคำสั่งซื้อขายต่อไปนี้</p>
                {{if eq .ActionTaken "ยืนยัน"}}
                <p>กรุณาตรวจสอบและยืนยันสัญญานี้เพื่อดำเนินการธุรกรรม การยืนยันของคุณเป็นขั้นตอนสุดท้ายในการทำให้สัญญามีผลบังคับใช้</p>
                <p><strong>ข้อควรระวัง:</strong> หากคุณไม่ดำเนินการภายใน 3 วัน สัญญาจะถูกยกเลิกโดยอัตโนมัติและคำสั่งซื้อขายของทั้งสองฝ่ายจะกลับไปยังตลาด</p>
                <p>หลังจากยืนยันสัญญาแล้ว คุณควรติดต่อกับคู่ค้าโดยตรงเพื่อตกลงรายละเอียดการส่งมอบสินค้าและการชำระเงิน</p>
                <p><strong style="color: #ff6d00;">สถานะสัญญา: รอการยืนยันจากคุณ ⏳</strong></p>
                {{else}}
                <div style="background-color: #ffebee; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                    <p style="font-size: 18px; color: #d32f2f; margin-top: 0;"><strong>⚠️ สัญญาได้ถูกปฏิเสธโดยคู่ค้าของคุณ</strong></p>
                    <p>คู่ค้าของคุณได้ตัดสินใจที่จะไม่ดำเนินการตามสัญญานี้ คุณสามารถติดต่อพวกเขาเพื่อขอข้อมูลเพิ่มเติมหรือสร้างคำสั่งใหม่</p>
                </div>
                <p>คำสั่งซื้อขายของคุณจะถูกส่งกลับไปยังตลาดเพื่อจับคู่กับคำสั่งอื่นต่อไป ระบบจะแจ้งเตือนคุณเมื่อมีการจับคู่ใหม่</p>
                <p>ระบบได้บันทึกข้อมูลการปฏิเสธไว้เป็นหลักฐานเรียบร้อยแล้ว</p>
                <p><strong style="color: #d32f2f;">สถานะสัญญา: ถูกปฏิเสธ ✗</strong></p>
                {{end}}
            </div>
            {{end}}

            <div class="order-details">
                <h3>รายละเอียดสัญญา:</h3>
                <table>
                    <tr>
                        <th>รหัสสัญญา:</th>
                        <td>{{.ContractID}}</td>
                    </tr>
                    <tr>
                        <th>บทบาทของคุณ:</th>
                        <td>{{if eq .Role "buyer"}}ผู้ซื้อ{{else}}ผู้ขาย{{end}}</td>
                    </tr>
                    <tr>
                        <th>คู่ค้า:</th>
                        <td>{{.CounterpartyName}}</td>
                    </tr>
                    <tr>
                        <th>บริษัทคู่ค้า:</th>
                        <td>{{if ne .CounterpartyCompany ""}}{{.CounterpartyCompany}}{{else}}ไม่ระบุชื่อองค์กร{{end}}</td>
                    </tr>
                    <tr>
                        <th>สินค้า:</th>
                        <td>{{.ProductName}}</td>
                    </tr>
                    <tr>
                        <th>ปริมาณ:</th>
                        <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
                    </tr>
                    <tr>
                        <th>ราคา:</th>
                        <td>{{.Price}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>มูลค่ารวม:</th>
                        <td>{{.TotalValue}} {{.Currency}}</td>
                    </tr>
                    <tr>
                        <th>เงื่อนไขการส่งมอบ:</th>
                        <td>{{.DeliveryTerms}}</td>
                    </tr>
                </table>
            </div>

            {{if not .IsActionTaker}}
            {{if eq .ActionTaken "ยืนยัน"}}
            <div class="next-steps">
                <h3>ขั้นตอนต่อไป:</h3>
                <ul>
                    <li>ตรวจสอบรายละเอียดสัญญาให้ครบถ้วน</li>
                    <li>ยืนยันสัญญาหากคุณเห็นด้วยกับเงื่อนไขทั้งหมด</li>
                    <li>หากมีข้อสงสัย คุณสามารถติดต่อคู่ค้าโดยตรงก่อนตัดสินใจ</li>
                    <li>โปรดทราบว่าสัญญาจะหมดอายุภายใน 3 วันหากไม่ได้รับการยืนยันจากทั้งสองฝ่าย</li>
                </ul>
            </div>
            <p class="action-required">ต้องดำเนินการ: กรุณาตรวจสอบและยืนยันสัญญานี้เพื่อให้ข้อตกลงเสร็จสมบูรณ์</p>
            {{end}}
            {{end}}

            <p>คุณสามารถดูรายละเอียดสัญญาทั้งหมด{{if not .IsActionTaker}}{{if eq .ActionTaken "ยืนยัน"}}และดำเนินการ{{end}}{{end}}ได้โดยคลิกปุ่มด้านล่าง:</p>

            <div style="text-align: center;">
                <a href="{{.ContractDetailsURL}}" class="button">ดูรายละเอียดสัญญา</a>
            </div>
        </div>

        <p>หากคุณมีคำถามหรือต้องการความช่วยเหลือ โปรดติดต่อทีมสนับสนุนของเรา</p>
        <p>ขอแสดงความนับถือ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}" style="color: white;">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body>
</html>`
)

// Email templates for contract rejection notification (English and Thai)
const (
	// Template for the user who rejected the contract (action taker)
	contractRejectionActionTakerTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>You Rejected the Contract</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #f44336;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            color: white;
            font-weight: bold;
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .alert {
            color: #f44336;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #ff6d00;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #e65100;
        }
        .action-confirmation {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 15px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Contract Rejection Confirmation
    </div>
    <div class="content">
        <h2>Contract Rejection Confirmation</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>

        <div class="action-confirmation">
            <p>You have successfully rejected the contract.</p>
            <p>Your action has been processed and recorded in our system.</p>
        </div>

        <h3>Contract Details:</h3>
        <table>
            <tr>
                <th>Contract ID</th>
                <td>{{.ContractID}}</td>
            </tr>
            <tr>
                <th>Your Role</th>
                <td>{{if eq .Role "buyer"}}Buyer{{else}}Seller{{end}}</td>
            </tr>
            <tr>
                <th>Counterparty</th>
                <td>{{.CounterpartyName}}</td>
            </tr>
            <tr>
                <th>Counterparty Company</th>
                <td>{{.CounterpartyCompany}}</td>
            </tr>
            <tr>
                <th>Product</th>
                <td>{{.ProductName}}</td>
            </tr>
            <tr>
                <th>Quantity</th>
                <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
            </tr>
            <tr>
                <th>Price</th>
                <td>{{.Price}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>Total Value</th>
                <td>{{.TotalValue}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>Delivery Terms</th>
                <td>{{.DeliveryTerms}}</td>
            </tr>
        </table>

        <p>You will receive a separate notification about the contract cancellation and next steps.</p>

        <p>If you have any questions or need assistance, please contact our support team.</p>

        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}" style="color: white;">Visit Our Website</a></p>
    </div>
</body>
</html>`

	contractRejectionActionTakerTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>คุณได้ปฏิเสธสัญญาเรียบร้อยแล้ว</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #f44336;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            color: white;
            font-weight: bold;
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .alert {
            color: #f44336;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #ff6d00;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #e65100;
        }
        .action-confirmation {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 15px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        การยืนยันการปฏิเสธสัญญา
    </div>
    <div class="content">
        <h2>การยืนยันการปฏิเสธสัญญา</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>

        <div class="action-confirmation">
            <p>คุณได้ปฏิเสธสัญญาเรียบร้อยแล้ว</p>
            <p>การดำเนินการของคุณได้รับการประมวลผลและบันทึกในระบบของเราแล้ว</p>
        </div>

        <h3>รายละเอียดสัญญา:</h3>
        <table>
            <tr>
                <th>รหัสสัญญา</th>
                <td>{{.ContractID}}</td>
            </tr>
            <tr>
                <th>บทบาทของคุณ</th>
                <td>{{if eq .Role "buyer"}}ผู้ซื้อ{{else}}ผู้ขาย{{end}}</td>
            </tr>
            <tr>
                <th>คู่สัญญา</th>
                <td>{{.CounterpartyName}}</td>
            </tr>
            <tr>
                <th>บริษัทคู่สัญญา</th>
                <td>{{.CounterpartyCompany}}</td>
            </tr>
            <tr>
                <th>สินค้า</th>
                <td>{{.ProductName}}</td>
            </tr>
            <tr>
                <th>ปริมาณ</th>
                <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
            </tr>
            <tr>
                <th>ราคา</th>
                <td>{{.Price}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>มูลค่ารวม</th>
                <td>{{.TotalValue}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>เงื่อนไขการส่งมอบ</th>
                <td>{{.DeliveryTerms}}</td>
            </tr>
        </table>

        <p>คุณจะได้รับการแจ้งเตือนแยกต่างหากเกี่ยวกับการยกเลิกสัญญาและขั้นตอนต่อไป</p>

        <p>หากคุณมีคำถามหรือต้องการความช่วยเหลือ โปรดติดต่อทีมสนับสนุนของเรา</p>

        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}" style="color: white;">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body>
</html>`

	// Template for the counterparty who received the rejection
	contractRejectionCounterpartyTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Contract Rejected by Counterparty</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #f44336;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            color: white;
            font-weight: bold;
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .alert {
            color: #f44336;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #ff6d00;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #e65100;
        }
        .rejection-notice {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 15px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Contract Rejected by Counterparty
    </div>
    <div class="content">
        <h2>Contract Rejection Notice</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>

        <div class="rejection-notice">
            <p><strong>IMPORTANT: Your contract has been ACTIVELY REJECTED by {{.CounterpartyName}}.</strong></p>
            <p>The counterparty has explicitly declined to proceed with this contract. This is a direct rejection by a specific user, not an automatic system cancellation.</p>
            <p>You may wish to contact them directly to understand their reasons or negotiate new terms.</p>
        </div>

        <h3>Contract Details:</h3>
        <table>
            <tr>
                <th colspan="2" style="background-color: #ffebee; color: #b71c1c; text-align: center; font-size: 16px;">
                    ⚠️ REJECTED CONTRACT - Action Required ⚠️
                </th>
            </tr>
            <tr>
                <th>Contract ID</th>
                <td>{{.ContractID}}</td>
            </tr>
            <tr>
                <th>Rejection Status</th>
                <td><strong style="color: #b71c1c;">REJECTED BY COUNTERPARTY</strong></td>
            </tr>
            <tr>
                <th>Your Role</th>
                <td>{{if eq .Role "buyer"}}Buyer{{else}}Seller{{end}}</td>
            </tr>
            <tr>
                <th>Who Rejected</th>
                <td>{{.CounterpartyName}} {{if ne .CounterpartyCompany ""}}({{.CounterpartyCompany}}){{end}}</td>
            </tr>
            <!-- Removed Rejection Date field that was causing errors -->
            <tr>
                <th>Product</th>
                <td>{{.ProductName}}</td>
            </tr>
            <tr>
                <th>Quantity</th>
                <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
            </tr>
            <tr>
                <th>Price</th>
                <td>{{.Price}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>Total Value</th>
                <td>{{.TotalValue}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>Next Steps</th>
                <td>Your order will return to the marketplace for new matching opportunities</td>
            </tr>
        </table>

        <p>You will receive a separate notification about the contract cancellation and next steps.</p>

        <div style="text-align: center; margin-top: 20px;">
            <a href="{{.BaseURL}}/order_book" class="button">View Order History</a>
        </div>

        <p>If you have any questions or need assistance, please contact our support team.</p>

        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}" style="color: white;">Visit Our Website</a></p>
    </div>
</body>
</html>`

	contractRejectionCounterpartyTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>สัญญาถูกปฏิเสธโดยคู่สัญญา</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #f44336;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            color: white;
            font-weight: bold;
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .alert {
            color: #f44336;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #ff6d00;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #e65100;
        }
        .rejection-notice {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 15px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        สัญญาถูกปฏิเสธโดยคู่สัญญา
    </div>
    <div class="content">
        <h2>แจ้งการปฏิเสธสัญญา</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>

        <div class="rejection-notice">
            <p><strong>สำคัญ: สัญญาของคุณถูกปฏิเสธโดยตรงจาก {{.CounterpartyName}}</strong></p>
            <p>คู่สัญญาของคุณได้ปฏิเสธที่จะดำเนินการตามสัญญานี้อย่างชัดเจน นี่เป็นการปฏิเสธโดยตรงจากผู้ใช้ ไม่ใช่การยกเลิกโดยอัตโนมัติจากระบบ</p>
            <p>คุณอาจต้องการติดต่อพวกเขาโดยตรงเพื่อทำความเข้าใจเหตุผลหรือเจรจาเงื่อนไขใหม่</p>
        </div>

        <h3>รายละเอียดสัญญา:</h3>
        <table>
            <tr>
                <th colspan="2" style="background-color: #ffebee; color: #b71c1c; text-align: center; font-size: 16px;">
                    ⚠️ สัญญาถูกปฏิเสธ - ต้องการการดำเนินการ ⚠️
                </th>
            </tr>
            <tr>
                <th>รหัสสัญญา</th>
                <td>{{.ContractID}}</td>
            </tr>
            <tr>
                <th>สถานะการปฏิเสธ</th>
                <td><strong style="color: #b71c1c;">ถูกปฏิเสธโดยคู่สัญญา</strong></td>
            </tr>
            <tr>
                <th>บทบาทของคุณ</th>
                <td>{{if eq .Role "buyer"}}ผู้ซื้อ{{else}}ผู้ขาย{{end}}</td>
            </tr>
            <tr>
                <th>ผู้ที่ปฏิเสธ</th>
                <td>{{.CounterpartyName}} {{if ne .CounterpartyCompany ""}}({{.CounterpartyCompany}}){{end}}</td>
            </tr>
            <!-- Removed Rejection Date field that was causing errors -->
            <tr>
                <th>สินค้า</th>
                <td>{{.ProductName}}</td>
            </tr>
            <tr>
                <th>ปริมาณ</th>
                <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
            </tr>
            <tr>
                <th>ราคา</th>
                <td>{{.Price}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>มูลค่ารวม</th>
                <td>{{.TotalValue}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>ขั้นตอนต่อไป</th>
                <td>คำสั่งซื้อขายของคุณจะกลับไปยังตลาดเพื่อโอกาสในการจับคู่ใหม่</td>
            </tr>
        </table>

        <p>คุณจะได้รับการแจ้งเตือนแยกต่างหากเกี่ยวกับการยกเลิกสัญญาและขั้นตอนต่อไป</p>

        <div style="text-align: center; margin-top: 20px;">
            <a href="{{.BaseURL}}/th/order_book" class="button">ดูประวัติคำสั่งซื้อขาย</a>
        </div>

        <p>หากคุณมีคำถามหรือต้องการความช่วยเหลือ โปรดติดต่อทีมสนับสนุนของเรา</p>

        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}" style="color: white;">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body>
</html>`
)

// Email templates for contract cancellation notification (English and Thai)
const (
	contractCancellationTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Contract Cancelled</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #f44336;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            color: white;
            font-weight: bold;
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .alert {
            color: #f44336;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #ff6d00;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #e65100;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Contract Cancellation
    </div>
    <div class="content">
        <h2>Contract Cancellation Notice</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>
        <p class="alert">We regret to inform you that the contract has been automatically cancelled by the system.</p>

        <h3>Cancellation Reason:</h3>
        <p>{{.CancellationReason}}</p>

        <div style="background-color: #e8f5e9; padding: 15px; border-left: 4px solid #4caf50; margin: 15px 0;">
            <p><strong>System Notice:</strong> This is a system-generated cancellation, not a direct rejection by the counterparty.</p>
            <p>Your order has been returned to the marketplace and is now available for new matching with other customers.</p>
        </div>

        <h3>Contract Details:</h3>
        <table>
            <tr>
                <th colspan="2" style="background-color: #e8f5e9; color: #2e7d32; text-align: center; font-size: 16px;">
                    ℹ️ SYSTEM CANCELLATION - Order Returned to Marketplace ℹ️
                </th>
            </tr>
            <tr>
                <th>Contract ID</th>
                <td>{{.ContractID}}</td>
            </tr>
            <tr>
                <th>Cancellation Status</th>
                <td><strong style="color: #2e7d32;">AUTOMATICALLY CANCELLED BY SYSTEM</strong></td>
            </tr>
            <tr>
                <th>Cancellation Reason</th>
                <td>{{.CancellationReason}}</td>
            </tr>
            <!-- Removed Cancellation Date field that was causing errors -->
            <tr>
                <th>Your Role</th>
                <td>{{if eq .Role "buyer"}}Buyer{{else}}Seller{{end}}</td>
            </tr>
            <tr>
                <th>Product</th>
                <td>{{.ProductName}}</td>
            </tr>
            <tr>
                <th>Quantity</th>
                <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
            </tr>
            <tr>
                <th>Price</th>
                <td>{{.Price}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>Total Value</th>
                <td>{{.TotalValue}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>Order Status</th>
                <td>Returned to marketplace for new matching</td>
            </tr>
        </table>

        <p><strong>Your order has been automatically returned to the marketplace</strong> and is now available for matching with other buyers/sellers. You do not need to resubmit your order - it will continue to be active in the system.</p>

        <div style="text-align: center;">
            <a href="{{.BaseURL}}/order_book" class="button">View Order History</a>
        </div>

        <p>If you have any questions or need assistance, please contact our support team.</p>

        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}" style="color: white;">Visit Our Website</a></p>
    </div>
</body>
</html>`

	contractCancellationTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>สัญญาถูกยกเลิก</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #f44336;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            color: white;
            font-weight: bold;
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
        h1, h2 {
            color: black;
        }
        .alert {
            color: #f44336;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .button {
            display: inline-block;
            background-color: #ff6d00;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
        }
        .button:hover {
            background-color: #e65100;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        การยกเลิกสัญญา
    </div>
    <div class="content">
        <h2>แจ้งการยกเลิกสัญญา</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>
        <p class="alert">เราเสียใจที่ต้องแจ้งให้ทราบว่าสัญญาได้ถูกยกเลิกโดยอัตโนมัติจากระบบแล้ว</p>

        <h3>เหตุผลในการยกเลิก:</h3>
        <p>{{if eq .CancellationReason "The contract was rejected by the counterparty."}}สัญญาถูกปฏิเสธโดยคู่สัญญา{{else if eq .CancellationReason "The contract was rejected by you."}}สัญญาถูกปฏิเสธโดยคุณ{{else if eq .CancellationReason "The contract confirmation deadline has expired."}}ครบกำหนดเวลายืนยันสัญญาแล้ว{{else if eq .CancellationReason "The contract was cancelled by the system."}}สัญญาถูกยกเลิกโดยระบบ{{else if eq .CancellationReason "The contract was cancelled by the administrator."}}สัญญาถูกยกเลิกโดยผู้ดูแลระบบ{{else}}{{.CancellationReason}}{{end}}</p>

        <div style="background-color: #e8f5e9; padding: 15px; border-left: 4px solid #4caf50; margin: 15px 0;">
            <p><strong>ประกาศจากระบบ:</strong> นี่เป็นการยกเลิกที่สร้างโดยระบบ ไม่ใช่การปฏิเสธโดยตรงจากคู่สัญญา</p>
            <p>คำสั่งซื้อขายของคุณได้ถูกส่งกลับไปยังตลาดและพร้อมสำหรับการจับคู่ใหม่กับลูกค้ารายอื่น</p>
        </div>

        <h3>รายละเอียดสัญญา:</h3>
        <table>
            <tr>
                <th colspan="2" style="background-color: #e8f5e9; color: #2e7d32; text-align: center; font-size: 16px;">
                    ℹ️ การยกเลิกโดยระบบ - คำสั่งซื้อขายถูกส่งกลับไปยังตลาด ℹ️
                </th>
            </tr>
            <tr>
                <th>รหัสสัญญา</th>
                <td>{{.ContractID}}</td>
            </tr>
            <tr>
                <th>สถานะการยกเลิก</th>
                <td><strong style="color: #2e7d32;">ยกเลิกโดยอัตโนมัติจากระบบ</strong></td>
            </tr>
            <tr>
                <th>เหตุผลการยกเลิก</th>
                <td>{{if eq .CancellationReason "The contract was rejected by the counterparty."}}สัญญาถูกปฏิเสธโดยคู่สัญญา{{else if eq .CancellationReason "The contract was rejected by you."}}สัญญาถูกปฏิเสธโดยคุณ{{else if eq .CancellationReason "The contract confirmation deadline has expired."}}ครบกำหนดเวลายืนยันสัญญาแล้ว{{else if eq .CancellationReason "The contract was cancelled by the system."}}สัญญาถูกยกเลิกโดยระบบ{{else if eq .CancellationReason "The contract was cancelled by the administrator."}}สัญญาถูกยกเลิกโดยผู้ดูแลระบบ{{else}}{{.CancellationReason}}{{end}}</td>
            </tr>
            <!-- Removed Cancellation Date field that was causing errors -->
            <tr>
                <th>บทบาทของคุณ</th>
                <td>{{if eq .Role "buyer"}}ผู้ซื้อ{{else}}ผู้ขาย{{end}}</td>
            </tr>
            <tr>
                <th>สินค้า</th>
                <td>{{.ProductName}}</td>
            </tr>
            <tr>
                <th>ปริมาณ</th>
                <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
            </tr>
            <tr>
                <th>ราคา</th>
                <td>{{.Price}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>มูลค่ารวม</th>
                <td>{{.TotalValue}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>สถานะคำสั่งซื้อขาย</th>
                <td>ถูกส่งกลับไปยังตลาดเพื่อการจับคู่ใหม่</td>
            </tr>
        </table>

        <p><strong>คำสั่งซื้อขายของคุณได้ถูกส่งกลับไปยังตลาดโดยอัตโนมัติ</strong> และพร้อมสำหรับการจับคู่กับผู้ซื้อ/ผู้ขายรายอื่น คุณไม่จำเป็นต้องส่งคำสั่งซื้อขายใหม่ - คำสั่งซื้อขายของคุณจะยังคงใช้งานได้ในระบบ</p>

        <div style="text-align: center;">
            <a href="{{.BaseURL}}/th/order_book" class="button">ดูประวัติคำสั่งซื้อขาย</a>
        </div>

        <p>หากคุณมีคำถามหรือต้องการความช่วยเหลือ โปรดติดต่อทีมสนับสนุนของเรา</p>

        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}" style="color: white;">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body>
</html>`

	// Contract Cancellation Rejection templates
	contractCancellationRejectionTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Contract Cancelled Due to Rejection - BiomassX</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #d9534f;
            color: white;
            text-align: center;
            padding: 1em;
        }
        .content {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .footer {
            background-color: #f1f1f1;
            padding: 10px;
            text-align: center;
            font-size: 0.8em;
            color: #666;
        }
        .button {
            display: inline-block;
            background-color: #5bc0de;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .button:hover {
            background-color: #46b8da;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            width: 40%;
        }
        .alert {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
            margin: 20px 0;
        }
        .total {
            font-weight: bold;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="content">
        <h2>Contract Cancellation Notice</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>
        <p class="alert">We regret to inform you that contract #{{.ContractID}} has been cancelled due to rejection by one of the parties.</p>

        <h3>Contract Details:</h3>
        <table>
            <tr>
                <th>Contract ID</th>
                <td>#{{.ContractID}}</td>
            </tr>
            <tr>
                <th>Product</th>
                <td>{{.ProductName}}</td>
            </tr>
            <tr>
                <th>Quantity</th>
                <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
            </tr>
            <tr>
                <th>Price</th>
                <td>{{.Price}} {{.Currency}}/{{.UnitOfMeasure}}</td>
            </tr>
            <tr>
                <th>Total Value</th>
                <td class="total">{{.TotalValue}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>Delivery Terms</th>
                <td>{{.DeliveryTerms}}</td>
            </tr>
            <tr>
                <th>Your Role</th>
                <td>{{if eq .Role "buyer"}}Buyer{{else}}Seller{{end}}</td>
            </tr>
            <tr>
                <th>Counterparty</th>
                <td>{{.CounterpartyName}}</td>
            </tr>
            <tr>
                <th>Counterparty Company</th>
                <td>{{if ne .CounterpartyCompany ""}}{{.CounterpartyCompany}}{{else}}Not specified{{end}}</td>
            </tr>
        </table>

        <p>This contract has been cancelled and the associated orders are now available for new matching. You can view your orders in the order book.</p>

        <a href="{{.BaseURL}}/order_book" class="button">View Order Book</a>

        <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
        <p>Best regards,<br>The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>&copy; {{.CurrentYear}} BiomassX. All rights reserved.</p>
        <p>This email was sent to {{.Email}}</p>
    </div>
</body>
</html>`

	contractCancellationRejectionTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>สัญญาถูกยกเลิกเนื่องจากการปฏิเสธ - BiomassX</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #d9534f;
            color: white;
            text-align: center;
            padding: 1em;
        }
        .content {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .footer {
            background-color: #f1f1f1;
            padding: 10px;
            text-align: center;
            font-size: 0.8em;
            color: #666;
        }
        .button {
            display: inline-block;
            background-color: #5bc0de;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .button:hover {
            background-color: #46b8da;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            width: 40%;
        }
        .alert {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
            margin: 20px 0;
        }
        .total {
            font-weight: bold;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="content">
        <h2>แจ้งการยกเลิกสัญญา</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>
        <p class="alert">เราเสียใจที่ต้องแจ้งให้ทราบว่าสัญญาเลขที่ #{{.ContractID}} ได้ถูกยกเลิกเนื่องจากการปฏิเสธโดยฝ่ายใดฝ่ายหนึ่ง</p>

        <h3>รายละเอียดสัญญา:</h3>
        <table>
            <tr>
                <th>รหัสสัญญา</th>
                <td>#{{.ContractID}}</td>
            </tr>
            <tr>
                <th>สินค้า</th>
                <td>{{.ProductName}}</td>
            </tr>
            <tr>
                <th>ปริมาณ</th>
                <td>{{.Quantity}} {{.UnitOfMeasure}}</td>
            </tr>
            <tr>
                <th>ราคา</th>
                <td>{{.Price}} {{.Currency}}/{{.UnitOfMeasure}}</td>
            </tr>
            <tr>
                <th>มูลค่ารวม</th>
                <td class="total">{{.TotalValue}} {{.Currency}}</td>
            </tr>
            <tr>
                <th>เงื่อนไขการส่งมอบ</th>
                <td>{{.DeliveryTerms}}</td>
            </tr>
            <tr>
                <th>บทบาทของคุณ</th>
                <td>{{if eq .Role "buyer"}}ผู้ซื้อ{{else}}ผู้ขาย{{end}}</td>
            </tr>
            <tr>
                <th>คู่ค้า</th>
                <td>{{.CounterpartyName}}</td>
            </tr>
            <tr>
                <th>บริษัทคู่ค้า</th>
                <td>{{if ne .CounterpartyCompany ""}}{{.CounterpartyCompany}}{{else}}ไม่ระบุชื่อองค์กร{{end}}</td>
            </tr>
        </table>

        <p>สัญญานี้ได้ถูกยกเลิกแล้วและคำสั่งซื้อขายที่เกี่ยวข้องพร้อมสำหรับการจับคู่ใหม่ คุณสามารถดูคำสั่งซื้อขายของคุณได้ในสมุดคำสั่งซื้อขาย</p>

        <a href="{{.BaseURL}}/th/order_book" class="button">ดูสมุดคำสั่งซื้อขาย</a>

        <p>หากคุณมีคำถามหรือต้องการความช่วยเหลือ โปรดอย่าลังเลที่จะติดต่อทีมสนับสนุนของเรา</p>
        <p>ขอแสดงความนับถือ<br>ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>&copy; {{.CurrentYear}} BiomassX สงวนลิขสิทธิ์</p>
        <p>อีเมลนี้ถูกส่งไปยัง {{.Email}}</p>
    </div>
</body>
</html>`
)

// Email templates for profile incomplete notification (English and Thai)
const (
	profileIncompleteTemplateEN = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Complete Your Profile and Address</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
            width: 100%;
        }
        h1, h2, h3 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #b2ff59;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
            border-color: #ff6d00;
        }
        .missing-section {
            background-color: #fff;
            border-left: 4px solid #ff6d00;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .missing-section h3 {
            margin-top: 0;
            color: #ff6d00;
        }
        .missing-section ul {
            list-style-type: none;
            padding-left: 5px;
        }
        .missing-section li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .missing-section li:last-child {
            border-bottom: none;
        }
        .field-importance {
            font-size: 0.9em;
            color: #666;
            font-style: italic;
            margin-top: 3px;
        }
        .section-link {
            display: inline-block;
            background-color: #f0f0f0;
            color: #333;
            padding: 5px 10px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Please complete your profile
    </div>
    <div class="content">
        <h2>Finish Setting Up Your Account</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>
        <div class="card">
            <p>We noticed that your BiomassX account is missing some important information. To fully utilize our platform, please complete the following:</p>
            {{if .MissingBasicFields}}
            <div class="missing-section">
                <h3>Basic Information</h3>
                <ul>
                    {{range .MissingBasicFields}}
                    <li>
                        <strong>{{.}}</strong>
                        {{if index $.FieldImportance .}}
                            <div class="field-importance">{{index $.FieldImportance .}}</div>
                        {{end}}
                    </li>
                    {{end}}
                </ul>
                {{if .BasicInfoURL}}
                <a href="{{.BasicInfoURL}}" class="section-link">Complete Basic Info</a>
                {{end}}
            </div>
            {{end}}
            {{if .MissingAddressFields}}
            <div class="missing-section">
                <h3>Address Information</h3>
                <ul>
                    {{range .MissingAddressFields}}
                    <li>
                        <strong>{{.}}</strong>
                        {{if index $.FieldImportance .}}
                        <div class="field-importance">{{index $.FieldImportance .}}</div>
                        {{end}}
                    </li>
                    {{end}}
                </ul>
                {{if .AddressURL}}
                <a href="{{.AddressURL}}" class="section-link">Add Your Address</a>
                {{end}}
            </div>
            {{end}}
            <p>Completing your profile and address helps us:</p>
            <ul>
                <li>Provide personalized services</li>
                <li>Ensure accurate product delivery</li>
                <li>Maintain compliance with regulations</li>
                <li>Streamline your transactions</li>
            </ul>

            <div class="missing-section" style="background-color: #fff9c4; border-left: 4px solid #fbc02d; padding: 15px; margin: 15px 0;">
                <h3 style="color: #f57c00; margin-top: 0;">Important: Complete Your Role</h3>
                <p>Please make sure to select your <strong>role</strong> in the system (e.g., Buyer, Seller, or both). This is essential for accessing all platform features and ensuring you receive relevant notifications.</p>
            </div>
            <a href="{{.ProfileURL}}" class="button">Complete Your Profile Now</a>
        </div>
        <p>If you need assistance, contact our customer service <NAME_EMAIL>.</p>
        <p>Thank you for being part of BiomassX!</p>
        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p>
            <a href="{{.BaseURL}}/privacy" style="color: white; margin-right: 15px;">Privacy Policy</a>
            <a href="{{.BaseURL}}/terms" style="color: white;">Terms of Service</a>
        </p>
    </div>
</body>
</html>`

	profileIncompleteTemplateTH = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>กรุณากรอกโปรไฟล์และที่อยู่ให้ครบถ้วน</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 30px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            text-align: center;
            padding: 20px;
            width: 100%;
        }
        h1, h2, h3 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #b2ff59;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
            border-color: #ff6d00;
        }
        .missing-section {
            background-color: #fff;
            border-left: 4px solid #ff6d00;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .missing-section h3 {
            margin-top: 0;
            color: #ff6d00;
        }
        .missing-section ul {
            list-style-type: none;
            padding-left: 5px;
        }
        .missing-section li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .missing-section li:last-child {
            border-bottom: none;
        }
        .field-importance {
            font-size: 0.9em;
            color: #666;
            font-style: italic;
            margin-top: 3px;
        }
        .section-link {
            display: inline-block;
            background-color: #f0f0f0;
            color: #333;
            padding: 5px 10px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        กรุณากรอกโปรไฟล์ให้ครบถ้วน
    </div>
    <div class="content">
        <h2>ดำเนินการตั้งค่าบัญชีของคุณให้เสร็จสิ้น</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>
        <div class="card">
            <p>เราสังเกตเห็นว่าบัญชี BiomassX ของคุณยังขาดข้อมูลที่สำคัญบางอย่าง เพื่อให้สามารถใช้งานแพลตฟอร์มของเราได้อย่างเต็มที่ กรุณากรอกข้อมูลต่อไปนี้ให้ครบถ้วน:</p>
            {{if .MissingBasicFields}}
            <div class="missing-section">
                <h3>ข้อมูลพื้นฐาน</h3>
                <ul>
                    {{range .MissingBasicFields}}
                    <li>
                        <strong>{{ if eq . "First Name"}}ชื่อ{{else if eq . "Last Name"}}นามสกุล{{else if eq . "Phone"}}โทรศัพท์{{else if eq . "Organization Name"}}ชื่อองค์กร{{else}}{{.}}{{end}}</strong>
                        {{if index $.FieldImportance .}}
                            <div class="field-importance">{{if eq . "First Name"}}จำเป็นสำหรับการปรับแต่งบัญชี{{else if eq . "Last Name"}}จำเป็นสำหรับการปรับแต่งบัญชี{{else if eq . "Phone"}}จำเป็นสำหรับการติดต่อและยืนยันตัวตน{{else if eq . "Organization Name"}}จำเป็นสำหรับบัญชีธุรกิจ{{else}}{{index $.FieldImportance .}}{{end}}</div>
                        {{end}}
                    </li>
                    {{end}}
                </ul>
                {{if .BasicInfoURL}}
                <a href="{{.BasicInfoURL}}" class="section-link">กรอกข้อมูลพื้นฐาน</a>
                {{end}}
            </div>
            {{end}}
            {{if .MissingAddressFields}}
            <div class="missing-section">
                <h3>ข้อมูลที่อยู่</h3>
                <ul>
                    {{range .MissingAddressFields}}
                    <li>
                        <strong>{{if eq . "Country"}}ประเทศ{{else if eq . "Province"}}จังหวัด{{else if eq . "District"}}อำเภอ{{else if eq . "Subdistrict"}}ตำบล{{else if eq . "Address Details"}}รายละเอียดที่อยู่{{else if eq . "Postal Code"}}รหัสไปรษณีย์{{else if eq . "Street Address"}}ที่อยู่ถนน{{else if eq . "City"}}เมือง{{else}}{{.}}{{end}}</strong>
                        {{if index $.FieldImportance .}}
                        <div class="field-importance">{{if eq . "Street Address"}}จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ{{else if eq . "City"}}จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ{{else if eq . "Postal Code"}}จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ{{else if eq . "Country"}}จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ{{else}}{{index $.FieldImportance .}}{{end}}</div>
                        {{end}}
                    </li>
                    {{end}}
                </ul>
                {{if .AddressURL}}
                <a href="{{.AddressURL}}" class="section-link">เพิ่มที่อยู่ของคุณ</a>
                {{end}}
            </div>
            {{end}}
            <p>การกรอกโปรไฟล์และที่อยู่ให้ครบถ้วนช่วยให้เรา:</p>
            <ul>
                <li>ให้บริการที่ปรับแต่งตามความต้องการ</li>
                <li>รับประกันการจัดส่งสินค้าที่ถูกต้อง</li>
                <li>ปฏิบัติตามกฎระเบียบ</li>
                <li>ทำให้การทำธุรกรรมของคุณคล่องตัว</li>
            </ul>

            <div class="missing-section" style="background-color: #fff9c4; border-left: 4px solid #fbc02d; padding: 15px; margin: 15px 0;">
                <h3 style="color: #f57c00; margin-top: 0;">สำคัญ: กรุณาเลือกบทบาทของคุณ</h3>
                <p>โปรดตรวจสอบให้แน่ใจว่าคุณได้เลือก <strong>บทบาท</strong> ในระบบ (เช่น ผู้ซื้อ, ผู้ขาย, หรือทั้งสองอย่าง) นี่เป็นสิ่งสำคัญสำหรับการเข้าถึงคุณสมบัติทั้งหมดของแพลตฟอร์มและเพื่อให้แน่ใจว่าคุณได้รับการแจ้งเตือนที่เกี่ยวข้อง</p>
            </div>
            <a href="{{.ProfileURL}}" class="button">กรอกโปรไฟล์ของคุณตอนนี้</a>
        </div>
        <p>หากคุณต้องการความช่วยเหลือ ติดต่อทีมบริการลูกค้าของเราที่ <EMAIL></p>
        <p>ขอบคุณที่เป็นส่วนหนึ่งของ BiomassX!</p>
        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p>
            <a href="{{.BaseURL}}/privacy" style="color: white; margin-right: 15px;">นโยบายความเป็นส่วนตัว</a>
            <a href="{{.BaseURL}}/terms" style="color: white;">เงื่อนไขการให้บริการ</a>
        </p>
    </div>
</body>
</html>`
)

// NewEmailService creates a new email service
func NewEmailService(repo Repository, config *EmailConfig) *EmailService {
	service := &EmailService{
		repo:                 repo,
		config:               config,
		notificationChannels: sync.Map{},
	}
	// Initialize notification types at startup
	service.initializeNotificationTypes()
	return service
}

// initializeNotificationTypes ensures all required notification types exist in the database
func (s *EmailService) initializeNotificationTypes() {
	types := []string{
		string(NotificationTypeEmail),
		string(NotificationTypeSMS),
		string(NotificationTypeRegistration),
		string(NotificationTypePasswordReset),
		string(NotificationTypePasswordResetSuccess),
		string(NotificationTypeProfileAndAddress),
		string(NotificationTypeOrderSubmission),
		string(NotificationTypeMatching),
		string(NotificationTypeContractConfirmation),
		string(NotificationTypeContractCancellation),
		string(NotificationTypeContractStatusUpdate),
		string(NotificationTypeMatchExpiryReminder),
		string(NotificationTypeMatchExpiryReminderConfirmed),
	}
	for _, typeName := range types {
		_, err := s.getOrCreateNotificationType(typeName)
		if err != nil {
			log.Printf("Failed to initialize notification type %s: %v", typeName, err)
		} else {
			log.Printf("Ensured notification type %s exists", typeName)
		}
	}
}

// SendEmail sends an email using the existing sendEmail method
func (s *EmailService) SendEmail(message *EmailMessage) error {
	return s.sendEmail(message.To, message.Subject, message.Body)
}

// SendRegistrationEmail sends a registration confirmation email
func (s *EmailService) SendRegistrationEmail(data interface{}) error {
	regData, ok := data.(*RegistrationNotification)
	if !ok {
		return fmt.Errorf("invalid data type for registration notification")
	}

	// Ensure valid language
	regData.Language = ensureValidLanguage(regData.Language)
	lang := regData.Language

	log.Printf("Starting registration notification for user ID: %d, email: %s, language: %s",
		regData.UserID, regData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	regData.BaseURL = baseURL

	// Create email subject and select template based on language
	// Note: These subject lines are used for language detection in SendSmartProfileCompletionNotification
	// so they must match the strings checked there
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = "ยินดีต้อนรับสู่ BiomassX" // This exact string is checked in language detection
		tmplStr = registrationTemplateTH
	default:
		subject = "Welcome to BiomassX" // This exact string is checked in language detection
		tmplStr = registrationTemplateEN
	}

	// Parse email template
	tmpl, err := template.New("registration").Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*RegistrationNotification
		CurrentYear int
	}{
		RegistrationNotification: regData,
		CurrentYear:              time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeRegistration))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             regData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              regData.Email,
		Phone:              regData.Phone,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	err = s.repo.CreateNotification(notification)
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Send notification asynchronously
	s.sendNotificationAsync(notification, lang)

	log.Printf("Queued registration email for user ID: %d, email: %s, language: %s",
		regData.UserID, regData.Email, lang)
	return nil
}

// SendPasswordResetEmail sends a password reset email
func (s *EmailService) SendPasswordResetEmail(data interface{}) error {
	resetData, ok := data.(*PasswordResetNotification)
	if !ok {
		return fmt.Errorf("invalid data type for password reset notification")
	}

	// Ensure valid language
	resetData.Language = ensureValidLanguage(resetData.Language)
	lang := resetData.Language

	log.Printf("Starting password reset notification for user ID: %d, email: %s, language: %s",
		resetData.UserID, resetData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	resetData.BaseURL = baseURL

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = "BiomassX - คำขอรีเซ็ตรหัสผ่าน"
		tmplStr = passwordResetTemplateTH
	default:
		subject = "BiomassX - Password reset request"
		tmplStr = passwordResetTemplateEN
	}

	// Parse email template
	tmpl, err := template.New("password_reset").Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*PasswordResetNotification
		CurrentYear int
	}{
		PasswordResetNotification: resetData,
		CurrentYear:               time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypePasswordReset))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             resetData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              resetData.Email,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	log.Printf("Creating password reset notification record in database for user ID: %d, email: %s", resetData.UserID, resetData.Email)
	err = s.repo.CreateNotification(notification)
	if err != nil {
		log.Printf("ERROR: Failed to create password reset notification record: %v", err)
		return fmt.Errorf("failed to create notification record: %w", err)
	}
	log.Printf("Successfully created password reset notification record with ID: %d", notification.ID)

	// Send notification asynchronously
	log.Printf("Sending password reset notification asynchronously for user ID: %d, email: %s", resetData.UserID, resetData.Email)
	s.sendNotificationAsync(notification, lang)

	log.Printf("Queued password reset email for user ID: %d, email: %s, language: %s",
		resetData.UserID, resetData.Email, lang)
	return nil
}

// SendPasswordResetSuccessEmail sends a notification when password reset is successful
func (s *EmailService) SendPasswordResetSuccessEmail(data interface{}) error {
	resetSuccessData, ok := data.(*PasswordResetSuccessNotification)
	if !ok {
		return fmt.Errorf("invalid data type for password reset success notification")
	}

	// Ensure valid language
	resetSuccessData.Language = ensureValidLanguage(resetSuccessData.Language)
	lang := resetSuccessData.Language

	log.Printf("Starting password reset success notification for user ID: %d, email: %s, language: %s",
		resetSuccessData.UserID, resetSuccessData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	resetSuccessData.BaseURL = baseURL

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = "BiomassX - การยืนยันการรีเซ็ตรหัสผ่าน"
		tmplStr = passwordResetSuccessTemplateTH
	default:
		subject = "BiomassX - Password reset confirmation"
		tmplStr = passwordResetSuccessTemplateEN
	}

	// Parse email template
	tmpl, err := template.New("password_reset_success").Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*PasswordResetSuccessNotification
		CurrentYear int
	}{
		PasswordResetSuccessNotification: resetSuccessData,
		CurrentYear:                      time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypePasswordResetSuccess))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             resetSuccessData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              resetSuccessData.Email,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	err = s.repo.CreateNotification(notification)
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Send notification asynchronously
	s.sendNotificationAsync(notification, lang)

	log.Printf("Queued password reset success email for user ID: %d, email: %s, language: %s",
		resetSuccessData.UserID, resetSuccessData.Email, lang)
	return nil
}

// SendOrderSubmissionNotification sends a notification when a user submits an order
func (s *EmailService) SendOrderSubmissionNotification(data interface{}) error {
	orderData, ok := data.(*OrderSubmissionNotification)
	if !ok {
		return fmt.Errorf("invalid data type for order submission notification")
	}

	// Ensure valid language
	orderData.Language = ensureValidLanguage(orderData.Language)
	lang := orderData.Language

	log.Printf("Starting order submission notification for user ID: %d, email: %s, language: %s",
		orderData.UserID, orderData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	orderData.BaseURL = baseURL

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = "BiomassX - ยืนยันการส่งคำสั่งซื้อ"
		tmplStr = orderSubmissionTemplateTH
	default:
		subject = "BiomassX - Order Submission Confirmation"
		tmplStr = orderSubmissionTemplateEN
	}

	// Parse email template
	tmpl, err := template.New("order_submission").Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*OrderSubmissionNotification
		CurrentYear int
	}{
		OrderSubmissionNotification: orderData,
		CurrentYear:                 time.Now().Year(),
	}

	// Execute template with data
	var bodyBuffer bytes.Buffer
	if err := tmpl.Execute(&bodyBuffer, templateData); err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeOrderSubmission))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             orderData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              orderData.Email,
		Subject:            subject,
		Body:               bodyBuffer.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	if err := s.repo.CreateNotification(notification); err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Send notification asynchronously
	done := s.sendNotificationAsync(notification, lang)

	// Store the completion channel in case matching notifications need to wait for this one
	s.mu.Lock()
	submissionKey := fmt.Sprintf("order_submission_%d", orderData.UserID)
	s.notificationChannels.Store(submissionKey, done)
	s.mu.Unlock()

	log.Printf("Queued order submission notification for user ID: %d, email: %s, language: %s",
		orderData.UserID, orderData.Email, lang)
	return nil
}

// SendProfileIncompleteNotification sends a notification to remind users to complete their profile and address
func (s *EmailService) SendProfileIncompleteNotification(data interface{}) error {
	profileData, ok := data.(*ProfileIncompleteNotification)
	if !ok {
		return fmt.Errorf("invalid data type for profile incomplete notification")
	}

	// Ensure valid language
	profileData.Language = ensureValidLanguage(profileData.Language)
	lang := profileData.Language

	log.Printf("Starting profile and address completion notification for user ID: %d, email: %s, language: %s, missing basic fields: %v, missing address fields: %v",
		profileData.UserID, profileData.Email, lang, profileData.MissingBasicFields, profileData.MissingAddressFields)

	// We're no longer checking for recent notifications since we only want to send
	// the profile completion notification once after registration

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = "BiomassX - กรุณากรอกโปรไฟล์ให้ครบถ้วน"
		tmplStr = profileIncompleteTemplateTH
	default:
		subject = "BiomassX - Please complete your profile"
		tmplStr = profileIncompleteTemplateEN
	}

	// Parse email template
	tmpl, err := template.New("profile_incomplete").Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*ProfileIncompleteNotification
		CurrentYear int
	}{
		ProfileIncompleteNotification: profileData,
		CurrentYear:                   time.Now().Year(),
	}

	// Execute template with data
	var bodyBuffer bytes.Buffer
	if err := tmpl.Execute(&bodyBuffer, templateData); err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeProfileAndAddress))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             profileData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              profileData.Email,
		Subject:            subject,
		Body:               bodyBuffer.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	if err := s.repo.CreateNotification(notification); err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Send notification asynchronously
	s.sendNotificationAsync(notification, lang)

	log.Printf("Queued profile and address notification for user ID: %d, email: %s, language: %s",
		profileData.UserID, profileData.Email, lang)
	return nil
}

// SendMatchingNotification sends a notification when orders are matched
func (s *EmailService) SendMatchingNotification(data interface{}) error {
	matchData, ok := data.(*MatchingNotification)
	if !ok {
		return fmt.Errorf("invalid data type for matching notification")
	}

	// Determine language
	if matchData.Language == "" {
		// Get database connection
		db := database.GetDB()
		defer db.Close()

		// Use the determineUserLanguage function which doesn't rely on the language column
		matchData.Language = determineUserLanguage(db, matchData.UserID)
		log.Printf("Using determined language for matching notification: %s", matchData.Language)
	}

	// Ensure valid language
	matchData.Language = ensureValidLanguage(matchData.Language)
	lang := matchData.Language

	log.Printf("Starting matching notification for user ID: %d, email: %s, language: %s",
		matchData.UserID, matchData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	matchData.BaseURL = baseURL

	// Pre-calculate total value
	if matchData.Price > 0 && matchData.Quantity > 0 {
		matchData.TotalValue = matchData.Price * matchData.Quantity
	}

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = "BiomassX - พบการจับคู่คำสั่งซื้อขาย"
		tmplStr = matchingTemplateTH
	default:
		subject = "BiomassX - Order Match Found"
		tmplStr = matchingTemplateEN
	}

	// Create template with functions
	funcMap := template.FuncMap{
		"mul": func(a, b float64) float64 {
			return a * b
		},
	}

	// Parse email template with function map
	tmpl, err := template.New("matching").Funcs(funcMap).Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*MatchingNotification
		CurrentYear int
	}{
		MatchingNotification: matchData,
		CurrentYear:          time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeMatching))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             matchData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              matchData.Email,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	err = s.repo.CreateNotification(notification)
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Add a consistent delay to ensure order submission notifications are received first
	log.Printf("Adding %v delay before sending matching notification for user ID: %d, email: %s",
		NotificationSequenceDelay, matchData.UserID, matchData.Email)
	time.Sleep(NotificationSequenceDelay)

	// Send notification asynchronously
	done := s.sendNotificationAsync(notification, lang)

	// Store the completion channel in case other notifications need to wait for this one
	s.mu.Lock()
	matchingKey := fmt.Sprintf("matching_%d_%d", matchData.ContractID, matchData.UserID)
	s.notificationChannels.Store(matchingKey, done)
	s.mu.Unlock()

	log.Printf("Queued matching notification for user ID: %d, email: %s, language: %s",
		matchData.UserID, matchData.Email, lang)
	return nil
}

// SendContractConfirmationNotification sends a notification when both parties confirm the contract
func (s *EmailService) SendContractConfirmationNotification(data interface{}) error {
	confirmData, ok := data.(*ContractConfirmationNotification)
	if !ok {
		return fmt.Errorf("invalid data type for contract confirmation notification")
	}

	// Ensure valid language
	confirmData.Language = ensureValidLanguage(confirmData.Language)
	lang := confirmData.Language

	log.Printf("Starting contract confirmation notification for user ID: %d, email: %s, language: %s",
		confirmData.UserID, confirmData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	confirmData.BaseURL = baseURL

	// Pre-calculate total value
	if confirmData.Price > 0 && confirmData.Quantity > 0 {
		confirmData.TotalValue = confirmData.Price * confirmData.Quantity
	}

	// For Thai language, try to get Thai versions of product name, UOM, and delivery terms
	if lang == "th" {
		// Get database connection
		db := database.GetDB()

		// Get product Thai name if needed
		if confirmData.ProductName != "" {
			var productThName string
			err := db.QueryRow("SELECT th_name FROM products WHERE en_name = $1", confirmData.ProductName).Scan(&productThName)
			if err == nil && productThName != "" {
				// Replace product name with Thai version
				confirmData.ProductName = productThName
			}
		}

		// Get UOM Thai name if needed
		if confirmData.UnitOfMeasure != "" {
			var uomThName string
			err := db.QueryRow("SELECT th_name FROM uoms WHERE en_name = $1", confirmData.UnitOfMeasure).Scan(&uomThName)
			if err == nil && uomThName != "" {
				// Replace UOM with Thai version
				confirmData.UnitOfMeasure = uomThName
			}
		}

		// Get delivery terms Thai name if needed
		if confirmData.DeliveryTerms != "" {
			var deliveryTermThName string
			err := db.QueryRow("SELECT th_name FROM delivery_terms WHERE en_name = $1", confirmData.DeliveryTerms).Scan(&deliveryTermThName)
			if err == nil && deliveryTermThName != "" {
				// Replace delivery terms with Thai version
				confirmData.DeliveryTerms = deliveryTermThName
			}
		}
	}

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = "BiomassX - สัญญาได้รับการยืนยันเรียบร้อยแล้ว"
		tmplStr = contractConfirmationTemplateTH
	default:
		subject = "BiomassX - Contract Successfully Confirmed"
		tmplStr = contractConfirmationTemplateEN
	}

	// Create template with functions
	funcMap := template.FuncMap{
		"mul": func(a, b float64) float64 {
			return a * b
		},
	}

	// Parse email template with function map
	tmpl, err := template.New("contract_confirmation").Funcs(funcMap).Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*ContractConfirmationNotification
		CurrentYear int
	}{
		ContractConfirmationNotification: confirmData,
		CurrentYear:                      time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeContractConfirmation))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             confirmData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              confirmData.Email,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	err = s.repo.CreateNotification(notification)
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Wait for any status update notifications for this contract to complete first
	// Check for status updates for both the current user and any other users involved in this contract
	// This ensures we wait for ALL status update notifications related to this contract

	// First, check for status update for this user
	statusUpdateKey := fmt.Sprintf("status_update_%d_%d", confirmData.ContractID, confirmData.UserID)

	// Also check for status updates for any user with this contract ID
	contractStatusKey := fmt.Sprintf("status_update_%d_", confirmData.ContractID)

	// Get all keys from the notification channels map
	var keysToWaitFor []string
	var channelsToWaitFor []chan bool

	s.mu.Lock()
	s.notificationChannels.Range(func(k, v interface{}) bool {
		key := k.(string)
		if key == statusUpdateKey || strings.HasPrefix(key, contractStatusKey) {
			keysToWaitFor = append(keysToWaitFor, key)
			channelsToWaitFor = append(channelsToWaitFor, v.(chan bool))
		}
		return true
	})
	s.mu.Unlock()

	// Wait for all status update notifications to complete
	if len(channelsToWaitFor) > 0 {
		log.Printf("Waiting for %d status update notifications to complete before sending confirmation for contract ID: %d, user ID: %d",
			len(channelsToWaitFor), confirmData.ContractID, confirmData.UserID)

		for i, done := range channelsToWaitFor {
			if !waitForNotification(done, 10*time.Second) {
				log.Printf("Warning: Timed out waiting for status update notification %s to complete for contract ID: %d",
					keysToWaitFor[i], confirmData.ContractID)
			} else {
				log.Printf("Status update notification %s completed for contract ID: %d",
					keysToWaitFor[i], confirmData.ContractID)
			}

			// Remove the channel from the map
			s.mu.Lock()
			s.notificationChannels.Delete(keysToWaitFor[i])
			s.mu.Unlock()
		}
	}

	// Add a consistent delay to ensure notifications are received in order
	log.Printf("Adding %v delay before sending contract confirmation notification for contract ID: %d, user ID: %d",
		NotificationSequenceDelay, confirmData.ContractID, confirmData.UserID)
	time.Sleep(NotificationSequenceDelay)

	// Send notification asynchronously
	done := s.sendNotificationAsync(notification, lang)

	// Store the completion channel in case other notifications need to wait for this one
	s.mu.Lock()
	confirmationKey := fmt.Sprintf("confirmation_%d_%d", confirmData.ContractID, confirmData.UserID)
	s.notificationChannels.Store(confirmationKey, done)
	s.mu.Unlock()

	log.Printf("Queued contract confirmation notification for user ID: %d, email: %s, language: %s",
		confirmData.UserID, confirmData.Email, lang)
	return nil
}

// SendContractDeadlineReminderNotification sends a reminder notification to users who haven't confirmed a contract
// that is approaching its 3-day deadline
func (s *EmailService) SendContractDeadlineReminderNotification(data interface{}) error {
	statusData, ok := data.(*ContractStatusUpdateNotification)
	if !ok {
		return fmt.Errorf("invalid data type for contract deadline reminder notification")
	}

	// Ensure valid language
	statusData.Language = ensureValidLanguage(statusData.Language)
	lang := statusData.Language

	log.Printf("Starting contract deadline reminder notification for user ID: %d, email: %s, language: %s",
		statusData.UserID, statusData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	statusData.BaseURL = baseURL

	// Pre-calculate total value
	if statusData.Price > 0 && statusData.Quantity > 0 {
		statusData.TotalValue = statusData.Price * statusData.Quantity
	}

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = fmt.Sprintf("BiomassX - แจ้งเตือนด่วน: สัญญาของคุณ #%d จะหมดอายุวันนี้", statusData.ContractID)
		tmplStr = contractDeadlineReminderTemplateTH
	default:
		subject = fmt.Sprintf("BiomassX - URGENT: Your Contract #%d Will Expire Today", statusData.ContractID)
		tmplStr = contractDeadlineReminderTemplateEN
	}

	// Create template with functions
	funcMap := template.FuncMap{
		"mul": func(a, b float64) float64 {
			return a * b
		},
	}

	// Parse email template with function map
	tmpl, err := template.New("contract_deadline_reminder").Funcs(funcMap).Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*ContractStatusUpdateNotification
		CurrentYear int
	}{
		ContractStatusUpdateNotification: statusData,
		CurrentYear:                      time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeMatchExpiryReminder))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             statusData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              statusData.Email,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	err = s.repo.CreateNotification(notification)
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Send notification asynchronously
	s.sendNotificationAsync(notification, lang)

	log.Printf("Queued contract deadline reminder notification for user ID: %d, email: %s, language: %s",
		statusData.UserID, statusData.Email, lang)
	return nil
}

// SendContractWaitingNotification sends a notification to users who have already confirmed a contract
// but are waiting for their counterparty to confirm before the 3-day deadline
func (s *EmailService) SendContractWaitingNotification(data interface{}) error {
	statusData, ok := data.(*ContractStatusUpdateNotification)
	if !ok {
		return fmt.Errorf("invalid data type for contract waiting notification")
	}

	// Ensure valid language
	statusData.Language = ensureValidLanguage(statusData.Language)
	lang := statusData.Language

	log.Printf("Starting contract waiting notification for user ID: %d, email: %s, language: %s",
		statusData.UserID, statusData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	statusData.BaseURL = baseURL

	// Pre-calculate total value
	if statusData.Price > 0 && statusData.Quantity > 0 {
		statusData.TotalValue = statusData.Price * statusData.Quantity
	}

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = fmt.Sprintf("BiomassX - แจ้งเตือน: คู่สัญญาของคุณยังไม่ได้ยืนยันสัญญา #%d", statusData.ContractID)
		tmplStr = contractWaitingNotificationTemplateTH
	default:
		subject = fmt.Sprintf("BiomassX - Your Counterparty Hasn't Confirmed Contract #%d Yet", statusData.ContractID)
		tmplStr = contractWaitingNotificationTemplateEN
	}

	// Create template with functions
	funcMap := template.FuncMap{
		"mul": func(a, b float64) float64 {
			return a * b
		},
	}

	// Parse email template with function map
	tmpl, err := template.New("contract_waiting_notification").Funcs(funcMap).Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*ContractStatusUpdateNotification
		CurrentYear int
	}{
		ContractStatusUpdateNotification: statusData,
		CurrentYear:                      time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeMatchExpiryReminderConfirmed))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             statusData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              statusData.Email,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	err = s.repo.CreateNotification(notification)
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Send notification asynchronously
	s.sendNotificationAsync(notification, lang)

	log.Printf("Queued contract waiting notification for user ID: %d, email: %s, language: %s",
		statusData.UserID, statusData.Email, lang)
	return nil
}

// Define email templates for invoice payment notifications
const (
	invoicePaymentTemplateEN = `<!DOCTYPE html><html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Payment Reminder</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
            width: 100%;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #b2ff59;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
            border-color: #ff6d00;
        }
        .footer a {
            color: #b2ff59;
            text-decoration: none;
        }
        .footer a:hover {
            color: #ff6d00;
        }
        @media only screen and (max-width: 600px) {
            .content {
                padding: 10px;
            }
            .card {
                padding: 15px;
            }
            .header {
                padding: 15px;
                font-size: 20px;
            }
        }
        .invoice-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .invoice-details ul {
            list-style-type: none;
            padding-left: 5px;
        }
        .invoice-details li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .invoice-details li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        Invoice Payment Reminder
    </div>
    <div class="content">
        <h2>Invoice Payment Reminder</h2>
        <p>Hello {{.FirstName}} {{.LastName}},</p>
        <div class="card">
            <p>Your contract has been successfully signed by both parties. Please pay the platform fee invoice as detailed below:</p>
            <div class="invoice-details">
                <p><strong>Invoice Details:</strong></p>
                <ul>
                    <li><strong>Invoice Number:</strong> {{.InvoiceNumber}}</li>
                    <li><strong>Contract Number:</strong> {{.ContractNumber}}</li>
                    <li><strong>Amount:</strong> {{printf "%.2f" .Amount}} {{.Currency}}</li>
                    <li><strong>Due Date:</strong> {{.DueDate}}</li>
                </ul>
            </div>
            <p>Please log in to your account to view and pay this invoice.</p>
            <div style="text-align: center;">
                <a href="{{.InvoiceDetailsURL}}" class="button">View Invoice</a>
            </div>
        </div>
        <p>Best regards,<br>
        The BiomassX Team</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>© {{.CurrentYear}} BiomassX.com. All rights reserved.</p>
        <p><a href="{{.BaseURL}}">Visit Our Website</a></p>
    </div>
</body></html>`

	invoicePaymentTemplateTH = `<!DOCTYPE html><html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แจ้งเตือนการชำระเงินใบแจ้งหนี้</title>
    <style>
        body {
            background-color: #ffffff;
            font-family: 'Verdana', 'Geneva', sans-serif;
            color: #333;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .header {
            background-color: #ff6d00;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        .nav-bar {
            background-color: #b2ff59;
            padding: 15px;
            text-align: center;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        }
        .content {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
        }
        .footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
            width: 100%;
        }
        h1, h2 {
            color: black;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }
        .button {
            display: inline-block;
            background-color: #b2ff59;
            color: #333;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 16px;
            margin-top: 20px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #b2ff59;
        }
        .button:hover {
            background-color: #ff6d00;
            color: white;
            border-color: #ff6d00;
        }
        .footer a {
            color: #b2ff59;
            text-decoration: none;
        }
        .footer a:hover {
            color: #ff6d00;
        }
        @media only screen and (max-width: 600px) {
            .content {
                padding: 10px;
            }
            .card {
                padding: 15px;
            }
            .header {
                padding: 15px;
                font-size: 20px;
            }
        }
        .invoice-details {
            background-color: #fff;
            border-left: 4px solid #b2ff59;
            padding: 10px 15px;
            margin: 15px 0;
        }
        .invoice-details ul {
            list-style-type: none;
            padding-left: 5px;
        }
        .invoice-details li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .invoice-details li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BiomassX</h1>
    </div>
    <div class="nav-bar">
        แจ้งเตือนการชำระเงินใบแจ้งหนี้
    </div>
    <div class="content">
        <h2>แจ้งเตือนการชำระเงินใบแจ้งหนี้</h2>
        <p>สวัสดี {{.FirstName}} {{.LastName}},</p>
        <div class="card">
            <p>สัญญาของคุณได้รับการลงนามโดยทั้งสองฝ่ายเรียบร้อยแล้ว กรุณาชำระค่าธรรมเนียมแพลตฟอร์มตามรายละเอียดด้านล่าง:</p>
            <div class="invoice-details">
                <p><strong>รายละเอียดใบแจ้งหนี้:</strong></p>
                <ul>
                    <li><strong>เลขที่ใบแจ้งหนี้:</strong> {{.InvoiceNumber}}</li>
                    <li><strong>เลขที่สัญญา:</strong> {{.ContractNumber}}</li>
                    <li><strong>จำนวนเงิน:</strong> {{printf "%.2f" .Amount}} {{.Currency}}</li>
                    <li><strong>วันครบกำหนด:</strong> {{.DueDate}}</li>
                </ul>
            </div>
            <p>กรุณาเข้าสู่ระบบบัญชีของคุณเพื่อดูและชำระใบแจ้งหนี้นี้</p>
            <div style="text-align: center;">
                <a href="{{.InvoiceDetailsURL}}" class="button">ดูใบแจ้งหนี้</a>
            </div>
        </div>
        <p>ด้วยความเคารพ,<br>
        ทีมงาน BiomassX</p>
    </div>
    <div class="footer">
        <p>นี่เป็นข้อความอัตโนมัติ กรุณาอย่าตอบกลับอีเมลนี้</p>
        <p>© {{.CurrentYear}} BiomassX.com สงวนลิขสิทธิ์</p>
        <p><a href="{{.BaseURL}}">เยี่ยมชมเว็บไซต์ของเรา</a></p>
    </div>
</body></html>`
)

// SendInvoicePaymentNotification sends a notification for invoice payment
func (s *EmailService) SendInvoicePaymentNotification(data interface{}) error {
	invoiceData, ok := data.(*InvoicePaymentNotification)
	if !ok {
		return fmt.Errorf("invalid data type for invoice payment notification")
	}

	// Ensure valid language
	invoiceData.Language = ensureValidLanguage(invoiceData.Language)
	lang := invoiceData.Language

	log.Printf("Starting invoice payment notification for user ID: %d, email: %s, language: %s",
		invoiceData.UserID, invoiceData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	invoiceData.BaseURL = baseURL

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = "BiomassX - แจ้งเตือนการชำระเงินใบแจ้งหนี้"
		tmplStr = invoicePaymentTemplateTH
	default:
		subject = "BiomassX - Invoice Payment Reminder"
		tmplStr = invoicePaymentTemplateEN
	}

	// Parse email template
	tmpl, err := template.New("invoice_payment").Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*InvoicePaymentNotification
		CurrentYear int
	}{
		InvoicePaymentNotification: invoiceData,
		CurrentYear:                time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeInvoicePayment))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Check if a notification for this invoice already exists
	// This prevents duplicate notifications
	exists, err := s.repo.NotificationExists(invoiceData.UserID, notificationType.ID, invoiceData.InvoiceID)
	if err != nil {
		log.Printf("Warning: Failed to check if notification exists: %v", err)
	} else if exists {
		log.Printf("Skipping duplicate invoice payment notification for user ID: %d, invoice ID: %d",
			invoiceData.UserID, invoiceData.InvoiceID)
		return nil
	}

	// Create notification record
	notification := &Notification{
		UserID:             invoiceData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              invoiceData.Email,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
		Data: map[string]interface{}{
			"invoice_id": invoiceData.InvoiceID,
		},
	}

	// Save notification to database
	err = s.repo.CreateNotification(notification)
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Send notification asynchronously
	s.sendNotificationAsync(notification, lang)

	log.Printf("Queued invoice payment notification for user ID: %d, email: %s, language: %s",
		invoiceData.UserID, invoiceData.Email, lang)
	return nil
}

// SendContractStatusUpdateNotification sends a notification when a contract status is updated
func (s *EmailService) SendContractStatusUpdateNotification(data interface{}) error {
	statusData, ok := data.(*ContractStatusUpdateNotification)
	if !ok {
		return fmt.Errorf("invalid data type for contract status update notification")
	}

	// Ensure valid language
	statusData.Language = ensureValidLanguage(statusData.Language)
	lang := statusData.Language

	log.Printf("Starting contract status update notification for user ID: %d, email: %s, language: %s",
		statusData.UserID, statusData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	statusData.BaseURL = baseURL

	// Pre-calculate total value
	if statusData.Price > 0 && statusData.Quantity > 0 {
		statusData.TotalValue = statusData.Price * statusData.Quantity
	}

	// For Thai language, try to get Thai versions of product name, UOM, and delivery terms
	if lang == "th" {
		// Get database connection
		db := database.GetDB()

		// Get product Thai name if needed
		if statusData.ProductName != "" {
			var productThName string
			err := db.QueryRow("SELECT th_name FROM products WHERE en_name = $1", statusData.ProductName).Scan(&productThName)
			if err == nil && productThName != "" {
				// Replace product name with Thai version
				statusData.ProductName = productThName
			}
		}

		// Get UOM Thai name if needed
		if statusData.UnitOfMeasure != "" {
			var uomThName string
			err := db.QueryRow("SELECT th_name FROM uoms WHERE en_name = $1", statusData.UnitOfMeasure).Scan(&uomThName)
			if err == nil && uomThName != "" {
				// Replace UOM with Thai version
				statusData.UnitOfMeasure = uomThName
			}
		}

		// Get delivery terms Thai name if needed
		if statusData.DeliveryTerms != "" {
			var deliveryTermThName string
			err := db.QueryRow("SELECT th_name FROM delivery_terms WHERE en_name = $1", statusData.DeliveryTerms).Scan(&deliveryTermThName)
			if err == nil && deliveryTermThName != "" {
				// Replace delivery terms with Thai version
				statusData.DeliveryTerms = deliveryTermThName
			}
		}
	}

	// Create email subject and select template based on language and action
	var subject, tmplStr string

	// Check if this is a rejection notification
	if statusData.ActionTaken == "rejected" || statusData.ActionTaken == "ปฏิเสธ" {
		// Use different templates and subjects for rejection notifications
		if statusData.IsActionTaker {
			// This user rejected the contract
			switch lang {
			case "th":
				subject = "BiomassX - คุณได้ปฏิเสธสัญญาเรียบร้อยแล้ว"
				tmplStr = contractRejectionActionTakerTemplateTH
			default:
				subject = "BiomassX - You Rejected the Contract"
				tmplStr = contractRejectionActionTakerTemplateEN
			}
		} else {
			// This user's counterparty rejected the contract
			switch lang {
			case "th":
				subject = "BiomassX - คู่สัญญาของคุณได้ปฏิเสธสัญญา"
				tmplStr = contractRejectionCounterpartyTemplateTH
			default:
				subject = "BiomassX - Contract Rejected by Counterparty"
				tmplStr = contractRejectionCounterpartyTemplateEN
			}
		}
	} else {
		// Use standard templates for other status updates
		switch lang {
		case "th":
			subject = "BiomassX - การอัปเดตสถานะสัญญา"
			tmplStr = contractStatusUpdateTemplateTH
		default:
			subject = "BiomassX - Contract Status Update"
			tmplStr = contractStatusUpdateTemplateEN
		}
	}

	// Create template with functions
	funcMap := template.FuncMap{
		"mul": func(a, b float64) float64 {
			return a * b
		},
	}

	// Parse email template with function map
	tmpl, err := template.New("contract_status_update").Funcs(funcMap).Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*ContractStatusUpdateNotification
		CurrentYear int
	}{
		ContractStatusUpdateNotification: statusData,
		CurrentYear:                      time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeContractStatusUpdate))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             statusData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              statusData.Email,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	err = s.repo.CreateNotification(notification)
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Send notification asynchronously and return the completion channel
	done := s.sendNotificationAsync(notification, lang)

	log.Printf("Queued contract status update notification for user ID: %d, email: %s, language: %s",
		statusData.UserID, statusData.Email, lang)

	// Store the completion channel in a map for contract confirmation notifications to wait on
	s.mu.Lock()
	notificationKey := fmt.Sprintf("status_update_%d_%d", statusData.ContractID, statusData.UserID)
	s.notificationChannels.Store(notificationKey, done)
	s.mu.Unlock()

	return nil
}

// SendContractCancellationNotification sends a notification when a contract is cancelled
func (s *EmailService) SendContractCancellationNotification(data interface{}) error {
	cancelData, ok := data.(*ContractCancellationNotification)
	if !ok {
		return fmt.Errorf("invalid data type for contract cancellation notification")
	}

	// Ensure valid language
	cancelData.Language = ensureValidLanguage(cancelData.Language)
	lang := cancelData.Language

	log.Printf("Starting contract cancellation notification for user ID: %d, email: %s, language: %s",
		cancelData.UserID, cancelData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	cancelData.BaseURL = baseURL

	// Pre-calculate total value
	if cancelData.Price > 0 && cancelData.Quantity > 0 {
		cancelData.TotalValue = cancelData.Price * cancelData.Quantity
	}

	// For Thai language, try to get Thai version of product name
	if lang == "th" && cancelData.ProductName != "" {
		// Get database connection
		db := database.GetDB()

		// Get product Thai name if needed
		var productThName string
		err := db.QueryRow("SELECT th_name FROM products WHERE en_name = $1", cancelData.ProductName).Scan(&productThName)
		if err == nil && productThName != "" {
			// Replace product name with Thai version
			cancelData.ProductName = productThName
		}
	}

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = "BiomassX - สัญญาถูกยกเลิก"
		tmplStr = contractCancellationTemplateTH
	default:
		subject = "BiomassX - Contract Cancelled"
		tmplStr = contractCancellationTemplateEN
	}

	// Parse email template
	tmpl, err := template.New("contract_cancellation").Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*ContractCancellationNotification
		CurrentYear int
	}{
		ContractCancellationNotification: cancelData,
		CurrentYear:                      time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeContractCancellation))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Create notification record
	notification := &Notification{
		UserID:             cancelData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              cancelData.Email,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	err = s.repo.CreateNotification(notification)
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Wait for the status update notification to complete first
	// This ensures the "Contract Rejected by Counterparty" notification is sent before the cancellation
	time.Sleep(2 * time.Second)

	// Send notification asynchronously
	s.sendNotificationAsync(notification, lang)

	log.Printf("Queued contract cancellation notification for user ID: %d, email: %s, language: %s",
		cancelData.UserID, cancelData.Email, lang)
	return nil
}

// SendContractCancellationRejectionNotification sends a notification about a contract cancellation due to rejection
func (s *EmailService) SendContractCancellationRejectionNotification(data interface{}) error {
	cancelData, ok := data.(*ContractCancellationNotification)
	if !ok {
		return fmt.Errorf("invalid data type for contract cancellation rejection notification")
	}

	// Ensure valid language
	cancelData.Language = ensureValidLanguage(cancelData.Language)
	lang := cancelData.Language

	log.Printf("Starting contract cancellation rejection notification for user ID: %d, email: %s, language: %s",
		cancelData.UserID, cancelData.Email, lang)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Add baseURL to the template data
	cancelData.BaseURL = baseURL

	// Pre-calculate total value
	if cancelData.Price > 0 && cancelData.Quantity > 0 {
		cancelData.TotalValue = cancelData.Price * cancelData.Quantity
	}

	// For Thai language, try to get Thai version of product name
	if lang == "th" && cancelData.ProductName != "" {
		// Get database connection
		db := database.GetDB()

		// Get product Thai name if needed
		var productThName string
		err := db.QueryRow("SELECT th_name FROM products WHERE en_name = $1", cancelData.ProductName).Scan(&productThName)
		if err == nil && productThName != "" {
			// Replace product name with Thai version
			cancelData.ProductName = productThName
		}
	}

	// Create email subject and select template based on language
	var subject, tmplStr string
	switch lang {
	case "th":
		subject = fmt.Sprintf("BiomassX - สัญญา #%d ถูกยกเลิกเนื่องจากการปฏิเสธ", cancelData.ContractID)
		tmplStr = contractCancellationRejectionTemplateTH
	default:
		subject = fmt.Sprintf("BiomassX - Contract #%d Cancelled Due to Rejection", cancelData.ContractID)
		tmplStr = contractCancellationRejectionTemplateEN
	}

	// Create template with functions
	funcMap := template.FuncMap{
		"mul": func(a, b float64) float64 {
			return a * b
		},
	}

	// Parse email template with function map
	tmpl, err := template.New("contract_cancellation_rejection").Funcs(funcMap).Parse(tmplStr)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Add current year to the data
	templateData := struct {
		*ContractCancellationNotification
		CurrentYear int
	}{
		ContractCancellationNotification: cancelData,
		CurrentYear:                      time.Now().Year(),
	}

	// Execute template with data
	var body bytes.Buffer
	err = tmpl.Execute(&body, templateData)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Get or create notification type
	notificationType, err := s.getOrCreateNotificationType(string(NotificationTypeContractCancellation))
	if err != nil {
		return fmt.Errorf("failed to get/create notification type: %w", err)
	}

	// Check if a cancellation notification has already been sent for this contract
	var notificationCount int
	s.mu.Lock()
	// Get database connection
	db := database.GetDB()
	err = db.QueryRow(`
		SELECT COUNT(*)
		FROM notifications n
		JOIN notification_types nt ON n.notification_type_id = nt.id
		WHERE nt.name = $1
		AND n.body LIKE '%contract_id=' || $2 || '%'
		AND n.user_id = $3
		AND n.created_at > NOW() - INTERVAL '24 hours'
	`, string(NotificationTypeContractCancellation), cancelData.ContractID, cancelData.UserID).Scan(&notificationCount)
	s.mu.Unlock()

	if err != nil {
		log.Printf("Warning: Failed to check for existing cancellation notifications: %v", err)
		// Continue execution even if check fails
		notificationCount = 0
	}

	// If a cancellation notification has already been sent, skip sending another one
	if notificationCount > 0 {
		log.Printf("Cancellation notification already sent for contract ID: %d, user ID: %d, skipping",
			cancelData.ContractID, cancelData.UserID)
		return nil
	}

	// Create notification record
	notification := &Notification{
		UserID:             cancelData.UserID,
		NotificationTypeID: notificationType.ID,
		Email:              cancelData.Email,
		Subject:            subject,
		Body:               body.String(),
		Status:             string(NotificationStatusPending),
		CreatedAt:          time.Now(),
	}

	// Save notification to database
	err = s.repo.CreateNotification(notification)
	if err != nil {
		return fmt.Errorf("failed to create notification record: %w", err)
	}

	// Wait for the status update and cancellation notifications to complete first
	// This ensures the "Contract Rejected by Counterparty" notification is sent before the cancellation rejection
	time.Sleep(3 * time.Second)

	// Send notification asynchronously
	s.sendNotificationAsync(notification, lang)

	log.Printf("Queued contract cancellation due to rejection notification for user ID: %d, email: %s, language: %s",
		cancelData.UserID, cancelData.Email, lang)
	return nil
}

// SendProfileCompletionNotification sends a notification to remind users to complete their profile
func (s *EmailService) SendProfileCompletionNotification(userID int64) error {
	log.Printf("Checking profile completion for user %d", userID)
	completeness, err := s.CheckProfileCompleteness(userID)
	if err != nil {
		return fmt.Errorf("failed to check profile completeness: %w", err)
	}

	if completeness.HasBasicInfo && completeness.HasAddressInfo {
		log.Printf("User %d has a complete profile, no notification needed", userID)
		return nil
	}

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Create notification data
	notificationData := &ProfileIncompleteNotification{
		UserID:                      userID,
		Email:                       completeness.Email,
		FirstName:                   completeness.FirstName,
		LastName:                    completeness.LastName,
		MissingBasicFields:          completeness.MissingBasicFields,
		MissingAddressFields:        completeness.MissingAddressFields,
		ProfileCompletionPercentage: completeness.CompletionPercentage,
		ProfileURL:                  baseURL + "/profile?lang=" + "en", // Default to English for cron jobs
		BasicInfoURL:                baseURL + "/profile?section=basic&lang=" + "en",
		AddressURL:                  baseURL + "/profile?section=address&lang=" + "en",
		Language:                    "en", // Default to English for cron jobs
	}

	log.Printf("Sending profile and address notification for user %d: MissingBasicFields=%v, MissingAddressFields=%v",
		userID, notificationData.MissingBasicFields, notificationData.MissingAddressFields)
	return s.SendProfileIncompleteNotification(notificationData)
}

// CheckProfileCompleteness checks a user's profile and returns information about what's missing
func (s *EmailService) CheckProfileCompleteness(userID int64) (*ProfileCompleteness, error) {
	log.Printf("Checking profile completeness for user %d", userID)

	// Use repository method to get profile completeness
	completeness, err := s.repo.GetUserProfileCompleteness(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get profile completeness: %w", err)
	}

	// Log raw completeness data for debugging
	log.Printf("Raw profile completeness for user %d: HasBasicInfo=%v, HasAddressInfo=%v, MissingBasicFields=%v, MissingAddressFields=%v",
		userID, completeness.HasBasicInfo, completeness.HasAddressInfo, completeness.MissingBasicFields, completeness.MissingAddressFields)

	// Map repository fields to service fields
	completeness.UserID = userID
	user, err := s.repo.GetUserByID(userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user information: %w", err)
	}
	if user != nil {
		completeness.Email = user.Email
		completeness.FirstName = user.FirstName
		completeness.LastName = user.LastName
		completeness.OrganizationName = user.OrganizationName
		completeness.Phone = user.Phone
	} else {
		log.Printf("Warning: User %d not found", userID)
	}

	// Explicitly check if the user has addresses to ensure accuracy
	hasAddresses, err := s.repo.UserHasAddresses(userID)
	if err != nil {
		log.Printf("Error checking addresses for user %d: %v", userID, err)
		// Fallback: assume no addresses if check fails
		hasAddresses = false
	}
	if !hasAddresses {
		// If no addresses, ensure HasAddressInfo is false and add missing fields
		completeness.HasAddressInfo = false
		if len(completeness.MissingAddressFields) == 0 {
			// Add default missing address fields if none specified
			completeness.MissingAddressFields = []string{"Street Address", "City", "Postal Code", "Country"}
		}
		log.Printf("User %d has no addresses, marked as missing: %v", userID, completeness.MissingAddressFields)
	} else {
		// If addresses exist, ensure HasAddressInfo is true and clear missing fields
		completeness.HasAddressInfo = true
		completeness.MissingAddressFields = nil
	}

	// Ensure basic info fields are checked
	if len(completeness.MissingBasicFields) == 0 && !completeness.HasBasicInfo {
		// Add default missing fields if none specified
		completeness.MissingBasicFields = []string{"First Name", "Last Name", "Phone"}
	}

	// Log final completeness for debugging
	log.Printf("Final profile completeness for user %d: HasBasicInfo=%v, HasAddressInfo=%v, MissingBasicFields=%v, MissingAddressFields=%v",
		userID, completeness.HasBasicInfo, completeness.HasAddressInfo, completeness.MissingBasicFields, completeness.MissingAddressFields)

	return completeness, nil
}

// SendSmartProfileCompletionNotification sends a personalized notification based on what's missing
func (s *EmailService) SendSmartProfileCompletionNotification(userID int64) error {
	log.Printf("Starting smart profile completion notification for user %d", userID)

	// Check profile completeness
	completeness, err := s.CheckProfileCompleteness(userID)
	if err != nil {
		return fmt.Errorf("failed to check profile completeness: %w", err)
	}

	// If profile is complete, no need to send notification
	if completeness.HasBasicInfo && completeness.HasAddressInfo {
		log.Printf("User %d has a complete profile, no notification needed", userID)
		return nil
	}

	// Get user for username
	user, err := s.repo.GetUserByID(userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return fmt.Errorf("user %d not found", userID)
	}

	// Get language preference from the most recent registration notification
	language := "en" // Default to English

	// Check if we have any notifications with language information
	notifications, err := s.repo.GetNotificationsByUserID(userID)
	if err != nil {
		log.Printf("Error getting notifications for user %d: %v", userID, err)
	} else {
		// First look specifically for registration notifications as they're most likely to have the correct language
		// Start with most recent notifications (they're ordered by created_at DESC)
		for _, notification := range notifications {
			if notification.Type == string(NotificationTypeRegistration) {
				// Check subject for language indicators
				if strings.Contains(notification.Subject, "ยินดีต้อนรับสู่ BiomassX") {
					language = "th"
					log.Printf("Found Thai registration notification for user %d", userID)
					break
				} else if strings.Contains(notification.Subject, "Welcome to BiomassX") {
					language = "en"
					log.Printf("Found English registration notification for user %d", userID)
					break
				}
			}
		}

		// If no registration notification found, check other notification types
		if language == "en" { // Only check if we're still using the default
			for _, notification := range notifications {
				if notification.Type == string(NotificationTypePasswordReset) ||
					notification.Type == string(NotificationTypePasswordResetSuccess) ||
					notification.Type == string(NotificationTypeProfileAndAddress) {

					// Check subject for language indicators
					if strings.Contains(notification.Subject, "รีเซ็ตรหัสผ่าน") ||
						strings.Contains(notification.Subject, "กรุณากรอกโปรไฟล์") {
						language = "th"
						log.Printf("Found Thai notification for user %d", userID)
						break
					} else if strings.Contains(notification.Subject, "Password reset") ||
						strings.Contains(notification.Subject, "complete your profile") {
						language = "en"
						log.Printf("Found English notification for user %d", userID)
						break
					}
				}
			}
		}
	}

	log.Printf("Determined language for user %d: %s", userID, language)

	// Create field importance map based on language
	var fieldImportance map[string]string
	switch language {
	case "th":
		fieldImportance = map[string]string{
			"First Name":     "จำเป็นสำหรับการปรับแต่งบัญชี",
			"Last Name":      "จำเป็นสำหรับการปรับแต่งบัญชี",
			"Phone":          "จำเป็นสำหรับการติดต่อและยืนยันตัวตน",
			"Street Address": "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
			"City":           "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
			"Postal Code":    "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
			"Country":        "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
		}
	default:
		fieldImportance = map[string]string{
			"First Name":     "Required for account personalization",
			"Last Name":      "Required for account personalization",
			"Phone":          "Required for contact and verification",
			"Street Address": "Required for delivery and compliance",
			"City":           "Required for delivery and compliance",
			"Postal Code":    "Required for delivery and compliance",
			"Country":        "Required for delivery and compliance",
		}
	}

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}

	// Create notification data
	notificationData := &ProfileIncompleteNotification{
		UserID:                      userID,
		Email:                       completeness.Email,
		FirstName:                   completeness.FirstName,
		LastName:                    completeness.LastName,
		Username:                    user.Username,
		MissingBasicFields:          completeness.MissingBasicFields,
		MissingAddressFields:        completeness.MissingAddressFields,
		MissingFields:               append(completeness.MissingBasicFields, completeness.MissingAddressFields...),
		MissingCategories:           map[string][]string{},
		ProfileCompletionPercentage: completeness.CompletionPercentage,
		FieldImportance:             fieldImportance,
		ProfileURL:                  baseURL + "/profile?lang=" + language,
		BasicInfoURL:                baseURL + "/profile?section=basic&lang=" + language,
		AddressURL:                  baseURL + "/profile?section=address&lang=" + language,
		Language:                    language, // Use the determined language
	}

	log.Printf("Sending profile and address notification for user %d: Email=%s, MissingBasicFields=%v, MissingAddressFields=%v, Language=%s",
		userID, completeness.Email, notificationData.MissingBasicFields, notificationData.MissingAddressFields, notificationData.Language)
	return s.SendProfileIncompleteNotification(notificationData)
}

// ScheduleProfileCompletionReminders schedules reminders for users with incomplete profiles
func (s *EmailService) ScheduleProfileCompletionReminders() error {
	// Use service mutex to synchronize access to the database
	s.mu.Lock()
	// Get all active users
	users, err := s.repo.GetActiveUsers()
	s.mu.Unlock()

	if err != nil {
		return fmt.Errorf("failed to get active users: %w", err)
	}

	log.Printf("Scheduling profile and address completion reminders for %d active users", len(users))
	if len(users) == 0 {
		log.Println("No active users found for profile completion reminders")
	}

	// Process each user
	var wg sync.WaitGroup

	// Create a semaphore to limit concurrent goroutines
	// This helps prevent overwhelming the database and email server
	maxConcurrent := 5
	semaphore := make(chan struct{}, maxConcurrent)

	for _, user := range users {
		wg.Add(1)
		go func(userID int64) {
			defer wg.Done()

			// Acquire semaphore slot
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// Add delay to prevent overwhelming the system
			time.Sleep(time.Duration(rand.Intn(500)) * time.Millisecond)

			log.Printf("Processing profile completion for user %d", userID)

			// Use service mutex for database operations
			s.mu.Lock()
			completeness, err := s.repo.GetUserProfileCompleteness(userID)
			s.mu.Unlock()

			if err != nil {
				log.Printf("Error getting profile completeness for user %d: %v", userID, err)
				return
			}

			// Skip sending notification if profile is already complete
			if completeness != nil && completeness.CompletionPercentage == 100 {
				log.Printf("User %d has complete profile, skipping notification", userID)
				return
			}

			if err := s.SendSmartProfileCompletionNotification(userID); err != nil {
				log.Printf("Error sending profile completion notification to user %d: %v", userID, err)
			}
		}(user.ID)
	}
	wg.Wait()

	log.Println("Completed scheduling profile and address completion reminders")
	return nil
}

// StartScheduledNotifications starts background goroutines to periodically check for notifications
func (s *EmailService) StartScheduledNotifications() {
	log.Println("Starting scheduled notification service")

	// Process pending notifications hourly
	ticker := time.NewTicker(1 * time.Hour)
	go func() {
		for range ticker.C {
			log.Println("Processing pending notifications")
			if err := s.ProcessPendingNotifications(); err != nil {
				log.Printf("Error processing pending notifications: %v", err)
			}
		}
	}()

	// Check for contracts approaching deadline and expired contract confirmations once a day
	expiryTicker := time.NewTicker(24 * time.Hour)
	go func() {
		// Run immediately on startup, then once every 24 hours
		log.Println("Initial check for contracts approaching deadline")
		if err := s.CheckContractsApproachingDeadline(); err != nil {
			log.Printf("Error checking contracts approaching deadline: %v", err)
		}

		log.Println("Initial check for expired contract confirmations")
		if err := s.CheckExpiredContractConfirmations(); err != nil {
			log.Printf("Error checking expired contract confirmations: %v", err)
		}

		// Then run on the ticker schedule
		for range expiryTicker.C {
			log.Println("Checking for contracts approaching deadline")
			if err := s.CheckContractsApproachingDeadline(); err != nil {
				log.Printf("Error checking contracts approaching deadline: %v", err)
			}

			log.Println("Checking for expired contract confirmations")
			if err := s.CheckExpiredContractConfirmations(); err != nil {
				log.Printf("Error checking expired contract confirmations: %v", err)
			}
		}
	}()

	log.Println("Scheduled notification service started")
}

// CheckAddressForNewUser checks if a newly registered or logged-in user has profile or address info
func (s *EmailService) CheckAddressForNewUser(userID int64) error {
	log.Printf("Checking profile and address for new user %d", userID)
	return s.SendSmartProfileCompletionNotification(userID)
}

// sendEmail sends an email using SMTP with TLS support
func (s *EmailService) sendEmail(to, subject, body string) error {
	// Use mutex to prevent concurrent access to SMTP connection
	s.mu.Lock()
	defer s.mu.Unlock()

	if to == "" {
		log.Printf("ERROR: Cannot send email - recipient email address is empty")
		return fmt.Errorf("recipient email address is empty")
	}

	// Check SMTP configuration
	if s.config.Host == "" || s.config.Port == "" || s.config.Username == "" || s.config.Password == "" || s.config.From == "" {
		log.Printf("ERROR: SMTP configuration is incomplete. Host: %s, Port: %s, Username: %s, From: %s",
			s.config.Host, s.config.Port, s.config.Username, s.config.From)
		return fmt.Errorf("SMTP configuration is incomplete")
	}

	// Prepare email headers
	headers := make(map[string]string)
	headers["From"] = fmt.Sprintf("%s <%s>", s.config.FromName, s.config.From)
	headers["To"] = to
	headers["Subject"] = subject
	headers["MIME-Version"] = "1.0"
	headers["Content-Type"] = "text/html; charset=UTF-8"

	// Construct message
	var message bytes.Buffer
	for k, v := range headers {
		message.WriteString(fmt.Sprintf("%s: %s\r\n", k, v))
	}
	message.WriteString("\r\n")
	message.WriteString(body)

	// Log email attempt
	log.Printf("Attempting to send email to %s via %s:%s with username %s",
		to, s.config.Host, s.config.Port, s.config.Username)

	// Try sending with TLS first
	log.Printf("Trying to send email with TLS to %s", to)
	err := s.sendWithTLS(to, message.Bytes())
	if err != nil {
		// If TLS fails, try without TLS
		log.Printf("TLS email sending failed: %v. Trying without TLS...", err)
		err = s.sendWithoutTLS(to, message.Bytes())
		if err != nil {
			log.Printf("ERROR: Failed to send email (both with and without TLS): %v", err)
			return fmt.Errorf("failed to send email (both with and without TLS): %w", err)
		}
	}

	log.Printf("Email sent successfully to %s", to)
	return nil
}

// sendWithTLS sends an email using TLS
func (s *EmailService) sendWithTLS(to string, message []byte) error {
	// Create TLS config
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		ServerName:         s.config.Host,
	}

	// Connect to the server
	client, err := smtp.Dial(fmt.Sprintf("%s:%s", s.config.Host, s.config.Port))
	if err != nil {
		return fmt.Errorf("SMTP dial error: %w", err)
	}
	defer client.Close()

	// Start TLS
	if err = client.StartTLS(tlsConfig); err != nil {
		return fmt.Errorf("StartTLS error: %w", err)
	}

	// Auth
	if s.config.Username != "" && s.config.Password != "" {
		auth := smtp.PlainAuth("", s.config.Username, s.config.Password, s.config.Host)
		if err = client.Auth(auth); err != nil {
			return fmt.Errorf("SMTP authentication error: %w", err)
		}
	}

	// Set the sender and recipient
	if err = client.Mail(s.config.From); err != nil {
		return fmt.Errorf("failed to set sender: %w", err)
	}
	if err = client.Rcpt(to); err != nil {
		return fmt.Errorf("failed to set recipient: %w", err)
	}

	// Send the email body
	writer, err := client.Data()
	if err != nil {
		return fmt.Errorf("failed to open data connection: %w", err)
	}

	_, err = writer.Write(message)
	if err != nil {
		writer.Close()
		return fmt.Errorf("failed to write email data: %w", err)
	}

	err = writer.Close()
	if err != nil {
		return fmt.Errorf("failed to close data connection: %w", err)
	}

	// Send the quit command
	client.Quit()
	return nil
}

// sendWithoutTLS sends an email without TLS
func (s *EmailService) sendWithoutTLS(to string, message []byte) error {
	return smtp.SendMail(
		fmt.Sprintf("%s:%s", s.config.Host, s.config.Port),
		smtp.PlainAuth("", s.config.Username, s.config.Password, s.config.Host),
		s.config.From,
		[]string{to},
		message,
	)
}

// ProcessPendingNotifications processes all pending notifications
func (s *EmailService) ProcessPendingNotifications() error {
	// Use mutex to synchronize access to the database
	s.mu.Lock()
	// Get all pending notifications
	notifications, err := s.repo.GetPendingNotifications()
	s.mu.Unlock()

	if err != nil {
		return fmt.Errorf("failed to get pending notifications: %w", err)
	}

	log.Printf("Processing %d pending notifications", len(notifications))

	// Use a worker pool pattern for better concurrency control
	const maxWorkers = 5
	semaphore := make(chan struct{}, maxWorkers)
	var wg sync.WaitGroup

	for _, notification := range notifications {
		wg.Add(1)
		// Process each notification in a separate goroutine
		go func(n *Notification) {
			defer wg.Done()

			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// Check notification type and process accordingly
			switch n.Type {
			case string(NotificationTypeEmail),
				string(NotificationTypePasswordReset),
				string(NotificationTypePasswordResetSuccess),
				string(NotificationTypeRegistration),
				string(NotificationTypeProfileAndAddress),
				string(NotificationTypeOrderSubmission),
				string(NotificationTypeInvoicePayment):
				err := s.sendEmail(n.Email, n.Subject, n.Body)
				if err != nil {
					log.Printf("Failed to send notification ID %d: %v", n.ID, err)
					// Use mutex to synchronize database access
					s.mu.Lock()
					s.repo.MarkNotificationAsFailed(n.ID, err.Error())
					s.mu.Unlock()
					return
				}
				// Use mutex to synchronize database access
				s.mu.Lock()
				err = s.repo.MarkNotificationAsSent(n.ID)
				s.mu.Unlock()
				if err != nil {
					log.Printf("Failed to mark notification ID %d as sent: %v", n.ID, err)
				}
			case string(NotificationTypeSMS):
				log.Printf("SMS notifications not implemented yet")
				// Use mutex to synchronize database access
				s.mu.Lock()
				s.repo.MarkNotificationAsFailed(n.ID, "SMS notifications not implemented")
				s.mu.Unlock()
			default:
				log.Printf("Unknown notification type: %s", n.Type)
				// Use mutex to synchronize database access
				s.mu.Lock()
				s.repo.MarkNotificationAsFailed(n.ID, "Unknown notification type")
				s.mu.Unlock()
			}
		}(notification)
	}

	// Wait for all notifications to be processed
	wg.Wait()
	return nil
}

// Helper method to get or create a notification type
func (s *EmailService) getOrCreateNotificationType(typeName string) (*NotificationTypeInfo, error) {
	// Use mutex to synchronize access to the database
	s.mu.Lock()
	defer s.mu.Unlock()

	// First try to get the notification type
	notificationType, err := s.repo.GetNotificationTypeByName(typeName)
	if err != nil {
		return nil, fmt.Errorf("failed to get notification type %s: %w", typeName, err)
	}

	// If it doesn't exist, create it
	if notificationType == nil {
		notificationType, err = s.repo.CreateNotificationType(typeName)
		if err != nil {
			return nil, fmt.Errorf("failed to create notification type %s: %w", typeName, err)
		}
		log.Printf("Created notification type %s with ID %d", typeName, notificationType.ID)
	}

	return notificationType, nil
}

// GetProfileCompletionStats returns statistics about profile completion across users
func (s *EmailService) GetProfileCompletionStats() (map[string]interface{}, error) {
	// Use mutex to synchronize access to the database
	s.mu.Lock()
	users, err := s.repo.GetActiveUsers()
	s.mu.Unlock()

	if err != nil {
		return nil, fmt.Errorf("failed to get active users: %w", err)
	}

	totalUsers := len(users)
	completeProfiles := 0
	missingBasicInfo := 0
	missingAddressInfo := 0
	missingBoth := 0

	// Common missing fields counters
	missingFieldsCount := make(map[string]int)

	for _, user := range users {
		// Use mutex to synchronize access to the database
		s.mu.Lock()
		completeness, err := s.CheckProfileCompleteness(user.ID)
		s.mu.Unlock()

		if err != nil {
			log.Printf("Error checking profile completeness for user %d: %v", user.ID, err)
			continue
		}

		if completeness.HasBasicInfo && completeness.HasAddressInfo {
			completeProfiles++
		} else if !completeness.HasBasicInfo && !completeness.HasAddressInfo {
			missingBoth++
		} else if !completeness.HasBasicInfo {
			missingBasicInfo++
		} else if !completeness.HasAddressInfo {
			missingAddressInfo++
		}

		// Count missing fields
		for _, field := range completeness.MissingBasicFields {
			missingFieldsCount[field]++
		}
		for _, field := range completeness.MissingAddressFields {
			missingFieldsCount[field]++
		}
	}

	// Calculate percentages
	var completeProfilesPercent, missingBasicInfoPercent, missingAddressInfoPercent, missingBothPercent float64

	if totalUsers > 0 {
		completeProfilesPercent = float64(completeProfiles) / float64(totalUsers) * 100
		missingBasicInfoPercent = float64(missingBasicInfo) / float64(totalUsers) * 100
		missingAddressInfoPercent = float64(missingAddressInfo) / float64(totalUsers) * 100
		missingBothPercent = float64(missingBoth) / float64(totalUsers) * 100
	}

	// Find top 5 missing fields
	type fieldCount struct {
		Field string
		Count int
	}

	var missingFieldsList []fieldCount
	for field, count := range missingFieldsCount {
		missingFieldsList = append(missingFieldsList, fieldCount{Field: field, Count: count})
	}

	// Sort by count in descending order
	sort.Slice(missingFieldsList, func(i, j int) bool {
		return missingFieldsList[i].Count > missingFieldsList[j].Count
	})

	// Get top 5 or fewer if less than 5 exist
	topMissingFields := make([]map[string]interface{}, 0)
	for i, fc := range missingFieldsList {
		if i >= 5 {
			break
		}

		percent := float64(fc.Count) / float64(totalUsers) * 100
		topMissingFields = append(topMissingFields, map[string]interface{}{
			"field":   fc.Field,
			"count":   fc.Count,
			"percent": percent,
		})
	}

	// Return statistics
	return map[string]interface{}{
		"totalUsers": totalUsers,
		"completeProfiles": map[string]interface{}{
			"count":   completeProfiles,
			"percent": completeProfilesPercent,
		},
		"missingBasicInfo": map[string]interface{}{
			"count":   missingBasicInfo,
			"percent": missingBasicInfoPercent,
		},
		"missingAddressInfo": map[string]interface{}{
			"count":   missingAddressInfo,
			"percent": missingAddressInfoPercent,
		},
		"missingBoth": map[string]interface{}{
			"count":   missingBoth,
			"percent": missingBothPercent,
		},
		"topMissingFields": topMissingFields,
	}, nil
}

// ScheduleProfileCompletionChecks schedules periodic checks for all users with incomplete profiles
func (s *EmailService) ScheduleProfileCompletionChecks() {
	log.Println("Starting scheduled profile and address completion checks")
	go func() {
		// Wait for 2 minutes after startup before first check
		time.Sleep(2 * time.Minute)

		// Create a ticker that triggers every 5 days
		ticker := time.NewTicker(5 * 24 * time.Hour)
		defer ticker.Stop()

		// Function to run the profile completion check
		checkProfiles := func() {
			log.Println("Running scheduled profile and address completion check")
			if err := s.ScheduleProfileCompletionReminders(); err != nil {
				log.Printf("Error scheduling profile completion reminders: %v", err)
			}
			log.Println("Scheduled profile and address completion check completed")
		}

		// Run once immediately after the initial delay
		checkProfiles()

		// Then run on the ticker schedule
		for range ticker.C {
			checkProfiles()
		}
	}()
}

// determineUserLanguage tries to determine the user's language preference
// First checks if the language is directly passed to the notification function
// If not, falls back to checking previous notifications
func determineUserLanguage(db *sql.DB, userID int64) string {
	// Default language
	language := "en"

	// First, check the most recent login language from the sessions table
	sessionQuery := `
		SELECT data
		FROM sessions
		WHERE user_id = $1
		ORDER BY last_activity DESC
		LIMIT 1
	`
	var sessionData sql.NullString
	err := db.QueryRow(sessionQuery, userID).Scan(&sessionData)
	if err == nil && sessionData.Valid {
		// Check if the session data contains language information
		if strings.Contains(sessionData.String, "lang=th") {
			language = "th"
			log.Printf("Using language from recent session for user %d: %s", userID, language)
			return language
		} else if strings.Contains(sessionData.String, "lang=en") {
			language = "en"
			log.Printf("Using language from recent session for user %d: %s", userID, language)
			return language
		}
	}

	// Check recent order submission notifications (most reliable)
	recentQuery := `
		SELECT n.subject, n.body
		FROM notifications n
		JOIN notification_types nt ON n.notification_type_id = nt.id
		WHERE n.user_id = $1
		AND nt.name = 'order_submission'
		ORDER BY n.created_at DESC
		LIMIT 1
	`

	var recentSubject, recentBody sql.NullString
	err = db.QueryRow(recentQuery, userID).Scan(&recentSubject, &recentBody)
	if err == nil {
		// Check the content for language indicators
		if recentSubject.Valid && strings.Contains(recentSubject.String, "ยืนยันการส่งคำสั่ง") {
			language = "th"
			log.Printf("Using recent order submission language for user %d: %s", userID, language)
			return language
		} else if recentSubject.Valid && strings.Contains(recentSubject.String, "Order Submission") {
			language = "en"
			log.Printf("Using recent order submission language for user %d: %s", userID, language)
			return language
		}
	}

	// If we couldn't determine from recent order submission, check all recent notifications
	query := `
		SELECT n.subject, n.body
		FROM notifications n
		WHERE n.user_id = $1
		ORDER BY n.created_at DESC
		LIMIT 10
	`

	rows, err := db.Query(query, userID)
	if err != nil {
		log.Printf("Error querying notifications for user %d: %v", userID, err)
		return language
	}
	defer rows.Close()

	// Check subjects and bodies for language indicators
	thaiCount := 0
	englishCount := 0

	for rows.Next() {
		var subject, body sql.NullString
		if err := rows.Scan(&subject, &body); err != nil {
			continue
		}

		// Check for Thai language indicators in the subject or body
		if subject.Valid && (strings.Contains(subject.String, "ยินดีต้อนรับ") ||
			strings.Contains(subject.String, "รีเซ็ตรหัสผ่าน") ||
			strings.Contains(subject.String, "ยืนยันการส่งคำสั่ง") ||
			strings.Contains(subject.String, "กรุณากรอกโปรไฟล์") ||
			strings.Contains(subject.String, "การอัปเดตสถานะสัญญา") ||
			strings.Contains(subject.String, "พบการจับคู่")) {
			thaiCount++
		} else if subject.Valid && (strings.Contains(subject.String, "Welcome") ||
			strings.Contains(subject.String, "Password Reset") ||
			strings.Contains(subject.String, "Order Submission") ||
			strings.Contains(subject.String, "Order Match") ||
			strings.Contains(subject.String, "Contract")) {
			englishCount++
		}

		if body.Valid && (strings.Contains(body.String, "ยินดีต้อนรับ") ||
			strings.Contains(body.String, "รีเซ็ตรหัสผ่าน") ||
			strings.Contains(body.String, "ยืนยันการส่งคำสั่ง") ||
			strings.Contains(body.String, "กรุณากรอกโปรไฟล์") ||
			strings.Contains(body.String, "การอัปเดตสถานะสัญญา") ||
			strings.Contains(body.String, "พบการจับคู่")) {
			thaiCount++
		} else if body.Valid && (strings.Contains(body.String, "Welcome") ||
			strings.Contains(body.String, "Password Reset") ||
			strings.Contains(body.String, "Order Submission") ||
			strings.Contains(body.String, "Order Match") ||
			strings.Contains(body.String, "Contract")) {
			englishCount++
		}
	}

	// Determine language based on which has more occurrences
	if thaiCount > englishCount {
		language = "th"
		log.Printf("Detected Thai language from notification history for user %d (Thai: %d, English: %d)",
			userID, thaiCount, englishCount)
	} else if englishCount > 0 {
		language = "en"
		log.Printf("Detected English language from notification history for user %d (Thai: %d, English: %d)",
			userID, thaiCount, englishCount)
	}

	log.Printf("Final determined language for user %d: %s", userID, language)
	return language
}

// CheckContractsApproachingDeadline checks for contracts that are on their final day (day 3)
// before the confirmation deadline and sends reminder notifications
func (s *EmailService) CheckContractsApproachingDeadline() error {
	log.Println("Checking for contracts approaching deadline")

	// Get database connection
	db := database.GetDB()

	// Find contracts that are pending confirmation and were created exactly 2 days ago
	// (which means today is the 3rd and final day before expiration)
	// Also ensure we haven't already sent a reminder notification for this contract today
	query := `
		SELECT
			c.id,
			c.seller_confirmation_status_id,
			c.buyer_confirmation_status_id,
			seller_user.id AS seller_id,
			buyer_user.id AS buyer_id,
			seller_user.email AS seller_email,
			seller_user.first_name AS seller_first_name,
			seller_user.last_name AS seller_last_name,
			seller_user.username AS seller_username,
			seller_user.phone AS seller_phone,
			seller_user.organization_name AS seller_organization_name,
			buyer_user.email AS buyer_email,
			buyer_user.first_name AS buyer_first_name,
			buyer_user.last_name AS buyer_last_name,
			buyer_user.username AS buyer_username,
			buyer_user.phone AS buyer_phone,
			buyer_user.organization_name AS buyer_organization_name,
			p.id AS product_id,
			p.en_name AS product_en_name,
			p.th_name AS product_th_name,
			c.price,
			c.quantity,
			c.currency_id,
			c.uom_id,
			c.delivery_term_id
		FROM contracts c
		JOIN matchings seller_match ON c.seller_matching_id = seller_match.id
		JOIN matchings buyer_match ON c.buyer_matching_id = buyer_match.id
		JOIN users seller_user ON seller_match.user_id = seller_user.id
		JOIN users buyer_user ON buyer_match.user_id = buyer_user.id
		JOIN products p ON c.product_id = p.id
		WHERE c.contract_status_id = 19 -- Pending confirmation status
		AND (c.seller_confirmation_status_id != 20 OR c.buyer_confirmation_status_id != 20) -- At least one party hasn't confirmed
		AND DATE(c.contract_date) = CURRENT_DATE - INTERVAL '2 days' -- Created exactly 2 days ago (today is day 3)
		-- Ensure we haven't already sent a reminder notification for this contract today
		AND NOT EXISTS (
			SELECT 1 FROM notifications n
			JOIN notification_types nt ON n.notification_type_id = nt.id
			WHERE (nt.name = 'match_expiry_reminder' OR nt.name = 'match_expiry_reminder_confirmed')
			AND n.subject LIKE '%' || c.id || '%' -- Check if contract ID is in the subject
			AND DATE(n.created_at) = CURRENT_DATE -- Only check today's notifications
		)
	`

	rows, err := db.Query(query)
	if err != nil {
		return fmt.Errorf("failed to query contracts approaching deadline: %w", err)
	}
	defer rows.Close()

	var approachingDeadlineContracts []struct {
		ContractID           int64
		SellerConfirmationID int
		BuyerConfirmationID  int
		SellerID             int64
		BuyerID              int64
		SellerEmail          string
		SellerFirstName      string
		SellerLastName       string
		SellerUsername       string
		SellerPhone          sql.NullString
		SellerOrganization   string
		SellerLanguage       string
		BuyerEmail           string
		BuyerFirstName       string
		BuyerLastName        string
		BuyerUsername        string
		BuyerPhone           sql.NullString
		BuyerOrganization    string
		BuyerLanguage        string
		ProductID            int
		ProductEnName        string
		ProductThName        string
		Price                float64
		Quantity             float64
		CurrencyID           int
		UOMID                int
		DeliveryTermID       int
	}

	for rows.Next() {
		var contract struct {
			ContractID           int64
			SellerConfirmationID int
			BuyerConfirmationID  int
			SellerID             int64
			BuyerID              int64
			SellerEmail          string
			SellerFirstName      string
			SellerLastName       string
			SellerUsername       string
			SellerPhone          sql.NullString
			SellerOrganization   string
			SellerLanguage       string
			BuyerEmail           string
			BuyerFirstName       string
			BuyerLastName        string
			BuyerUsername        string
			BuyerPhone           sql.NullString
			BuyerOrganization    string
			BuyerLanguage        string
			ProductID            int
			ProductEnName        string
			ProductThName        string
			Price                float64
			Quantity             float64
			CurrencyID           int
			UOMID                int
			DeliveryTermID       int
		}

		err := rows.Scan(
			&contract.ContractID,
			&contract.SellerConfirmationID,
			&contract.BuyerConfirmationID,
			&contract.SellerID,
			&contract.BuyerID,
			&contract.SellerEmail,
			&contract.SellerFirstName,
			&contract.SellerLastName,
			&contract.SellerUsername,
			&contract.SellerPhone,
			&contract.SellerOrganization,
			&contract.BuyerEmail,
			&contract.BuyerFirstName,
			&contract.BuyerLastName,
			&contract.BuyerUsername,
			&contract.BuyerPhone,
			&contract.BuyerOrganization,
			&contract.ProductID,
			&contract.ProductEnName,
			&contract.ProductThName,
			&contract.Price,
			&contract.Quantity,
			&contract.CurrencyID,
			&contract.UOMID,
			&contract.DeliveryTermID,
		)

		if err != nil {
			log.Printf("Error scanning contract row: %v", err)
			continue
		}

		// Determine language preferences
		contract.SellerLanguage = determineUserLanguage(db, contract.SellerID)
		contract.BuyerLanguage = determineUserLanguage(db, contract.BuyerID)

		approachingDeadlineContracts = append(approachingDeadlineContracts, contract)
	}

	log.Printf("Found %d contracts approaching deadline", len(approachingDeadlineContracts))

	// Process each contract approaching deadline
	for _, contract := range approachingDeadlineContracts {
		log.Printf("Processing contract approaching deadline ID: %d", contract.ContractID)

		// Get additional data needed for notifications
		var currencyCode, uomEnName, uomThName, deliveryTermEnName, deliveryTermThName string

		// Get currency code
		err := db.QueryRow("SELECT code FROM currencies WHERE id = $1", contract.CurrencyID).Scan(&currencyCode)
		if err != nil {
			log.Printf("Error getting currency code: %v", err)
			currencyCode = "THB" // Default
		}

		// Get UOM name
		err = db.QueryRow("SELECT en_name, th_name FROM uoms WHERE id = $1", contract.UOMID).Scan(&uomEnName, &uomThName)
		if err != nil {
			log.Printf("Error getting UOM name: %v", err)
			uomEnName = "Unit"  // Default
			uomThName = "หน่วย" // Default
		}

		// Get delivery term name
		err = db.QueryRow("SELECT en_name, th_name FROM delivery_terms WHERE id = $1", contract.DeliveryTermID).Scan(&deliveryTermEnName, &deliveryTermThName)
		if err != nil {
			log.Printf("Error getting delivery term name: %v", err)
			deliveryTermEnName = "Standard Delivery" // Default
			deliveryTermThName = "การจัดส่งมาตรฐาน"  // Default
		}

		// Send appropriate notifications based on confirmation status

		// 1. If seller hasn't confirmed, send reminder to seller
		if contract.SellerConfirmationID != 20 {
			// Create notification data for unconfirmed seller
			// Prepare language-specific values for seller
			var sellerProductName, sellerUnitOfMeasure, sellerDeliveryTerms string
			if contract.SellerLanguage == "th" {
				sellerProductName = contract.ProductThName
				sellerUnitOfMeasure = uomThName
				sellerDeliveryTerms = deliveryTermThName
			} else {
				sellerProductName = contract.ProductEnName
				sellerUnitOfMeasure = uomEnName
				sellerDeliveryTerms = deliveryTermEnName
			}

			sellerNotification := &ContractStatusUpdateNotification{
				UserID:              contract.SellerID,
				Email:               contract.SellerEmail,
				FirstName:           contract.SellerFirstName,
				LastName:            contract.SellerLastName,
				Username:            contract.SellerUsername,
				ContractID:          contract.ContractID,
				Role:                "seller",
				CounterpartyName:    fmt.Sprintf("%s %s", contract.BuyerFirstName, contract.BuyerLastName),
				CounterpartyEmail:   contract.BuyerEmail,
				CounterpartyCompany: contract.BuyerOrganization,
				ProductName:         sellerProductName,
				Quantity:            contract.Quantity,
				UnitOfMeasure:       sellerUnitOfMeasure,
				Price:               contract.Price,
				Currency:            currencyCode,
				DeliveryTerms:       sellerDeliveryTerms,
				ActionTaken:         "deadline",
				IsActionTaker:       false,
				ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), contract.ContractID, contract.SellerLanguage),
				Language:            contract.SellerLanguage,
				BaseURL:             os.Getenv("BASE_URL"),
			}

			// Send deadline reminder to unconfirmed seller
			err = s.SendContractDeadlineReminderNotification(sellerNotification)
			if err != nil {
				log.Printf("Error sending deadline reminder to seller: %v", err)
			} else {
				log.Printf("Successfully sent deadline reminder to seller for contract ID: %d", contract.ContractID)
			}
		}

		// 2. If buyer hasn't confirmed, send reminder to buyer
		if contract.BuyerConfirmationID != 20 {
			// Create notification data for unconfirmed buyer
			// Prepare language-specific values for buyer
			var buyerProductName, buyerUnitOfMeasure, buyerDeliveryTerms string
			if contract.BuyerLanguage == "th" {
				buyerProductName = contract.ProductThName
				buyerUnitOfMeasure = uomThName
				buyerDeliveryTerms = deliveryTermThName
			} else {
				buyerProductName = contract.ProductEnName
				buyerUnitOfMeasure = uomEnName
				buyerDeliveryTerms = deliveryTermEnName
			}

			buyerNotification := &ContractStatusUpdateNotification{
				UserID:              contract.BuyerID,
				Email:               contract.BuyerEmail,
				FirstName:           contract.BuyerFirstName,
				LastName:            contract.BuyerLastName,
				Username:            contract.BuyerUsername,
				ContractID:          contract.ContractID,
				Role:                "buyer",
				CounterpartyName:    fmt.Sprintf("%s %s", contract.SellerFirstName, contract.SellerLastName),
				CounterpartyEmail:   contract.SellerEmail,
				CounterpartyCompany: contract.SellerOrganization,
				ProductName:         buyerProductName,
				Quantity:            contract.Quantity,
				UnitOfMeasure:       buyerUnitOfMeasure,
				Price:               contract.Price,
				Currency:            currencyCode,
				DeliveryTerms:       buyerDeliveryTerms,
				ActionTaken:         "deadline",
				IsActionTaker:       false,
				ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), contract.ContractID, contract.BuyerLanguage),
				Language:            contract.BuyerLanguage,
				BaseURL:             os.Getenv("BASE_URL"),
			}

			// Send deadline reminder to unconfirmed buyer
			err = s.SendContractDeadlineReminderNotification(buyerNotification)
			if err != nil {
				log.Printf("Error sending deadline reminder to buyer: %v", err)
			} else {
				log.Printf("Successfully sent deadline reminder to buyer for contract ID: %d", contract.ContractID)
			}
		}

		// 3. If seller has confirmed but buyer hasn't, send notification to seller about buyer's pending confirmation
		if contract.SellerConfirmationID == 20 && contract.BuyerConfirmationID != 20 {
			// Create notification data for confirmed seller
			// Prepare language-specific values for seller
			var sellerProductName, sellerUnitOfMeasure, sellerDeliveryTerms string
			if contract.SellerLanguage == "th" {
				sellerProductName = contract.ProductThName
				sellerUnitOfMeasure = uomThName
				sellerDeliveryTerms = deliveryTermThName
			} else {
				sellerProductName = contract.ProductEnName
				sellerUnitOfMeasure = uomEnName
				sellerDeliveryTerms = deliveryTermEnName
			}

			sellerNotification := &ContractStatusUpdateNotification{
				UserID:              contract.SellerID,
				Email:               contract.SellerEmail,
				FirstName:           contract.SellerFirstName,
				LastName:            contract.SellerLastName,
				Username:            contract.SellerUsername,
				ContractID:          contract.ContractID,
				Role:                "seller",
				CounterpartyName:    fmt.Sprintf("%s %s", contract.BuyerFirstName, contract.BuyerLastName),
				CounterpartyEmail:   contract.BuyerEmail,
				CounterpartyCompany: contract.BuyerOrganization,
				ProductName:         sellerProductName,
				Quantity:            contract.Quantity,
				UnitOfMeasure:       sellerUnitOfMeasure,
				Price:               contract.Price,
				Currency:            currencyCode,
				DeliveryTerms:       sellerDeliveryTerms,
				ActionTaken:         "waiting",
				IsActionTaker:       true,
				ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), contract.ContractID, contract.SellerLanguage),
				Language:            contract.SellerLanguage,
				BaseURL:             os.Getenv("BASE_URL"),
			}

			// Send waiting notification to confirmed seller
			err = s.SendContractWaitingNotification(sellerNotification)
			if err != nil {
				log.Printf("Error sending waiting notification to seller: %v", err)
			} else {
				log.Printf("Successfully sent waiting notification to seller for contract ID: %d", contract.ContractID)
			}
		}

		// 4. If buyer has confirmed but seller hasn't, send notification to buyer about seller's pending confirmation
		if contract.BuyerConfirmationID == 20 && contract.SellerConfirmationID != 20 {
			// Create notification data for confirmed buyer
			// Prepare language-specific values for buyer
			var buyerProductName, buyerUnitOfMeasure, buyerDeliveryTerms string
			if contract.BuyerLanguage == "th" {
				buyerProductName = contract.ProductThName
				buyerUnitOfMeasure = uomThName
				buyerDeliveryTerms = deliveryTermThName
			} else {
				buyerProductName = contract.ProductEnName
				buyerUnitOfMeasure = uomEnName
				buyerDeliveryTerms = deliveryTermEnName
			}

			buyerNotification := &ContractStatusUpdateNotification{
				UserID:              contract.BuyerID,
				Email:               contract.BuyerEmail,
				FirstName:           contract.BuyerFirstName,
				LastName:            contract.BuyerLastName,
				Username:            contract.BuyerUsername,
				ContractID:          contract.ContractID,
				Role:                "buyer",
				CounterpartyName:    fmt.Sprintf("%s %s", contract.SellerFirstName, contract.SellerLastName),
				CounterpartyEmail:   contract.SellerEmail,
				CounterpartyCompany: contract.SellerOrganization,
				ProductName:         buyerProductName,
				Quantity:            contract.Quantity,
				UnitOfMeasure:       buyerUnitOfMeasure,
				Price:               contract.Price,
				Currency:            currencyCode,
				DeliveryTerms:       buyerDeliveryTerms,
				ActionTaken:         "waiting",
				IsActionTaker:       true,
				ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), contract.ContractID, contract.BuyerLanguage),
				Language:            contract.BuyerLanguage,
				BaseURL:             os.Getenv("BASE_URL"),
			}

			// Send waiting notification to confirmed buyer
			err = s.SendContractWaitingNotification(buyerNotification)
			if err != nil {
				log.Printf("Error sending waiting notification to buyer: %v", err)
			} else {
				log.Printf("Successfully sent waiting notification to buyer for contract ID: %d", contract.ContractID)
			}
		}
	}

	return nil
}

// CheckExpiredContractConfirmations checks for contracts that have passed the 3-day confirmation deadline
// and cancels them if they haven't been confirmed by both parties
func (s *EmailService) CheckExpiredContractConfirmations() error {
	log.Println("Checking for expired contract confirmations")

	// Get database connection
	db := database.GetDB()

	// Find contracts that are pending confirmation and were created more than 3 days ago
	query := `
		SELECT
			c.id,
			c.seller_confirmation_status_id,
			c.buyer_confirmation_status_id,
			seller_user.id AS seller_id,
			buyer_user.id AS buyer_id,
			seller_user.email AS seller_email,
			seller_user.first_name AS seller_first_name,
			seller_user.last_name AS seller_last_name,
			seller_user.username AS seller_username,
			seller_user.organization_name AS seller_organization_name,
			buyer_user.email AS buyer_email,
			buyer_user.first_name AS buyer_first_name,
			buyer_user.last_name AS buyer_last_name,
			buyer_user.username AS buyer_username,
			buyer_user.organization_name AS buyer_organization_name,
			p.id AS product_id,
			p.en_name AS product_en_name,
			p.th_name AS product_th_name
		FROM contracts c
		JOIN matchings seller_match ON c.seller_matching_id = seller_match.id
		JOIN matchings buyer_match ON c.buyer_matching_id = buyer_match.id
		JOIN users seller_user ON seller_match.user_id = seller_user.id
		JOIN users buyer_user ON buyer_match.user_id = buyer_user.id
		JOIN products p ON c.product_id = p.id
		WHERE c.contract_status_id = 19 -- Pending confirmation status
		AND (c.seller_confirmation_status_id != 20 OR c.buyer_confirmation_status_id != 20) -- At least one party hasn't confirmed
		AND c.contract_date < NOW() - INTERVAL '3 days' -- Created more than 3 days ago
	`

	rows, err := db.Query(query)
	if err != nil {
		return fmt.Errorf("failed to query expired contracts: %w", err)
	}
	defer rows.Close()

	var expiredContracts []struct {
		ContractID           int64
		SellerConfirmationID int
		BuyerConfirmationID  int
		SellerID             int64
		BuyerID              int64
		SellerEmail          string
		SellerFirstName      string
		SellerLastName       string
		SellerUsername       string
		SellerOrganization   string
		SellerLanguage       string
		BuyerEmail           string
		BuyerFirstName       string
		BuyerLastName        string
		BuyerUsername        string
		BuyerOrganization    string
		BuyerLanguage        string
		ProductID            int
		ProductEnName        string
		ProductThName        string
	}

	for rows.Next() {
		var contract struct {
			ContractID           int64
			SellerConfirmationID int
			BuyerConfirmationID  int
			SellerID             int64
			BuyerID              int64
			SellerEmail          string
			SellerFirstName      string
			SellerLastName       string
			SellerUsername       string
			SellerOrganization   string
			SellerLanguage       string
			BuyerEmail           string
			BuyerFirstName       string
			BuyerLastName        string
			BuyerUsername        string
			BuyerOrganization    string
			BuyerLanguage        string
			ProductID            int
			ProductEnName        string
			ProductThName        string
		}

		err := rows.Scan(
			&contract.ContractID,
			&contract.SellerConfirmationID,
			&contract.BuyerConfirmationID,
			&contract.SellerID,
			&contract.BuyerID,
			&contract.SellerEmail,
			&contract.SellerFirstName,
			&contract.SellerLastName,
			&contract.SellerUsername,
			&contract.SellerOrganization,
			&contract.BuyerEmail,
			&contract.BuyerFirstName,
			&contract.BuyerLastName,
			&contract.BuyerUsername,
			&contract.BuyerOrganization,
			&contract.ProductID,
			&contract.ProductEnName,
			&contract.ProductThName,
		)

		// Determine language preferences
		contract.SellerLanguage = determineUserLanguage(db, contract.SellerID)
		contract.BuyerLanguage = determineUserLanguage(db, contract.BuyerID)

		if err != nil {
			log.Printf("Error scanning contract row: %v", err)
			continue
		}

		expiredContracts = append(expiredContracts, contract)
	}

	log.Printf("Found %d expired contract confirmations", len(expiredContracts))

	// Process each expired contract
	for _, contract := range expiredContracts {
		log.Printf("Processing expired contract ID: %d", contract.ContractID)

		// Start a transaction
		tx, err := db.Begin()
		if err != nil {
			log.Printf("Error starting transaction for contract %d: %v", contract.ContractID, err)
			continue
		}

		// Update contract status to canceled (22 = Canceled)
		_, err = tx.Exec("UPDATE contracts SET contract_status_id = 22 WHERE id = $1", contract.ContractID)
		if err != nil {
			tx.Rollback()
			log.Printf("Error updating contract status for contract %d: %v", contract.ContractID, err)
			continue
		}

		// Commit the transaction
		err = tx.Commit()
		if err != nil {
			log.Printf("Error committing transaction for contract %d: %v", contract.ContractID, err)
			continue
		}

		// Send cancellation notifications to both parties
		reason := "The contract was automatically canceled because it was not confirmed by both parties within 3 days."

		// Prepare language-specific values for seller
		var sellerProductName string
		if contract.SellerLanguage == "th" {
			sellerProductName = contract.ProductThName
		} else {
			sellerProductName = contract.ProductEnName
		}

		// Send to seller
		sellerNotification := &ContractCancellationNotification{
			UserID:              contract.SellerID,
			Email:               contract.SellerEmail,
			FirstName:           contract.SellerFirstName,
			LastName:            contract.SellerLastName,
			Username:            contract.SellerUsername,
			ContractID:          contract.ContractID,
			Role:                "seller",
			CounterpartyName:    fmt.Sprintf("%s %s", contract.BuyerFirstName, contract.BuyerLastName),
			CounterpartyCompany: contract.BuyerOrganization,
			ProductName:         sellerProductName,
			CancellationReason:  reason,
			ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), contract.ContractID, contract.SellerLanguage),
			Language:            contract.SellerLanguage,
			BaseURL:             os.Getenv("BASE_URL"),
		}

		err = s.SendContractCancellationNotification(sellerNotification)
		if err != nil {
			log.Printf("Error sending cancellation notification to seller for contract %d: %v", contract.ContractID, err)
		}

		// Prepare language-specific values for buyer
		var buyerProductName string
		if contract.BuyerLanguage == "th" {
			buyerProductName = contract.ProductThName
		} else {
			buyerProductName = contract.ProductEnName
		}

		// Send to buyer
		buyerNotification := &ContractCancellationNotification{
			UserID:              contract.BuyerID,
			Email:               contract.BuyerEmail,
			FirstName:           contract.BuyerFirstName,
			LastName:            contract.BuyerLastName,
			Username:            contract.BuyerUsername,
			ContractID:          contract.ContractID,
			Role:                "buyer",
			CounterpartyName:    fmt.Sprintf("%s %s", contract.SellerFirstName, contract.SellerLastName),
			CounterpartyCompany: contract.SellerOrganization,
			ProductName:         buyerProductName,
			CancellationReason:  reason,
			ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), contract.ContractID, contract.BuyerLanguage),
			Language:            contract.BuyerLanguage,
			BaseURL:             os.Getenv("BASE_URL"),
		}

		err = s.SendContractCancellationNotification(buyerNotification)
		if err != nil {
			log.Printf("Error sending cancellation notification to buyer for contract %d: %v", contract.ContractID, err)
		}

		log.Printf("Successfully processed expired contract ID: %d", contract.ContractID)
	}

	return nil
}

// ensureValidLanguage makes sure we have a valid language value
func ensureValidLanguage(language string) string {
	// Normalize language value
	language = strings.ToLower(strings.TrimSpace(language))

	// Validate language
	if language != "th" {
		// Default to English for anything that's not explicitly Thai
		return "en"
	}

	return language
}

// sendNotificationAsync sends a notification asynchronously with proper language handling
// Returns a channel that will be closed when the notification is sent
func (s *EmailService) sendNotificationAsync(notification *Notification, language string) chan bool {
	// Ensure valid language
	language = ensureValidLanguage(language)

	// Log the start of sending
	log.Printf("Starting async send of notification ID %d in language: %s", notification.ID, language)

	// Create a channel to signal completion
	done := make(chan bool, 1)

	// Use a goroutine to send the email
	go func() {
		// Send the email
		err := s.sendEmail(notification.Email, notification.Subject, notification.Body)

		// Update notification status based on result
		if err != nil {
			log.Printf("Failed to send notification ID %d: %v", notification.ID, err)
			s.repo.MarkNotificationAsFailed(notification.ID, err.Error())
		} else {
			log.Printf("Successfully sent notification ID %d in language: %s", notification.ID, language)
			s.repo.MarkNotificationAsSent(notification.ID)
		}

		// Signal completion
		done <- true
		close(done)
	}()

	return done
}

// waitForNotification waits for a notification to complete with timeout
func waitForNotification(done chan bool, timeout time.Duration) bool {
	select {
	case <-done:
		return true
	case <-time.After(timeout):
		log.Printf("Warning: Timed out waiting for notification to complete")
		return false
	}
}
