package contract

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/thongsoi/biomassx/database"
	"github.com/thongsoi/biomassx/internal/invoice"
	"github.com/thongsoi/biomassx/internal/notification"
)

// containsThaiCharacters checks if a string contains any Thai characters
func containsThaiCharacters(s string) bool {
	// Thai Unicode range: U+0E00 to U+0E7F
	for _, r := range s {
		if r >= 0x0E00 && r <= 0x0E7F {
			return true
		}
	}
	return false
}

// determineUserLanguage tries to determine the user's language preference from previous notifications
func determineUserLanguage(db *sql.DB, userID int64) string {
	// Default language
	language := "en"

	// First, check if we can get the language from the most recent order
	// This is the most reliable way to determine the user's current language preference
	orderQuery := `
		SELECT language
		FROM orders
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT 1
	`

	var orderLanguage sql.NullString
	err := db.QueryRow(orderQuery, userID).Scan(&orderLanguage)
	if err == nil && orderLanguage.Valid && orderLanguage.String != "" {
		language = orderLanguage.String
		log.Printf("Using language from most recent order for user %d: %s", userID, language)
		return language
	}

	// If we couldn't determine from orders, check recent order submission notifications
	recentQuery := `
		SELECT n.subject, n.body
		FROM notifications n
		JOIN notification_types nt ON n.notification_type_id = nt.id
		WHERE n.user_id = $1
		AND nt.name = 'order_submission'
		AND n.created_at > NOW() - INTERVAL '5 minutes'
		ORDER BY n.created_at DESC
		LIMIT 1
	`

	var recentSubject, recentBody string
	err = db.QueryRow(recentQuery, userID).Scan(&recentSubject, &recentBody)
	if err == nil {
		// Check if the recent notification is in Thai by looking for Thai characters
		// or specific Thai phrases in both subject and body
		if strings.Contains(recentSubject, "ยืนยันการส่งคำสั่ง") ||
			strings.Contains(recentSubject, "ส่งคำสั่ง") ||
			strings.Contains(recentSubject, "การยืนยัน") ||
			strings.Contains(recentBody, "ยืนยันการส่งคำสั่ง") ||
			strings.Contains(recentBody, "ส่งคำสั่ง") ||
			strings.Contains(recentBody, "การยืนยัน") ||
			// Check for any Thai character in the subject or body
			containsThaiCharacters(recentSubject) ||
			containsThaiCharacters(recentBody) {
			language = "th"
			log.Printf("Using recent order submission language for user %d: %s", userID, language)
			return language
		} else {
			language = "en"
			log.Printf("Using recent order submission language for user %d: %s", userID, language)
			return language
		}
	}

	// If we couldn't determine from recent order submission, check other notifications
	query := `
		SELECT n.subject, n.body
		FROM notifications n
		JOIN notification_types nt ON n.notification_type_id = nt.id
		WHERE n.user_id = $1
		ORDER BY n.created_at DESC
		LIMIT 5
	`

	rows, err := db.Query(query, userID)
	if err != nil {
		log.Printf("Error querying notifications for user %d: %v", userID, err)
		return language
	}
	defer rows.Close()

	// Check subjects and bodies for Thai language indicators
	for rows.Next() {
		var subject, body string
		if err := rows.Scan(&subject, &body); err != nil {
			continue
		}

		// Check for Thai language indicators in the subject or body
		if strings.Contains(subject, "ยินดีต้อนรับ") ||
			strings.Contains(subject, "รีเซ็ตรหัสผ่าน") ||
			strings.Contains(subject, "ยืนยันการส่งคำสั่ง") ||
			strings.Contains(subject, "กรุณากรอกโปรไฟล์") ||
			strings.Contains(subject, "การอัปเดตสถานะสัญญา") ||
			strings.Contains(subject, "พบการจับคู่") ||
			strings.Contains(subject, "ส่งคำสั่ง") ||
			strings.Contains(subject, "การยืนยัน") ||
			strings.Contains(body, "ยินดีต้อนรับ") ||
			strings.Contains(body, "รีเซ็ตรหัสผ่าน") ||
			strings.Contains(body, "ยืนยันการส่งคำสั่ง") ||
			strings.Contains(body, "กรุณากรอกโปรไฟล์") ||
			strings.Contains(body, "การอัปเดตสถานะสัญญา") ||
			strings.Contains(body, "พบการจับคู่") ||
			strings.Contains(body, "ส่งคำสั่ง") ||
			strings.Contains(body, "การยืนยัน") ||
			// Check for any Thai character in the subject or body
			containsThaiCharacters(subject) ||
			containsThaiCharacters(body) {
			language = "th"
			log.Printf("Detected Thai language from notification for user %d", userID)
			break
		}
	}

	log.Printf("Determined language for user %d: %s", userID, language)
	return language
}

func FetchContractData(db *sql.DB) ([]Contract, error) {
	query := `
	SELECT
		c.id,
		c.contract_date,
		seller.user_id AS seller_user_id,
		seller_user.first_name AS seller_first_name,
		seller_user.last_name AS seller_last_name,
		seller_user.organization_name AS seller_org_name,
		buyer.user_id AS buyer_user_id,
		buyer_user.first_name AS buyer_first_name,
		buyer_user.last_name AS buyer_last_name,
		buyer_user.organization_name AS buyer_org_name,
		c.product_id,
		prod.en_name AS product_en_name, prod.th_name AS product_th_name,
		q.standard_id,
		std.en_name AS standard_en_name, std.th_name AS standard_th_name,
		q.grade_id,
		g.en_name AS grade_en_name, g.th_name AS grade_th_name,
		c.price,
		cur.en_name AS currency_en_name, cur.th_name AS currency_th_name,
		c.quantity,
		u.en_name AS uom_en, u.th_name AS uom_th,
		u.description AS uom_description,
		c.packing_id,
		pck.en_name AS packing_en, pck.th_name AS packing_th,
		dt.en_name AS delivery_term_en, dt.th_name AS delivery_term_th,
		p.en_name AS province_en, p.th_name AS province_th,
		d.en_name AS district_en, d.th_name AS district_th,
		sub.en_name AS subdistrict_en, sub.th_name AS subdistrict_th,
		scs_delivery.en_name AS delivery_status_en, scs_delivery.th_name AS delivery_status_th,
		c.start_delivery_date,
		c.finish_delivery_date,
		pt.en_name AS payment_term_en, pt.th_name AS payment_term_th,
		pt.description AS payment_term_description,
		scs_payment.en_name AS payment_status_en, scs_payment.th_name AS payment_status_th,
		c.seller_remark,
		c.buyer_remark,
		seller_status.en_name AS seller_confirmation_status_en, seller_status.th_name AS seller_confirmation_status_th,
		buyer_status.en_name AS buyer_confirmation_status_en, buyer_status.th_name AS buyer_confirmation_status_th,
		contract_status.en_name AS contract_status_en, contract_status.th_name AS contract_status_th,
		seller_pol.en_name AS seller_pol_en, seller_pol.th_name AS seller_pol_th,
		seller_pod.en_name AS seller_pod_en, seller_pod.th_name AS seller_pod_th,
		buyer_pol.en_name AS buyer_pol_en, buyer_pol.th_name AS buyer_pol_th,
		buyer_pod.en_name AS buyer_pod_en, buyer_pod.th_name AS buyer_pod_th,
		seller_country.en_name AS seller_country_en, seller_country.th_name AS seller_country_th,
		buyer_country.en_name AS buyer_country_en, buyer_country.th_name AS buyer_country_th,
		ct.en_name AS contract_type_en_name,
		ct.th_name AS contract_type_th_name,
		marketspace.en_name AS marketspace_en_name, marketspace.th_name AS marketspace_th_name,  -- marketspace info
		market.en_name AS market_en_name, market.th_name AS market_th_name,  -- market info
		submarket.en_name AS submarket_en_name, submarket.th_name AS submarket_th_name  -- submarket info
	FROM contracts c
	LEFT JOIN matchings seller ON c.seller_matching_id = seller.id
	LEFT JOIN users seller_user ON seller.user_id = seller_user.id
	LEFT JOIN matchings buyer ON c.buyer_matching_id = buyer.id
	LEFT JOIN users buyer_user ON buyer.user_id = buyer_user.id
	LEFT JOIN products prod ON c.product_id = prod.id
	LEFT JOIN qualities q ON c.quality_id = q.id
	LEFT JOIN standards std ON q.standard_id = std.id
	LEFT JOIN grades g ON q.grade_id = g.id
	LEFT JOIN currencies cur ON c.currency_id = cur.id
	LEFT JOIN uoms u ON c.uom_id = u.id
	LEFT JOIN delivery_terms dt ON c.delivery_term_id = dt.id
	LEFT JOIN provinces p ON c.place_of_delivery_province = p.id
	LEFT JOIN districts d ON c.place_of_delivery_district = d.id
	LEFT JOIN subdistricts sub ON c.place_of_delivery_subdistrict = sub.id
	LEFT JOIN packings pck ON c.packing_id = pck.id
	LEFT JOIN payment_terms pt ON c.payment_term_id = pt.id
	LEFT JOIN status_categories_statuses scs_seller ON c.seller_confirmation_status_id = scs_seller.id
	LEFT JOIN statuses seller_status ON scs_seller.status_id = seller_status.id
	LEFT JOIN status_categories_statuses scs_buyer ON c.buyer_confirmation_status_id = scs_buyer.id
	LEFT JOIN statuses buyer_status ON scs_buyer.status_id = buyer_status.id
	LEFT JOIN status_categories_statuses scs_contract ON c.contract_status_id = scs_contract.id
	LEFT JOIN statuses contract_status ON scs_contract.status_id = contract_status.id
	LEFT JOIN status_categories_statuses scs_delivery_status ON c.delivery_status_id = scs_delivery_status.id
	LEFT JOIN statuses scs_delivery ON scs_delivery_status.status_id = scs_delivery.id
	LEFT JOIN status_categories_statuses scs_payment_status ON c.payment_status_id = scs_payment_status.id
	LEFT JOIN statuses scs_payment ON scs_payment_status.status_id = scs_payment.id
	LEFT JOIN ports seller_pol ON c.seller_port_of_loading_id = seller_pol.id
	LEFT JOIN ports seller_pod ON c.seller_port_of_discharge_id = seller_pod.id
	LEFT JOIN ports buyer_pol ON c.buyer_port_of_loading_id = buyer_pol.id
	LEFT JOIN ports buyer_pod ON c.buyer_port_of_discharge_id = buyer_pod.id
	LEFT JOIN countries seller_country ON c.seller_country_id = seller_country.id
	LEFT JOIN countries buyer_country ON c.buyer_country_id = buyer_country.id
	LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
	LEFT JOIN marketspaces marketspace ON c.marketspace_id = marketspace.id  -- เพิ่มการ join กับ marketspaces
	LEFT JOIN markets market ON c.market_id = market.id  -- เพิ่มการ join กับ markets
	LEFT JOIN submarkets submarket ON c.submarket_id = submarket.id  -- เพิ่มการ join กับ submarkets
	WHERE c.contract_status_id IN (19,20,21,22,23,24)
	`

	rows, err := db.Query(query)
	if err != nil {
		log.Println("Error querying contracts:", err)
		return nil, err
	}
	defer rows.Close()

	var contracts []Contract
	for rows.Next() {
		var contract Contract
		err := rows.Scan(
			&contract.ID, &contract.ContractDate,
			&contract.SellerUserID, &contract.SellerFirstName, &contract.SellerLastName, &contract.SellerOrgName,
			&contract.BuyerUserID, &contract.BuyerFirstName, &contract.BuyerLastName, &contract.BuyerOrgName,
			&contract.ProductID, &contract.ProductEnName, &contract.ProductThName,
			&contract.StandardID, &contract.StandardEnName, &contract.StandardThName,
			&contract.GradeID, &contract.GradeEnName, &contract.GradeThName,
			&contract.Price, &contract.CurrencyEnName, &contract.CurrencyThName, &contract.Quantity,
			&contract.UomEn, &contract.UomTh, &contract.UomDescription,
			&contract.PackingID, &contract.PackingEn, &contract.PackingTh,
			&contract.DeliveryTermEn, &contract.DeliveryTermTh,
			&contract.PodProvinceEn, &contract.PodProvinceTh,
			&contract.PodDistrictEn, &contract.PodDistrictTh,
			&contract.PodSubdistrictEn, &contract.PodSubdistrictTh,
			&contract.DeliveryStatusEn, &contract.DeliveryStatusTh,
			&contract.StartDelivery, &contract.FinishDelivery,
			&contract.PaymentTermEn, &contract.PaymentTermTh, &contract.PaymentTermDescription,
			&contract.PaymentStatusEn, &contract.PaymentStatusTh,
			&contract.SellerRemark, &contract.BuyerRemark,
			&contract.SellerConfirmStatusEn, &contract.SellerConfirmStatusTh,
			&contract.BuyerConfirmStatusEn, &contract.BuyerConfirmStatusTh,
			&contract.ContractStatusEn, &contract.ContractStatusTh,
			&contract.SellerPolEn, &contract.SellerPolTh,
			&contract.SellerPodEn, &contract.SellerPodTh,
			&contract.BuyerPolEn, &contract.BuyerPolTh,
			&contract.BuyerPodEn, &contract.BuyerPodTh,
			&contract.SellerCountryEn, &contract.SellerCountryTh,
			&contract.BuyerCountryEn, &contract.BuyerCountryTh,
			&contract.ContractTypeEnName,
			&contract.ContractTypeThName,
			&contract.MarketspaceEnName, &contract.MarketspaceThName, // marketspace
			&contract.MarketEnName, &contract.MarketThName, // market
			&contract.SubmarketEnName, &contract.SubmarketThName, // submarket
		)
		if err != nil {
			log.Println("Error scanning contract:", err)
			return nil, err
		}
		contracts = append(contracts, contract)
	}
	return contracts, nil
}

func FetchContractUserDetails(db *sql.DB, contractID string) (ContractUserDetails, error) {
	query := `
        SELECT
            c.id, c.contract_date,
            seller.first_name, seller.last_name, seller.organization_name, seller.id,
            buyer.first_name, buyer.last_name, buyer.organization_name, buyer.id,
            c.product_id,  -- เพิ่ม product_id
            p.en_name AS product_en_name, p.th_name AS product_th_name,  -- เพิ่มชื่อภาษาอังกฤษและไทยของสินค้า
            q.standard_id, std.en_name, std.th_name,
            q.grade_id, g.en_name, g.th_name,
            c.price, cur.en_name, cur.th_name, c.quantity,
            u.en_name, u.th_name, u.description,
            c.packing_id, pck.en_name, pck.th_name,
            dt.en_name, dt.th_name,
            prov.en_name, prov.th_name,
            d.en_name, d.th_name,
            sub.en_name, sub.th_name,
            scs_delivery.en_name, scs_delivery.th_name,
            c.start_delivery_date, c.finish_delivery_date,
            pt.en_name, pt.th_name, pt.description,
            scs_payment.en_name, scs_payment.th_name,
            c.seller_remark, c.buyer_remark,
            seller_status.en_name, seller_status.th_name,
            buyer_status.en_name, buyer_status.th_name,
            contract_status.en_name, contract_status.th_name,
            seller_pol.en_name, seller_pol.th_name,
            seller_pod.en_name, seller_pod.th_name,
            buyer_pol.en_name, buyer_pol.th_name,
            buyer_pod.en_name, buyer_pod.th_name,
            seller_country.en_name, seller_country.th_name,
            buyer_country.en_name, buyer_country.th_name ,
            ct.en_name AS contract_type_en_name,   -- เพิ่มข้อมูลของ contract type ภาษาอังกฤษ
            ct.th_name AS contract_type_th_name,    -- เพิ่มข้อมูลของ contract type ภาษาไทย
            marketspace.en_name AS marketspace_en_name, marketspace.th_name AS marketspace_th_name,  -- marketspace info
		    market.en_name AS market_en_name, market.th_name AS market_th_name,  -- market info
		    submarket.en_name AS submarket_en_name, submarket.th_name AS submarket_th_name  -- submarket info
        FROM contracts c
        LEFT JOIN matchings seller_match ON c.seller_matching_id = seller_match.id
        LEFT JOIN matchings buyer_match ON c.buyer_matching_id = buyer_match.id
        LEFT JOIN users seller ON seller_match.user_id = seller.id
        LEFT JOIN users buyer ON buyer_match.user_id = buyer.id
        LEFT JOIN products p ON c.product_id = p.id  -- เชื่อมโยงกับตาราง products ด้วย product_id
        LEFT JOIN qualities q ON c.quality_id = q.id
        LEFT JOIN standards std ON q.standard_id = std.id
        LEFT JOIN grades g ON q.grade_id = g.id
        LEFT JOIN currencies cur ON c.currency_id = cur.id
        LEFT JOIN uoms u ON c.uom_id = u.id
        LEFT JOIN delivery_terms dt ON c.delivery_term_id = dt.id
        LEFT JOIN provinces prov ON c.place_of_delivery_province = prov.id
        LEFT JOIN districts d ON c.place_of_delivery_district = d.id
        LEFT JOIN subdistricts sub ON c.place_of_delivery_subdistrict = sub.id
        LEFT JOIN packings pck ON c.packing_id = pck.id
        LEFT JOIN payment_terms pt ON c.payment_term_id = pt.id
        LEFT JOIN status_categories_statuses scs_seller ON c.seller_confirmation_status_id = scs_seller.id
        LEFT JOIN statuses seller_status ON scs_seller.status_id = seller_status.id
        LEFT JOIN status_categories_statuses scs_buyer ON c.buyer_confirmation_status_id = scs_buyer.id
        LEFT JOIN statuses buyer_status ON scs_buyer.status_id = buyer_status.id
        LEFT JOIN status_categories_statuses scs_contract ON c.contract_status_id = scs_contract.id
        LEFT JOIN statuses contract_status ON scs_contract.status_id = contract_status.id
        LEFT JOIN status_categories_statuses scs_delivery_status ON c.delivery_status_id = scs_delivery_status.id
        LEFT JOIN statuses scs_delivery ON scs_delivery_status.status_id = scs_delivery.id
        LEFT JOIN status_categories_statuses scs_payment_status ON c.payment_status_id = scs_payment_status.id
        LEFT JOIN statuses scs_payment ON scs_payment_status.status_id = scs_payment.id
        LEFT JOIN ports seller_pol ON c.seller_port_of_loading_id = seller_pol.id
        LEFT JOIN ports seller_pod ON c.seller_port_of_discharge_id = seller_pod.id
        LEFT JOIN ports buyer_pol ON c.buyer_port_of_loading_id = buyer_pol.id
        LEFT JOIN ports buyer_pod ON c.buyer_port_of_discharge_id = buyer_pod.id
        LEFT JOIN countries seller_country ON c.seller_country_id = seller_country.id
        LEFT JOIN countries buyer_country ON c.buyer_country_id = buyer_country.id
        LEFT JOIN contract_types ct ON c.contract_type_id = ct.id
        LEFT JOIN marketspaces marketspace ON c.marketspace_id = marketspace.id  -- เพิ่มการ join กับ marketspaces
	    LEFT JOIN markets market ON c.market_id = market.id  -- เพิ่มการ join กับ markets
	    LEFT JOIN submarkets submarket ON c.submarket_id = submarket.id  -- เพิ่มการ join กับ submarkets
        WHERE c.id = $1
    `

	row := db.QueryRow(query, contractID)

	var details ContractUserDetails
	err := row.Scan(
		&details.ID, &details.ContractDate,
		&details.SellerFirstName, &details.SellerLastName, &details.SellerOrgName, &details.SellerUserID,
		&details.BuyerFirstName, &details.BuyerLastName, &details.BuyerOrgName, &details.BuyerUserID,
		&details.ProductID,                             // เพิ่ม ProductID
		&details.ProductEnName, &details.ProductThName, // เพิ่มชื่อภาษาอังกฤษและไทยของสินค้า
		&details.StandardID, &details.StandardEnName, &details.StandardThName,
		&details.GradeID, &details.GradeEnName, &details.GradeThName,
		&details.Price, &details.CurrencyEnName, &details.CurrencyThName, &details.Quantity,
		&details.UomEn, &details.UomTh, &details.UomDescription,
		&details.PackingID, &details.PackingEn, &details.PackingTh,
		&details.DeliveryTermEn, &details.DeliveryTermTh,
		&details.PodProvinceEn, &details.PodProvinceTh,
		&details.PodDistrictEn, &details.PodDistrictTh,
		&details.PodSubdistrictEn, &details.PodSubdistrictTh,
		&details.DeliveryStatusEn, &details.DeliveryStatusTh,
		&details.StartDelivery, &details.FinishDelivery,
		&details.PaymentTermEn, &details.PaymentTermTh, &details.PaymentTermDescription,
		&details.PaymentStatusEn, &details.PaymentStatusTh,
		&details.SellerRemark, &details.BuyerRemark,
		&details.SellerConfirmStatusEn, &details.SellerConfirmStatusTh,
		&details.BuyerConfirmStatusEn, &details.BuyerConfirmStatusTh,
		&details.ContractStatusEn, &details.ContractStatusTh,
		&details.SellerPolEn, &details.SellerPolTh,
		&details.SellerPodEn, &details.SellerPodTh,
		&details.BuyerPolEn, &details.BuyerPolTh,
		&details.BuyerPodEn, &details.BuyerPodTh,
		&details.SellerCountryEn, &details.SellerCountryTh,
		&details.BuyerCountryEn, &details.BuyerCountryTh,
		&details.ContractTypeEnName,                            // เพิ่มการดึงข้อมูล contract_type_en_name
		&details.ContractTypeThName,                            // เพิ่มการดึงข้อมูล contract_type_th_name
		&details.MarketspaceEnName, &details.MarketspaceThName, // marketspace
		&details.MarketEnName, &details.MarketThName, // market
		&details.SubmarketEnName, &details.SubmarketThName, // submarket

	)

	if err != nil {
		log.Println("Error fetching contract user details by ID:", err)
		return ContractUserDetails{}, err
	}

	return details, nil
}

func UpdateContractStatusHandler(w http.ResponseWriter, r *http.Request) {
	log.Println("[UpdateContractStatusHandler] 🔄 Handling contract update request")

	// ดึง user_id จาก token อัตโนมัติ
	userID, err := getUserIDFromToken(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// รองรับ HTMX (x-www-form-urlencoded) และ JSON
	var req UpdateContractRequest
	contentType := r.Header.Get("Content-Type")

	switch contentType {
	case "application/json":
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, "Invalid JSON request", http.StatusBadRequest)
			return
		}
	case "application/x-www-form-urlencoded":
		if err := r.ParseForm(); err != nil {
			http.Error(w, "Invalid form request", http.StatusBadRequest)
			return
		}
		contractID, err := strconv.ParseInt(r.FormValue("contract_id"), 10, 64)
		if err != nil {
			http.Error(w, "Invalid contract ID", http.StatusBadRequest)
			return
		}
		req.ContractID = contractID
	default:
		http.Error(w, "Unsupported Content-Type", http.StatusBadRequest)
		return
	}

	req.UserID = int64(userID)

	// เริ่ม transaction
	db := database.GetDB()
	tx, err := db.Begin()
	if err != nil {
		http.Error(w, "Failed to start transaction", http.StatusInternalServerError)
		return
	}
	defer tx.Rollback()

	// ดึงข้อมูล contract และตรวจสอบสิทธิ์
	var sellerID, buyerID int64
	var sellerConfirm, buyerConfirm int
	query := `SELECT seller.id, buyer.id, c.seller_confirmation_status_id, c.buyer_confirmation_status_id
    FROM contracts c
    LEFT JOIN matchings seller_match ON c.seller_matching_id = seller_match.id
    LEFT JOIN matchings buyer_match ON c.buyer_matching_id = buyer_match.id
    LEFT JOIN users seller ON seller_match.user_id = seller.id
    LEFT JOIN users buyer ON buyer_match.user_id = buyer.id
    WHERE c.id = $1`
	err = db.QueryRow(query, req.ContractID).Scan(&sellerID, &buyerID, &sellerConfirm, &buyerConfirm)
	if err != nil {
		log.Printf("Error executing query: %v", err)
		http.Error(w, "Contract not found", http.StatusNotFound)
		return
	}

	// ตรวจสอบสิทธิ์และกำหนดคอลัมน์ที่จะอัพเดท
	var columnToUpdate string
	switch req.UserID {
	case sellerID:
		columnToUpdate = "seller_confirmation_status_id"
	case buyerID:
		columnToUpdate = "buyer_confirmation_status_id"
	default:
		http.Error(w, "User not authorized", http.StatusForbidden)
		return
	}

	// อัพเดทสถานะการยืนยัน
	updateQuery := fmt.Sprintf("UPDATE contracts SET %s = $1 WHERE id = $2", columnToUpdate)
	result, err := tx.Exec(updateQuery, 20, req.ContractID)
	if err != nil {
		http.Error(w, "Failed to update confirmation status", http.StatusInternalServerError)
		return
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil || rowsAffected == 0 {
		http.Error(w, "No rows updated", http.StatusInternalServerError)
		return
	}

	// กำหนดสถานะสัญญา
	contractStatusID := 21 // สถานะเริ่มต้น
	bothConfirmed := false

	if (req.UserID == sellerID && buyerConfirm == 20) || (req.UserID == buyerID && sellerConfirm == 20) {
		contractStatusID = 20 // สถานะเมื่อทั้งสองฝ่ายยืนยัน
		bothConfirmed = true
	}

	// อัพเดทสถานะสัญญา
	result, err = tx.Exec("UPDATE contracts SET contract_status_id = $1 WHERE id = $2",
		contractStatusID, req.ContractID)
	if err != nil {
		http.Error(w, "Failed to update contract status", http.StatusInternalServerError)
		return
	}

	rowsAffected, err = result.RowsAffected()
	if err != nil || rowsAffected == 0 {
		http.Error(w, "No rows updated", http.StatusInternalServerError)
		return
	}

	// Generate invoices if both parties have signed the contract
	if bothConfirmed {
		// Commit the transaction first to ensure contract status is updated
		err = tx.Commit()
		if err != nil {
			log.Printf("Error committing transaction: %v", err)
			http.Error(w, "Failed to commit transaction", http.StatusInternalServerError)
			return
		}

		// Generate invoices
		invoiceService := invoice.NewService(database.GetDB())
		err = invoiceService.GenerateInvoicesForContract(req.ContractID)
		if err != nil {
			log.Printf("Error generating invoices: %v", err)
			// Continue execution even if invoice generation fails
			// We don't want to roll back the contract signing just because invoice generation failed
		}
	} else {
		// Commit the transaction if both parties haven't signed yet
		err = tx.Commit()
		if err != nil {
			log.Printf("Error committing transaction: %v", err)
			http.Error(w, "Failed to commit transaction", http.StatusInternalServerError)
			return
		}
	}

	// Get contract details for notifications
	var productID int
	var productEnName, productThName string
	var quantity float64
	var price float64
	var currencyID, uomID, deliveryTermID int
	var currencyCode, uomName, deliveryTermName string

	err = db.QueryRow(`
		SELECT
			c.product_id,
			p.en_name,
			p.th_name,
			c.quantity,
			c.price,
			c.currency_id,
			c.uom_id,
			c.delivery_term_id,
			curr.code,
			u.en_name,
			dt.en_name
		FROM contracts c
		JOIN products p ON c.product_id = p.id
		JOIN currencies curr ON c.currency_id = curr.id
		JOIN uoms u ON c.uom_id = u.id
		JOIN delivery_terms dt ON c.delivery_term_id = dt.id
		WHERE c.id = $1
	`, req.ContractID).Scan(
		&productID,
		&productEnName,
		&productThName,
		&quantity,
		&price,
		&currencyID,
		&uomID,
		&deliveryTermID,
		&currencyCode,
		&uomName,
		&deliveryTermName,
	)

	if err != nil {
		log.Printf("Warning: Failed to get contract product details: %v", err)
		// Continue execution even if we can't get product details
	}

	// Get buyer and seller details
	var buyerUser, sellerUser struct {
		UserID    int64
		Email     string
		FirstName string
		LastName  string
		Username  string
		Language  string
	}

	// Get buyer details
	err = db.QueryRow(`
		SELECT u.id, u.email, u.first_name, u.last_name, u.username
		FROM users u
		WHERE u.id = $1
	`, buyerID).Scan(&buyerUser.UserID, &buyerUser.Email, &buyerUser.FirstName, &buyerUser.LastName, &buyerUser.Username)

	if err != nil {
		log.Printf("Warning: Failed to get buyer details: %v", err)
		// Continue execution even if we can't get buyer details
	}

	// Determine buyer's language preference
	// First check the current session language (most reliable)
	var buyerSessionLang sql.NullString
	err = db.QueryRow(`
		SELECT language FROM sessions
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT 1
	`, buyerID).Scan(&buyerSessionLang)

	if err == nil && buyerSessionLang.Valid && buyerSessionLang.String != "" {
		buyerUser.Language = buyerSessionLang.String
		log.Printf("Using language from current session for buyer: %s", buyerUser.Language)
	} else {
		// If not in session, fall back to determineUserLanguage
		buyerUser.Language = determineUserLanguage(db, buyerID)
	}

	// Get seller details
	err = db.QueryRow(`
		SELECT u.id, u.email, u.first_name, u.last_name, u.username
		FROM users u
		WHERE u.id = $1
	`, sellerID).Scan(&sellerUser.UserID, &sellerUser.Email, &sellerUser.FirstName, &sellerUser.LastName, &sellerUser.Username)

	if err != nil {
		log.Printf("Warning: Failed to get seller details: %v", err)
		// Continue execution even if we can't get seller details
	}

	// Determine seller's language preference
	// First check the current session language (most reliable)
	var sellerSessionLang sql.NullString
	err = db.QueryRow(`
		SELECT language FROM sessions
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT 1
	`, sellerID).Scan(&sellerSessionLang)

	if err == nil && sellerSessionLang.Valid && sellerSessionLang.String != "" {
		sellerUser.Language = sellerSessionLang.String
		log.Printf("Using language from current session for seller: %s", sellerUser.Language)
	} else {
		// If not in session, fall back to determineUserLanguage
		sellerUser.Language = determineUserLanguage(db, sellerID)
	}

	// Send notifications about the status update
	if req.UserID == sellerID {
		// Seller confirmed, send notifications to both seller and buyer

		// 1. Check if notification has already been sent to the seller (action taker) for this contract recently
		var sellerActionNotificationCount int
		err = db.QueryRow(`
			SELECT COUNT(*)
			FROM notifications n
			JOIN notification_types nt ON n.notification_type_id = nt.id
			WHERE nt.name = 'contract_status_update'
			AND n.body LIKE '%' || $1 || '%'
			AND n.user_id = $2
			AND n.created_at > NOW() - INTERVAL '30 minutes'
			AND (
				(n.body LIKE '%Your Action Confirmed%' AND $3 = 'en') OR
				(n.body LIKE '%การดำเนินการของคุณได้รับการยืนยัน%' AND $3 = 'th')
			)
		`, req.ContractID, sellerUser.UserID, sellerUser.Language).Scan(&sellerActionNotificationCount)

		if err != nil {
			log.Printf("Warning: Failed to check for existing seller action notifications: %v", err)
			// Continue execution even if check fails
		}

		// Only send notification to seller if none has been sent recently
		if sellerActionNotificationCount == 0 {
			// Send notification to the seller (action taker)
			actionTakenSeller := "confirmed"
			if sellerUser.Language == "th" {
				actionTakenSeller = "ยืนยัน"
			}

			sellerActionNotification := &notification.ContractStatusUpdateNotification{
				UserID:             sellerUser.UserID,
				Email:              sellerUser.Email,
				FirstName:          sellerUser.FirstName,
				LastName:           sellerUser.LastName,
				Username:           sellerUser.Username,
				ContractID:         req.ContractID,
				Role:               "seller",
				CounterpartyName:   fmt.Sprintf("%s %s", buyerUser.FirstName, buyerUser.LastName),
				ProductName:        productEnName,
				Quantity:           quantity,
				UnitOfMeasure:      uomName,
				Price:              price,
				Currency:           currencyCode,
				DeliveryTerms:      deliveryTermName,
				ActionTaken:        actionTakenSeller,
				IsActionTaker:      true, // Seller took the action
				ContractDetailsURL: fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), req.ContractID, sellerUser.Language),
				Language:           sellerUser.Language,
				BaseURL:            os.Getenv("BASE_URL"),
			}

			err = notification.SendContractStatusUpdateNotification(sellerActionNotification)
			if err != nil {
				log.Printf("Warning: Failed to send contract status update notification to seller (action taker): %v", err)
				// Continue execution even if notification fails
			} else {
				log.Printf("Successfully sent contract status update notification to seller (action taker) for contract ID: %d", req.ContractID)
			}
		} else {
			log.Printf("Seller action notification already sent recently for contract ID: %d, skipping", req.ContractID)
		}

		// Skip sending notification to buyer if both parties have confirmed
		// Instead, we'll send the Contract Confirmation notification to both parties
		if !bothConfirmed {
			// 2. Check if notification has already been sent to the buyer (counterparty) for this contract recently
			var buyerNotificationCount int
			err = db.QueryRow(`
				SELECT COUNT(*)
				FROM notifications n
				JOIN notification_types nt ON n.notification_type_id = nt.id
				WHERE nt.name = 'contract_status_update'
				AND n.body LIKE '%' || $1 || '%'
				AND n.user_id = $2
				AND n.created_at > NOW() - INTERVAL '30 minutes'
				AND (
					(n.body LIKE '%Counterparty Action%' AND $3 = 'en') OR
					(n.body LIKE '%การดำเนินการของคู่ค้า%' AND $3 = 'th')
				)
			`, req.ContractID, buyerUser.UserID, buyerUser.Language).Scan(&buyerNotificationCount)

			if err != nil {
				log.Printf("Warning: Failed to check for existing buyer contract status update notifications: %v", err)
				// Continue execution even if check fails
			}

			// Only send notification to buyer if none has been sent recently
			if buyerNotificationCount == 0 {
				actionTakenBuyer := "confirmed"
				if buyerUser.Language == "th" {
					actionTakenBuyer = "ยืนยัน"
				}

				// Get organization names for both users
				var sellerOrgName, buyerOrgName string
				err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", sellerID).Scan(&sellerOrgName)
				if err != nil {
					log.Printf("Warning: Failed to get seller organization name: %v", err)
				}
				err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", buyerID).Scan(&buyerOrgName)
				if err != nil {
					log.Printf("Warning: Failed to get buyer organization name: %v", err)
				}

				buyerNotification := &notification.ContractStatusUpdateNotification{
					UserID:              buyerUser.UserID,
					Email:               buyerUser.Email,
					FirstName:           buyerUser.FirstName,
					LastName:            buyerUser.LastName,
					Username:            buyerUser.Username,
					ContractID:          req.ContractID,
					Role:                "buyer",
					CounterpartyName:    fmt.Sprintf("%s %s", sellerUser.FirstName, sellerUser.LastName),
					CounterpartyCompany: sellerOrgName,
					ProductName:         productEnName,
					Quantity:            quantity,
					UnitOfMeasure:       uomName,
					Price:               price,
					Currency:            currencyCode,
					DeliveryTerms:       deliveryTermName,
					ActionTaken:         actionTakenBuyer,
					IsActionTaker:       false, // Buyer is receiving notification about seller's action
					ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), req.ContractID, buyerUser.Language),
					Language:            buyerUser.Language,
					BaseURL:             os.Getenv("BASE_URL"),
				}

				err = notification.SendContractStatusUpdateNotification(buyerNotification)
				if err != nil {
					log.Printf("Warning: Failed to send contract status update notification to buyer: %v", err)
					// Continue execution even if notification fails
				} else {
					log.Printf("Successfully sent contract status update notification to buyer for contract ID: %d", req.ContractID)
				}
			} else {
				log.Printf("Buyer notification already sent recently for contract ID: %d, skipping", req.ContractID)
			}
		} else {
			log.Printf("Both parties have confirmed contract ID: %d, skipping redundant notification to buyer", req.ContractID)
		}
	} else if req.UserID == buyerID {
		// Buyer confirmed, send notifications to both buyer and seller

		// 1. Check if notification has already been sent to the buyer (action taker) for this contract recently
		var buyerActionNotificationCount int
		err = db.QueryRow(`
			SELECT COUNT(*)
			FROM notifications n
			JOIN notification_types nt ON n.notification_type_id = nt.id
			WHERE nt.name = 'contract_status_update'
			AND n.body LIKE '%' || $1 || '%'
			AND n.user_id = $2
			AND n.created_at > NOW() - INTERVAL '30 minutes'
			AND (
				(n.body LIKE '%Your Action Confirmed%' AND $3 = 'en') OR
				(n.body LIKE '%การดำเนินการของคุณได้รับการยืนยัน%' AND $3 = 'th')
			)
		`, req.ContractID, buyerUser.UserID, buyerUser.Language).Scan(&buyerActionNotificationCount)

		if err != nil {
			log.Printf("Warning: Failed to check for existing buyer action notifications: %v", err)
			// Continue execution even if check fails
		}

		// Only send notification to buyer if none has been sent recently
		if buyerActionNotificationCount == 0 {
			// Send notification to the buyer (action taker)
			actionTakenBuyer := "confirmed"
			if buyerUser.Language == "th" {
				actionTakenBuyer = "ยืนยัน"
			}

			// Get organization names for both users if not already retrieved
			var sellerOrgName, buyerOrgName string
			err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", sellerID).Scan(&sellerOrgName)
			if err != nil {
				log.Printf("Warning: Failed to get seller organization name: %v", err)
			}
			err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", buyerID).Scan(&buyerOrgName)
			if err != nil {
				log.Printf("Warning: Failed to get buyer organization name: %v", err)
			}

			buyerActionNotification := &notification.ContractStatusUpdateNotification{
				UserID:              buyerUser.UserID,
				Email:               buyerUser.Email,
				FirstName:           buyerUser.FirstName,
				LastName:            buyerUser.LastName,
				Username:            buyerUser.Username,
				ContractID:          req.ContractID,
				Role:                "buyer",
				CounterpartyName:    fmt.Sprintf("%s %s", sellerUser.FirstName, sellerUser.LastName),
				CounterpartyCompany: sellerOrgName,
				ProductName:         productEnName,
				Quantity:            quantity,
				UnitOfMeasure:       uomName,
				Price:               price,
				Currency:            currencyCode,
				DeliveryTerms:       deliveryTermName,
				ActionTaken:         actionTakenBuyer,
				IsActionTaker:       true, // Buyer took the action
				ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), req.ContractID, buyerUser.Language),
				Language:            buyerUser.Language,
				BaseURL:             os.Getenv("BASE_URL"),
			}

			err = notification.SendContractStatusUpdateNotification(buyerActionNotification)
			if err != nil {
				log.Printf("Warning: Failed to send contract status update notification to buyer (action taker): %v", err)
				// Continue execution even if notification fails
			} else {
				log.Printf("Successfully sent contract status update notification to buyer (action taker) for contract ID: %d", req.ContractID)
			}
		} else {
			log.Printf("Buyer action notification already sent recently for contract ID: %d, skipping", req.ContractID)
		}

		// Skip sending notification to seller if both parties have confirmed
		// Instead, we'll send the Contract Confirmation notification to both parties
		if !bothConfirmed {
			// 2. Check if notification has already been sent to the seller (counterparty) for this contract recently
			var sellerNotificationCount int
			err = db.QueryRow(`
				SELECT COUNT(*)
				FROM notifications n
				JOIN notification_types nt ON n.notification_type_id = nt.id
				WHERE nt.name = 'contract_status_update'
				AND n.body LIKE '%' || $1 || '%'
				AND n.user_id = $2
				AND n.created_at > NOW() - INTERVAL '30 minutes'
				AND (
					(n.body LIKE '%Counterparty Action%' AND $3 = 'en') OR
					(n.body LIKE '%การดำเนินการของคู่ค้า%' AND $3 = 'th')
				)
			`, req.ContractID, sellerUser.UserID, sellerUser.Language).Scan(&sellerNotificationCount)

			if err != nil {
				log.Printf("Warning: Failed to check for existing seller contract status update notifications: %v", err)
				// Continue execution even if check fails
			}

			// Only send notification to seller if none has been sent recently
			if sellerNotificationCount == 0 {
				actionTakenSeller := "confirmed"
				if sellerUser.Language == "th" {
					actionTakenSeller = "ยืนยัน"
				}

				// Get organization names for both users if not already retrieved
				var sellerOrgName, buyerOrgName string
				err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", sellerID).Scan(&sellerOrgName)
				if err != nil {
					log.Printf("Warning: Failed to get seller organization name: %v", err)
				}
				err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", buyerID).Scan(&buyerOrgName)
				if err != nil {
					log.Printf("Warning: Failed to get buyer organization name: %v", err)
				}

				sellerNotification := &notification.ContractStatusUpdateNotification{
					UserID:              sellerUser.UserID,
					Email:               sellerUser.Email,
					FirstName:           sellerUser.FirstName,
					LastName:            sellerUser.LastName,
					Username:            sellerUser.Username,
					ContractID:          req.ContractID,
					Role:                "seller",
					CounterpartyName:    fmt.Sprintf("%s %s", buyerUser.FirstName, buyerUser.LastName),
					CounterpartyCompany: buyerOrgName,
					ProductName:         productEnName,
					Quantity:            quantity,
					UnitOfMeasure:       uomName,
					Price:               price,
					Currency:            currencyCode,
					DeliveryTerms:       deliveryTermName,
					ActionTaken:         actionTakenSeller,
					IsActionTaker:       false, // Seller is receiving notification about buyer's action
					ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), req.ContractID, sellerUser.Language),
					Language:            sellerUser.Language,
					BaseURL:             os.Getenv("BASE_URL"),
				}

				err = notification.SendContractStatusUpdateNotification(sellerNotification)
				if err != nil {
					log.Printf("Warning: Failed to send contract status update notification to seller: %v", err)
					// Continue execution even if notification fails
				} else {
					log.Printf("Successfully sent contract status update notification to seller for contract ID: %d", req.ContractID)
				}
			} else {
				log.Printf("Seller notification already sent recently for contract ID: %d, skipping", req.ContractID)
			}
		} else {
			log.Printf("Both parties have confirmed contract ID: %d, skipping redundant notification to seller", req.ContractID)
		}
	}

	// If both parties have confirmed, send contract confirmation notifications
	if bothConfirmed {
		log.Printf("Contract ID: %d has been fully confirmed by both parties", req.ContractID)

		// Add a delay to ensure status update notifications are processed first
		// This is in addition to the delay in the notification service
		log.Printf("Adding delay before sending contract confirmation notifications for contract ID: %d", req.ContractID)
		time.Sleep(notification.NotificationSequenceDelay)

		// Check if we've already sent a confirmation notification for this contract
		var notificationCount int
		err = db.QueryRow(`
			SELECT COUNT(*) FROM notifications n
			JOIN notification_types nt ON n.notification_type_id = nt.id
			WHERE nt.name = 'contract_confirmation'
			AND n.body LIKE '%/contract/' || $1 || '%'
		`, req.ContractID).Scan(&notificationCount)

		if err != nil {
			log.Printf("Warning: Failed to check existing contract confirmation notifications: %v", err)
			// Continue execution even if check fails
			notificationCount = 0
		}

		// Only send contract confirmation notifications if none have been sent before
		if notificationCount == 0 {
			// Send contract confirmation notification to buyer
			log.Printf("Sending contract confirmation notification to buyer for contract ID: %d", req.ContractID)
			// Get buyer's phone
			var buyerPhone string
			err = db.QueryRow("SELECT phone FROM users WHERE id = $1", buyerUser.UserID).Scan(&buyerPhone)
			if err != nil {
				log.Printf("Warning: Failed to get buyer's phone: %v", err)
			}

			// Get seller's phone
			var sellerPhone string
			err = db.QueryRow("SELECT phone FROM users WHERE id = $1", sellerUser.UserID).Scan(&sellerPhone)
			if err != nil {
				log.Printf("Warning: Failed to get seller's phone: %v", err)
			}

			buyerNotification := &notification.ContractConfirmationNotification{
				UserID:              buyerUser.UserID,
				Email:               buyerUser.Email,
				FirstName:           buyerUser.FirstName,
				LastName:            buyerUser.LastName,
				Username:            buyerUser.Username,
				Phone:               buyerPhone,
				ContractID:          req.ContractID,
				Role:                "buyer",
				CounterpartyName:    fmt.Sprintf("%s %s", sellerUser.FirstName, sellerUser.LastName),
				CounterpartyPhone:   sellerPhone,
				CounterpartyEmail:   sellerUser.Email,
				ProductName:         productEnName,
				Quantity:            quantity,
				UnitOfMeasure:       uomName,
				Price:               price,
				Currency:            currencyCode,
				DeliveryTerms:       deliveryTermName,
				ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), req.ContractID, buyerUser.Language),
				Language:            buyerUser.Language,
				BaseURL:             os.Getenv("BASE_URL"),
				IsFirstConfirmation: true,
			}

			err = notification.SendContractConfirmationNotification(buyerNotification)
			if err != nil {
				log.Printf("Warning: Failed to send contract confirmation notification to buyer: %v", err)
				// Continue execution even if notification fails
			} else {
				log.Printf("Successfully sent contract confirmation notification to buyer for contract ID: %d", req.ContractID)
			}

			// Send contract confirmation notification to seller
			log.Printf("Sending contract confirmation notification to seller for contract ID: %d", req.ContractID)
			sellerNotification := &notification.ContractConfirmationNotification{
				UserID:              sellerUser.UserID,
				Email:               sellerUser.Email,
				FirstName:           sellerUser.FirstName,
				LastName:            sellerUser.LastName,
				Username:            sellerUser.Username,
				Phone:               sellerPhone,
				ContractID:          req.ContractID,
				Role:                "seller",
				CounterpartyName:    fmt.Sprintf("%s %s", buyerUser.FirstName, buyerUser.LastName),
				CounterpartyPhone:   buyerPhone,
				CounterpartyEmail:   buyerUser.Email,
				ProductName:         productEnName,
				Quantity:            quantity,
				UnitOfMeasure:       uomName,
				Price:               price,
				Currency:            currencyCode,
				DeliveryTerms:       deliveryTermName,
				ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), req.ContractID, sellerUser.Language),
				Language:            sellerUser.Language,
				BaseURL:             os.Getenv("BASE_URL"),
				IsFirstConfirmation: true,
			}

			err = notification.SendContractConfirmationNotification(sellerNotification)
			if err != nil {
				log.Printf("Warning: Failed to send contract confirmation notification to seller: %v", err)
				// Continue execution even if notification fails
			} else {
				log.Printf("Successfully sent contract confirmation notification to seller for contract ID: %d", req.ContractID)
			}
		}
	}

	// Determine language from referer URL
	language := "en" // Default to English
	referer := r.Header.Get("Referer")
	if strings.Contains(referer, "lang=th") || strings.Contains(referer, "/th/") {
		language = "th"
	}

	// Send response based on request format and language
	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("Content-Type", "text/html")

		// Check if both parties have signed (invoices were generated)
		invoicesGenerated := bothConfirmed

		if language == "th" {
			// Thai success message
			var message string
			if invoicesGenerated {
				message = `
				<dialog id="status-dialog">
					<p>คุณได้ลงนามในสัญญาเรียบร้อยแล้ว</p>
					<p>ใบแจ้งหนี้ได้ถูกสร้างขึ้นแล้ว คุณสามารถดูได้ที่หน้า <a href="/invoice?lang=th" style="color: #0096FE;">ใบแจ้งหนี้</a></p>
					<form method="dialog">
						<button id="confirm-button" type="submit">ยืนยัน</button>
					</form>
				</dialog>
				<script>
					const dialog = document.getElementById('status-dialog');
					dialog.showModal();
					dialog.addEventListener('close', () => {
						location.reload();
					});
				</script>
				`
			} else {
				message = `
				<dialog id="status-dialog">
					<p>คุณได้ลงนามในสัญญาเรียบร้อยแล้ว</p>
					<form method="dialog">
						<button id="confirm-button" type="submit">ยืนยัน</button>
					</form>
				</dialog>
				<script>
					const dialog = document.getElementById('status-dialog');
					dialog.showModal();
					dialog.addEventListener('close', () => {
						location.reload();
					});
				</script>
				`
			}
			fmt.Fprint(w, message)
		} else {
			// English success message
			var message string
			if invoicesGenerated {
				message = `
				<dialog id="status-dialog">
					<p>You have successfully signed the contract</p>
					<p>Invoices have been generated. You can view them on the <a href="/invoice?lang=en" style="color: #0096FE;">Invoices</a> page.</p>
					<form method="dialog">
						<button id="confirm-button" type="submit">Confirm</button>
					</form>
				</dialog>
				<script>
					const dialog = document.getElementById('status-dialog');
					dialog.showModal();
					dialog.addEventListener('close', () => {
						location.reload();
					});
				</script>
				`
			} else {
				message = `
				<dialog id="status-dialog">
					<p>You have successfully signed the contract</p>
					<form method="dialog">
						<button id="confirm-button" type="submit">Confirm</button>
					</form>
				</dialog>
				<script>
					const dialog = document.getElementById('status-dialog');
					dialog.showModal();
					dialog.addEventListener('close', () => {
						location.reload();
					});
				</script>
				`
			}
			fmt.Fprint(w, message)
		}
		return
	} else {
		w.Header().Set("Content-Type", "application/json")

		// Different message based on language
		message := "Contract status updated successfully"
		if language == "th" {
			message = "อัปเดตสถานะสัญญาเรียบร้อยแล้ว"
		}

		json.NewEncoder(w).Encode(map[string]interface{}{
			"contract_status": contractStatusID,
			"message":         message,
		})
	}
}

func RejectContractHandler(w http.ResponseWriter, r *http.Request) {
	log.Println("[RejectContractHandler] 🔄 Handling contract rejection request")

	// ดึง user_id จาก token อัตโนมัติ
	userID, err := getUserIDFromToken(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// รองรับ HTMX (x-www-form-urlencoded) และ JSON
	var req RejectContractRequest
	contentType := r.Header.Get("Content-Type")

	switch contentType {
	case "application/json":
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, "Invalid JSON request", http.StatusBadRequest)
			return
		}
	case "application/x-www-form-urlencoded":
		if err := r.ParseForm(); err != nil {
			http.Error(w, "Invalid form request", http.StatusBadRequest)
			return
		}
		contractID, err := strconv.ParseInt(r.FormValue("contract_id"), 10, 64)
		if err != nil {
			http.Error(w, "Invalid contract ID", http.StatusBadRequest)
			return
		}
		req.ContractID = contractID
	default:
		http.Error(w, "Unsupported Content-Type", http.StatusBadRequest)
		return
	}

	req.UserID = int64(userID)

	// เริ่ม transaction
	db := database.GetDB()
	tx, err := db.Begin()
	if err != nil {
		http.Error(w, "Failed to start transaction", http.StatusInternalServerError)
		return
	}
	defer tx.Rollback()

	// ดึงข้อมูล contract และตรวจสอบสิทธิ์
	var sellerID, buyerID int64
	var sellerConfirm, buyerConfirm, contractStatusID int
	query := `SELECT seller.id, buyer.id, c.seller_confirmation_status_id, c.buyer_confirmation_status_id, c.contract_status_id
    FROM contracts c
    LEFT JOIN matchings seller_match ON c.seller_matching_id = seller_match.id
    LEFT JOIN matchings buyer_match ON c.buyer_matching_id = buyer_match.id
    LEFT JOIN users seller ON seller_match.user_id = seller.id
    LEFT JOIN users buyer ON buyer_match.user_id = buyer.id
    WHERE c.id = $1`
	err = db.QueryRow(query, req.ContractID).Scan(&sellerID, &buyerID, &sellerConfirm, &buyerConfirm, &contractStatusID)
	if err != nil {
		log.Printf("Error executing query: %v", err)
		http.Error(w, "Contract not found", http.StatusNotFound)
		return
	}

	// ตรวจสอบว่าสัญญาถูกปฏิเสธไปแล้วหรือไม่
	if contractStatusID == 22 {
		log.Printf("Contract ID %d has already been rejected. Skipping update and notifications.", req.ContractID)

		// ส่งข้อความแจ้งผู้ใช้ว่าสัญญาถูกปฏิเสธไปแล้ว
		language := "en" // Default to English
		referer := r.Header.Get("Referer")
		if strings.Contains(referer, "lang=th") || strings.Contains(referer, "/th/") {
			language = "th"
		}

		if r.Header.Get("HX-Request") == "true" {
			w.Header().Set("Content-Type", "text/html")

			if language == "th" {
				fmt.Fprint(w, `
					<dialog id="status-dialog">
						<p>สัญญานี้ถูกปฏิเสธไปแล้ว</p>
						<form method="dialog">
							<button id="confirm-button" type="submit">ตกลง</button>
						</form>
					</dialog>
					<script>
						const dialog = document.getElementById('status-dialog');
						dialog.showModal();
						dialog.addEventListener('close', () => {
							location.reload();
						});
					</script>
				`)
			} else {
				fmt.Fprint(w, `
					<dialog id="status-dialog">
						<p>This contract has already been rejected</p>
						<form method="dialog">
							<button id="confirm-button" type="submit">OK</button>
						</form>
					</dialog>
					<script>
						const dialog = document.getElementById('status-dialog');
						dialog.showModal();
						dialog.addEventListener('close', () => {
							location.reload();
						});
					</script>
				`)
			}
		} else {
			w.Header().Set("Content-Type", "application/json")
			message := "Contract has already been rejected"
			if language == "th" {
				message = "สัญญานี้ถูกปฏิเสธไปแล้ว"
			}
			json.NewEncoder(w).Encode(map[string]interface{}{
				"contract_status": 22,
				"message":         message,
			})
		}
		return
	}

	// ตรวจสอบสิทธิ์และกำหนดคอลัมน์ที่จะอัพเดท
	var columnToUpdate string
	switch req.UserID {
	case sellerID:
		columnToUpdate = "seller_confirmation_status_id"
	case buyerID:
		columnToUpdate = "buyer_confirmation_status_id"
	default:
		http.Error(w, "User not authorized", http.StatusForbidden)
		return
	}

	// อัพเดทสถานะการยืนยันเป็น "ปฏิเสธ" (21)
	updateQuery := fmt.Sprintf("UPDATE contracts SET %s = $1 WHERE id = $2", columnToUpdate)
	result, err := tx.Exec(updateQuery, 21, req.ContractID)
	if err != nil {
		http.Error(w, "Failed to update confirmation status", http.StatusInternalServerError)
		return
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil || rowsAffected == 0 {
		http.Error(w, "No rows updated", http.StatusInternalServerError)
		return
	}

	// อัพเดทสถานะสัญญาเป็น "ปฏิเสธ" (22)
	result, err = tx.Exec("UPDATE contracts SET contract_status_id = $1 WHERE id = $2",
		22, req.ContractID)
	if err != nil {
		http.Error(w, "Failed to update contract status", http.StatusInternalServerError)
		return
	}

	rowsAffected, err = result.RowsAffected()
	if err != nil || rowsAffected == 0 {
		http.Error(w, "No rows updated", http.StatusInternalServerError)
		return
	}

	// ยืนยัน transaction
	if err := tx.Commit(); err != nil {
		http.Error(w, "Transaction commit failed", http.StatusInternalServerError)
		return
	}

	// Get contract details for notifications
	var productID int
	var productEnName, productThName string
	var quantity float64
	var price float64
	var currencyID, uomID, deliveryTermID int
	var currencyCode, uomName, deliveryTermName string

	err = db.QueryRow(`
		SELECT
			c.product_id,
			p.en_name,
			p.th_name,
			c.quantity,
			c.price,
			c.currency_id,
			c.uom_id,
			c.delivery_term_id,
			curr.code,
			u.en_name,
			dt.en_name
		FROM contracts c
		JOIN products p ON c.product_id = p.id
		JOIN currencies curr ON c.currency_id = curr.id
		JOIN uoms u ON c.uom_id = u.id
		JOIN delivery_terms dt ON c.delivery_term_id = dt.id
		WHERE c.id = $1
	`, req.ContractID).Scan(
		&productID,
		&productEnName,
		&productThName,
		&quantity,
		&price,
		&currencyID,
		&uomID,
		&deliveryTermID,
		&currencyCode,
		&uomName,
		&deliveryTermName,
	)

	if err != nil {
		log.Printf("Warning: Failed to get contract product details: %v", err)
		// Continue execution even if we can't get product details
	}

	// Get buyer and seller details
	var buyerUser, sellerUser struct {
		UserID    int64
		Email     string
		FirstName string
		LastName  string
		Username  string
		Language  string
	}

	// Get buyer details
	err = db.QueryRow(`
		SELECT u.id, u.email, u.first_name, u.last_name, u.username
		FROM users u
		WHERE u.id = $1
	`, buyerID).Scan(&buyerUser.UserID, &buyerUser.Email, &buyerUser.FirstName, &buyerUser.LastName, &buyerUser.Username)

	if err != nil {
		log.Printf("Warning: Failed to get buyer details: %v", err)
		// Continue execution even if we can't get buyer details
	}

	// Determine buyer's language preference
	buyerUser.Language = determineUserLanguage(db, buyerID)

	// Get seller details
	err = db.QueryRow(`
		SELECT u.id, u.email, u.first_name, u.last_name, u.username
		FROM users u
		WHERE u.id = $1
	`, sellerID).Scan(&sellerUser.UserID, &sellerUser.Email, &sellerUser.FirstName, &sellerUser.LastName, &sellerUser.Username)

	if err != nil {
		log.Printf("Warning: Failed to get seller details: %v", err)
		// Continue execution even if we can't get seller details
	}

	// Determine seller's language preference
	sellerUser.Language = determineUserLanguage(db, sellerID)

	// For a new contract rejection, we should always send notifications
	// We'll only check for existing notifications if we need to prevent duplicates
	// from multiple clicks of the reject button

	// Initialize notification counters to 0 (meaning no notifications sent yet)
	var actionTakerNotificationCount, counterpartyNotificationCount int

	// Get the current contract status to determine if this is a new rejection
	var currentContractStatus int
	err = db.QueryRow(`
		SELECT contract_status_id
		FROM contracts
		WHERE id = $1
	`, req.ContractID).Scan(&currentContractStatus)

	if err != nil {
		log.Printf("Warning: Failed to get current contract status: %v", err)
		// If we can't determine the status, assume it's a new rejection
		currentContractStatus = 0
	}

	// If the contract is already in rejected status (22), check for existing notifications
	// to prevent duplicates. Otherwise, always send notifications for a new rejection.
	if currentContractStatus == 22 {
		log.Printf("Contract ID %d is already in rejected status, checking for existing notifications", req.ContractID)

		if req.UserID == sellerID {
			// Check if seller (action taker) already received a rejection notification
			err = db.QueryRow(`
				SELECT COUNT(*)
				FROM notifications n
				JOIN notification_types nt ON n.notification_type_id = nt.id
				WHERE nt.name = 'contract_status_update'
				AND n.body LIKE '%contract_id=' || $1 || '%'
				AND n.user_id = $2
				AND n.created_at > NOW() - INTERVAL '24 hours'
				AND (
					(n.body LIKE '%"action_taken":"rejected"%' AND $3 = 'en') OR
					(n.body LIKE '%"action_taken":"ปฏิเสธ"%' AND $3 = 'th')
				)
			`, req.ContractID, sellerUser.UserID, sellerUser.Language).Scan(&actionTakerNotificationCount)

			if err != nil {
				log.Printf("Warning: Failed to check for existing seller rejection notifications: %v", err)
				// Continue execution even if check fails
				actionTakerNotificationCount = 0
			}

			log.Printf("Found %d existing rejection notifications for seller (action taker) for contract ID: %d",
				actionTakerNotificationCount, req.ContractID)

			// Check if buyer (counterparty) already received a rejection notification
			err = db.QueryRow(`
				SELECT COUNT(*)
				FROM notifications n
				JOIN notification_types nt ON n.notification_type_id = nt.id
				WHERE nt.name = 'contract_status_update'
				AND n.body LIKE '%contract_id=' || $1 || '%'
				AND n.user_id = $2
				AND n.created_at > NOW() - INTERVAL '24 hours'
				AND (
					(n.body LIKE '%"action_taken":"rejected"%' AND $3 = 'en') OR
					(n.body LIKE '%"action_taken":"ปฏิเสธ"%' AND $3 = 'th')
				)
			`, req.ContractID, buyerUser.UserID, buyerUser.Language).Scan(&counterpartyNotificationCount)

			if err != nil {
				log.Printf("Warning: Failed to check for existing buyer rejection notifications: %v", err)
				// Continue execution even if check fails
				counterpartyNotificationCount = 0
			}

			log.Printf("Found %d existing rejection notifications for buyer (counterparty) for contract ID: %d",
				counterpartyNotificationCount, req.ContractID)

		} else if req.UserID == buyerID {
			// Check if buyer (action taker) already received a rejection notification
			err = db.QueryRow(`
				SELECT COUNT(*)
				FROM notifications n
				JOIN notification_types nt ON n.notification_type_id = nt.id
				WHERE nt.name = 'contract_status_update'
				AND n.body LIKE '%contract_id=' || $1 || '%'
				AND n.user_id = $2
				AND n.created_at > NOW() - INTERVAL '24 hours'
				AND (
					(n.body LIKE '%"action_taken":"rejected"%' AND $3 = 'en') OR
					(n.body LIKE '%"action_taken":"ปฏิเสธ"%' AND $3 = 'th')
				)
			`, req.ContractID, buyerUser.UserID, buyerUser.Language).Scan(&actionTakerNotificationCount)

			if err != nil {
				log.Printf("Warning: Failed to check for existing buyer rejection notifications: %v", err)
				// Continue execution even if check fails
				actionTakerNotificationCount = 0
			}

			log.Printf("Found %d existing rejection notifications for buyer (action taker) for contract ID: %d",
				actionTakerNotificationCount, req.ContractID)

			// Check if seller (counterparty) already received a rejection notification
			err = db.QueryRow(`
				SELECT COUNT(*)
				FROM notifications n
				JOIN notification_types nt ON n.notification_type_id = nt.id
				WHERE nt.name = 'contract_status_update'
				AND n.body LIKE '%contract_id=' || $1 || '%'
				AND n.user_id = $2
				AND n.created_at > NOW() - INTERVAL '24 hours'
				AND (
					(n.body LIKE '%"action_taken":"rejected"%' AND $3 = 'en') OR
					(n.body LIKE '%"action_taken":"ปฏิเสธ"%' AND $3 = 'th')
				)
			`, req.ContractID, sellerUser.UserID, sellerUser.Language).Scan(&counterpartyNotificationCount)

			if err != nil {
				log.Printf("Warning: Failed to check for existing seller rejection notifications: %v", err)
				// Continue execution even if check fails
				counterpartyNotificationCount = 0
			}

			log.Printf("Found %d existing rejection notifications for seller (counterparty) for contract ID: %d",
				counterpartyNotificationCount, req.ContractID)
		}
	} else {
		// This is a new rejection, always send notifications
		log.Printf("Contract ID %d is not yet in rejected status, will send notifications", req.ContractID)
		actionTakerNotificationCount = 0
		counterpartyNotificationCount = 0
	}

	// Send notifications about the rejection
	if req.UserID == sellerID {
		// Seller rejected, send notifications to both seller and buyer

		// Send notification to the seller (action taker) if not already sent
		if actionTakerNotificationCount == 0 || currentContractStatus != 22 {
			// Either this is a new rejection or no notification has been sent yet
			actionTakenSeller := "rejected"
			if sellerUser.Language == "th" {
				actionTakenSeller = "ปฏิเสธ"
			}

			// Get organization names for both users
			var sellerOrgName, buyerOrgName string
			err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", sellerID).Scan(&sellerOrgName)
			if err != nil {
				log.Printf("Warning: Failed to get seller organization name: %v", err)
			}
			err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", buyerID).Scan(&buyerOrgName)
			if err != nil {
				log.Printf("Warning: Failed to get buyer organization name: %v", err)
			}

			sellerActionNotification := &notification.ContractStatusUpdateNotification{
				UserID:              sellerUser.UserID,
				Email:               sellerUser.Email,
				FirstName:           sellerUser.FirstName,
				LastName:            sellerUser.LastName,
				Username:            sellerUser.Username,
				ContractID:          req.ContractID,
				Role:                "seller",
				CounterpartyName:    fmt.Sprintf("%s %s", buyerUser.FirstName, buyerUser.LastName),
				CounterpartyCompany: buyerOrgName,
				ProductName:         productEnName,
				Quantity:            quantity,
				UnitOfMeasure:       uomName,
				Price:               price,
				Currency:            currencyCode,
				DeliveryTerms:       deliveryTermName,
				ActionTaken:         actionTakenSeller,
				IsActionTaker:       true, // Seller took the action
				ContractDetailsURL:  fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), req.ContractID, sellerUser.Language),
				Language:            sellerUser.Language,
				BaseURL:             os.Getenv("BASE_URL"),
			}

			err = notification.SendContractStatusUpdateNotification(sellerActionNotification)
			if err != nil {
				log.Printf("Warning: Failed to send contract rejection notification to seller (action taker): %v", err)
				// Continue execution even if notification fails
			} else {
				log.Printf("Successfully sent contract rejection notification to seller (action taker) for contract ID: %d", req.ContractID)
			}
		} else {
			log.Printf("Seller rejection notification already sent for contract ID: %d, skipping", req.ContractID)
		}

		// Send notification to the buyer (counterparty) if not already sent
		if counterpartyNotificationCount == 0 || currentContractStatus != 22 {
			// Either this is a new rejection or no notification has been sent yet
			actionTakenBuyer := "rejected"
			if buyerUser.Language == "th" {
				actionTakenBuyer = "ปฏิเสธ"
			}

			buyerNotification := &notification.ContractStatusUpdateNotification{
				UserID:             buyerUser.UserID,
				Email:              buyerUser.Email,
				FirstName:          buyerUser.FirstName,
				LastName:           buyerUser.LastName,
				Username:           buyerUser.Username,
				ContractID:         req.ContractID,
				Role:               "buyer",
				CounterpartyName:   fmt.Sprintf("%s %s", sellerUser.FirstName, sellerUser.LastName),
				ProductName:        productEnName,
				Quantity:           quantity,
				UnitOfMeasure:      uomName,
				Price:              price,
				Currency:           currencyCode,
				DeliveryTerms:      deliveryTermName,
				ActionTaken:        actionTakenBuyer,
				IsActionTaker:      false, // Buyer is receiving notification about seller's action
				ContractDetailsURL: fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), req.ContractID, buyerUser.Language),
				Language:           buyerUser.Language,
				BaseURL:            os.Getenv("BASE_URL"),
			}

			err = notification.SendContractStatusUpdateNotification(buyerNotification)
			if err != nil {
				log.Printf("Warning: Failed to send contract rejection notification to buyer: %v", err)
				// Continue execution even if notification fails
			} else {
				log.Printf("Successfully sent contract rejection notification to buyer for contract ID: %d", req.ContractID)
			}
		} else {
			log.Printf("Buyer rejection notification already sent for contract ID: %d, skipping", req.ContractID)
		}

		// Send cancellation notifications to both parties
		// Get organization names for both users
		var sellerOrgName, buyerOrgName string
		err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", sellerID).Scan(&sellerOrgName)
		if err != nil {
			log.Printf("Warning: Failed to get seller organization name: %v", err)
		}
		err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", buyerID).Scan(&buyerOrgName)
		if err != nil {
			log.Printf("Warning: Failed to get buyer organization name: %v", err)
		}

		// Send cancellation notification to seller (action taker)
		sellerCancellationNotification := &notification.ContractCancellationNotification{
			UserID:              sellerUser.UserID,
			Email:               sellerUser.Email,
			FirstName:           sellerUser.FirstName,
			LastName:            sellerUser.LastName,
			ContractID:          req.ContractID,
			ProductName:         productEnName,
			Quantity:            quantity,
			UnitOfMeasure:       uomName,
			Price:               price,
			Currency:            currencyCode,
			DeliveryTerms:       deliveryTermName,
			CounterpartyName:    fmt.Sprintf("%s %s", buyerUser.FirstName, buyerUser.LastName),
			CounterpartyCompany: buyerOrgName,
			Role:                "seller",
			Language:            sellerUser.Language,
			BaseURL:             os.Getenv("BASE_URL"),
			CancellationReason:  "The contract was rejected by you.",
		}

		err = notification.SendContractCancellationNotification(sellerCancellationNotification)
		if err != nil {
			log.Printf("Warning: Failed to send contract cancellation notification to seller: %v", err)
		} else {
			log.Printf("Successfully sent contract cancellation notification to seller for contract ID: %d", req.ContractID)
		}

		// Send cancellation notification to buyer (counterparty)
		buyerCancellationNotification := &notification.ContractCancellationNotification{
			UserID:              buyerUser.UserID,
			Email:               buyerUser.Email,
			FirstName:           buyerUser.FirstName,
			LastName:            buyerUser.LastName,
			ContractID:          req.ContractID,
			ProductName:         productEnName,
			Quantity:            quantity,
			UnitOfMeasure:       uomName,
			Price:               price,
			Currency:            currencyCode,
			DeliveryTerms:       deliveryTermName,
			CounterpartyName:    fmt.Sprintf("%s %s", sellerUser.FirstName, sellerUser.LastName),
			CounterpartyCompany: sellerOrgName,
			Role:                "buyer",
			Language:            buyerUser.Language,
			BaseURL:             os.Getenv("BASE_URL"),
			CancellationReason:  "The contract was rejected by the counterparty.",
		}

		err = notification.SendContractCancellationNotification(buyerCancellationNotification)
		if err != nil {
			log.Printf("Warning: Failed to send contract cancellation notification to buyer: %v", err)
		} else {
			log.Printf("Successfully sent contract cancellation notification to buyer for contract ID: %d", req.ContractID)
		}
	} else if req.UserID == buyerID {
		// Buyer rejected, send notifications to both buyer and seller

		// Send notification to the buyer (action taker) if not already sent
		if actionTakerNotificationCount == 0 || currentContractStatus != 22 {
			// Either this is a new rejection or no notification has been sent yet
			actionTakenBuyer := "rejected"
			if buyerUser.Language == "th" {
				actionTakenBuyer = "ปฏิเสธ"
			}

			buyerActionNotification := &notification.ContractStatusUpdateNotification{
				UserID:             buyerUser.UserID,
				Email:              buyerUser.Email,
				FirstName:          buyerUser.FirstName,
				LastName:           buyerUser.LastName,
				Username:           buyerUser.Username,
				ContractID:         req.ContractID,
				Role:               "buyer",
				CounterpartyName:   fmt.Sprintf("%s %s", sellerUser.FirstName, sellerUser.LastName),
				ProductName:        productEnName,
				Quantity:           quantity,
				UnitOfMeasure:      uomName,
				Price:              price,
				Currency:           currencyCode,
				DeliveryTerms:      deliveryTermName,
				ActionTaken:        actionTakenBuyer,
				IsActionTaker:      true, // Buyer took the action
				ContractDetailsURL: fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), req.ContractID, buyerUser.Language),
				Language:           buyerUser.Language,
				BaseURL:            os.Getenv("BASE_URL"),
			}

			err = notification.SendContractStatusUpdateNotification(buyerActionNotification)
			if err != nil {
				log.Printf("Warning: Failed to send contract rejection notification to buyer (action taker): %v", err)
				// Continue execution even if notification fails
			} else {
				log.Printf("Successfully sent contract rejection notification to buyer (action taker) for contract ID: %d", req.ContractID)
			}
		} else {
			log.Printf("Buyer rejection notification already sent for contract ID: %d, skipping", req.ContractID)
		}

		// Send notification to the seller (counterparty) if not already sent
		if counterpartyNotificationCount == 0 || currentContractStatus != 22 {
			// Either this is a new rejection or no notification has been sent yet
			actionTakenSeller := "rejected"
			if sellerUser.Language == "th" {
				actionTakenSeller = "ปฏิเสธ"
			}

			sellerNotification := &notification.ContractStatusUpdateNotification{
				UserID:             sellerUser.UserID,
				Email:              sellerUser.Email,
				FirstName:          sellerUser.FirstName,
				LastName:           sellerUser.LastName,
				Username:           sellerUser.Username,
				ContractID:         req.ContractID,
				Role:               "seller",
				CounterpartyName:   fmt.Sprintf("%s %s", buyerUser.FirstName, buyerUser.LastName),
				ProductName:        productEnName,
				Quantity:           quantity,
				UnitOfMeasure:      uomName,
				Price:              price,
				Currency:           currencyCode,
				DeliveryTerms:      deliveryTermName,
				ActionTaken:        actionTakenSeller,
				IsActionTaker:      false, // Seller is receiving notification about buyer's action
				ContractDetailsURL: fmt.Sprintf("%s/contract/%d?lang=%s", os.Getenv("BASE_URL"), req.ContractID, sellerUser.Language),
				Language:           sellerUser.Language,
				BaseURL:            os.Getenv("BASE_URL"),
			}

			err = notification.SendContractStatusUpdateNotification(sellerNotification)
			if err != nil {
				log.Printf("Warning: Failed to send contract rejection notification to seller: %v", err)
				// Continue execution even if notification fails
			} else {
				log.Printf("Successfully sent contract rejection notification to seller for contract ID: %d", req.ContractID)
			}
		} else {
			log.Printf("Seller rejection notification already sent for contract ID: %d, skipping", req.ContractID)
		}

		// Send cancellation notifications to both parties
		// Get organization names for both users
		var sellerOrgName, buyerOrgName string
		err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", sellerID).Scan(&sellerOrgName)
		if err != nil {
			log.Printf("Warning: Failed to get seller organization name: %v", err)
		}
		err = db.QueryRow("SELECT organization_name FROM users WHERE id = $1", buyerID).Scan(&buyerOrgName)
		if err != nil {
			log.Printf("Warning: Failed to get buyer organization name: %v", err)
		}

		// Send cancellation notification to buyer (action taker)
		buyerCancellationNotification := &notification.ContractCancellationNotification{
			UserID:              buyerUser.UserID,
			Email:               buyerUser.Email,
			FirstName:           buyerUser.FirstName,
			LastName:            buyerUser.LastName,
			ContractID:          req.ContractID,
			ProductName:         productEnName,
			Quantity:            quantity,
			UnitOfMeasure:       uomName,
			Price:               price,
			Currency:            currencyCode,
			DeliveryTerms:       deliveryTermName,
			CounterpartyName:    fmt.Sprintf("%s %s", sellerUser.FirstName, sellerUser.LastName),
			CounterpartyCompany: sellerOrgName,
			Role:                "buyer",
			Language:            buyerUser.Language,
			BaseURL:             os.Getenv("BASE_URL"),
		}

		// Set cancellation reason
		buyerCancellationNotification.CancellationReason = "The contract was rejected by you."

		err = notification.SendContractCancellationNotification(buyerCancellationNotification)
		if err != nil {
			log.Printf("Warning: Failed to send contract cancellation notification to buyer: %v", err)
		} else {
			log.Printf("Successfully sent contract cancellation notification to buyer for contract ID: %d", req.ContractID)
		}

		// Send cancellation notification to seller (counterparty)
		sellerCancellationNotification := &notification.ContractCancellationNotification{
			UserID:              sellerUser.UserID,
			Email:               sellerUser.Email,
			FirstName:           sellerUser.FirstName,
			LastName:            sellerUser.LastName,
			ContractID:          req.ContractID,
			ProductName:         productEnName,
			Quantity:            quantity,
			UnitOfMeasure:       uomName,
			Price:               price,
			Currency:            currencyCode,
			DeliveryTerms:       deliveryTermName,
			CounterpartyName:    fmt.Sprintf("%s %s", buyerUser.FirstName, buyerUser.LastName),
			CounterpartyCompany: buyerOrgName,
			Role:                "seller",
			Language:            sellerUser.Language,
			BaseURL:             os.Getenv("BASE_URL"),
			CancellationReason:  "The contract was rejected by the counterparty.",
		}

		err = notification.SendContractCancellationNotification(sellerCancellationNotification)
		if err != nil {
			log.Printf("Warning: Failed to send contract cancellation notification to seller: %v", err)
		} else {
			log.Printf("Successfully sent contract cancellation notification to seller for contract ID: %d", req.ContractID)
		}
	}

	// Determine language from referer URL
	language := "en" // Default to English
	referer := r.Header.Get("Referer")
	if strings.Contains(referer, "lang=th") || strings.Contains(referer, "/th/") {
		language = "th"
	}

	// Send response based on request format and language
	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("Content-Type", "text/html")

		if language == "th" {
			// Thai success message
			fmt.Fprint(w, `
				<dialog id="status-dialog">
					<p>คุณได้ปฏิเสธสัญญาเรียบร้อยแล้ว</p>
					<form method="dialog">
						<button id="confirm-button" type="submit">ตกลง</button>
					</form>
				</dialog>
				<script>
					const dialog = document.getElementById('status-dialog');
					dialog.showModal();
					dialog.addEventListener('close', () => {
						location.reload();
					});
				</script>
			`)
		} else {
			// English success message
			fmt.Fprint(w, `
				<dialog id="status-dialog">
					<p>You have successfully rejected the contract</p>
					<form method="dialog">
						<button id="confirm-button" type="submit">OK</button>
					</form>
				</dialog>
				<script>
					const dialog = document.getElementById('status-dialog');
					dialog.showModal();
					dialog.addEventListener('close', () => {
						location.reload();
					});
				</script>
			`)
		}
		return
	} else {
		w.Header().Set("Content-Type", "application/json")

		// Different message based on language
		message := "Contract rejected successfully"
		if language == "th" {
			message = "ปฏิเสธสัญญาเรียบร้อยแล้ว"
		}

		json.NewEncoder(w).Encode(map[string]interface{}{
			"contract_status": 22, // Rejected status
			"message":         message,
		})
	}
}

// FetchUserAndLocationDetails retrieves user and address details from the database

func FetchUserAndLocationDetails(db *sql.DB, userID sql.NullInt64) (User, Address, error) {
	if !userID.Valid {
		log.Println("UserID is NULL, skipping fetch")
		return User{}, Address{}, nil
	}

	var user User
	var address Address

	log.Printf("Fetching user details for userID: %d", userID.Int64)

	query := "SELECT email, phone FROM users WHERE id = $1"
	log.Printf("Executing Query: %s with ID: %d", query, userID.Int64)

	row := db.QueryRow(query, userID.Int64)
	err := row.Scan(&user.Email, &user.Phone)
	if err != nil {
		log.Printf("Error fetching user details for userID %d: %v", userID.Int64, err)
		return User{}, Address{}, err
	}

	log.Println("Fetching address details")

	query = "SELECT address_type_id, branch_number, country_id, province_id, district_id, subdistrict_id, street, address, postal_code FROM addresses WHERE user_id = $1"
	log.Printf("Executing Query: %s with userID: %d", query, userID.Int64)

	row = db.QueryRow(query, userID.Int64)
	err = row.Scan(&address.AddressTypeID, &address.BranchNumber, &address.CountryID, &address.ProvinceID, &address.DistrictID, &address.SubdistrictID, &address.Street, &address.Address, &address.PostalCode)
	if err != nil {
		log.Printf("Error fetching address details for userID %d: %v", userID.Int64, err)
		return User{}, Address{}, err
	}

	log.Println("Fetching location details")

	if address.ProvinceID.Valid {
		log.Printf("Fetching Province Name for ID: %d", address.ProvinceID.Int64)
		err = db.QueryRow("SELECT en_name, th_name FROM provinces WHERE id = $1", address.ProvinceID.Int64).Scan(&address.ProvinceEN, &address.ProvinceTH)
		if err != nil {
			log.Printf("Error fetching province: %v", err)
		}
	}
	if address.DistrictID.Valid {
		log.Printf("Fetching District Name for ID: %d", address.DistrictID.Int64)
		err = db.QueryRow("SELECT en_name, th_name FROM districts WHERE id = $1", address.DistrictID.Int64).Scan(&address.DistrictEN, &address.DistrictTH)
		if err != nil {
			log.Printf("Error fetching district: %v", err)
		}
	}
	if address.SubdistrictID.Valid {
		log.Printf("Fetching Subdistrict Name for ID: %d", address.SubdistrictID.Int64)
		err = db.QueryRow("SELECT en_name, th_name FROM subdistricts WHERE id = $1", address.SubdistrictID.Int64).Scan(&address.SubdistrictEN, &address.SubdistrictTH)
		if err != nil {
			log.Printf("Error fetching subdistrict: %v", err)
		}
	}
	if address.CountryID.Valid {
		log.Printf("Fetching Country Name for ID: %d", address.CountryID.Int64)
		err = db.QueryRow("SELECT en_name, th_name FROM countries WHERE id = $1", address.CountryID.Int64).Scan(&address.CountryEN, &address.CountryTH)
		if err != nil {
			log.Printf("Error fetching country: %v", err)
		}
	}

	log.Printf("User and location details fetched for ID: %d", userID.Int64)
	return user, address, nil
}
