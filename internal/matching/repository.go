package matching

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/thongsoi/biomassx/database"
	"github.com/thongsoi/biomassx/internal/notification"
)

// เพิ่มฟังก์ชันสำหรับอัปเดตข้อมูลใน table matchings
func updateOrder(db *sql.DB, orderID int, quantity float64, statusID int) error {
	// เริ่ม Transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	// อัปเดต table matchings
	matchingQuery := `
		UPDATE matchings
		SET quantity = $1, status_id = $2, updated_at = NOW()
		WHERE id = $3
	`
	_, err = tx.Exec(matchingQuery, quantity, statusID, orderID)
	if err != nil {
		tx.Rollback() // ยกเลิกการเปลี่ยนแปลง
		return fmt.Errorf("failed to update matchings: %w", err)
	}

	// อัปเดต table orders
	orderQuery := `
		UPDATE orders
		SET status_id = $1, updated_at = NOW()
		WHERE id = $2
	`
	_, err = tx.Exec(orderQuery, statusID, orderID)
	if err != nil {
		tx.Rollback() // ยกเลิกการเปลี่ยนแปลง
		return fmt.Errorf("failed to update orders: %w", err)
	}

	// Commit การเปลี่ยนแปลง
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

func insertContract(db *sql.DB, sellerID, buyerID, qualityID int, price float64, currencyID, uomID, packingID, paymentTermID, deliveryTermID, sellerCountryID, buyerCountryID int, volume float64, buyer Order, seller Order) error {
	var provinceID, districtID, subDistrictID int
	var startDeliveryDate, finishDeliveryDate time.Time
	var sellerPOL, sellerPOD, buyerPOL, buyerPOD sql.NullInt64
	var contractID int64

	// เลือกค่าที่ใช้สำหรับ delivery
	switch deliveryTermID {
	case 5: // ใช้ข้อมูลจาก buyer
		provinceID = buyer.ProvinceID
		districtID = buyer.DistrictID
		subDistrictID = buyer.SubDistrictID
		startDeliveryDate = buyer.FirstDeliveryDate
		finishDeliveryDate = buyer.LastDeliveryDate
	case 8, 9: // ใช้ข้อมูลจาก seller
		provinceID = seller.ProvinceID
		districtID = seller.DistrictID
		subDistrictID = seller.SubDistrictID
		startDeliveryDate = seller.FirstDeliveryDate
		finishDeliveryDate = seller.LastDeliveryDate
	default:
		if seller.PortOfLoadingID > 0 {
			sellerPOL = sql.NullInt64{Int64: int64(seller.PortOfLoadingID), Valid: true}
		}
		if seller.PortOfDischargeID > 0 {
			sellerPOD = sql.NullInt64{Int64: int64(seller.PortOfDischargeID), Valid: true}
		}
		if buyer.PortOfLoadingID > 0 {
			buyerPOL = sql.NullInt64{Int64: int64(buyer.PortOfLoadingID), Valid: true}
		}
		if buyer.PortOfDischargeID > 0 {
			buyerPOD = sql.NullInt64{Int64: int64(buyer.PortOfDischargeID), Valid: true}
		}
	}

	query := `
	INSERT INTO contracts (
		contract_date, seller_matching_id, buyer_matching_id,
		marketspace_id, market_id, submarket_id, contract_type_id, product_id,
		quality_id, price, currency_id, uom_id, packing_id,
		payment_term_id, delivery_term_id, quantity,
		delivery_status_id, payment_status_id,
		seller_confirmation_status_id, buyer_confirmation_status_id,
		contract_status_id, place_of_delivery_province, place_of_delivery_district, place_of_delivery_subdistrict,
		start_delivery_date, finish_delivery_date,
		seller_port_of_loading_id, seller_port_of_discharge_id,
		buyer_port_of_loading_id, buyer_port_of_discharge_id,
		seller_country_id, buyer_country_id,
		seller_remark, buyer_remark
	) VALUES (
		NOW(), $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15,
		33, 28, 19, 19, 19, $16, $17, $18, $19, $20,
		$21, $22, $23, $24, $25, $26,
		$27, $28
	) RETURNING id
	`

	err := db.QueryRow(query, sellerID, buyerID,
		seller.MarketspaceID, seller.MarketID, seller.SubMarketID, seller.ContractTypeID, seller.ProductID,
		qualityID, price, currencyID, uomID, packingID,
		paymentTermID, deliveryTermID, volume,
		provinceID, districtID, subDistrictID, startDeliveryDate, finishDeliveryDate,
		sellerPOL, sellerPOD, buyerPOL, buyerPOD, sellerCountryID, buyerCountryID,
		seller.Remark.String, buyer.Remark.String,
	).Scan(&contractID)

	if err != nil {
		return fmt.Errorf("failed to insert contract: %w", err)
	}

	// First, check if order submission notifications have been sent
	// If not, we'll delay the matching notifications
	var buyerNotificationCount, sellerNotificationCount int

	// Check for buyer's order submission notification
	err = db.QueryRow(`
		SELECT COUNT(*)
		FROM notifications n
		JOIN notification_types nt ON n.notification_type_id = nt.id
		WHERE nt.name = 'order_submission'
		AND n.user_id = $1
		AND n.created_at > NOW() - INTERVAL '5 minutes'
	`, buyer.UserID).Scan(&buyerNotificationCount)

	if err != nil {
		log.Printf("Warning: Failed to check for buyer's order submission notification: %v", err)
	}

	// Check for seller's order submission notification
	err = db.QueryRow(`
		SELECT COUNT(*)
		FROM notifications n
		JOIN notification_types nt ON n.notification_type_id = nt.id
		WHERE nt.name = 'order_submission'
		AND n.user_id = $1
		AND n.created_at > NOW() - INTERVAL '5 minutes'
	`, seller.UserID).Scan(&sellerNotificationCount)

	if err != nil {
		log.Printf("Warning: Failed to check for seller's order submission notification: %v", err)
	}

	// Check if matching notifications have already been sent for this specific contract
	var matchingNotificationCount int
	err = db.QueryRow(`
		SELECT COUNT(*)
		FROM notifications n
		JOIN notification_types nt ON n.notification_type_id = nt.id
		WHERE nt.name = 'matching'
		AND n.body LIKE '%' || $1 || '%'
		AND n.created_at > NOW() - INTERVAL '1 minute'
	`, contractID).Scan(&matchingNotificationCount)

	if err != nil {
		log.Printf("Warning: Failed to check for existing matching notifications: %v", err)
	}

	// Only send matching notifications if they haven't been sent for this contract yet
	if matchingNotificationCount == 0 {
		// Create a function to send matching notifications after ensuring order submission notifications are sent
		sendMatchingNotificationsWhenReady := func() {
			// Wait for order submission notifications to be sent
			maxRetries := 10
			retryDelay := 500 * time.Millisecond

			for i := 0; i < maxRetries; i++ {
				// Check if both users have received order submission notifications
				var buyerCount, sellerCount int

				// Check buyer notification
				err := db.QueryRow(`
					SELECT COUNT(*)
					FROM notifications n
					JOIN notification_types nt ON n.notification_type_id = nt.id
					WHERE nt.name = 'order_submission'
					AND n.user_id = $1
					AND n.created_at > NOW() - INTERVAL '5 minutes'
					AND n.status = 'sent'
				`, buyer.UserID).Scan(&buyerCount)

				if err != nil {
					log.Printf("Warning: Failed to check for buyer's order submission notification: %v", err)
				}

				// Check seller notification
				err = db.QueryRow(`
					SELECT COUNT(*)
					FROM notifications n
					JOIN notification_types nt ON n.notification_type_id = nt.id
					WHERE nt.name = 'order_submission'
					AND n.user_id = $1
					AND n.created_at > NOW() - INTERVAL '5 minutes'
					AND n.status = 'sent'
				`, seller.UserID).Scan(&sellerCount)

				if err != nil {
					log.Printf("Warning: Failed to check for seller's order submission notification: %v", err)
				}

				// If both notifications have been sent, send matching notifications
				if buyerCount > 0 && sellerCount > 0 {
					log.Printf("Both order submission notifications have been sent, sending matching notifications")
					// Pass the language from the orders to the matching notifications
					err := sendMatchingNotifications(db, contractID, buyer, seller, volume, price, currencyID, uomID, deliveryTermID)
					if err != nil {
						log.Printf("Warning: Failed to send matching notifications: %v", err)
					}
					return
				}

				// If we've reached the maximum number of retries, send matching notifications anyway
				if i == maxRetries-1 {
					log.Printf("Maximum retries reached, sending matching notifications anyway")
					// Pass the language from the orders to the matching notifications
					err := sendMatchingNotifications(db, contractID, buyer, seller, volume, price, currencyID, uomID, deliveryTermID)
					if err != nil {
						log.Printf("Warning: Failed to send matching notifications: %v", err)
					}
					return
				}

				// Wait before checking again
				time.Sleep(retryDelay)
			}
		}

		// Start the process in a goroutine to avoid blocking
		go sendMatchingNotificationsWhenReady()
	} else {
		log.Printf("Matching notifications already sent for contract ID: %d, skipping", contractID)
	}

	return nil
}

// formatDeliveryLocation formats the delivery location based on the delivery term and language
func formatDeliveryLocation(buyer Order, seller Order, deliveryTermID int, language ...string) string {
	var location string
	// Default to English if no language is specified
	lang := "en"
	if len(language) > 0 && language[0] == "th" {
		lang = "th"
	}

	switch deliveryTermID {
	case 5: // Local delivery to buyer's location
		if buyer.ProvinceID > 0 {
			parts := []string{}

			// Get province, district, subdistrict names from database
			db := database.GetDB()

			var provinceName, districtName, subdistrictName string

			// Get province name
			if buyer.ProvinceID > 0 {
				var query string
				if lang == "th" {
					query = "SELECT th_name FROM provinces WHERE id = $1"
				} else {
					query = "SELECT en_name FROM provinces WHERE id = $1"
				}
				err := db.QueryRow(query, buyer.ProvinceID).Scan(&provinceName)
				if err == nil && provinceName != "" {
					parts = append(parts, provinceName)
				}
			}

			// Get district name
			if buyer.DistrictID > 0 {
				var query string
				if lang == "th" {
					query = "SELECT th_name FROM districts WHERE id = $1"
				} else {
					query = "SELECT en_name FROM districts WHERE id = $1"
				}
				err := db.QueryRow(query, buyer.DistrictID).Scan(&districtName)
				if err == nil && districtName != "" {
					parts = append(parts, districtName)
				}
			}

			// Get subdistrict name
			if buyer.SubDistrictID > 0 {
				var query string
				if lang == "th" {
					query = "SELECT th_name FROM subdistricts WHERE id = $1"
				} else {
					query = "SELECT en_name FROM subdistricts WHERE id = $1"
				}
				err := db.QueryRow(query, buyer.SubDistrictID).Scan(&subdistrictName)
				if err == nil && subdistrictName != "" {
					parts = append(parts, subdistrictName)
				}
			}

			location = strings.Join(parts, ", ")
		}
	case 8, 9: // Local delivery to seller's location
		if seller.ProvinceID > 0 {
			parts := []string{}

			// Get province, district, subdistrict names from database
			db := database.GetDB()

			var provinceName, districtName, subdistrictName string

			// Get province name
			if seller.ProvinceID > 0 {
				var query string
				if lang == "th" {
					query = "SELECT th_name FROM provinces WHERE id = $1"
				} else {
					query = "SELECT en_name FROM provinces WHERE id = $1"
				}
				err := db.QueryRow(query, seller.ProvinceID).Scan(&provinceName)
				if err == nil && provinceName != "" {
					parts = append(parts, provinceName)
				}
			}

			// Get district name
			if seller.DistrictID > 0 {
				var query string
				if lang == "th" {
					query = "SELECT th_name FROM districts WHERE id = $1"
				} else {
					query = "SELECT en_name FROM districts WHERE id = $1"
				}
				err := db.QueryRow(query, seller.DistrictID).Scan(&districtName)
				if err == nil && districtName != "" {
					parts = append(parts, districtName)
				}
			}

			// Get subdistrict name
			if seller.SubDistrictID > 0 {
				var query string
				if lang == "th" {
					query = "SELECT th_name FROM subdistricts WHERE id = $1"
				} else {
					query = "SELECT en_name FROM subdistricts WHERE id = $1"
				}
				err := db.QueryRow(query, seller.SubDistrictID).Scan(&subdistrictName)
				if err == nil && subdistrictName != "" {
					parts = append(parts, subdistrictName)
				}
			}

			location = strings.Join(parts, ", ")
		}
	default: // International delivery
		// For international delivery, use port information
		db := database.GetDB()

		if seller.PortOfLoadingID > 0 {
			var portName string
			var query string
			if lang == "th" {
				query = "SELECT th_name FROM ports WHERE id = $1"
				err := db.QueryRow(query, seller.PortOfLoadingID).Scan(&portName)
				if err == nil && portName != "" {
					location = "ท่าเรือต้นทาง: " + portName
				}
			} else {
				query = "SELECT en_name FROM ports WHERE id = $1"
				err := db.QueryRow(query, seller.PortOfLoadingID).Scan(&portName)
				if err == nil && portName != "" {
					location = "Port of Loading: " + portName
				}
			}
		}

		if buyer.PortOfDischargeID > 0 {
			var portName string
			var query string
			if lang == "th" {
				query = "SELECT th_name FROM ports WHERE id = $1"
				err := db.QueryRow(query, buyer.PortOfDischargeID).Scan(&portName)
				if err == nil && portName != "" {
					if location != "" {
						location += ", ท่าเรือปลายทาง: " + portName
					} else {
						location = "ท่าเรือปลายทาง: " + portName
					}
				}
			} else {
				query = "SELECT en_name FROM ports WHERE id = $1"
				err := db.QueryRow(query, buyer.PortOfDischargeID).Scan(&portName)
				if err == nil && portName != "" {
					if location != "" {
						location += ", Port of Discharge: " + portName
					} else {
						location = "Port of Discharge: " + portName
					}
				}
			}
		}
	}

	return location
}

// containsThaiCharacters checks if a string contains any Thai characters
func containsThaiCharacters(s string) bool {
	// Thai Unicode range: U+0E00 to U+0E7F
	for _, r := range s {
		if r >= 0x0E00 && r <= 0x0E7F {
			return true
		}
	}
	return false
}

// determineUserLanguage tries to determine the user's language preference
func determineUserLanguage(db *sql.DB, userID int) string {
	// Default language
	language := "en"

	// First, check if we can get the language from the most recent order
	// This is the most reliable way to determine the user's current language preference
	orderQuery := `
		SELECT language
		FROM orders
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT 1
	`

	var orderLanguage sql.NullString
	err := db.QueryRow(orderQuery, userID).Scan(&orderLanguage)
	if err == nil && orderLanguage.Valid && orderLanguage.String != "" {
		language = orderLanguage.String
		log.Printf("Using language from most recent order for user %d: %s", userID, language)
		return language
	}

	// If we couldn't determine from orders, check recent order submission notifications
	recentQuery := `
		SELECT n.subject, n.body
		FROM notifications n
		JOIN notification_types nt ON n.notification_type_id = nt.id
		WHERE n.user_id = $1
		AND nt.name = 'order_submission'
		AND n.created_at > NOW() - INTERVAL '5 minutes'
		ORDER BY n.created_at DESC
		LIMIT 1
	`

	var recentSubject, recentBody string
	err = db.QueryRow(recentQuery, userID).Scan(&recentSubject, &recentBody)
	if err == nil {
		// Check if the recent notification is in Thai by looking for Thai characters
		// or specific Thai phrases in both subject and body
		if strings.Contains(recentSubject, "ยืนยันการส่งคำสั่ง") ||
			strings.Contains(recentSubject, "ส่งคำสั่ง") ||
			strings.Contains(recentSubject, "การยืนยัน") ||
			strings.Contains(recentBody, "ยืนยันการส่งคำสั่ง") ||
			strings.Contains(recentBody, "ส่งคำสั่ง") ||
			strings.Contains(recentBody, "การยืนยัน") ||
			// Check for any Thai character in the subject or body
			containsThaiCharacters(recentSubject) ||
			containsThaiCharacters(recentBody) {
			language = "th"
			log.Printf("Using recent order submission language for user %d: %s", userID, language)
			return language
		} else {
			language = "en"
			log.Printf("Using recent order submission language for user %d: %s", userID, language)
			return language
		}
	}

	// If we couldn't determine from recent order submission, check other notifications
	query := `
		SELECT n.subject, n.body
		FROM notifications n
		JOIN notification_types nt ON n.notification_type_id = nt.id
		WHERE n.user_id = $1
		ORDER BY n.created_at DESC
		LIMIT 5
	`

	rows, err := db.Query(query, userID)
	if err != nil {
		log.Printf("Error querying notifications for user %d: %v", userID, err)
		return language
	}
	defer rows.Close()

	// Check subjects and bodies for Thai language indicators
	for rows.Next() {
		var subject, body string
		if err := rows.Scan(&subject, &body); err != nil {
			continue
		}

		// Check for Thai language indicators in the subject or body
		if strings.Contains(subject, "ยินดีต้อนรับ") ||
			strings.Contains(subject, "รีเซ็ตรหัสผ่าน") ||
			strings.Contains(subject, "ยืนยันการส่งคำสั่ง") ||
			strings.Contains(subject, "กรุณากรอกโปรไฟล์") ||
			strings.Contains(subject, "การอัปเดตสถานะสัญญา") ||
			strings.Contains(subject, "พบการจับคู่") ||
			strings.Contains(subject, "ส่งคำสั่ง") ||
			strings.Contains(subject, "การยืนยัน") ||
			strings.Contains(body, "ยินดีต้อนรับ") ||
			strings.Contains(body, "รีเซ็ตรหัสผ่าน") ||
			strings.Contains(body, "ยืนยันการส่งคำสั่ง") ||
			strings.Contains(body, "กรุณากรอกโปรไฟล์") ||
			strings.Contains(body, "การอัปเดตสถานะสัญญา") ||
			strings.Contains(body, "พบการจับคู่") ||
			strings.Contains(body, "ส่งคำสั่ง") ||
			strings.Contains(body, "การยืนยัน") ||
			// Check for any Thai character in the subject or body
			containsThaiCharacters(subject) ||
			containsThaiCharacters(body) {
			language = "th"
			log.Printf("Detected Thai language from notification for user %d", userID)
			break
		}
	}

	log.Printf("Determined language for user %d: %s", userID, language)
	return language
}

// sendMatchingNotifications sends notifications to both buyer and seller about the match
func sendMatchingNotifications(db *sql.DB, contractID int64, buyer Order, seller Order, quantity float64, price float64, currencyID, uomID, deliveryTermID int) error {
	// Get buyer and seller user details
	var buyerUser struct {
		UserID           int64
		Email            string
		FirstName        string
		LastName         string
		Username         string
		Language         string
		Phone            sql.NullString
		OrganizationName sql.NullString
		CountryID        sql.NullInt64
		ProvinceID       sql.NullInt64
		DistrictID       sql.NullInt64
		SubdistrictID    sql.NullInt64
		CountryName      sql.NullString
		ProvinceName     sql.NullString
		DistrictName     sql.NullString
		SubdistrictName  sql.NullString
		Address          sql.NullString
	}

	var sellerUser struct {
		UserID           int64
		Email            string
		FirstName        string
		LastName         string
		Username         string
		Language         string
		Phone            sql.NullString
		OrganizationName sql.NullString
		CountryID        sql.NullInt64
		ProvinceID       sql.NullInt64
		DistrictID       sql.NullInt64
		SubdistrictID    sql.NullInt64
		CountryName      sql.NullString
		ProvinceName     sql.NullString
		DistrictName     sql.NullString
		SubdistrictName  sql.NullString
		Address          sql.NullString
	}

	// Get product name
	var productEnName, productThName string
	err := db.QueryRow("SELECT en_name, th_name FROM products WHERE id = $1", buyer.ProductID).Scan(&productEnName, &productThName)
	if err != nil {
		return fmt.Errorf("failed to get product name: %w", err)
	}

	// Get currency name
	var currencyCode string
	err = db.QueryRow("SELECT code FROM currencies WHERE id = $1", currencyID).Scan(&currencyCode)
	if err != nil {
		return fmt.Errorf("failed to get currency code: %w", err)
	}

	// Get UOM name
	var uomEnName, uomThName string
	err = db.QueryRow("SELECT en_name, th_name FROM uoms WHERE id = $1", uomID).Scan(&uomEnName, &uomThName)
	if err != nil {
		return fmt.Errorf("failed to get UOM name: %w", err)
	}

	// Get delivery term name
	var deliveryTermEnName, deliveryTermThName string
	err = db.QueryRow("SELECT en_name, th_name FROM delivery_terms WHERE id = $1", deliveryTermID).Scan(&deliveryTermEnName, &deliveryTermThName)
	if err != nil {
		return fmt.Errorf("failed to get delivery term name: %w", err)
	}

	// Get buyer user details with extended information
	buyerQuery := `
		SELECT u.id, u.email, u.first_name, u.last_name, u.username, u.phone, u.organization_name,
		       a.country_id, a.province_id, a.district_id, a.subdistrict_id, a.address,
		       c.en_name AS country_name,
		       p.en_name AS province_name,
		       d.en_name AS district_name,
		       s.en_name AS subdistrict_name
		FROM users u
		LEFT JOIN addresses a ON u.id = a.user_id
		LEFT JOIN countries c ON a.country_id = c.id
		LEFT JOIN provinces p ON a.province_id = p.id
		LEFT JOIN districts d ON a.district_id = d.id
		LEFT JOIN subdistricts s ON a.subdistrict_id = s.id
		WHERE u.id = $1
		LIMIT 1
	`
	err = db.QueryRow(buyerQuery, buyer.UserID).Scan(
		&buyerUser.UserID, &buyerUser.Email, &buyerUser.FirstName, &buyerUser.LastName, &buyerUser.Username,
		&buyerUser.Phone, &buyerUser.OrganizationName, &buyerUser.CountryID, &buyerUser.ProvinceID,
		&buyerUser.DistrictID, &buyerUser.SubdistrictID, &buyerUser.Address, &buyerUser.CountryName,
		&buyerUser.ProvinceName, &buyerUser.DistrictName, &buyerUser.SubdistrictName)
	if err != nil {
		return fmt.Errorf("failed to get buyer user details: %w", err)
	}

	// Get seller user details with extended information
	sellerQuery := `
		SELECT u.id, u.email, u.first_name, u.last_name, u.username, u.phone, u.organization_name,
		       a.country_id, a.province_id, a.district_id, a.subdistrict_id, a.address,
		       c.en_name AS country_name,
		       p.en_name AS province_name,
		       d.en_name AS district_name,
		       s.en_name AS subdistrict_name
		FROM users u
		LEFT JOIN addresses a ON u.id = a.user_id
		LEFT JOIN countries c ON a.country_id = c.id
		LEFT JOIN provinces p ON a.province_id = p.id
		LEFT JOIN districts d ON a.district_id = d.id
		LEFT JOIN subdistricts s ON a.subdistrict_id = s.id
		WHERE u.id = $1
		LIMIT 1
	`
	err = db.QueryRow(sellerQuery, seller.UserID).Scan(
		&sellerUser.UserID, &sellerUser.Email, &sellerUser.FirstName, &sellerUser.LastName, &sellerUser.Username,
		&sellerUser.Phone, &sellerUser.OrganizationName, &sellerUser.CountryID, &sellerUser.ProvinceID,
		&sellerUser.DistrictID, &sellerUser.SubdistrictID, &sellerUser.Address, &sellerUser.CountryName,
		&sellerUser.ProvinceName, &sellerUser.DistrictName, &sellerUser.SubdistrictName)
	if err != nil {
		return fmt.Errorf("failed to get seller user details: %w", err)
	}

	// IMPORTANT: First check if the language is specified in the order form
	// This is the most reliable source of language preference
	if buyer.Language != "" {
		buyerUser.Language = buyer.Language
		log.Printf("Using language from order form for buyer: %s", buyerUser.Language)
	} else {
		// First, check the most recent session language
		sessionQuery := `
			SELECT data
			FROM sessions
			WHERE user_id = $1
			ORDER BY last_activity DESC
			LIMIT 1
		`
		var sessionData sql.NullString
		err := db.QueryRow(sessionQuery, buyer.UserID).Scan(&sessionData)
		if err == nil && sessionData.Valid {
			// Check if the session data contains language information
			if strings.Contains(sessionData.String, "lang=th") {
				buyerUser.Language = "th"
				log.Printf("Using language from recent session for buyer: %s", buyerUser.Language)
			} else if strings.Contains(sessionData.String, "lang=en") {
				buyerUser.Language = "en"
				log.Printf("Using language from recent session for buyer: %s", buyerUser.Language)
			}
		}

		// If we couldn't determine from session, use the determineUserLanguage function
		if buyerUser.Language == "" {
			buyerUser.Language = determineUserLanguage(db, buyer.UserID)
			log.Printf("Using determined language for buyer: %s", buyerUser.Language)
		}
	}

	if seller.Language != "" {
		sellerUser.Language = seller.Language
		log.Printf("Using language from order form for seller: %s", sellerUser.Language)
	} else {
		// First, check the most recent session language
		sessionQuery := `
			SELECT data
			FROM sessions
			WHERE user_id = $1
			ORDER BY last_activity DESC
			LIMIT 1
		`
		var sessionData sql.NullString
		err := db.QueryRow(sessionQuery, seller.UserID).Scan(&sessionData)
		if err == nil && sessionData.Valid {
			// Check if the session data contains language information
			if strings.Contains(sessionData.String, "lang=th") {
				sellerUser.Language = "th"
				log.Printf("Using language from recent session for seller: %s", sellerUser.Language)
			} else if strings.Contains(sessionData.String, "lang=en") {
				sellerUser.Language = "en"
				log.Printf("Using language from recent session for seller: %s", sellerUser.Language)
			}
		}

		// If we couldn't determine from session, use the determineUserLanguage function
		if sellerUser.Language == "" {
			sellerUser.Language = determineUserLanguage(db, seller.UserID)
			log.Printf("Using determined language for seller: %s", sellerUser.Language)
		}
	}

	// Calculate deadline date (3 days from now)
	deadlineDate := time.Now().AddDate(0, 0, 3).Format("2006-01-02")

	// Get localized product name, UOM, and delivery terms for buyer based on language
	var buyerProductName, buyerUnitOfMeasure, buyerDeliveryTerms string
	if buyerUser.Language == "th" {
		buyerProductName = productThName
		buyerUnitOfMeasure = uomThName
		buyerDeliveryTerms = deliveryTermThName
	} else {
		buyerProductName = productEnName
		buyerUnitOfMeasure = uomEnName
		buyerDeliveryTerms = deliveryTermEnName
	}

	// Check if this is a partial match for the buyer
	var buyerOriginalQuantity float64
	var buyerRemainingQuantity float64
	var buyerStatusID int
	var isBuyerPartialMatch bool

	// First, try to get the original quantity from the buyer's order
	buyerOriginalQuantity = buyer.Quantity

	// Get the current quantity and status from the database
	err = db.QueryRow("SELECT quantity, status_id FROM matchings WHERE id = $1", buyer.ID).Scan(&buyerRemainingQuantity, &buyerStatusID)
	if err != nil {
		log.Printf("Warning: Failed to get buyer order status: %v", err)
		// If we can't get the current quantity from the database, use the original quantity from the order
		if buyerOriginalQuantity > 0 {
			// For a new match, the remaining is what's left after this match
			buyerRemainingQuantity = buyerOriginalQuantity - quantity
			// Ensure remaining quantity is never negative
			if buyerRemainingQuantity < 0 {
				buyerRemainingQuantity = 0
			}
			isBuyerPartialMatch = buyerOriginalQuantity > quantity && buyerRemainingQuantity > 0
		}
	} else {
		// If we don't have the original quantity from the order, calculate it
		if buyerOriginalQuantity <= 0 {
			// For existing orders, we need to add the current remaining + this match quantity
			buyerOriginalQuantity = buyerRemainingQuantity + quantity
		}

		// Update the remaining quantity to be what's left after this match
		buyerRemainingQuantity = buyerOriginalQuantity - quantity
		// Ensure remaining quantity is never negative
		if buyerRemainingQuantity < 0 {
			buyerRemainingQuantity = 0
		}

		// It's a partial match if status is 14 (partially filled) or if there's quantity remaining
		isBuyerPartialMatch = (buyerStatusID == 14) || (buyerOriginalQuantity > quantity && buyerRemainingQuantity > 0)

		// Log the partial match information for debugging
		log.Printf("Buyer partial match check: originalQty=%f, matchedQty=%f, remainingQty=%f, statusID=%d, isPartialMatch=%v",
			buyerOriginalQuantity, quantity, buyerRemainingQuantity, buyerStatusID, isBuyerPartialMatch)
	}

	// Create notification data for buyer with enhanced information
	buyerNotification := &notification.MatchingNotification{
		UserID:                  buyerUser.UserID,
		Email:                   buyerUser.Email,
		FirstName:               buyerUser.FirstName,
		LastName:                buyerUser.LastName,
		Username:                buyerUser.Username,
		ContractID:              contractID,
		Role:                    "buyer",
		CounterpartyName:        fmt.Sprintf("%s %s", sellerUser.FirstName, sellerUser.LastName),
		CounterpartyCompany:     sellerUser.OrganizationName.String,
		CounterpartyPhone:       sellerUser.Phone.String,
		CounterpartyEmail:       sellerUser.Email,
		CounterpartyCountry:     sellerUser.CountryName.String,
		CounterpartyProvince:    sellerUser.ProvinceName.String,
		CounterpartyDistrict:    sellerUser.DistrictName.String,
		CounterpartySubdistrict: sellerUser.SubdistrictName.String,
		CounterpartyLocation:    sellerUser.Address.String,
		ProductName:             buyerProductName,
		Quantity:                quantity,
		UnitOfMeasure:           buyerUnitOfMeasure,
		Price:                   price,
		Currency:                currencyCode,
		IsPartialMatch:          isBuyerPartialMatch,
		OriginalQuantity:        buyerOriginalQuantity,
		RemainingQuantity:       buyerRemainingQuantity,
		DeliveryTerms:           buyerDeliveryTerms,
		DeliveryLocation:        formatDeliveryLocation(buyer, seller, deliveryTermID, buyerUser.Language),
		FirstDeliveryDate:       buyer.FirstDeliveryDate.Format("2006-01-02"),
		LastDeliveryDate:        buyer.LastDeliveryDate.Format("2006-01-02"),
		DeadlineDate:            deadlineDate,
		Language:                buyerUser.Language,
		BaseURL:                 os.Getenv("BASE_URL"),
	}

	// Get localized product name, UOM, and delivery terms for seller based on language
	var sellerProductName, sellerUnitOfMeasure, sellerDeliveryTerms string
	if sellerUser.Language == "th" {
		sellerProductName = productThName
		sellerUnitOfMeasure = uomThName
		sellerDeliveryTerms = deliveryTermThName
	} else {
		sellerProductName = productEnName
		sellerUnitOfMeasure = uomEnName
		sellerDeliveryTerms = deliveryTermEnName
	}

	// Check if this is a partial match for the seller
	var sellerOriginalQuantity float64
	var sellerRemainingQuantity float64
	var sellerStatusID int
	var isSellerPartialMatch bool

	// First, try to get the original quantity from the seller's order
	sellerOriginalQuantity = seller.Quantity

	// Get the current quantity and status from the database
	err = db.QueryRow("SELECT quantity, status_id FROM matchings WHERE id = $1", seller.ID).Scan(&sellerRemainingQuantity, &sellerStatusID)
	if err != nil {
		log.Printf("Warning: Failed to get seller order status: %v", err)
		// If we can't get the current quantity from the database, use the original quantity from the order
		if sellerOriginalQuantity > 0 {
			// For a new match, the remaining is what's left after this match
			sellerRemainingQuantity = sellerOriginalQuantity - quantity
			// Ensure remaining quantity is never negative
			if sellerRemainingQuantity < 0 {
				sellerRemainingQuantity = 0
			}
			isSellerPartialMatch = sellerOriginalQuantity > quantity && sellerRemainingQuantity > 0
		}
	} else {
		// If we don't have the original quantity from the order, calculate it
		if sellerOriginalQuantity <= 0 {
			// For existing orders, we need to add the current remaining + this match quantity
			sellerOriginalQuantity = sellerRemainingQuantity + quantity
		}

		// Update the remaining quantity to be what's left after this match
		sellerRemainingQuantity = sellerOriginalQuantity - quantity
		// Ensure remaining quantity is never negative
		if sellerRemainingQuantity < 0 {
			sellerRemainingQuantity = 0
		}

		// It's a partial match if status is 14 (partially filled) or if there's quantity remaining
		isSellerPartialMatch = (sellerStatusID == 14) || (sellerOriginalQuantity > quantity && sellerRemainingQuantity > 0)

		// Log the partial match information for debugging
		log.Printf("Seller partial match check: originalQty=%f, matchedQty=%f, remainingQty=%f, statusID=%d, isPartialMatch=%v",
			sellerOriginalQuantity, quantity, sellerRemainingQuantity, sellerStatusID, isSellerPartialMatch)
	}

	// Create notification data for seller with enhanced information
	sellerNotification := &notification.MatchingNotification{
		UserID:                  sellerUser.UserID,
		Email:                   sellerUser.Email,
		FirstName:               sellerUser.FirstName,
		LastName:                sellerUser.LastName,
		Username:                sellerUser.Username,
		ContractID:              contractID,
		Role:                    "seller",
		CounterpartyName:        fmt.Sprintf("%s %s", buyerUser.FirstName, buyerUser.LastName),
		CounterpartyCompany:     buyerUser.OrganizationName.String,
		CounterpartyPhone:       buyerUser.Phone.String,
		CounterpartyEmail:       buyerUser.Email,
		CounterpartyCountry:     buyerUser.CountryName.String,
		CounterpartyProvince:    buyerUser.ProvinceName.String,
		CounterpartyDistrict:    buyerUser.DistrictName.String,
		CounterpartySubdistrict: buyerUser.SubdistrictName.String,
		CounterpartyLocation:    buyerUser.Address.String,
		ProductName:             sellerProductName,
		Quantity:                quantity,
		UnitOfMeasure:           sellerUnitOfMeasure,
		Price:                   price,
		Currency:                currencyCode,
		IsPartialMatch:          isSellerPartialMatch,
		OriginalQuantity:        sellerOriginalQuantity,
		RemainingQuantity:       sellerRemainingQuantity,
		DeliveryTerms:           sellerDeliveryTerms,
		DeliveryLocation:        formatDeliveryLocation(buyer, seller, deliveryTermID, sellerUser.Language),
		FirstDeliveryDate:       seller.FirstDeliveryDate.Format("2006-01-02"),
		LastDeliveryDate:        seller.LastDeliveryDate.Format("2006-01-02"),
		DeadlineDate:            deadlineDate,
		Language:                sellerUser.Language,
		BaseURL:                 os.Getenv("BASE_URL"),
	}

	// Set contract details URL with language parameter
	buyerNotification.ContractDetailsURL = fmt.Sprintf("%s/contract/%d?lang=%s", buyerNotification.BaseURL, contractID, buyerUser.Language)
	sellerNotification.ContractDetailsURL = fmt.Sprintf("%s/contract/%d?lang=%s", sellerNotification.BaseURL, contractID, sellerUser.Language)

	// Log notification data for debugging
	log.Printf("Sending buyer matching notification: ContractID=%d, Quantity=%f, OriginalQuantity=%f, RemainingQuantity=%f, IsPartialMatch=%v",
		buyerNotification.ContractID, buyerNotification.Quantity, buyerNotification.OriginalQuantity, buyerNotification.RemainingQuantity, buyerNotification.IsPartialMatch)

	log.Printf("Sending seller matching notification: ContractID=%d, Quantity=%f, OriginalQuantity=%f, RemainingQuantity=%f, IsPartialMatch=%v",
		sellerNotification.ContractID, sellerNotification.Quantity, sellerNotification.OriginalQuantity, sellerNotification.RemainingQuantity, sellerNotification.IsPartialMatch)

	// Add a delay to ensure order submission notifications are processed first
	log.Printf("Adding delay before sending matching notifications for contract ID: %d", contractID)
	time.Sleep(notification.NotificationSequenceDelay)

	// Send notifications
	err = notification.SendMatchingNotification(buyerNotification)
	if err != nil {
		log.Printf("Warning: Failed to send matching notification to buyer: %v", err)
		// Continue to send seller notification even if buyer notification fails
	}

	// Add a small delay between buyer and seller notifications
	time.Sleep(1 * time.Second)

	err = notification.SendMatchingNotification(sellerNotification)
	if err != nil {
		return fmt.Errorf("failed to send matching notification to seller: %v", err)
	}

	return nil
}
