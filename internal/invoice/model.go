package invoice

import (
	"database/sql"
	"time"
)

// Invoice represents an invoice in the system
type Invoice struct {
	ID                int64           `json:"id"`
	InvoiceNumber     string          `json:"invoice_number"`
	ItemID            int64           `json:"item_id"`
	ContractID        int64           `json:"contract_id"`
	SellerID          int64           `json:"seller_id"`
	BuyerID           int64           `json:"buyer_id"`
	ContractValue     float64         `json:"contract_value"`
	ContractFee       sql.NullFloat64 `json:"contract_fee"`
	CurrencyID        int64           `json:"currency_id"`
	InvoiceDate       time.Time       `json:"invoice_date"`
	DueDate           time.Time       `json:"due_date"`
	Status            string          `json:"status"`
	StatusID          sql.NullInt64   `json:"status_id"`
	PaymentTermID     sql.NullInt64   `json:"payment_term_id"`
	BuyerPaidDate     sql.NullTime    `json:"buyer_paid_date"`
	SellerPaidDate    sql.NullTime    `json:"seller_paid_date"`
	PaymentSlipBuyer  sql.NullString  `json:"payment_slip_buyer"`
	PaymentSlipSeller sql.NullString  `json:"payment_slip_seller"`
	CreatedAt         time.Time       `json:"created_at"`
	UpdatedAt         sql.NullTime    `json:"updated_at"`

	// Additional fields from joins
	ItemEnName     string `json:"item_en_name"`
	ItemThName     string `json:"item_th_name"`
	SellerName     string `json:"seller_name"`
	BuyerName      string `json:"buyer_name"`
	CurrencyCode   string `json:"currency_code"`
	ContractNumber string `json:"contract_number"`

	// Optional fields for product invoices
	ProductName   string  `json:"product_name"`
	Quantity      float64 `json:"quantity"`
	UnitOfMeasure string  `json:"unit_of_measure"`

	// Deprecated fields kept for backward compatibility
	DatePaid        sql.NullTime   `json:"-"`
	PaymentSlipPath sql.NullString `json:"-"`

	// Fields for displaying other party's fee status on a fee invoice
	OtherPartyOriginalRole sql.NullString `json:"other_party_original_role"`  // "Buyer" or "Seller"
	OtherPartyFeeStatus    sql.NullString `json:"other_party_fee_status"`     // "paid", "proof_submitted", "pending"
	OtherPartyFeeProofPath sql.NullString `json:"other_party_fee_proof_path"` // Path to other party's fee proof
	OtherPartyName         sql.NullString `json:"other_party_name"`           // Name of the other party
}

// Item represents the type of item (formerly invoice type)
type Item struct {
	ID     int
	EnName string
	ThName string
}

// InvoiceStatus constants
const (
	InvoiceStatusDraft    = "draft"
	InvoiceStatusSent     = "sent"
	InvoiceStatusViewed   = "viewed"
	InvoiceStatusPaid     = "paid"
	InvoiceStatusOverdue  = "overdue"
	InvoiceStatusCanceled = "canceled"
	InvoiceStatusDisputed = "disputed"
)

// ItemID constants
const (
	ItemBrokerageFee  = 1 // Brokerage fee (ค่าธรรมเนียมแพลตฟอร์ม)
	ItemInspectionFee = 2 // Inspection fee (ค่าตรวจสอบ)
	ItemAnalysisFee   = 3 // Analysis fee (ค่าวิเคราะห์)
)

// PDFData contains all data needed to generate an invoice PDF
type PDFData struct {
	Invoice       Invoice
	SellerAddress string
	BuyerAddress  string
	Language      string
	PlatformName  string
	PlatformEmail string
	PlatformPhone string
	PlatformWeb   string
	LogoPath      string
}
