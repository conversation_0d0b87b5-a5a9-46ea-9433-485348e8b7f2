package invoice

import (
	"bytes"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/go-pdf/fpdf"
	"github.com/thongsoi/biomassx/internal/notification"
)

// Service handles business logic for invoices
type Service struct {
	repo *Repository
	db   *sql.DB
}

// NewService creates a new invoice service
func NewService(db *sql.DB) *Service {
	return &Service{
		repo: NewRepository(db),
		db:   db,
	}
}

// GenerateInvoicesForContract creates all necessary invoices when a contract is signed
func (s *Service) GenerateInvoicesForContract(contractID int64) error {
	log.Printf("Starting invoice generation for contract ID: %d", contractID)

	// First, ensure items exist
	_, err := s.db.Exec(`
		INSERT INTO items (id, en_name, th_name)
		VALUES
		(1, 'Brokerage fee', 'ค่าธรรมเนียมแพลตฟอร์ม'),
		(2, 'Inspection fee', 'ค่าบริการตรวจสอบ'),
		(3, 'Analysis fee', 'ค่าบริการวิเคราะห์')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		log.Printf("Warning: Failed to ensure items: %v", err)
	}

	// Get contract details with a direct query
	contractQuery := `
		SELECT c.id, c.price, c.quantity, c.currency_id, c.seller_matching_id, c.buyer_matching_id
		FROM contracts c
		WHERE c.id = $1
	`

	var price, quantity float64
	var currencyID int
	var sellerMatchingID, buyerMatchingID int64

	err = s.db.QueryRow(contractQuery, contractID).Scan(
		&contractID, &price, &quantity, &currencyID, &sellerMatchingID, &buyerMatchingID,
	)
	if err != nil {
		log.Printf("Error getting contract details: %v", err)
		return fmt.Errorf("failed to get contract details: %w", err)
	}

	// Get seller ID
	var sellerID int64
	err = s.db.QueryRow("SELECT user_id FROM matchings WHERE id = $1", sellerMatchingID).Scan(&sellerID)
	if err != nil {
		log.Printf("Warning: Failed to get seller ID from matchings: %v", err)
		// Try to get the user ID directly from the contract
		err = s.db.QueryRow("SELECT user_id FROM orders WHERE id IN (SELECT order_id FROM matchings WHERE id = $1)", sellerMatchingID).Scan(&sellerID)
		if err != nil {
			log.Printf("Warning: Failed to get seller ID from orders: %v", err)
			sellerID = 1 // Use admin/platform user as fallback
		}
	}

	// Get buyer ID
	var buyerID int64
	err = s.db.QueryRow("SELECT user_id FROM matchings WHERE id = $1", buyerMatchingID).Scan(&buyerID)
	if err != nil {
		log.Printf("Warning: Failed to get buyer ID from matchings: %v", err)
		// Try to get the user ID directly from the contract
		err = s.db.QueryRow("SELECT user_id FROM orders WHERE id IN (SELECT order_id FROM matchings WHERE id = $1)", buyerMatchingID).Scan(&buyerID)
		if err != nil {
			log.Printf("Warning: Failed to get buyer ID from orders: %v", err)
			buyerID = 1 // Use admin/platform user as fallback
		}
	}

	// Ensure we have valid user IDs
	if sellerID <= 0 {
		log.Printf("Invalid seller ID: %d, using admin user", sellerID)
		sellerID = 1
	}

	if buyerID <= 0 {
		log.Printf("Invalid buyer ID: %d, using admin user", buyerID)
		buyerID = 1
	}

	log.Printf("Contract details retrieved successfully. SellerID: %d, BuyerID: %d, Price: %f, Quantity: %f",
		sellerID, buyerID, price, quantity)

	// Calculate total amount and fee
	totalAmount := price * quantity
	feeAmount := totalAmount * 0.005 // 0.5% fee

	// Invoice types have been checked and inserted if needed

	// 1. Create main product invoice (seller to buyer)
	// Get status ID for "sent" status
	var sentStatusID int64
	err = s.db.QueryRow("SELECT id FROM statuses WHERE en_name = $1", InvoiceStatusSent).Scan(&sentStatusID)
	if err != nil {
		log.Printf("Warning: Failed to get status ID for 'sent': %v", err)
		// Continue without status ID
	}

	// Get payment term ID (default to COD - ID 1)
	var paymentTermID int64 = 1
	err = s.db.QueryRow("SELECT id FROM payment_terms WHERE en_name = 'COD' OR en_name = 'Cash on Delivery' LIMIT 1").Scan(&paymentTermID)
	if err != nil {
		log.Printf("Warning: Failed to get payment term ID: %v", err)
		// Continue with default payment term ID
	}

	// Set due date (30 days from now)
	now := time.Now()
	dueDate := now.AddDate(0, 0, 30)

	// 1. Create seller fee invoice (platform to original seller)
	sellerFeeInvoice := &Invoice{
		ItemID:        ItemBrokerageFee,
		ContractID:    contractID,
		SellerID:      1, // Platform ID (assuming 1 is the platform's user ID)
		BuyerID:       sellerID,
		ContractValue: feeAmount,                                          // The fee amount (0.5% of contract value)
		ContractFee:   sql.NullFloat64{Float64: totalAmount, Valid: true}, // Store the original contract amount for reference
		CurrencyID:    int64(currencyID),
		InvoiceDate:   now,
		DueDate:       dueDate,
		Status:        InvoiceStatusSent,
		StatusID:      sql.NullInt64{Int64: sentStatusID, Valid: true},  // Set StatusID
		PaymentTermID: sql.NullInt64{Int64: paymentTermID, Valid: true}, // Set PaymentTermID
	}

	log.Printf("Creating seller fee invoice: Amount=%.2f (fee), FeeAmount=%.2f (original contract value)", feeAmount, totalAmount)

	// Get next invoice number
	invoiceNumber, err := s.repo.GetNextInvoiceNumber()
	if err != nil {
		return fmt.Errorf("failed to generate invoice number: %w", err)
	}
	sellerFeeInvoice.InvoiceNumber = invoiceNumber

	// Try repository method first
	err = s.repo.CreateInvoice(sellerFeeInvoice)
	if err != nil {
		log.Printf("Repository CreateInvoice failed for seller fee, trying direct insert: %v", err)

		// Try direct insert
		insertQuery := `
			INSERT INTO invoices (
				invoice_number, item_id, contract_id, seller_id, buyer_id,
				contract_value, contract_fee, currency_id, invoice_date, due_date, status
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
			) RETURNING id
		`

		err = s.db.QueryRow(
			insertQuery,
			sellerFeeInvoice.InvoiceNumber,
			sellerFeeInvoice.ItemID,
			sellerFeeInvoice.ContractID,
			sellerFeeInvoice.SellerID,
			sellerFeeInvoice.BuyerID,
			sellerFeeInvoice.ContractValue,
			sellerFeeInvoice.ContractFee,
			sellerFeeInvoice.CurrencyID,
			sellerFeeInvoice.InvoiceDate,
			sellerFeeInvoice.DueDate,
			sellerFeeInvoice.Status,
		).Scan(&sellerFeeInvoice.ID)

		if err != nil {
			log.Printf("Warning: Failed to create seller fee invoice: %v", err)
			// Continue with buyer fee invoice even if seller fee invoice fails
		} else {
			log.Printf("Created seller fee invoice with ID: %d", sellerFeeInvoice.ID)
		}
	} else {
		log.Printf("Created seller fee invoice with ID: %d", sellerFeeInvoice.ID)
	}

	// 2. Create buyer fee invoice (platform to original buyer)
	buyerFeeInvoice := &Invoice{
		ItemID:        ItemBrokerageFee,
		ContractID:    contractID,
		SellerID:      1, // Platform ID (assuming 1 is the platform's user ID)
		BuyerID:       buyerID,
		ContractValue: feeAmount,                                          // The fee amount (0.5% of contract value)
		ContractFee:   sql.NullFloat64{Float64: totalAmount, Valid: true}, // Store the original contract amount for reference
		CurrencyID:    int64(currencyID),
		InvoiceDate:   now,
		DueDate:       dueDate,
		Status:        InvoiceStatusSent,
		StatusID:      sql.NullInt64{Int64: sentStatusID, Valid: true},  // Set StatusID
		PaymentTermID: sql.NullInt64{Int64: paymentTermID, Valid: true}, // Set PaymentTermID
	}

	log.Printf("Creating buyer fee invoice: Amount=%.2f (fee), FeeAmount=%.2f (original contract value)", feeAmount, totalAmount)

	// Get next invoice number
	invoiceNumber, err = s.repo.GetNextInvoiceNumber()
	if err != nil {
		return fmt.Errorf("failed to generate invoice number: %w", err)
	}
	buyerFeeInvoice.InvoiceNumber = invoiceNumber

	// Try repository method first
	err = s.repo.CreateInvoice(buyerFeeInvoice)
	if err != nil {
		log.Printf("Repository CreateInvoice failed for buyer fee, trying direct insert: %v", err)

		// Try direct insert
		insertQuery := `
			INSERT INTO invoices (
				invoice_number, item_id, contract_id, seller_id, buyer_id,
				contract_value, contract_fee, currency_id, invoice_date, due_date, status
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
			) RETURNING id
		`

		err = s.db.QueryRow(
			insertQuery,
			buyerFeeInvoice.InvoiceNumber,
			buyerFeeInvoice.ItemID,
			buyerFeeInvoice.ContractID,
			buyerFeeInvoice.SellerID,
			buyerFeeInvoice.BuyerID,
			buyerFeeInvoice.ContractValue,
			buyerFeeInvoice.ContractFee,
			buyerFeeInvoice.CurrencyID,
			buyerFeeInvoice.InvoiceDate,
			buyerFeeInvoice.DueDate,
			buyerFeeInvoice.Status,
		).Scan(&buyerFeeInvoice.ID)

		if err != nil {
			log.Printf("Warning: Failed to create buyer fee invoice: %v", err)
			// Continue even if buyer fee invoice fails
		} else {
			log.Printf("Created buyer fee invoice with ID: %d", buyerFeeInvoice.ID)
		}
	} else {
		log.Printf("Created buyer fee invoice with ID: %d", buyerFeeInvoice.ID)
	}

	// Send notifications only if both fee invoices were created successfully
	if sellerFeeInvoice.ID > 0 && buyerFeeInvoice.ID > 0 {
		err = s.sendInvoiceNotifications(sellerFeeInvoice.ID, buyerFeeInvoice.ID)
		if err != nil {
			log.Printf("Warning: Failed to send invoice notifications: %v", err)
			// Continue execution even if notifications fail
		} else {
			log.Printf("Successfully sent invoice notifications")
		}
	} else {
		log.Printf("Skipping notifications as not all fee invoices were created successfully")
	}

	log.Printf("Invoice generation completed for contract ID: %d", contractID)
	return nil
}

// GetInvoice retrieves an invoice by ID
func (s *Service) GetInvoice(id int64) (*Invoice, error) {
	return s.repo.GetInvoiceByID(id)
}

// GetUserInvoices retrieves all invoices for a user
func (s *Service) GetUserInvoices(userID int64) ([]Invoice, error) {
	return s.repo.GetInvoicesForUser(userID)
}

// UpdateInvoiceStatus updates the status of an invoice
func (s *Service) UpdateInvoiceStatus(id int64, status string) error {
	return s.repo.UpdateInvoiceStatus(id, status)
}

// MarkInvoiceAsViewed marks an invoice as viewed
func (s *Service) MarkInvoiceAsViewed(id int64) error {
	return s.repo.UpdateInvoiceStatus(id, InvoiceStatusViewed)
}

// MarkInvoiceAsPaid marks an invoice as paid
func (s *Service) MarkInvoiceAsPaid(id int64) error {
	return s.repo.UpdateInvoiceStatus(id, InvoiceStatusPaid)
}

// UploadPaymentProof uploads a payment proof for an invoice and marks it as paid
func (s *Service) UploadPaymentProof(id int64, filePath string, datePaid time.Time) error {
	// Get the invoice first to ensure it exists and get user roles
	invoice, err := s.repo.GetInvoiceByID(id)
	if err != nil {
		return fmt.Errorf("failed to get invoice: %w", err)
	}
	if invoice == nil {
		return fmt.Errorf("invoice not found")
	}

	// Make sure filePath has the correct format (buyer/ or seller/)
	if !strings.HasPrefix(filePath, "buyer/") && !strings.HasPrefix(filePath, "seller/") {
		return fmt.Errorf("invalid payment proof file path: must start with buyer/ or seller/")
	}

	// Call repository to update the invoice with the payment proof
	return s.repo.UpdateInvoicePaymentProof(id, filePath, datePaid)
}

// GetInvoicesForContract retrieves all invoices for a specific contract
func (s *Service) GetInvoicesForContract(contractID int64, userID int64) ([]Invoice, error) {
	return s.repo.GetInvoicesForContract(contractID, userID)
}

// CheckOverdueInvoices checks for overdue invoices and updates their status
func (s *Service) CheckOverdueInvoices() error {
	// Use the status ID for "overdue" status (27 = expired in your database)
	overdueStatusID := int64(27)

	// Update both status and status_id
	query := `
		UPDATE invoices
		SET status = $1, status_id = $2, updated_at = $3
		WHERE due_date < $4
		AND status NOT IN ($5, $6, $7)
	`

	_, err := s.db.Exec(
		query,
		InvoiceStatusOverdue,
		overdueStatusID,
		time.Now(),
		time.Now(),
		InvoiceStatusPaid,
		InvoiceStatusCanceled,
		InvoiceStatusDisputed,
	)

	if err != nil {
		return fmt.Errorf("failed to update overdue invoices: %w", err)
	}

	return nil
}

// GenerateInvoicesForSignedContracts finds contracts that have been signed by both parties
// but don't have invoices yet, and generates invoices for them
func (s *Service) GenerateInvoicesForSignedContracts() error {
	// Ensure only Brokerage fee item exists
	_, err := s.db.Exec(`
		INSERT INTO items (id, en_name, th_name)
		VALUES
		(1, 'Brokerage fee', 'ค่าธรรมเนียมแพลตฟอร์ม')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		log.Printf("Warning: Failed to ensure items: %v", err)
	}

	// Find contracts that have been signed by both parties but don't have invoices
	query := `
		SELECT c.id
		FROM contracts c
		WHERE c.seller_confirmation_status_id = 20 -- Signed
		AND c.buyer_confirmation_status_id = 20 -- Signed
		AND c.contract_status_id = 20 -- Signed
		AND NOT EXISTS (
			SELECT 1 FROM invoices i WHERE i.contract_id = c.id
		)
	`

	rows, err := s.db.Query(query)
	if err != nil {
		return fmt.Errorf("failed to query signed contracts: %w", err)
	}
	defer rows.Close()

	var contractIDs []int64
	for rows.Next() {
		var id int64
		if err := rows.Scan(&id); err != nil {
			return fmt.Errorf("failed to scan contract ID: %w", err)
		}
		contractIDs = append(contractIDs, id)
	}

	if err := rows.Err(); err != nil {
		return fmt.Errorf("error iterating contract rows: %w", err)
	}

	log.Printf("Found %d signed contracts without invoices", len(contractIDs))

	// Generate invoices for each contract
	for _, contractID := range contractIDs {
		log.Printf("Generating invoices for contract ID: %d", contractID)
		err := s.GenerateInvoicesForContract(contractID)
		if err != nil {
			log.Printf("Error generating invoices for contract %d: %v", contractID, err)
			continue
		}
		log.Printf("Successfully generated invoices for contract ID: %d", contractID)
	}

	return nil
}

// GenerateInvoicePDF generates a PDF for an invoice
func (s *Service) GenerateInvoicePDF(invoiceID int64, language string) ([]byte, error) {
	// Get invoice details
	invoice, err := s.repo.GetInvoiceByID(invoiceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get invoice: %w", err)
	}
	if invoice == nil {
		return nil, fmt.Errorf("invoice not found")
	}

	// Get addresses
	sellerAddress, err := s.getUserAddress(invoice.SellerID)
	if err != nil {
		log.Printf("Warning: Failed to get seller address: %v", err)
		sellerAddress = "Address not available"
	}

	buyerAddress, err := s.getUserAddress(invoice.BuyerID)
	if err != nil {
		log.Printf("Warning: Failed to get buyer address: %v", err)
		buyerAddress = "Address not available"
	}

	// Prepare PDF data
	pdfData := PDFData{
		Invoice:       *invoice,
		SellerAddress: sellerAddress,
		BuyerAddress:  buyerAddress,
		Language:      language,
		PlatformName:  "Biomassx",
		PlatformEmail: "<EMAIL>",
		PlatformPhone: "+66 818073767",
		PlatformWeb:   "www.biomassx.com",
		LogoPath:      "static/images/logo.png",
	}

	// Generate PDF
	pdfBytes, err := s.generatePDF(pdfData)
	if err != nil {
		return nil, fmt.Errorf("failed to generate PDF: %w", err)
	}

	return pdfBytes, nil
}

// generatePDF creates a PDF document for an invoice
func (s *Service) generatePDF(data PDFData) ([]byte, error) {
	// Create a new PDF
	pdf := fpdf.New("P", "mm", "A4", "")
	pdf.AddPage()

	// Set font
	pdf.SetFont("Arial", "B", 16)

	// Add logo if exists
	if _, err := os.Stat(data.LogoPath); err == nil {
		pdf.Image(data.LogoPath, 10, 10, 30, 0, false, "", 0, "")
	}

	// Title
	pdf.SetXY(10, 10)
	pdf.SetFont("Arial", "B", 18)
	if data.Language == "th" {
		pdf.Cell(190, 10, "ใบแจ้งหนี้")
	} else {
		pdf.Cell(190, 10, "INVOICE")
	}

	// Invoice details
	pdf.SetFont("Arial", "B", 12)
	pdf.SetXY(120, 20)
	if data.Language == "th" {
		pdf.Cell(80, 10, "เลขที่ใบแจ้งหนี้: "+data.Invoice.InvoiceNumber)
		pdf.SetXY(120, 25)
		pdf.Cell(80, 10, "วันที่: "+data.Invoice.InvoiceDate.Format("02/01/2006"))
		pdf.SetXY(120, 30)
		pdf.Cell(80, 10, "วันครบกำหนด: "+data.Invoice.DueDate.Format("02/01/2006"))
	} else {
		pdf.Cell(80, 10, "Invoice Number: "+data.Invoice.InvoiceNumber)
		pdf.SetXY(120, 25)
		pdf.Cell(80, 10, "Date: "+data.Invoice.InvoiceDate.Format("01/02/2006"))
		pdf.SetXY(120, 30)
		pdf.Cell(80, 10, "Due Date: "+data.Invoice.DueDate.Format("01/02/2006"))
	}

	// Platform details
	pdf.SetFont("Arial", "", 10)
	pdf.SetXY(10, 25)
	pdf.Cell(100, 5, data.PlatformName)
	pdf.SetXY(10, 30)
	pdf.Cell(100, 5, data.PlatformEmail)
	pdf.SetXY(10, 35)
	pdf.Cell(100, 5, data.PlatformPhone)
	pdf.SetXY(10, 40)
	pdf.Cell(100, 5, data.PlatformWeb)

	// Seller and Buyer details
	pdf.SetFont("Arial", "B", 12)
	pdf.SetXY(10, 50)
	if data.Language == "th" {
		pdf.Cell(90, 10, "ผู้ขาย:")
		pdf.SetXY(110, 50)
		pdf.Cell(90, 10, "ผู้ซื้อ:")
	} else {
		pdf.Cell(90, 10, "Seller:")
		pdf.SetXY(110, 50)
		pdf.Cell(90, 10, "Buyer:")
	}

	pdf.SetFont("Arial", "", 10)
	pdf.SetXY(10, 60)
	pdf.MultiCell(90, 5, data.Invoice.SellerName+"\n"+data.SellerAddress, "", "", false)
	pdf.SetXY(110, 60)
	pdf.MultiCell(90, 5, data.Invoice.BuyerName+"\n"+data.BuyerAddress, "", "", false)

	// Invoice items header
	pdf.SetFont("Arial", "B", 12)
	pdf.SetXY(10, 85)
	if data.Language == "th" {
		pdf.Cell(80, 10, "รายละเอียด")
		pdf.SetXY(90, 85)
		pdf.Cell(30, 10, "จำนวน")
		pdf.SetXY(120, 85)
		pdf.Cell(30, 10, "ราคาต่อหน่วย")
		pdf.SetXY(150, 85)
		pdf.Cell(40, 10, "จำนวนเงิน")
	} else {
		pdf.Cell(80, 10, "Description")
		pdf.SetXY(90, 85)
		pdf.Cell(30, 10, "Quantity")
		pdf.SetXY(120, 85)
		pdf.Cell(30, 10, "Unit Price")
		pdf.SetXY(150, 85)
		pdf.Cell(40, 10, "Amount")
	}

	// Draw line
	pdf.Line(10, 95, 190, 95)

	// Invoice items
	pdf.SetFont("Arial", "", 10)
	pdf.SetXY(10, 100)

	// Different content based on item type
	var description string
	if data.Invoice.ItemID == ItemBrokerageFee {
		if data.Language == "th" {
			description = "ค่าธรรมเนียมแพลตฟอร์ม (0.5%)"
		} else {
			description = "Brokerage Fee (0.5%)"
		}

		pdf.MultiCell(80, 5, description, "", "", false)
		pdf.SetXY(90, 100)
		pdf.Cell(30, 5, "1")
		pdf.SetXY(120, 100)
		pdf.Cell(30, 5, fmt.Sprintf("%.2f %s", data.Invoice.ContractValue, data.Invoice.CurrencyCode))
	} else {
		// Other item types
		if data.Language == "th" {
			description = "สินค้า: " + data.Invoice.ProductName
		} else {
			description = "Product: " + data.Invoice.ProductName
		}
		pdf.MultiCell(80, 5, description, "", "", false)
		pdf.SetXY(90, 100)
		pdf.Cell(30, 5, fmt.Sprintf("%.2f %s", data.Invoice.Quantity, data.Invoice.UnitOfMeasure))
		pdf.SetXY(120, 100)
		pdf.Cell(30, 5, fmt.Sprintf("%.2f %s", data.Invoice.ContractValue/data.Invoice.Quantity, data.Invoice.CurrencyCode))

		// Add note about the contract amount on a new line
		if data.Invoice.ContractFee.Valid {
			pdf.SetXY(10, 110)
			pdf.SetFont("Arial", "I", 8) // Italic, smaller font

			// Calculate the original contract value (multiply fee by 200 to get original value)
			originalContractValue := data.Invoice.ContractValue * 200

			var noteText string
			if data.Language == "th" {
				noteText = fmt.Sprintf("* ค่าธรรมเนียมนี้ (%.2f %s) คำนวณจาก 0.5%% ของมูลค่าสัญญา (%.2f %s)",
					data.Invoice.ContractValue, data.Invoice.CurrencyCode,
					originalContractValue, data.Invoice.CurrencyCode)
			} else {
				noteText = fmt.Sprintf("* This fee (%.2f %s) is calculated as 0.5%% of the contract value (%.2f %s)",
					data.Invoice.ContractValue, data.Invoice.CurrencyCode,
					originalContractValue, data.Invoice.CurrencyCode)
			}

			pdf.MultiCell(180, 4, noteText, "", "R", false) // Right-aligned
			pdf.SetFont("Arial", "", 10)                    // Reset font
		}
	}

	// Amount
	pdf.SetXY(150, 100)
	pdf.Cell(40, 5, fmt.Sprintf("%.2f %s", data.Invoice.ContractValue, data.Invoice.CurrencyCode))

	// Total
	pdf.SetFont("Arial", "B", 12)
	pdf.SetXY(120, 120)
	if data.Language == "th" {
		pdf.Cell(30, 10, "รวมทั้งสิ้น:")
	} else {
		pdf.Cell(30, 10, "Total:")
	}
	pdf.SetXY(150, 120)
	pdf.Cell(40, 10, fmt.Sprintf("%.2f %s", data.Invoice.ContractValue, data.Invoice.CurrencyCode))

	// Status
	pdf.SetFont("Arial", "B", 14)
	pdf.SetXY(10, 140)
	var statusText string
	switch data.Invoice.Status {
	case InvoiceStatusPaid:
		if data.Language == "th" {
			statusText = "ชำระแล้ว"
		} else {
			statusText = "PAID"
		}
		pdf.SetTextColor(0, 128, 0) // Green
	case InvoiceStatusOverdue:
		if data.Language == "th" {
			statusText = "เกินกำหนดชำระ"
		} else {
			statusText = "OVERDUE"
		}
		pdf.SetTextColor(255, 0, 0) // Red
	case InvoiceStatusCanceled:
		if data.Language == "th" {
			statusText = "ยกเลิก"
		} else {
			statusText = "CANCELED"
		}
		pdf.SetTextColor(128, 128, 128) // Gray
	default:
		if data.Language == "th" {
			statusText = "รอการชำระเงิน"
		} else {
			statusText = "PENDING PAYMENT"
		}
		pdf.SetTextColor(0, 0, 255) // Blue
	}
	pdf.Cell(190, 10, statusText)

	// Footer
	pdf.SetTextColor(0, 0, 0) // Reset to black
	pdf.SetFont("Arial", "I", 8)
	pdf.SetXY(10, 270)
	if data.Language == "th" {
		pdf.Cell(190, 10, "ขอบคุณสำหรับธุรกิจของคุณ")
	} else {
		pdf.Cell(190, 10, "Thank you for your business")
	}

	// Output PDF to buffer
	var buf bytes.Buffer
	err := pdf.Output(&buf)
	if err != nil {
		return nil, fmt.Errorf("failed to output PDF: %w", err)
	}

	return buf.Bytes(), nil
}

// getUserAddress gets the formatted address for a user
func (s *Service) getUserAddress(userID int64) (string, error) {
	query := `
		SELECT
			a.address, a.street,
			sd.en_name as subdistrict, d.en_name as district,
			p.en_name as province, c.en_name as country,
			a.postal_code
		FROM addresses a
		LEFT JOIN subdistricts sd ON a.subdistrict_id = sd.id
		LEFT JOIN districts d ON a.district_id = d.id
		LEFT JOIN provinces p ON a.province_id = p.id
		LEFT JOIN countries c ON a.country_id = c.id
		WHERE a.user_id = $1 AND a.address_type_id = 1
		LIMIT 1
	`

	var address, street, subdistrict, district, province, country, postalCode sql.NullString
	err := s.db.QueryRow(query, userID).Scan(
		&address, &street, &subdistrict, &district, &province, &country, &postalCode,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return "Address not available", nil
		}
		return "", fmt.Errorf("failed to get user address: %w", err)
	}

	// Build address string
	addressParts := []string{}
	if address.Valid && address.String != "" {
		addressParts = append(addressParts, address.String)
	}
	if street.Valid && street.String != "" {
		addressParts = append(addressParts, street.String)
	}
	if subdistrict.Valid && subdistrict.String != "" {
		addressParts = append(addressParts, subdistrict.String)
	}
	if district.Valid && district.String != "" {
		addressParts = append(addressParts, district.String)
	}
	if province.Valid && province.String != "" {
		addressParts = append(addressParts, province.String)
	}
	if country.Valid && country.String != "" {
		addressParts = append(addressParts, country.String)
	}
	if postalCode.Valid && postalCode.String != "" {
		addressParts = append(addressParts, postalCode.String)
	}

	if len(addressParts) == 0 {
		return "Address not available", nil
	}

	// Join all parts with commas
	fullAddress := ""
	for i, part := range addressParts {
		if i > 0 {
			fullAddress += ", "
		}
		fullAddress += part
	}

	return fullAddress, nil
}

// sendInvoiceNotifications sends notifications for new invoices
func (s *Service) sendInvoiceNotifications(sellerFeeInvoiceID, buyerFeeInvoiceID int64) error {
	// Get seller fee invoice details
	sellerFeeInvoice, err := s.repo.GetInvoiceByID(sellerFeeInvoiceID)
	if err != nil {
		return fmt.Errorf("failed to get seller fee invoice details: %w", err)
	}

	// Get buyer fee invoice details
	buyerFeeInvoice, err := s.repo.GetInvoiceByID(buyerFeeInvoiceID)
	if err != nil {
		return fmt.Errorf("failed to get buyer fee invoice details: %w", err)
	}

	// Get buyer and seller details
	var buyerEmail, sellerEmail, buyerFirstName, buyerLastName, sellerFirstName, sellerLastName string
	err = s.db.QueryRow("SELECT email, first_name, last_name FROM users WHERE id = $1", buyerFeeInvoice.BuyerID).Scan(&buyerEmail, &buyerFirstName, &buyerLastName) // Original Buyer
	if err != nil {
		log.Printf("Warning: Failed to get buyer details: %v", err)
	}

	err = s.db.QueryRow("SELECT email, first_name, last_name FROM users WHERE id = $1", sellerFeeInvoice.BuyerID).Scan(&sellerEmail, &sellerFirstName, &sellerLastName) // Original Seller (buyer of sellerFeeInvoice)
	if err != nil {
		log.Printf("Warning: Failed to get seller details: %v", err)
	}

	// Create notifications in the database
	repo := notification.NewPostgresRepository(s.db)
	emailConfig := notification.NewEmailConfig(
		os.Getenv("SMTP_HOST"),
		os.Getenv("SMTP_PORT"),
		os.Getenv("SMTP_USERNAME"),
		os.Getenv("FASTMAIL_APP_PASSWORD"),
		os.Getenv("SMTP_SENDER_EMAIL"),
		"BiomassX Platform",
	)
	notificationService := notification.NewEmailService(repo, emailConfig)

	// Get base URL from environment
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		baseURL = "https://biomassx.com" // Default fallback
	}

	// Schedule invoice payment notifications with a 2-minute delay
	// We'll only send these delayed notifications and remove the immediate ones
	go func() {
		// Wait for 2 minutes
		time.Sleep(2 * time.Minute)

		// 1. Send invoice payment notification to seller for fee invoice
		if sellerEmail != "" {
			sellerInvoiceURL := fmt.Sprintf("%s/invoice/%d", baseURL, sellerFeeInvoice.ID)
			sellerNotificationData := &notification.InvoicePaymentNotification{
				UserID:            sellerFeeInvoice.BuyerID, // Original Seller ID
				Email:             sellerEmail,
				FirstName:         sellerFirstName,
				LastName:          sellerLastName,
				InvoiceID:         sellerFeeInvoice.ID,
				InvoiceNumber:     sellerFeeInvoice.InvoiceNumber,
				ContractID:        sellerFeeInvoice.ContractID,
				ContractNumber:    sellerFeeInvoice.ContractNumber,
				Role:              "seller",
				Amount:            sellerFeeInvoice.ContractValue,
				Currency:          sellerFeeInvoice.CurrencyCode,
				DueDate:           sellerFeeInvoice.DueDate.Format("2006-01-02"),
				ProductName:       "Platform Fee", // Generic name for fee
				InvoiceDetailsURL: sellerInvoiceURL,
				Language:          "en", // Default to English, can be improved by getting user's language preference
				BaseURL:           baseURL,
			}

			err = notificationService.SendInvoicePaymentNotification(sellerNotificationData)
			if err != nil {
				log.Printf("Warning: Failed to send invoice payment notification to seller: %v", err)
			} else {
				log.Printf("Successfully sent invoice payment notification to seller: %s", sellerEmail)
			}
		}

		// 2. Send invoice payment notification to buyer for fee invoice
		if buyerEmail != "" {
			buyerInvoiceURL := fmt.Sprintf("%s/invoice/%d", baseURL, buyerFeeInvoice.ID)
			buyerNotificationData := &notification.InvoicePaymentNotification{
				UserID:            buyerFeeInvoice.BuyerID, // Original Buyer ID
				Email:             buyerEmail,
				FirstName:         buyerFirstName,
				LastName:          buyerLastName,
				InvoiceID:         buyerFeeInvoice.ID,
				InvoiceNumber:     buyerFeeInvoice.InvoiceNumber,
				ContractID:        buyerFeeInvoice.ContractID,
				ContractNumber:    buyerFeeInvoice.ContractNumber,
				Role:              "buyer",
				Amount:            buyerFeeInvoice.ContractValue,
				Currency:          buyerFeeInvoice.CurrencyCode,
				DueDate:           buyerFeeInvoice.DueDate.Format("2006-01-02"),
				ProductName:       "Platform Fee", // Generic name for fee
				InvoiceDetailsURL: buyerInvoiceURL,
				Language:          "en", // Default to English, can be improved by getting user's language preference
				BaseURL:           baseURL,
			}

			err = notificationService.SendInvoicePaymentNotification(buyerNotificationData)
			if err != nil {
				log.Printf("Warning: Failed to send invoice payment notification to buyer: %v", err)
			} else {
				log.Printf("Successfully sent invoice payment notification to buyer: %s", buyerEmail)
			}
		}
	}()

	// Create notification type if it doesn't exist
	invoiceNotificationType, err := repo.GetNotificationTypeByName("invoice")
	if err != nil || invoiceNotificationType == nil {
		invoiceNotificationType, err = repo.CreateNotificationType("invoice")
		if err != nil {
			return fmt.Errorf("failed to create invoice notification type: %w", err)
		}
	}

	// 3. Notify seller about fee invoice
	if sellerEmail != "" {
		sellerFeeNotification := &notification.Notification{
			UserID:             sellerFeeInvoice.BuyerID, // Seller is the buyer in this invoice
			NotificationTypeID: invoiceNotificationType.ID,
			Email:              sellerEmail,
			Subject:            "Platform Fee Invoice",
			Body:               fmt.Sprintf("You have received a platform fee invoice (#%s) for contract #%s", sellerFeeInvoice.InvoiceNumber, sellerFeeInvoice.ContractNumber),
			Status:             string(notification.NotificationStatusPending),
			CreatedAt:          time.Now(),
		}

		err = repo.CreateNotification(sellerFeeNotification)
		if err != nil {
			log.Printf("Warning: Failed to create fee notification for seller: %v", err)
		}
	}

	// 4. Notify buyer about fee invoice
	if buyerEmail != "" {
		buyerFeeNotification := &notification.Notification{
			UserID:             buyerFeeInvoice.BuyerID,
			NotificationTypeID: invoiceNotificationType.ID,
			Email:              buyerEmail,
			Subject:            "Platform Fee Invoice",
			Body:               fmt.Sprintf("You have received a platform fee invoice (#%s) for contract #%s", buyerFeeInvoice.InvoiceNumber, buyerFeeInvoice.ContractNumber),
			Status:             string(notification.NotificationStatusPending),
			CreatedAt:          time.Now(),
		}

		err = repo.CreateNotification(buyerFeeNotification)
		if err != nil {
			log.Printf("Warning: Failed to create fee notification for buyer: %v", err)
		}
	}

	return nil
}
