package invoice

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/golang-jwt/jwt"
	"github.com/gorilla/mux"
	"github.com/thongsoi/biomassx/database"
)

var jwtKey = []byte("your_secret_key")

// getUserIDFromToken extracts the user ID from the JWT token
func getUserIDFromToken(r *http.Request) (int64, error) {
	cookie, err := r.Cookie("token")
	if err != nil {
		return 0, err
	}

	token, err := jwt.Parse(cookie.Value, func(token *jwt.Token) (interface{}, error) {
		return jwtKey, nil
	})
	if err != nil || !token.Valid {
		return 0, err
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return 0, fmt.Errorf("invalid token claims")
	}

	userID := int64(claims["user_id"].(float64))
	return userID, nil
}

// getLanguageFromToken extracts the language from the JWT token
func getLanguageFromToken(r *http.Request) string {
	cookie, err := r.Cookie("token")
	if err != nil {
		return "en" // Default language if no cookie
	}

	token, err := jwt.Parse(cookie.Value, func(token *jwt.Token) (interface{}, error) {
		return jwtKey, nil
	})
	if err != nil || !token.Valid {
		return "en" // Default language if token is invalid
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return "en"
	}

	lang, ok := claims["language"].(string)
	if !ok {
		return "en"
	}

	return lang
}

// InvoiceHandler displays the invoice list page
func InvoiceHandler(w http.ResponseWriter, r *http.Request) {
	// Get user ID from session
	userID, err := getUserIDFromToken(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get language preference
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en" // Default to English
	}

	// Initialize invoice service
	service := NewService(database.GetDB())

	// Get invoices for the user
	invoices, err := service.GetUserInvoices(userID)
	if err != nil {
		log.Printf("Error getting invoices: %v", err)
		http.Error(w, "Error retrieving invoices", http.StatusInternalServerError)
		return
	}

	// Select template file based on language
	var contentFile string
	switch language {
	case "th":
		contentFile = "views/pages/th/invoice_th.html"
	case "en":
		contentFile = "views/pages/en/invoice_en.html"
	default:
		contentFile = "views/pages/en/invoice_en.html"
	}

	// Load templates with custom functions
	funcMap := template.FuncMap{
		"multiply": func(a, b float64) float64 {
			return a * b
		},
	}

	tmpl, err := template.New("base").Funcs(funcMap).ParseFiles("views/pages/invoice.html", contentFile)
	if err != nil {
		log.Printf("Error parsing template: %v", err)
		http.Error(w, "Template not found", http.StatusInternalServerError)
		return
	}

	// Prepare data for template
	data := struct {
		Invoices []Invoice
		Lang     string
		UserID   int64
	}{
		Invoices: invoices,
		Lang:     language,
		UserID:   userID,
	}

	// Create a buffer to render the template first
	var buf bytes.Buffer

	// Render template to buffer
	err = tmpl.ExecuteTemplate(&buf, "base", data)
	if err != nil {
		log.Printf("Error executing template: %v", err)
		http.Error(w, "Error rendering page", http.StatusInternalServerError)
		return
	}

	// Set content type and write the rendered template to the response
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write(buf.Bytes())
}

// InvoiceDetailHandler displays the invoice detail page
func InvoiceDetailHandler(w http.ResponseWriter, r *http.Request) {
	// Get user ID from session
	userID, err := getUserIDFromToken(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get invoice ID from URL
	vars := mux.Vars(r)
	invoiceID, err := strconv.ParseInt(vars["id"], 10, 64)
	if err != nil {
		http.Error(w, "Invalid invoice ID", http.StatusBadRequest)
		return
	}

	// Get language preference
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en" // Default to English
	}

	// Initialize invoice service
	service := NewService(database.GetDB())

	// Get invoice details
	invoice, err := service.GetInvoice(invoiceID)
	if err != nil {
		log.Printf("Error getting invoice: %v", err)
		http.Error(w, "Error retrieving invoice", http.StatusInternalServerError)
		return
	}

	// Check if invoice exists
	if invoice == nil {
		http.Error(w, "Invoice not found", http.StatusNotFound)
		return
	}

	// Check if user has access to this invoice
	if invoice.BuyerID != userID && invoice.SellerID != userID {
		http.Error(w, "Access denied", http.StatusForbidden)
		return
	}

	// Check if fee invoice is being accessed by the correct user type
	// ItemInspectionFee (2) should only be visible to sellers
	// ItemAnalysisFee (3) should only be visible to buyers
	if (invoice.ItemID == 2 && invoice.SellerID != userID) ||
		(invoice.ItemID == 3 && invoice.BuyerID != userID) {
		http.Error(w, "Access denied", http.StatusForbidden)
		return
	}

	// For privacy, if the user is a buyer, don't show seller details
	// If the user is a seller, don't show buyer details
	if invoice.BuyerID == userID && invoice.SellerID != userID {
		// User is the buyer, mask seller information
		invoice.SellerName = "Platform" // Replace seller name with generic "Platform"
	} else if invoice.SellerID == userID && invoice.BuyerID != userID {
		// User is the seller, mask buyer information
		invoice.BuyerName = "Platform" // Replace buyer name with generic "Platform"
	}

	// Mark invoice as viewed if user is the buyer
	if invoice.BuyerID == userID && invoice.Status == InvoiceStatusSent {
		err = service.MarkInvoiceAsViewed(invoiceID)
		if err != nil {
			log.Printf("Error marking invoice as viewed: %v", err)
			// Continue anyway
		}
	}

	// --- New logic for Fee Invoices (ItemID == 1) ---
	var myFeeStatus string
	var myFeeProofPath sql.NullString
	// Ensure OtherParty fields are not populated or used for display
	invoice.OtherPartyName = sql.NullString{Valid: false}
	invoice.OtherPartyFeeStatus = sql.NullString{Valid: false}
	invoice.OtherPartyFeeProofPath = sql.NullString{Valid: false}

	if invoice.ItemID == 1 { // This is a platform fee invoice
		// Determine "my" fee invoice details (the one for the viewingUser - userID)
		myFeeInvoiceForThisContract, errMyFee := service.repo.GetFeeInvoiceByContractAndUser(invoice.ContractID, userID)
		if errMyFee != nil && errMyFee != sql.ErrNoRows {
			log.Printf("Error getting 'my' fee invoice for contract %d, user %d: %v", invoice.ContractID, userID, errMyFee)
		} else if myFeeInvoiceForThisContract != nil {
			myFeeStatus = myFeeInvoiceForThisContract.Status
			// For fee invoices, the proof is always in PaymentSlipBuyer as the user is the buyer of their own fee.
			myFeeProofPath = myFeeInvoiceForThisContract.PaymentSlipBuyer
		}

		// Logic for fetching and displaying the other party's fee status and proof is removed.
		// The invoice.OtherParty... fields will remain as sql.NullString{Valid: false}
	}
	// --- End of new logic for Fee Invoices ---

	// Select template file based on language
	var contentFile string
	switch language {
	case "th":
		contentFile = "views/pages/th/invoice_detail_th.html"
	case "en":
		contentFile = "views/pages/en/invoice_detail_en.html"
	default:
		contentFile = "views/pages/en/invoice_detail_en.html"
	}

	// Load templates with custom functions
	funcMap := template.FuncMap{
		"multiply": func(a, b float64) float64 {
			return a * b
		},
	}

	tmpl, err := template.New("base").Funcs(funcMap).ParseFiles("views/pages/invoice.html", contentFile)
	if err != nil {
		log.Printf("Error parsing template: %v", err)
		http.Error(w, "Template not found", http.StatusInternalServerError)
		return
	}

	// Format dates for display
	invoiceDate := invoice.InvoiceDate.Format("2006-01-02")
	dueDate := invoice.DueDate.Format("2006-01-02")

	// Calculate unit price if this is a product invoice
	var unitPrice float64
	if invoice.ItemID != ItemBrokerageFee && invoice.Quantity > 0 {
		unitPrice = invoice.ContractValue / invoice.Quantity
	} else {
		unitPrice = invoice.ContractValue
	}

	// Synthesize a display path for the current invoice's payment slip
	// This avoids relying on the potentially misformatted Invoice.PaymentSlipPath from the repository
	var displayPaymentSlipPath sql.NullString
	if invoice.PaymentSlipBuyer.Valid {
		displayPaymentSlipPath = invoice.PaymentSlipBuyer
	} else if invoice.PaymentSlipSeller.Valid {
		displayPaymentSlipPath = invoice.PaymentSlipSeller
	} else {
		displayPaymentSlipPath = sql.NullString{Valid: false}
	}

	// Check if the payment slip for the *currently viewed invoice* was uploaded by the *current user*
	var paymentUploadedByCurrentUser bool = false
	var actualSlipPathForThisInvoice sql.NullString

	if invoice.PaymentSlipBuyer.Valid {
		actualSlipPathForThisInvoice = invoice.PaymentSlipBuyer
	} else if invoice.PaymentSlipSeller.Valid {
		actualSlipPathForThisInvoice = invoice.PaymentSlipSeller
	}

	if actualSlipPathForThisInvoice.Valid {
		userIDPattern := fmt.Sprintf("user%d", userID)
		paymentUploadedByCurrentUser = strings.Contains(actualSlipPathForThisInvoice.String, userIDPattern)
	}

	// Prepare data for template - only include fields that are actually used in the template
	data := struct {
		Invoice                      *Invoice
		InvoiceDate                  string
		DueDate                      string
		Lang                         string
		UserID                       int64
		UnitPrice                    float64
		PaymentUploadedByCurrentUser bool
		DisplayPaymentSlipPath       sql.NullString // Use this in template for the current invoice's slip
		// PaymentUploadedByAdmin       bool // Correspondingly remove from data struct
		MyFeeStatus    string         // New field
		MyFeeProofPath sql.NullString // New field
	}{
		Invoice:                      invoice,
		InvoiceDate:                  invoiceDate,
		DueDate:                      dueDate,
		Lang:                         language,
		UserID:                       userID,
		UnitPrice:                    unitPrice,
		PaymentUploadedByCurrentUser: paymentUploadedByCurrentUser,
		DisplayPaymentSlipPath:       displayPaymentSlipPath,
		// PaymentUploadedByAdmin:       paymentUploadedByAdmin,
		MyFeeStatus:    myFeeStatus,    // Populate new field
		MyFeeProofPath: myFeeProofPath, // Populate new field
	}

	// Create a buffer to render the template first
	var buf bytes.Buffer

	// Render template to buffer
	err = tmpl.ExecuteTemplate(&buf, "base", data)
	if err != nil {
		log.Printf("Error executing template: %v", err)
		http.Error(w, "Error rendering page", http.StatusInternalServerError)
		return
	}

	// Set content type and write the rendered template to the response
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write(buf.Bytes())
}

// DownloadInvoicePDFHandler generates and serves a PDF for an invoice
func DownloadInvoicePDFHandler(w http.ResponseWriter, r *http.Request) {
	// Get user ID from session
	userID, err := getUserIDFromToken(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get invoice ID from URL
	vars := mux.Vars(r)
	invoiceID, err := strconv.ParseInt(vars["id"], 10, 64)
	if err != nil {
		http.Error(w, "Invalid invoice ID", http.StatusBadRequest)
		return
	}

	// Get language preference
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en" // Default to English
	}

	// Initialize invoice service
	service := NewService(database.GetDB())

	// Get invoice details to check access
	invoice, err := service.GetInvoice(invoiceID)
	if err != nil {
		log.Printf("Error getting invoice: %v", err)
		http.Error(w, "Error retrieving invoice", http.StatusInternalServerError)
		return
	}

	// Check if invoice exists
	if invoice == nil {
		http.Error(w, "Invoice not found", http.StatusNotFound)
		return
	}

	// Check if user has access to this invoice
	if invoice.BuyerID != userID && invoice.SellerID != userID {
		http.Error(w, "Access denied", http.StatusForbidden)
		return
	}

	// Check if fee invoice is being accessed by the correct user type
	// ItemInspectionFee (2) should only be visible to sellers
	// ItemAnalysisFee (3) should only be visible to buyers
	if (invoice.ItemID == 2 && invoice.SellerID != userID) ||
		(invoice.ItemID == 3 && invoice.BuyerID != userID) {
		http.Error(w, "Access denied", http.StatusForbidden)
		return
	}

	// Generate PDF
	pdfBytes, err := service.GenerateInvoicePDF(invoiceID, language)
	if err != nil {
		log.Printf("Error generating PDF: %v", err)
		http.Error(w, "Error generating PDF", http.StatusInternalServerError)
		return
	}

	// Set response headers
	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=invoice-%s.pdf", invoice.InvoiceNumber))
	w.Header().Set("Content-Length", strconv.Itoa(len(pdfBytes)))

	// Write PDF to response
	_, err = w.Write(pdfBytes)
	if err != nil {
		log.Printf("Error writing PDF to response: %v", err)
		http.Error(w, "Error serving PDF", http.StatusInternalServerError)
		return
	}
}

// UploadPaymentProofHandler handles payment slip uploads for invoices
func UploadPaymentProofHandler(w http.ResponseWriter, r *http.Request) {
	// Only accept POST requests
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get user ID from session
	userID, err := getUserIDFromToken(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Parse multipart form (max 10MB)
	err = r.ParseMultipartForm(10 << 20)
	if err != nil {
		http.Error(w, "Failed to parse form", http.StatusBadRequest)
		return
	}

	// Get invoice ID from form
	invoiceIDStr := r.FormValue("invoice_id")
	if invoiceIDStr == "" {
		http.Error(w, "Invoice ID is required", http.StatusBadRequest)
		return
	}

	invoiceID, err := strconv.ParseInt(invoiceIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid invoice ID", http.StatusBadRequest)
		return
	}

	// Use current date as payment date
	datePaid := time.Now()

	// Get file from form
	file, handler, err := r.FormFile("payment_slip")
	if err != nil {
		http.Error(w, "Failed to get file from form", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Initialize invoice service
	service := NewService(database.GetDB())

	// Check if invoice exists and belongs to the user
	invoice, err := service.GetInvoice(invoiceID)
	if err != nil {
		log.Printf("Error getting invoice: %v", err)
		http.Error(w, "Error retrieving invoice", http.StatusInternalServerError)
		return
	}

	if invoice == nil {
		http.Error(w, "Invoice not found", http.StatusNotFound)
		return
	}

	// Check if user is authorized to upload payment proof for this invoice
	if invoice.BuyerID != userID && invoice.SellerID != userID {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Use separate directories for buyer and seller payment slips
	uploadsBaseDir := "payment_slip"
	userType := "buyer"
	if invoice.SellerID == userID {
		userType = "seller"
	}
	uploadsDir := filepath.Join(uploadsBaseDir, userType)

	// Create directory if it doesn't exist
	if err := os.MkdirAll(uploadsDir, 0755); err != nil {
		log.Printf("Error creating uploads directory: %v", err)
		http.Error(w, "Error creating uploads directory", http.StatusInternalServerError)
		return
	} // Generate unique filename that includes the user ID and type to track who uploaded it
	timestamp := time.Now().Format("20060102150405")
	filename := fmt.Sprintf("%s-user%d-invoice%d-%s", timestamp, userID, invoiceID, handler.Filename)
	relativeFilepath := filepath.Join(userType, filename)               // For database storage (relative path)
	absoluteFilepath := filepath.Join(uploadsBaseDir, relativeFilepath) // For file creation (absolute path)

	// Create file
	dst, err := os.Create(absoluteFilepath)
	if err != nil {
		log.Printf("Error creating file: %v", err)
		http.Error(w, "Error saving file", http.StatusInternalServerError)
		return
	}
	defer dst.Close()

	// Copy file contents
	if _, err = io.Copy(dst, file); err != nil {
		log.Printf("Error copying file: %v", err)
		http.Error(w, "Error saving file", http.StatusInternalServerError)
		return
	}

	// Update invoice with just the filename (not the full path)
	err = service.UploadPaymentProof(invoiceID, relativeFilepath, datePaid)
	if err != nil {
		log.Printf("Error updating invoice with payment slip: %v", err)
		http.Error(w, "Error updating invoice", http.StatusInternalServerError)
		return
	}

	// Redirect to invoice detail page
	http.Redirect(w, r, fmt.Sprintf("/invoice/%d?lang=%s", invoiceID, r.URL.Query().Get("lang")), http.StatusSeeOther)
}

// UpdateInvoiceStatusHandler updates the status of an invoice
func UpdateInvoiceStatusHandler(w http.ResponseWriter, r *http.Request) {
	// Only accept POST requests
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get user ID from session
	userID, err := getUserIDFromToken(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Parse request body - support both JSON and form data for htmx
	var invoiceID int64
	var status string

	contentType := r.Header.Get("Content-Type")
	if strings.Contains(contentType, "application/json") {
		var request struct {
			InvoiceID int64  `json:"invoice_id"`
			Status    string `json:"status"`
		}
		err = json.NewDecoder(r.Body).Decode(&request)
		if err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}
		invoiceID = request.InvoiceID
		status = request.Status
	} else {
		// Parse form data
		err = r.ParseForm()
		if err != nil {
			http.Error(w, "Invalid form data", http.StatusBadRequest)
			return
		}

		// Try to get from form values first (for regular form submissions)
		invoiceIDStr := r.FormValue("invoice_id")
		if invoiceIDStr == "" {
			// If not found, check for htmx JSON in the form values
			// htmx sends JSON as a string in the form values
			hxVals := r.FormValue("_hx-vals")
			if hxVals != "" {
				var hxValsMap map[string]interface{}
				err = json.Unmarshal([]byte(hxVals), &hxValsMap)
				if err == nil {
					if idVal, ok := hxValsMap["invoice_id"]; ok {
						if idFloat, ok := idVal.(float64); ok {
							invoiceID = int64(idFloat)
						}
					}
					if statusVal, ok := hxValsMap["status"]; ok {
						if statusStr, ok := statusVal.(string); ok {
							status = statusStr
						}
					}
				}
			}
		} else {
			invoiceID, err = strconv.ParseInt(invoiceIDStr, 10, 64)
			if err != nil {
				http.Error(w, "Invalid invoice ID", http.StatusBadRequest)
				return
			}
			status = r.FormValue("status")
		}
	}

	// Validate required parameters
	if invoiceID == 0 || status == "" {
		http.Error(w, "Missing required parameters", http.StatusBadRequest)
		return
	}

	// Initialize invoice service
	service := NewService(database.GetDB())

	// Get invoice details to check access
	invoice, err := service.GetInvoice(invoiceID)
	if err != nil {
		log.Printf("Error getting invoice: %v", err)
		http.Error(w, "Error retrieving invoice", http.StatusInternalServerError)
		return
	}

	// Check if invoice exists
	if invoice == nil {
		http.Error(w, "Invoice not found", http.StatusNotFound)
		return
	}

	// Check if user has access to this invoice
	if invoice.BuyerID != userID && invoice.SellerID != userID {
		http.Error(w, "Access denied", http.StatusForbidden)
		return
	}

	// Check if fee invoice is being accessed by the correct user type
	// ItemInspectionFee (2) should only be visible to sellers
	// ItemAnalysisFee (3) should only be visible to buyers
	if (invoice.ItemID == 2 && invoice.SellerID != userID) ||
		(invoice.ItemID == 3 && invoice.BuyerID != userID) {
		http.Error(w, "Access denied", http.StatusForbidden)
		return
	}

	// Validate status
	validStatuses := map[string]bool{
		InvoiceStatusPaid:     true,
		InvoiceStatusCanceled: true,
		InvoiceStatusDisputed: true,
	}

	if !validStatuses[status] {
		http.Error(w, "Invalid status", http.StatusBadRequest)
		return
	}

	// Update invoice status
	err = service.UpdateInvoiceStatus(invoiceID, status)
	if err != nil {
		log.Printf("Error updating invoice status: %v", err)
		http.Error(w, "Error updating invoice status", http.StatusInternalServerError)
		return
	}

	// Get updated invoice
	updatedInvoice, err := service.GetInvoice(invoiceID)
	if err != nil {
		log.Printf("Error getting updated invoice: %v", err)
		http.Error(w, "Error retrieving updated invoice", http.StatusInternalServerError)
		return
	}

	// Get language preference
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en" // Default to English
	}

	// Format dates for display
	invoiceDate := updatedInvoice.InvoiceDate.Format("2006-01-02")
	dueDate := updatedInvoice.DueDate.Format("2006-01-02")

	// Calculate unit price if this is a product invoice
	var unitPrice float64
	if updatedInvoice.ItemID != ItemBrokerageFee && updatedInvoice.Quantity > 0 {
		unitPrice = updatedInvoice.ContractValue / updatedInvoice.Quantity
	} else {
		unitPrice = updatedInvoice.ContractValue
	}

	// Prepare data for template
	// Prepare status message based on the action taken
	var statusMessage string
	var statusAction string

	switch status {
	case InvoiceStatusPaid:
		statusAction = "marked as paid"
		if language == "th" {
			statusMessage = "ใบแจ้งหนี้ถูกทำเครื่องหมายว่าชำระแล้ว"
		} else {
			statusMessage = "Invoice has been marked as paid"
		}
	case InvoiceStatusDisputed:
		statusAction = "disputed"
		if language == "th" {
			statusMessage = "ใบแจ้งหนี้ถูกทำเครื่องหมายว่าโต้แย้ง"
		} else {
			statusMessage = "Invoice has been disputed"
		}
	case InvoiceStatusCanceled:
		statusAction = "canceled"
		if language == "th" {
			statusMessage = "ใบแจ้งหนี้ถูกยกเลิกแล้ว"
		} else {
			statusMessage = "Invoice has been canceled"
		}
	default:
		statusAction = "updated"
		if language == "th" {
			statusMessage = "สถานะใบแจ้งหนี้ถูกอัปเดตแล้ว"
		} else {
			statusMessage = "Invoice status has been updated"
		}
	}

	// Check if the payment slip for the *currently viewed invoice* was uploaded by the *current user*
	var paymentUploadedByCurrentUser bool
	var actualSlipPathForThisInvoice sql.NullString
	var displayPaymentSlipPath sql.NullString // For template

	if updatedInvoice.PaymentSlipBuyer.Valid {
		actualSlipPathForThisInvoice = updatedInvoice.PaymentSlipBuyer
		displayPaymentSlipPath = updatedInvoice.PaymentSlipBuyer
	} else if updatedInvoice.PaymentSlipSeller.Valid {
		actualSlipPathForThisInvoice = updatedInvoice.PaymentSlipSeller
		displayPaymentSlipPath = updatedInvoice.PaymentSlipSeller
	}

	if actualSlipPathForThisInvoice.Valid {
		userIDPattern := fmt.Sprintf("user%d", userID)
		paymentUploadedByCurrentUser = strings.Contains(actualSlipPathForThisInvoice.String, userIDPattern)
	}

	// --- New logic for Fee Invoices (ItemID == 1) in UpdateInvoiceStatusHandler ---
	var myFeeStatus string
	var myFeeProofPath sql.NullString

	if updatedInvoice.ItemID == 1 { // This is a platform fee invoice
		// Get original contract parties
		_, _, _, _, errParties := service.repo.GetOriginalContractParties(updatedInvoice.ContractID)
		if errParties != nil {
			log.Printf("Error getting original contract parties for contract %d in UpdateInvoiceStatusHandler: %v", updatedInvoice.ContractID, errParties)
		} else {
			// Determine "my" fee invoice details (the one for the viewingUser)
			myFeeInvoiceForThisContract, errMyFee := service.repo.GetFeeInvoiceByContractAndUser(updatedInvoice.ContractID, userID)
			if errMyFee != nil && errMyFee != sql.ErrNoRows {
				log.Printf("Error getting 'my' fee invoice for contract %d, user %d in UpdateInvoiceStatusHandler: %v", updatedInvoice.ContractID, userID, errMyFee)
			} else if myFeeInvoiceForThisContract != nil {
				myFeeStatus = myFeeInvoiceForThisContract.Status
				myFeeProofPath = myFeeInvoiceForThisContract.PaymentSlipBuyer // User is always buyer of their own fee invoice
			} // We no longer fetch or display the other party's fee status
		}
	}
	// Clear other party fee details to ensure they are not accidentally displayed
	updatedInvoice.OtherPartyName = sql.NullString{}
	updatedInvoice.OtherPartyFeeStatus = sql.NullString{}
	// --- End of new logic for Fee Invoices in UpdateInvoiceStatusHandler ---
	data := struct {
		Invoice                      *Invoice
		InvoiceDate                  string
		DueDate                      string
		Lang                         string
		UserID                       int64
		UnitPrice                    float64
		StatusMessage                string
		StatusAction                 string
		PaymentUploadedByCurrentUser bool
		DisplayPaymentSlipPath       sql.NullString // Use this in template
		MyFeeStatus                  string         // Added for consistency, though might not be directly used if page reloads fully
		MyFeeProofPath               sql.NullString // Added for consistency
	}{
		Invoice:                      updatedInvoice,
		InvoiceDate:                  invoiceDate,
		DueDate:                      dueDate,
		Lang:                         language,
		UserID:                       userID,
		UnitPrice:                    unitPrice,
		StatusMessage:                statusMessage,
		StatusAction:                 statusAction,
		PaymentUploadedByCurrentUser: paymentUploadedByCurrentUser,
		DisplayPaymentSlipPath:       displayPaymentSlipPath,
		MyFeeStatus:                  myFeeStatus,    // Populate from updatedInvoice if it's the user's fee invoice
		MyFeeProofPath:               myFeeProofPath, // Populate from updatedInvoice if it's the user's fee invoice
	}

	// Select template file based on language
	var contentFile string
	switch language {
	case "th":
		contentFile = "views/pages/th/invoice_detail_th.html"
	case "en":
		contentFile = "views/pages/en/invoice_detail_en.html"
	default:
		contentFile = "views/pages/en/invoice_detail_en.html"
	}

	// Load templates with custom functions
	funcMap := template.FuncMap{
		"multiply": func(a, b float64) float64 {
			return a * b
		},
	}

	tmpl, err := template.New("base").Funcs(funcMap).ParseFiles("views/pages/invoice.html", contentFile)
	if err != nil {
		log.Printf("Error parsing template: %v", err)
		http.Error(w, "Template not found", http.StatusInternalServerError)
		return
	}

	// Create a buffer to render the template first
	var buf bytes.Buffer

	// Render template to buffer
	err = tmpl.ExecuteTemplate(&buf, "base", data)
	if err != nil {
		log.Printf("Error executing template: %v", err)
		http.Error(w, "Error rendering template", http.StatusInternalServerError)
		return
	}

	// Set content type and write the rendered template to the response
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write(buf.Bytes())
}

// GenerateInvoicesForContractHandler generates invoices for a contract
func GenerateInvoicesForContractHandler(w http.ResponseWriter, r *http.Request) {
	// Only accept POST requests
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse request body
	var request struct {
		ContractID int64 `json:"contract_id"`
	}

	err := json.NewDecoder(r.Body).Decode(&request)
	if err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Initialize invoice service
	service := NewService(database.GetDB())

	// Generate invoices
	err = service.GenerateInvoicesForContract(request.ContractID)
	if err != nil {
		log.Printf("Error generating invoices: %v", err)
		http.Error(w, "Error generating invoices", http.StatusInternalServerError)
		return
	}

	// Return success response
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Invoices generated successfully",
	})
}

// GenerateInvoicesForSignedContractsHandler generates invoices for all signed contracts that don't have invoices yet
func GenerateInvoicesForSignedContractsHandler(w http.ResponseWriter, r *http.Request) {
	// Only accept POST requests
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Initialize invoice service
	service := NewService(database.GetDB())

	// Generate invoices for all signed contracts
	err := service.GenerateInvoicesForSignedContracts()
	if err != nil {
		log.Printf("Error generating invoices for signed contracts: %v", err)
		http.Error(w, "Error generating invoices", http.StatusInternalServerError)
		return
	}

	// Return success response
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Invoices generated successfully for all signed contracts",
	})
}

// CheckOverdueInvoicesHandler checks for overdue invoices and updates their status
func CheckOverdueInvoicesHandler(w http.ResponseWriter, r *http.Request) {
	// Initialize invoice service
	service := NewService(database.GetDB())

	// Check for overdue invoices
	err := service.CheckOverdueInvoices()
	if err != nil {
		log.Printf("Error checking overdue invoices: %v", err)
		http.Error(w, "Error checking overdue invoices", http.StatusInternalServerError)
		return
	}

	// Return success response
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Overdue invoices checked successfully",
		"time":    time.Now().Format(time.RFC3339),
	})
}
