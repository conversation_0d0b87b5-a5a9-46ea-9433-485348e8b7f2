package invoice

import (
	"database/sql"
	"fmt"
	"log"
	"path/filepath"
	"strings"
	"time"
)

// Repository handles database operations for invoices
type Repository struct {
	db *sql.DB
}

// NewRepository creates a new invoice repository
func NewRepository(db *sql.DB) *Repository {
	return &Repository{db: db}
}

// CreateInvoice inserts a new invoice into the database
func (r *Repository) CreateInvoice(invoice *Invoice) error {
	query := `
		INSERT INTO invoices (
			invoice_number, item_id, contract_id, seller_id, buyer_id,
			contract_value, contract_fee, currency_id, invoice_date, due_date, status,
			status_id, payment_term_id,
			buyer_paid_date, seller_paid_date,
			payment_slip_buyer, payment_slip_seller
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13,
			CASE WHEN $4 = buyer_id THEN $14 ELSE NULL END,
			CASE WHEN $4 = seller_id THEN $14 ELSE NULL END,
			CASE WHEN $4 = buyer_id THEN $15 ELSE NULL END,
			CASE WHEN $4 = seller_id THEN $15 ELSE NULL END
		) RETURNING id, created_at
	`

	err := r.db.QueryRow(
		query,
		invoice.InvoiceNumber,
		invoice.ItemID,
		invoice.ContractID,
		invoice.SellerID,
		invoice.BuyerID,
		invoice.ContractValue,
		invoice.ContractFee,
		invoice.CurrencyID,
		invoice.InvoiceDate,
		invoice.DueDate,
		invoice.Status,
		invoice.StatusID,
		invoice.PaymentTermID,
		invoice.DatePaid,
		invoice.PaymentSlipPath,
	).Scan(&invoice.ID, &invoice.CreatedAt)

	if err != nil {
		return fmt.Errorf("failed to create invoice: %w", err)
	}

	return nil
}

// GetInvoiceByID retrieves an invoice by its ID
func (r *Repository) GetInvoiceByID(id int64) (*Invoice, error) {
	query := `
		SELECT i.id, i.invoice_number, i.item_id, i.contract_id,
		       i.seller_id, i.buyer_id, i.contract_value, i.contract_fee, i.currency_id,
		       i.invoice_date, i.due_date, i.status, i.created_at, i.updated_at,
		       i.status_id, i.payment_term_id,
		       i.buyer_paid_date, i.seller_paid_date,
		       i.payment_slip_buyer, i.payment_slip_seller,
		       it.en_name, it.th_name,
		       COALESCE(s.organization_name, CONCAT(s.first_name, ' ', s.last_name)) as seller_name,
		       COALESCE(b.organization_name, CONCAT(b.first_name, ' ', b.last_name)) as buyer_name,
		       c.code as currency_code,
		       CAST(i.contract_id as text) as contract_number,
		       '' as product_name,
		       0 as quantity,
		       '' as unit_of_measure
		FROM invoices i
		JOIN items it ON i.item_id = it.id
		JOIN users s ON i.seller_id = s.id
		JOIN users b ON i.buyer_id = b.id
		JOIN currencies c ON i.currency_id = c.id
		WHERE i.id = $1
	`

	invoice := &Invoice{}
	err := r.db.QueryRow(query, id).Scan(
		&invoice.ID, &invoice.InvoiceNumber, &invoice.ItemID, &invoice.ContractID,
		&invoice.SellerID, &invoice.BuyerID, &invoice.ContractValue, &invoice.ContractFee, &invoice.CurrencyID,
		&invoice.InvoiceDate, &invoice.DueDate, &invoice.Status, &invoice.CreatedAt, &invoice.UpdatedAt,
		&invoice.StatusID, &invoice.PaymentTermID,
		&invoice.BuyerPaidDate, &invoice.SellerPaidDate,
		&invoice.PaymentSlipBuyer, &invoice.PaymentSlipSeller,
		&invoice.ItemEnName, &invoice.ItemThName,
		&invoice.SellerName, &invoice.BuyerName, &invoice.CurrencyCode,
		&invoice.ContractNumber,
		&invoice.ProductName, &invoice.Quantity, &invoice.UnitOfMeasure,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get invoice: %w", err)
	}

	// For backward compatibility
	if invoice.BuyerPaidDate.Valid {
		invoice.DatePaid = invoice.BuyerPaidDate
	} else if invoice.SellerPaidDate.Valid {
		invoice.DatePaid = invoice.SellerPaidDate
	}
	if invoice.PaymentSlipBuyer.Valid {
		invoice.PaymentSlipPath = sql.NullString{
			String: filepath.Join("buyer", invoice.PaymentSlipBuyer.String),
			Valid:  true,
		}
	} else if invoice.PaymentSlipSeller.Valid {
		invoice.PaymentSlipPath = sql.NullString{
			String: filepath.Join("seller", invoice.PaymentSlipSeller.String),
			Valid:  true,
		}
	}

	// Get contract details if available
	if invoice.ContractID > 0 {
		// Try to get contract details
		contractQuery := `
			SELECT CAST(c.id as text) as contract_number, c.quantity, '' as unit_of_measure
			FROM contracts c
			WHERE c.id = $1
		`
		err = r.db.QueryRow(contractQuery, invoice.ContractID).Scan(&invoice.ContractNumber, &invoice.Quantity, &invoice.UnitOfMeasure)
		if err != nil && err != sql.ErrNoRows {
			log.Printf("Warning: Failed to get contract details for invoice %d: %v", invoice.ID, err)
			// Continue without contract details
		}
	}

	return invoice, nil
}

// GetOriginalContractParties retrieves the original seller and buyer IDs and names for a contract.
func (r *Repository) GetOriginalContractParties(contractID int64) (sellerID, buyerID int64, sellerName, buyerName string, err error) {
	query := `
		SELECT
			s_user.id AS seller_user_id,
			COALESCE(s_user.organization_name, CONCAT(s_user.first_name, ' ', s_user.last_name)) AS seller_user_name,
			b_user.id AS buyer_user_id,
			COALESCE(b_user.organization_name, CONCAT(b_user.first_name, ' ', b_user.last_name)) AS buyer_user_name
		FROM contracts c
		JOIN matchings seller_match ON c.seller_matching_id = seller_match.id
		JOIN users s_user ON seller_match.user_id = s_user.id
		JOIN matchings buyer_match ON c.buyer_matching_id = buyer_match.id
		JOIN users b_user ON buyer_match.user_id = b_user.id
		WHERE c.id = $1
	`
	err = r.db.QueryRow(query, contractID).Scan(&sellerID, &sellerName, &buyerID, &buyerName)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, 0, "", "", fmt.Errorf("contract not found with ID %d", contractID)
		}
		return 0, 0, "", "", fmt.Errorf("failed to get original contract parties for contract ID %d: %w", contractID, err)
	}
	return
}

// GetFeeInvoiceByContractAndUser retrieves a specific fee invoice for a contract and user (who is the buyer of the fee).
func (r *Repository) GetFeeInvoiceByContractAndUser(contractID, feePayerUserID int64) (*Invoice, error) {
	query := `
		SELECT i.id, i.invoice_number, i.item_id, i.contract_id,
		       i.seller_id, i.buyer_id, i.contract_value, i.contract_fee, i.currency_id,
		       i.invoice_date, i.due_date, i.status, i.created_at, i.updated_at,
		       i.status_id, i.payment_term_id,
		       i.buyer_paid_date, i.seller_paid_date,
		       i.payment_slip_buyer, i.payment_slip_seller
		FROM invoices i
		WHERE i.item_id = 1 AND i.contract_id = $1 AND i.buyer_id = $2
		LIMIT 1
	`
	invoice := &Invoice{}
	err := r.db.QueryRow(query, contractID, feePayerUserID).Scan(
		&invoice.ID, &invoice.InvoiceNumber, &invoice.ItemID, &invoice.ContractID,
		&invoice.SellerID, &invoice.BuyerID, &invoice.ContractValue, &invoice.ContractFee, &invoice.CurrencyID,
		&invoice.InvoiceDate, &invoice.DueDate, &invoice.Status, &invoice.CreatedAt, &invoice.UpdatedAt,
		&invoice.StatusID, &invoice.PaymentTermID,
		&invoice.BuyerPaidDate, &invoice.SellerPaidDate,
		&invoice.PaymentSlipBuyer, &invoice.PaymentSlipSeller,
	)
	return invoice, err // Return error directly, will be sql.ErrNoRows if not found
}

// GetInvoicesForUser retrieves all invoices for a specific user
func (r *Repository) GetInvoicesForUser(userID int64) ([]Invoice, error) {
	// This query is more specific about which invoices to return:
	// 1. Regular invoices where the user is either buyer or seller
	// 2. Fee invoices where the user is the buyer AND the seller is not the user (buyer fees)
	// 3. Fee invoices where the user is the seller AND the buyer is not the user (seller fees)
	query := `
		SELECT i.id, i.invoice_number, i.item_id, i.contract_id,
		       i.seller_id, i.buyer_id, i.contract_value, i.contract_fee, i.currency_id,
		       i.invoice_date, i.due_date, i.status, i.created_at, i.updated_at,
		       i.status_id, i.payment_term_id, 
		       COALESCE(i.buyer_paid_date, i.seller_paid_date) as date_paid,
		       COALESCE(i.payment_slip_buyer, i.payment_slip_seller) as payment_slip_path,
		       it.en_name, it.th_name,
		       COALESCE(s.organization_name, CONCAT(s.first_name, ' ', s.last_name)) as seller_name,
		       COALESCE(b.organization_name, CONCAT(b.first_name, ' ', b.last_name)) as buyer_name,
		       c.code as currency_code
		FROM invoices i
		JOIN items it ON i.item_id = it.id
		JOIN users s ON i.seller_id = s.id
		JOIN users b ON i.buyer_id = b.id
		JOIN currencies c ON i.currency_id = c.id
		WHERE
			(i.item_id = 1 AND i.contract_fee > 0 AND i.buyer_id = $1) -- User's own fee invoice
			OR
			( (i.item_id != 1 OR i.contract_fee IS NULL OR i.contract_fee = 0) AND (i.seller_id = $1 OR i.buyer_id = $1) ) -- Regular invoices
		ORDER BY i.invoice_date DESC
	`

	rows, err := r.db.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get invoices: %w", err)
	}
	defer rows.Close()

	var invoices []Invoice
	for rows.Next() {
		var invoice Invoice
		err := rows.Scan(
			&invoice.ID, &invoice.InvoiceNumber, &invoice.ItemID, &invoice.ContractID,
			&invoice.SellerID, &invoice.BuyerID, &invoice.ContractValue, &invoice.ContractFee, &invoice.CurrencyID,
			&invoice.InvoiceDate, &invoice.DueDate, &invoice.Status, &invoice.CreatedAt, &invoice.UpdatedAt,
			&invoice.StatusID, &invoice.PaymentTermID, &invoice.DatePaid, &invoice.PaymentSlipPath,
			&invoice.ItemEnName, &invoice.ItemThName,
			&invoice.SellerName, &invoice.BuyerName, &invoice.CurrencyCode,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan invoice row: %w", err)
		}

		// For Brokerage fee invoices (item_id = 1), we need to check if this is a fee invoice
		// or a regular product invoice. Fee invoices should only be visible to the user they're for.
		// We can determine this by checking if the user is the buyer or seller and if contract_fee is set.

		// If this is a fee invoice (contract_fee is set), only show it to the relevant user
		if invoice.ItemID == 1 && invoice.ContractFee.Valid && invoice.ContractFee.Float64 > 0 {
			// This is a fee invoice - check if it's for the current user
			// For buyer fee invoices, only show to the buyer
			if invoice.BuyerID == userID && invoice.SellerID != userID {
				// This is a buyer fee invoice, show it to the buyer
			} else if invoice.SellerID == userID && invoice.BuyerID != userID {
				// This is a seller fee invoice, show it to the seller
			} else {
				// This is a fee invoice for the other party, skip it
				continue
			}
		}

		// For backward compatibility, still handle old invoice types
		// ItemInspectionFee (2) should only be visible to sellers
		// ItemAnalysisFee (3) should only be visible to buyers
		if (invoice.ItemID == 2 && invoice.SellerID != userID) ||
			(invoice.ItemID == 3 && invoice.BuyerID != userID) {
			continue
		}

		// For privacy, if the user is a buyer, don't show seller details
		// If the user is a seller, don't show buyer details
		if invoice.BuyerID == userID && invoice.SellerID != userID {
			// User is the buyer, mask seller information
			invoice.SellerName = "Platform" // Replace seller name with generic "Platform"
		} else if invoice.SellerID == userID && invoice.BuyerID != userID {
			// User is the seller, mask buyer information
			invoice.BuyerName = "Platform" // Replace buyer name with generic "Platform"
		}

		invoices = append(invoices, invoice)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating invoice rows: %w", err)
	}

	return invoices, nil
}

// UpdateInvoiceStatus updates the status of an invoice
func (r *Repository) UpdateInvoiceStatus(id int64, status string) error {
	// Map status strings to status IDs based on your database mapping
	var statusID int64
	switch status {
	case InvoiceStatusDraft:
		statusID = 23 // draft
	case InvoiceStatusSent:
		statusID = 67 // sent
	case InvoiceStatusViewed:
		statusID = 54 // read
	case InvoiceStatusPaid:
		statusID = 11 // completed
	case InvoiceStatusOverdue:
		statusID = 27 // expired
	case InvoiceStatusCanceled:
		statusID = 9 // canceled
	case InvoiceStatusDisputed:
		statusID = 12 // complaint
	default:
		log.Printf("Warning: Unknown status '%s', using string status only", status)
		// Continue with just the string status
		query := `
			UPDATE invoices
			SET status = $1, updated_at = $2
			WHERE id = $3
		`
		_, err := r.db.Exec(query, status, time.Now(), id)
		if err != nil {
			return fmt.Errorf("failed to update invoice status: %w", err)
		}
		return nil
	}

	// Update both status and status_id
	query := `
		UPDATE invoices
		SET status = $1, status_id = NULLIF($2, 0), updated_at = $3
		WHERE id = $4
	`
	_, err := r.db.Exec(query, status, statusID, time.Now(), id)
	if err != nil {
		return fmt.Errorf("failed to update invoice status: %w", err)
	}

	return nil
}

// UpdateInvoicePaymentProof updates the payment proof and date_paid for an invoice
func (r *Repository) UpdateInvoicePaymentProof(id int64, paymentSlipPath string, datePaid time.Time) error {
	// Use the status ID for "paid" status (11 = completed in your database)
	statusID := int64(11)

	// First get the invoice to determine if uploader is buyer or seller
	var sellerID, buyerID int64
	err := r.db.QueryRow("SELECT seller_id, buyer_id FROM invoices WHERE id = $1", id).Scan(&sellerID, &buyerID)
	if err != nil {
		return fmt.Errorf("failed to get invoice details: %w", err)
	}

	// Get the user type from the payment slip path (buyer/seller)
	userType := "buyer" // default
	if strings.HasPrefix(paymentSlipPath, "seller/") {
		userType = "seller"
	}

	// Update both status and status_id, and set the appropriate payment columns based on user type
	// Format datePaid as YYYY-MM-DD string for DATE column compatibility
	datePaidStr := datePaid.Format("2006-01-02")

	query := `
    UPDATE invoices
    SET buyer_paid_date = CASE WHEN $7 = 'buyer' THEN $2::DATE ELSE NULL END,
        seller_paid_date = CASE WHEN $7 = 'seller' THEN $2::DATE ELSE NULL END,
        payment_slip_buyer = CASE WHEN $7 = 'buyer' THEN $1 ELSE NULL END,
        payment_slip_seller = CASE WHEN $7 = 'seller' THEN $1 ELSE NULL END,
        status = $3, status_id = $4, updated_at = $5
    WHERE id = $6
`

	if _, err := r.db.Exec(query, paymentSlipPath, datePaidStr, InvoiceStatusPaid, statusID, time.Now(), id, userType); err != nil {
		return fmt.Errorf("failed to update invoice payment proof: %w", err)
	}

	// Ensure contract_fee is set to 0.5% of contract_value if it's null
	query = `
		UPDATE invoices
		SET contract_fee = contract_value * 0.005
		WHERE id = $1 AND (contract_fee IS NULL OR contract_fee = 0)
	`
	_, err = r.db.Exec(query, id)
	if err != nil {
		log.Printf("Warning: Failed to update contract_fee: %v", err)
	}

	return nil
}

// GetNextInvoiceNumber generates the next invoice number
func (r *Repository) GetNextInvoiceNumber() (string, error) {
	var count int
	err := r.db.QueryRow("SELECT COUNT(*) FROM invoices").Scan(&count)
	if err != nil {
		return "", fmt.Errorf("failed to get invoice count: %w", err)
	}

	// Format: IV-YYYYMM-XXXX where XXXX is sequential (without day)
	now := time.Now()
	dateStr := now.Format("200601") // YYYYMM format
	invoiceNumber := fmt.Sprintf("IV-%s-%04d", dateStr, count+1)

	return invoiceNumber, nil
}

// GetItems retrieves all items (formerly invoice types)
func (r *Repository) GetItems() ([]Item, error) {
	query := "SELECT id, en_name, th_name FROM items ORDER BY id"

	rows, err := r.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to get items: %w", err)
	}
	defer rows.Close()

	var items []Item
	for rows.Next() {
		var item Item
		if err := rows.Scan(&item.ID, &item.EnName, &item.ThName); err != nil {
			return nil, fmt.Errorf("failed to scan item: %w", err)
		}
		items = append(items, item)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating item rows: %w", err)
	}

	return items, nil
}

// GetInvoicesForContract retrieves all invoices for a specific contract
func (r *Repository) GetInvoicesForContract(contractID int64, userID int64) ([]Invoice, error) {
	// This query is more specific about which invoices to return:
	// 1. Regular invoices where the user is either buyer or seller
	// 2. Fee invoices where the user is the buyer AND the seller is not the user (buyer fees)
	// 3. Fee invoices where the user is the seller AND the buyer is not the user (seller fees)
	query := `
		SELECT i.id, i.invoice_number, i.item_id, i.contract_id,
		       i.seller_id, i.buyer_id, i.contract_value, i.contract_fee, i.currency_id,
		       i.invoice_date, i.due_date, i.status, i.created_at, i.updated_at,
		       i.status_id, i.payment_term_id,
		       COALESCE(i.buyer_paid_date, i.seller_paid_date) as date_paid,
		       COALESCE(i.payment_slip_buyer, i.payment_slip_seller) as payment_slip_path,
		       it.en_name, it.th_name,
		       COALESCE(s.organization_name, CONCAT(s.first_name, ' ', s.last_name)) as seller_name,
		       COALESCE(b.organization_name, CONCAT(b.first_name, ' ', b.last_name)) as buyer_name,
		       c.code as currency_code
		FROM invoices i
		JOIN items it ON i.item_id = it.id
		JOIN users s ON i.seller_id = s.id
		JOIN users b ON i.buyer_id = b.id
		JOIN currencies c ON i.currency_id = c.id
		WHERE i.contract_id = $1
		AND ( 
			(i.item_id = 1 AND i.contract_fee > 0 AND i.buyer_id = $2) -- User's own fee invoice for this contract
			OR
			( (i.item_id != 1 OR i.contract_fee IS NULL OR i.contract_fee = 0) AND (i.seller_id = $2 OR i.buyer_id = $2) ) -- Regular invoices for this contract
		)
		ORDER BY i.invoice_date DESC
	`

	rows, err := r.db.Query(query, contractID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get invoices for contract: %w", err)
	}
	defer rows.Close()

	var invoices []Invoice
	for rows.Next() {
		var invoice Invoice
		err := rows.Scan(
			&invoice.ID, &invoice.InvoiceNumber, &invoice.ItemID, &invoice.ContractID,
			&invoice.SellerID, &invoice.BuyerID, &invoice.ContractValue, &invoice.ContractFee, &invoice.CurrencyID,
			&invoice.InvoiceDate, &invoice.DueDate, &invoice.Status, &invoice.CreatedAt, &invoice.UpdatedAt,
			&invoice.StatusID, &invoice.PaymentTermID, &invoice.DatePaid, &invoice.PaymentSlipPath,
			&invoice.ItemEnName, &invoice.ItemThName,
			&invoice.SellerName, &invoice.BuyerName, &invoice.CurrencyCode,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan invoice row: %w", err)
		}

		// Get additional contract details
		contractQuery := `
			SELECT CONCAT('C-', c.id), p.name, c.quantity, ''
			FROM contracts c
			JOIN products p ON c.product_id = p.id
			WHERE c.id = $1
		`

		err = r.db.QueryRow(contractQuery, invoice.ContractID).Scan(
			&invoice.ContractNumber, &invoice.ProductName, &invoice.Quantity,
			&invoice.UnitOfMeasure, // Left as empty string since unit_of_measures table doesn't exist
		)

		if err != nil {
			log.Printf("Warning: Failed to get contract details for invoice %d: %v", invoice.ID, err)
			// Continue without contract details
		}

		invoices = append(invoices, invoice)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating invoice rows: %w", err)
	}

	return invoices, nil
}
