package markets

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/gorilla/mux"
)

type Handler struct {
	service Service
}

func NewHandler(service Service) *Handler {
	return &Handler{service: service}
}

// SearchProductsHandler handles product search requests
func (h *Handler) SearchProductsHandler(w http.ResponseWriter, r *http.Request) {
	// Parse query parameters
	query := r.URL.Query().Get("q")
	lang := r.URL.Query().Get("lang")
	limit, _ := strconv.Atoi(r.URL.Query().Get("limit"))
	offset, _ := strconv.Atoi(r.URL.Query().Get("offset"))

	// Parse optional filter parameters
	var categoryID, subcategoryID, marketID, submarketID *int
	if catID, err := strconv.Atoi(r.URL.Query().Get("category_id")); err == nil {
		categoryID = &catID
	}
	if subcatID, err := strconv.Atoi(r.URL.Query().Get("subcategory_id")); err == nil {
		subcategoryID = &subcatID
	}
	if mktID, err := strconv.Atoi(r.URL.Query().Get("market_id")); err == nil {
		marketID = &mktID
	}
	if submktID, err := strconv.Atoi(r.URL.Query().Get("submarket_id")); err == nil {
		submarketID = &submktID
	}

	// Create search parameters
	params := ProductSearchParams{
		Query:         query,
		CategoryID:    categoryID,
		SubcategoryID: subcategoryID,
		MarketID:      marketID,
		SubmarketID:   submarketID,
		Language:      lang,
		Limit:         limit,
		Offset:        offset,
	}

	// Perform search
	results, err := h.service.SearchProducts(r.Context(), params)
	if err != nil {
		http.Error(w, "Error searching products: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// Write response
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(results); err != nil {
		http.Error(w, "Error encoding response: "+err.Error(), http.StatusInternalServerError)
		return
	}
}

// GetProductQualities handles requests for product qualities
func (h *Handler) GetProductQualities(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	productID, err := strconv.Atoi(vars["id"])
	if err != nil {
		http.Error(w, "Invalid product ID", http.StatusBadRequest)
		return
	}

	lang := r.URL.Query().Get("lang")
	if lang == "" {
		lang = "en" // Default to English
	}

	qualities, err := h.service.GetProductQualities(r.Context(), productID, lang)
	if err != nil {
		http.Error(w, "Error fetching qualities", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(qualities); err != nil {
		http.Error(w, "Error encoding response", http.StatusInternalServerError)
		return
	}
}

// GetDeliveryTerms handles requests for delivery terms
func (h *Handler) GetDeliveryTerms(w http.ResponseWriter, r *http.Request) {
	isLocal := r.URL.Query().Get("local") == "true"

	terms, err := h.service.GetDeliveryTerms(r.Context(), isLocal)
	if err != nil {
		http.Error(w, "Error fetching delivery terms", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(terms)
}

func (h *Handler) GetMarkets(w http.ResponseWriter, r *http.Request) {
	markets, err := h.service.GetMarkets(r.Context())
	if err != nil {
		http.Error(w, "Error fetching markets", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(markets)
}

func (h *Handler) GetContractTypes(w http.ResponseWriter, r *http.Request) {
	contractTypes, err := h.service.GetContractTypes(r.Context())
	if err != nil {
		http.Error(w, "Error fetching contract types", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(contractTypes)
}

func (h *Handler) GetPaymentTerms(w http.ResponseWriter, r *http.Request) {
	isLocal := r.URL.Query().Get("local") == "true"

	terms, err := h.service.GetPaymentTerms(r.Context(), isLocal)
	if err != nil {
		http.Error(w, "Error fetching payment terms", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(terms)
}

func (h *Handler) GetMarketspaces(w http.ResponseWriter, r *http.Request) {
	marketspaces, err := h.service.GetMarketspaces(r.Context())
	if err != nil {
		http.Error(w, "Error fetching marketspaces", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(marketspaces)
}

// GetFilteredMarketData handles requests for filtered market data
func (h *Handler) GetFilteredMarketData(w http.ResponseWriter, r *http.Request) {
	// Parse query parameters
	productID, err := strconv.Atoi(r.URL.Query().Get("product_id"))
	if err != nil || productID == 0 {
		http.Error(w, "Invalid or missing product ID", http.StatusBadRequest)
		return
	}

	days, err := strconv.Atoi(r.URL.Query().Get("days"))
	if err != nil || days == 0 {
		days = 1 // Default to 1 day if not specified or invalid
	}

	// Parse optional filter parameters with default 0
	marketID, _ := strconv.Atoi(r.URL.Query().Get("market_id"))
	marketspaceID, _ := strconv.Atoi(r.URL.Query().Get("marketspace_id"))
	qualityID, _ := strconv.Atoi(r.URL.Query().Get("quality_id"))
	deliveryTermID, _ := strconv.Atoi(r.URL.Query().Get("delivery_term_id"))
	paymentTermID, _ := strconv.Atoi(r.URL.Query().Get("payment_term_id"))
	contractTypeID, _ := strconv.Atoi(r.URL.Query().Get("contract_type_id"))

	filter := MarketDataFilter{
		ProductID:      productID,
		MarketID:       marketID,
		MarketspaceID:  marketspaceID,
		QualityID:      qualityID,
		DeliveryTermID: deliveryTermID,
		PaymentTermID:  paymentTermID,
		ContractTypeID: contractTypeID,
		Days:           days,
	}

	// Get filtered market data
	data, err := h.service.GetFilteredMarketData(r.Context(), filter)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error fetching market data: %v", err), http.StatusInternalServerError)
		return
	}

	// Write response
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(data); err != nil {
		http.Error(w, fmt.Sprintf("Error encoding response: %v", err), http.StatusInternalServerError)
		return
	}
}

// GetMarketFutures handles requests for futures market data using the exact SQL query
func (h *Handler) GetMarketFutures(w http.ResponseWriter, r *http.Request) {
	// Parse query parameters
	productID, err := strconv.Atoi(r.URL.Query().Get("product_id"))
	if err != nil || productID == 0 {
		http.Error(w, "Invalid or missing product ID", http.StatusBadRequest)
		return
	}

	days, err := strconv.Atoi(r.URL.Query().Get("days"))
	if err != nil || days == 0 {
		days = 1 // Default to 1 day if not specified or invalid
	}

	// Parse optional filter parameters with default 0
	marketID, _ := strconv.Atoi(r.URL.Query().Get("market_id"))
	marketspaceID, _ := strconv.Atoi(r.URL.Query().Get("marketspace_id"))
	qualityID, _ := strconv.Atoi(r.URL.Query().Get("quality_id"))
	deliveryTermID, _ := strconv.Atoi(r.URL.Query().Get("delivery_term_id"))
	paymentTermID, _ := strconv.Atoi(r.URL.Query().Get("payment_term_id"))
	contractTypeID, _ := strconv.Atoi(r.URL.Query().Get("contract_type_id"))

	filter := MarketDataFilter{
		ProductID:      productID,
		MarketID:       marketID,
		MarketspaceID:  marketspaceID,
		QualityID:      qualityID,
		DeliveryTermID: deliveryTermID,
		PaymentTermID:  paymentTermID,
		ContractTypeID: contractTypeID,
		Days:           days,
	}

	// Get filtered market data - uses the exact SQL query from the repository
	data, err := h.service.GetFilteredMarketData(r.Context(), filter)
	if err != nil {
		log.Printf("Error fetching futures market data: %v", err)
		http.Error(w, fmt.Sprintf("Error fetching futures market data: %v", err), http.StatusInternalServerError)
		return
	}

	// Debug log to see what data we're returning
	log.Printf("Returning %d market data rows", len(data))
	if len(data) > 0 {
		log.Printf("Sample row: %+v", data[0])
	} else {
		log.Printf("No data found for the given filters")
	}

	// Write response
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(data); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, fmt.Sprintf("Error encoding response: %v", err), http.StatusInternalServerError)
		return
	}
}

// PriceHistoryHandler handles requests for price history data
func (h *Handler) PriceHistoryHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Parse query parameters
	productID, err := strconv.Atoi(r.URL.Query().Get("product_id"))
	if err != nil || productID <= 0 {
		http.Error(w, "Invalid product_id parameter", http.StatusBadRequest)
		return
	}

	// Parse optional filter parameters
	days, _ := strconv.Atoi(r.URL.Query().Get("days"))
	if days <= 0 {
		days = 30 // Default to 30 days
	}

	marketID, _ := strconv.Atoi(r.URL.Query().Get("market_id"))
	marketspaceID, _ := strconv.Atoi(r.URL.Query().Get("marketspace_id"))
	qualityID, _ := strconv.Atoi(r.URL.Query().Get("quality_id"))
	deliveryTermID, _ := strconv.Atoi(r.URL.Query().Get("delivery_term_id"))
	paymentTermID, _ := strconv.Atoi(r.URL.Query().Get("payment_term_id"))
	contractTypeID, _ := strconv.Atoi(r.URL.Query().Get("contract_type_id"))

	// Create filter
	filter := PriceHistoryFilter{
		ProductID:      productID,
		Days:           days,
		MarketID:       marketID,
		MarketspaceID:  marketspaceID,
		QualityID:      qualityID,
		DeliveryTermID: deliveryTermID,
		PaymentTermID:  paymentTermID,
		ContractTypeID: contractTypeID,
	}

	// Get price history data
	priceHistory, err := h.service.GetPriceHistory(ctx, filter)
	if err != nil {
		log.Printf("Error getting price history: %v", err)
		http.Error(w, "Failed to get price history data", http.StatusInternalServerError)
		return
	}

	// Return JSON response
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(priceHistory); err != nil {
		log.Printf("Error encoding price history response: %v", err)
	}
}

// PriceHistoryRealtimeHandler handles requests for real-time price history data
func (h *Handler) PriceHistoryRealtimeHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Parse query parameters
	productID, err := strconv.Atoi(r.URL.Query().Get("product_id"))
	if err != nil || productID <= 0 {
		http.Error(w, "Invalid product_id parameter", http.StatusBadRequest)
		return
	}

	// Parse optional filter parameters
	days, _ := strconv.Atoi(r.URL.Query().Get("days"))
	if days <= 0 {
		days = 1 // Default to 1 day for real-time updates
	}

	marketID, _ := strconv.Atoi(r.URL.Query().Get("market_id"))
	marketspaceID, _ := strconv.Atoi(r.URL.Query().Get("marketspace_id"))
	qualityID, _ := strconv.Atoi(r.URL.Query().Get("quality_id"))
	deliveryTermID, _ := strconv.Atoi(r.URL.Query().Get("delivery_term_id"))
	paymentTermID, _ := strconv.Atoi(r.URL.Query().Get("payment_term_id"))
	contractTypeID, _ := strconv.Atoi(r.URL.Query().Get("contract_type_id"))

	// Create filter
	filter := PriceHistoryFilter{
		ProductID:      productID,
		Days:           days,
		MarketID:       marketID,
		MarketspaceID:  marketspaceID,
		QualityID:      qualityID,
		DeliveryTermID: deliveryTermID,
		PaymentTermID:  paymentTermID,
		ContractTypeID: contractTypeID,
	}

	// Get real-time price history data
	priceHistory, err := h.service.GetRealtimePriceHistory(ctx, filter)
	if err != nil {
		log.Printf("Error getting real-time price history: %v", err)
		http.Error(w, "Failed to get real-time price history data", http.StatusInternalServerError)
		return
	}

	// Return JSON response
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
	w.Header().Set("Pragma", "no-cache")
	w.Header().Set("Expires", "0")
	if err := json.NewEncoder(w).Encode(priceHistory); err != nil {
		log.Printf("Error encoding real-time price history response: %v", err)
	}
}
