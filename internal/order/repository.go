package order

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/thongsoi/biomassx/database"
	"github.com/thongsoi/biomassx/internal/matching"
	"github.com/thongsoi/biomassx/internal/notification"
)

func FetchMarketspaces(db *sql.DB) ([]Marketspace, error) {
	rows, err := db.Query("SELECT id, en_name, th_name FROM marketspaces ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var marketspaces []Marketspace
	for rows.Next() {
		var marketspace Marketspace
		err := rows.Scan(&marketspace.ID, &marketspace.EnName, &marketspace.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		marketspaces = append(marketspaces, marketspace)
	}
	return marketspaces, nil
}

func FetchMarkets(db *sql.DB) ([]Market, error) {
	rows, err := db.Query("SELECT id, en_name, th_name FROM markets ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var markets []Market
	for rows.Next() {
		var market Market
		err := rows.Scan(&market.ID, &market.EnName, &market.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		markets = append(markets, market)
	}
	return markets, nil
}

func FetchOrderTypes(db *sql.DB) ([]OrderType, error) {
	rows, err := db.Query("SELECT id, en_name,th_name FROM order_types ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var orderTypes []OrderType
	for rows.Next() {
		var orderType OrderType
		err := rows.Scan(&orderType.ID, &orderType.EnName, &orderType.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		orderTypes = append(orderTypes, orderType)
	}
	return orderTypes, nil
}

func FetchMatchingTypes(db *sql.DB) ([]MatchingType, error) {
	rows, err := db.Query("SELECT id, en_name, th_name FROM matching_types WHERE id=7 ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var matchingTypes []MatchingType
	for rows.Next() {
		var matchingType MatchingType
		err := rows.Scan(&matchingType.ID, &matchingType.EnName, &matchingType.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		matchingTypes = append(matchingTypes, matchingType)
	}
	return matchingTypes, nil
}

func FetchContractTypes(db *sql.DB) ([]ContractType, error) {
	rows, err := db.Query("SELECT id, en_name,th_name FROM contract_types ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var contractTypes []ContractType
	for rows.Next() {
		var contractType ContractType
		err := rows.Scan(&contractType.ID, &contractType.EnName, &contractType.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		contractTypes = append(contractTypes, contractType)
	}
	return contractTypes, nil
}

func FetchUom(db *sql.DB) ([]Uom_id, error) {
	rows, err := db.Query("SELECT id,en_name, th_name FROM uoms ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var uom_ids []Uom_id
	for rows.Next() {
		var uom_id Uom_id
		err := rows.Scan(&uom_id.ID, &uom_id.EnName, &uom_id.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		uom_ids = append(uom_ids, uom_id)
	}
	return uom_ids, nil
}

func FetchCurrency(db *sql.DB) ([]Currency, error) {
	rows, err := db.Query("SELECT id,code FROM currencies ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var currencies []Currency
	for rows.Next() {
		var currency Currency
		err := rows.Scan(&currency.ID, &currency.Code)
		if err != nil {
			log.Println(err)
			continue
		}
		currencies = append(currencies, currency)
	}
	return currencies, nil
}

func FetchPacking(db *sql.DB) ([]PackingID, error) {
	rows, err := db.Query("SELECT id,en_name,th_name FROM packings ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var packingIDs []PackingID
	for rows.Next() {
		var packingID PackingID
		err := rows.Scan(&packingID.ID, &packingID.EnName, &packingID.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		packingIDs = append(packingIDs, packingID)
	}
	return packingIDs, nil
}

func FetchCountry(db *sql.DB) ([]Country, error) {
	rows, err := db.Query("SELECT id, en_name, th_name FROM countries ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var countryIDs []Country
	for rows.Next() {
		var countryID Country
		err := rows.Scan(&countryID.ID, &countryID.EnName, &countryID.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		countryIDs = append(countryIDs, countryID)
	}
	return countryIDs, nil
}

func FetchSubmarketsByMarketID(db *sql.DB, marketID int) ([]Submarket, error) {
	rows, err := db.Query("SELECT id, en_name, th_name  FROM submarkets WHERE market_id = $1 ORDER BY en_name ASC", marketID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var submarkets []Submarket
	for rows.Next() {
		var submarket Submarket
		err := rows.Scan(&submarket.ID, &submarket.EnName, &submarket.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		submarkets = append(submarkets, submarket)
	}
	return submarkets, nil
}

func FetchProductsBySubmarketID(db *sql.DB, submarketID int) ([]Product, error) {
	rows, err := db.Query("SELECT id, en_name, th_name FROM products WHERE submarket_id = $1 ORDER BY en_name ASC", submarketID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var products []Product
	for rows.Next() {
		var product Product
		err := rows.Scan(&product.ID, &product.EnName, &product.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		products = append(products, product)
	}
	return products, nil
}

func FetchQualitiesByProductID(db *sql.DB, productID int) ([]Quality, error) {
	query := `
        SELECT q.id, q.standard_id, s.en_name as standard_en_name, q.grade_id, g.en_name as grade_en_name
        FROM qualities q
        LEFT JOIN standards s ON q.standard_id = s.id
        LEFT JOIN grades g ON q.grade_id = g.id
        WHERE q.product_id = $1
        ORDER BY s.en_name ASC
    `
	rows, err := db.Query(query, productID)
	if err != nil {
		return nil, fmt.Errorf("database query error: %w", err)
	}
	defer rows.Close()

	var qualities []Quality
	for rows.Next() {
		var quality Quality
		var standardEnName, gradeEnName string
		if err := rows.Scan(&quality.ID, &quality.StandardID, &standardEnName, &quality.GradeID, &gradeEnName); err != nil {
			log.Printf("Error scanning row: %v", err)
			continue
		}

		// เพิ่มค่า en_name ที่ดึงมาจาก standards และ grades ลงใน Quality struct
		quality.StandardID = standardEnName
		quality.GradeID = gradeEnName
		qualities = append(qualities, quality)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("row iteration error: %w", err)
	}

	return qualities, nil
}

func FetchPortofloadings(db *sql.DB) ([]Portofloading, error) {
	rows, err := db.Query("SELECT id,en_name,th_name FROM ports ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var portofloadings []Portofloading
	for rows.Next() {
		var portofloading Portofloading
		err := rows.Scan(&portofloading.ID, &portofloading.EnName, &portofloading.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		portofloadings = append(portofloadings, portofloading)
	}
	return portofloadings, nil
}

func FetchPortofdischarges(db *sql.DB) ([]Portofdischarge, error) {
	rows, err := db.Query("SELECT id,en_name,th_name FROM ports ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var portofdischarges []Portofdischarge
	for rows.Next() {
		var portofdischarge Portofdischarge
		err := rows.Scan(&portofdischarge.ID, &portofdischarge.EnName, &portofdischarge.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		portofdischarges = append(portofdischarges, portofdischarge)
	}
	return portofdischarges, nil
}

func FetchProvinces(db *sql.DB) ([]Province, error) {
	rows, err := db.Query("SELECT id,en_name,th_name FROM provinces ORDER BY id ASC")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var provinces []Province
	for rows.Next() {
		var province Province
		err := rows.Scan(&province.ID, &province.EnName, &province.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		provinces = append(provinces, province)
	}
	return provinces, nil
}

func FetchDistrictByProvinceID(db *sql.DB, provinceID int) ([]District, error) {
	rows, err := db.Query("SELECT id, en_name,th_name FROM districts WHERE province_id = $1 ORDER BY en_name ASC", provinceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var districts []District
	for rows.Next() {
		var district District
		err := rows.Scan(&district.ID, &district.EnName, &district.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		districts = append(districts, district)
	}
	return districts, nil
}

func FetchSubdistrictByProvinceID(db *sql.DB, districtID int) ([]Subdistrict, error) {
	rows, err := db.Query("SELECT id, en_name, th_name FROM subdistricts WHERE district_id = $1 ORDER BY en_name ASC", districtID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var subdistricts []Subdistrict
	for rows.Next() {
		var subdistrict Subdistrict
		err := rows.Scan(&subdistrict.ID, &subdistrict.EnName, &subdistrict.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		subdistricts = append(subdistricts, subdistrict)
	}
	return subdistricts, nil
}

func FetchPaymentbyMarketspaceID(db *sql.DB, marketspaceID int) ([]Payment, error) {
	query := `
	SELECT pt.id, pt.en_name, pt.th_name
	FROM payment_terms_marketspaces ptm
	INNER JOIN payment_terms pt ON ptm.payment_term_id = pt.id
	WHERE ptm.marketspace_id = $1
	ORDER BY pt.en_name ASC
`
	rows, err := db.Query(query, marketspaceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var payments []Payment
	for rows.Next() {
		var payment Payment
		err := rows.Scan(&payment.ID, &payment.EnName, &payment.ThName)
		if err != nil {
			log.Println(err)
			continue
		}
		payments = append(payments, payment)
	}
	return payments, nil
}

func FetchDeliveryByMarketspaceID(db *sql.DB, marketspaceID int) ([]Delivery, error) {
	query := `
        SELECT dt.id, dt.en_name, dt.th_name
        FROM delivery_terms_marketspaces dtm
        INNER JOIN delivery_terms dt ON dtm.delivery_term_id = dt.id
        WHERE dtm.marketspace_id = $1
        ORDER BY dt.en_name ASC
    `
	rows, err := db.Query(query, marketspaceID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var deliverys []Delivery // ใช้ตัวแปรนี้ตามที่คุณต้องการ
	for rows.Next() {
		var delivery Delivery
		err := rows.Scan(&delivery.ID, &delivery.EnName, &delivery.ThName)
		if err != nil {
			log.Println("Error scanning row:", err)
			continue
		}
		deliverys = append(deliverys, delivery)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return deliverys, nil // ส่งคืน deliverys
}

// /////////////////////////////////////////////////////////////////////
func SubmitOrderHandler(w http.ResponseWriter, r *http.Request) {
	db := database.GetDB()

	// Test database connection
	err := db.Ping()
	if err != nil {
		log.Printf("Database connection error: %v", err)
		http.Error(w, "Database connection error", http.StatusInternalServerError)
		return
	}

	// Add more detailed logging
	log.Printf("Received form submission method: %s", r.Method)
	log.Printf("Content-Type: %s", r.Header.Get("Content-Type"))

	if r.Method != "POST" {
		log.Printf("Invalid method: %s", r.Method)
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get user ID from token
	userID, err := getUserIDFromToken(r)
	if err != nil {
		log.Printf("Error parsing token: %v", err)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	err = r.ParseForm()
	if err != nil {
		log.Printf("Error parsing form: %v", err)
		http.Error(w, "Invalid form data", http.StatusBadRequest)
		return
	}

	// Log all form values
	log.Printf("Form values received: %+v", r.Form)

	var order Order
	order.UserID = userID

	// Map form data to struct
	order.MarketspaceID = atoi(r.FormValue("marketspaceID"))
	order.MarketID = atoi(r.FormValue("marketID"))
	order.SubmarketID = atoi(r.FormValue("submarketID"))
	order.OrderTypeID = atoi(r.FormValue("orderTypeID"))
	order.MatchingTypeID = atoi(r.FormValue("matchingTypeID"))
	order.ContractTypeID = atoi(r.FormValue("contractTypeID"))
	order.ProductID = atoi(r.FormValue("productID"))
	order.QualityID = atoi(r.FormValue("quality_id"))
	order.Price = atof(r.FormValue("price"))
	order.Currencies = atoi(r.FormValue("currency_id"))
	order.Quantity = atof(r.FormValue("quantity"))
	order.UOMID = atoi(r.FormValue("uom_id"))
	order.PackingID = atoi(r.FormValue("packing_id"))
	order.PaymentTermID = atoi(r.FormValue("payment_term_id"))
	order.DeliveryTermID = atoi(r.FormValue("delivery_term_id"))
	order.CountryIDs = atoi(r.FormValue("country_id"))
	order.PortofloadingID = atoi(r.FormValue("port_of_loading_id"))
	order.PortofdischargeID = atoi(r.FormValue("port_of_discharge_id"))
	order.ProvinceID = atoi(r.FormValue("province_id"))
	order.DistrictID = atoi(r.FormValue("district_id"))
	order.SubdistrictID = atoi(r.FormValue("subdistrict_id"))

	// Add the validation here, after mapping but before date parsing
	if order.MarketspaceID == 0 || order.ProductID == 0 || order.Quantity == 0 {
		log.Printf("Missing required fields: MarketspaceID=%d, ProductID=%d, Quantity=%f",
			order.MarketspaceID, order.ProductID, order.Quantity)
		http.Error(w, "Missing required fields", http.StatusBadRequest)
		return
	}

	// Parse dates
	firstDate := r.FormValue("first_delivery_date")
	lastDate := r.FormValue("last_delivery_date")

	log.Printf("First delivery date: %s", firstDate)
	log.Printf("Last delivery date: %s", lastDate)

	order.FirstDeliveryDate, err = time.Parse("2006-01-02", firstDate)
	if err != nil {
		log.Printf("Error parsing first delivery date: %v", err)
		http.Error(w, "Invalid first delivery date", http.StatusBadRequest)
		return
	}

	order.LastDeliveryDate, err = time.Parse("2006-01-02", lastDate)
	if err != nil {
		log.Printf("Error parsing last delivery date: %v", err)
		http.Error(w, "Invalid last delivery date", http.StatusBadRequest)
		return
	}

	// Validate dates
	if firstDate == "" || lastDate == "" {
		log.Println("Delivery dates are required")
		http.Error(w, "Delivery dates are required", http.StatusBadRequest)
		return
	}

	// Validate that last date is not before first date
	if order.LastDeliveryDate.Before(order.FirstDeliveryDate) {
		log.Println("Last delivery date cannot be before first delivery date")
		http.Error(w, "Last delivery date must be after first delivery date", http.StatusBadRequest)
		return
	}

	// Validate that first date is not in the past
	today := time.Now().Truncate(24 * time.Hour)
	if order.FirstDeliveryDate.Before(today) {
		log.Println("First delivery date cannot be in the past")
		http.Error(w, "First delivery date cannot be in the past", http.StatusBadRequest)
		return
	}

	order.Remark = r.FormValue("remark")

	// Set default status
	order.StatusID = 13

	price := r.FormValue("price")
	quantity := r.FormValue("quantity")

	if price == "" || quantity == "" {
		log.Println("Price or Quantity is missing")
		http.Error(w, "Price and Quantity are required fields", http.StatusBadRequest)
		return
	}

	// Add logging before database insertion
	log.Printf("Attempting to insert order with values: %+v", order)

	// Insert data into database
	queryOrders := `
		INSERT INTO orders (
       	 	user_id, marketspace_id, market_id, submarket_id, order_type_id, matching_type_id, contract_type_id,
        	product_id, quality_id, price, currency_id, quantity, uom_id, packing_id, payment_term_id, delivery_term_id,
        	country_id,port_of_loading_id,port_of_discharge_id,province_id, district_id, subdistrict_id, first_delivery_date, last_delivery_date,
			remark, status_id
    ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25 ,$26
    )RETURNING matching_type_id`

	var matchingTypeID int
	row := db.QueryRow(queryOrders, order.UserID, order.MarketspaceID, order.MarketID, order.SubmarketID, order.OrderTypeID,
		order.MatchingTypeID, order.ContractTypeID, order.ProductID, order.QualityID, order.Price, order.Currencies, order.Quantity,
		order.UOMID, order.PackingID, order.PaymentTermID, order.DeliveryTermID, order.CountryIDs, order.PortofloadingID, order.PortofdischargeID,
		order.ProvinceID, order.DistrictID, order.SubdistrictID, order.FirstDeliveryDate, order.LastDeliveryDate, order.Remark, order.StatusID)

	err = row.Scan(&matchingTypeID)

	if err != nil {
		log.Printf("Error inserting order: %v", err)
		http.Error(w, "Unable to create order", http.StatusInternalServerError)
		return
	}

	// Insert data into "matchings" table
	queryMatchings := `
    	INSERT INTO matchings (
       	 	user_id, marketspace_id, market_id, submarket_id, order_type_id, matching_type_id, contract_type_id,
        	product_id, quality_id, price, currency_id, quantity, uom_id, packing_id, payment_term_id, delivery_term_id,country_id,
        	port_of_loading_id,port_of_discharge_id,province_id, district_id, subdistrict_id, first_delivery_date, last_delivery_date,
			remark, status_id
    ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25 ,$26
    )`

	_, err = db.Exec(queryMatchings, order.UserID, order.MarketspaceID, order.MarketID, order.SubmarketID, order.OrderTypeID,
		order.MatchingTypeID, order.ContractTypeID, order.ProductID, order.QualityID, order.Price, order.Currencies, order.Quantity,
		order.UOMID, order.PackingID, order.PaymentTermID, order.DeliveryTermID, order.CountryIDs, order.PortofloadingID, order.PortofdischargeID,
		order.ProvinceID, order.DistrictID, order.SubdistrictID, order.FirstDeliveryDate, order.LastDeliveryDate, order.Remark, order.StatusID)

	if err != nil {
		log.Printf("Error inserting into matchings: %v", err)
		http.Error(w, "Unable to create matching", http.StatusInternalServerError)
		return
	}
	if matchingTypeID == 7 {
		err = matching.MatchOrders(db)
		if err != nil {
			log.Printf("Error matching orders: %v", err)
			http.Error(w, "Error processing matching orders", http.StatusInternalServerError)
			return
		}
	}

	// Send notification to user
	// First, get user information
	var userEmail, userFirstName, userLastName, username string
	userQuery := `
		SELECT email, first_name, last_name, username
		FROM users
		WHERE id = $1
	`
	err = db.QueryRow(userQuery, userID).Scan(&userEmail, &userFirstName, &userLastName, &username)
	if err != nil {
		log.Printf("Error getting user information: %v", err)
		// Continue with redirect even if notification fails
	} else {
		// Get language from request (check both URL query and form data)
		language := r.URL.Query().Get("lang")
		if language == "" {
			language = r.FormValue("lang") // Check form data
		}
		if language == "" {
			language = "en" // Default to English if no language specified
		}
		log.Printf("Using language %s for order submission notification", language)

		// Store the language in the order struct for use in matching
		order.Language = language

		// Determine which language field to use in queries
		var langField string
		if language == "th" {
			langField = "th_name"
		} else {
			langField = "en_name"
		}

		// Get order type name
		var orderTypeName string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM order_types WHERE id = $1", langField), order.OrderTypeID).Scan(&orderTypeName)
		if err != nil {
			log.Printf("Error getting order type name: %v", err)
			orderTypeName = "Unknown"
		}

		// Get marketspace name
		var marketspaceName string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM marketspaces WHERE id = $1", langField), order.MarketspaceID).Scan(&marketspaceName)
		if err != nil {
			log.Printf("Error getting marketspace name: %v", err)
			marketspaceName = "Unknown"
		}

		// Get market name
		var marketName string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM markets WHERE id = $1", langField), order.MarketID).Scan(&marketName)
		if err != nil {
			log.Printf("Error getting market name: %v", err)
			marketName = "Unknown"
		}

		// Get submarket name
		var submarketName string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM submarkets WHERE id = $1", langField), order.SubmarketID).Scan(&submarketName)
		if err != nil {
			log.Printf("Error getting submarket name: %v", err)
			submarketName = "Unknown"
		}

		// Get contract type name
		var contractTypeName string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM contract_types WHERE id = $1", langField), order.ContractTypeID).Scan(&contractTypeName)
		if err != nil {
			log.Printf("Error getting contract type name: %v", err)
			contractTypeName = "Unknown"
		}

		// Get product name
		var productName string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM products WHERE id = $1", langField), order.ProductID).Scan(&productName)
		if err != nil {
			log.Printf("Error getting product name: %v", err)
			productName = "Unknown"
		}

		// Get quality name
		var qualityName string
		var qualityQuery string
		if language == "th" {
			qualityQuery = `
				SELECT CONCAT(s.th_name, ' - ', g.th_name) as quality_name
				FROM qualities q
				LEFT JOIN standards s ON q.standard_id = s.id
				LEFT JOIN grades g ON q.grade_id = g.id
				WHERE q.id = $1
			`
		} else {
			qualityQuery = `
				SELECT CONCAT(s.en_name, ' - ', g.en_name) as quality_name
				FROM qualities q
				LEFT JOIN standards s ON q.standard_id = s.id
				LEFT JOIN grades g ON q.grade_id = g.id
				WHERE q.id = $1
			`
		}
		err = db.QueryRow(qualityQuery, order.QualityID).Scan(&qualityName)
		if err != nil {
			log.Printf("Error getting quality name: %v", err)
			qualityName = "Standard"
		}

		// Get unit of measure
		var unitOfMeasure string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM uoms WHERE id = $1", langField), order.UOMID).Scan(&unitOfMeasure)
		if err != nil {
			log.Printf("Error getting unit of measure: %v", err)
			unitOfMeasure = "unit"
		}

		// Get packing name
		var packingName string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM packings WHERE id = $1", langField), order.PackingID).Scan(&packingName)
		if err != nil {
			log.Printf("Error getting packing name: %v", err)
			packingName = "Standard"
		}

		// Get currency
		var currency string
		err = db.QueryRow("SELECT code FROM currencies WHERE id = $1", order.Currencies).Scan(&currency)
		if err != nil {
			log.Printf("Error getting currency: %v", err)
			currency = "THB"
		}

		// Get payment term name
		var paymentTermName string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM payment_terms WHERE id = $1", langField), order.PaymentTermID).Scan(&paymentTermName)
		if err != nil {
			log.Printf("Error getting payment term name: %v", err)
			paymentTermName = "Standard"
		}

		// Get delivery terms
		var deliveryTerms string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM delivery_terms WHERE id = $1", langField), order.DeliveryTermID).Scan(&deliveryTerms)
		if err != nil {
			log.Printf("Error getting delivery terms: %v", err)
			deliveryTerms = "Standard"
		}

		// Get country name
		var countryName string
		err = db.QueryRow(fmt.Sprintf("SELECT %s FROM countries WHERE id = $1", langField), order.CountryIDs).Scan(&countryName)
		if err != nil {
			log.Printf("Error getting country name: %v", err)
			countryName = "Unknown"
		}

		// Get port of loading and port of discharge names only if we're dealing with a global market
		var portOfLoadingName, portOfDischargeName string

		if order.MarketspaceID == 2 { // Global Market
			// Only try to get these values if the IDs are valid
			if order.PortofloadingID > 0 {
				err = db.QueryRow(fmt.Sprintf("SELECT %s FROM ports WHERE id = $1", langField), order.PortofloadingID).Scan(&portOfLoadingName)
				if err != nil {
					log.Printf("Error getting port of loading name: %v", err)
					portOfLoadingName = ""
				}
			}

			if order.PortofdischargeID > 0 {
				err = db.QueryRow(fmt.Sprintf("SELECT %s FROM ports WHERE id = $1", langField), order.PortofdischargeID).Scan(&portOfDischargeName)
				if err != nil {
					log.Printf("Error getting port of discharge name: %v", err)
					portOfDischargeName = ""
				}
			}
		} else {
			// For local markets, these fields are not used
			portOfLoadingName = ""
			portOfDischargeName = ""
		}

		// Get province, district, and subdistrict names only if we're dealing with a local market
		var provinceName, districtName, subdistrictName string

		if order.MarketspaceID == 1 { // Local Market
			// Only try to get these values if the IDs are valid
			if order.ProvinceID > 0 {
				err = db.QueryRow(fmt.Sprintf("SELECT %s FROM provinces WHERE id = $1", langField), order.ProvinceID).Scan(&provinceName)
				if err != nil {
					log.Printf("Error getting province name: %v", err)
					provinceName = ""
				}
			}

			if order.DistrictID > 0 {
				err = db.QueryRow(fmt.Sprintf("SELECT %s FROM districts WHERE id = $1", langField), order.DistrictID).Scan(&districtName)
				if err != nil {
					log.Printf("Error getting district name: %v", err)
					districtName = ""
				}
			}

			if order.SubdistrictID > 0 {
				err = db.QueryRow(fmt.Sprintf("SELECT %s FROM subdistricts WHERE id = $1", langField), order.SubdistrictID).Scan(&subdistrictName)
				if err != nil {
					log.Printf("Error getting subdistrict name: %v", err)
					subdistrictName = ""
				}
			}
		} else {
			// For global markets, these fields are not used
			provinceName = ""
			districtName = ""
			subdistrictName = ""
		}

		// Format dates
		firstDeliveryDate := order.FirstDeliveryDate.Format("2006-01-02")
		lastDeliveryDate := order.LastDeliveryDate.Format("2006-01-02")

		// Create order details URL
		baseURL := os.Getenv("BASE_URL")
		if baseURL == "" {
			log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
			// No fallback, rely only on the environment variable
		}
		orderDetailsURL := fmt.Sprintf("%s/orders?lang=%s", baseURL, language)

		// Send notification
		notificationData := &notification.OrderSubmissionNotification{
			UserID:              int64(userID),
			Email:               userEmail,
			FirstName:           userFirstName,
			LastName:            userLastName,
			Username:            username,
			OrderID:             0, // This will be set by the order system
			OrderType:           orderTypeName,
			MarketspaceName:     marketspaceName,
			MarketspaceID:       order.MarketspaceID,
			MarketName:          marketName,
			SubmarketName:       submarketName,
			ContractTypeName:    contractTypeName,
			ProductName:         productName,
			QualityName:         qualityName,
			Quantity:            order.Quantity,
			UnitOfMeasure:       unitOfMeasure,
			PackingName:         packingName,
			Price:               order.Price,
			Currency:            currency,
			PaymentTermName:     paymentTermName,
			DeliveryTerms:       deliveryTerms,
			DeliveryTermID:      order.DeliveryTermID,
			FirstDeliveryDate:   firstDeliveryDate,
			LastDeliveryDate:    lastDeliveryDate,
			CountryName:         countryName,
			PortOfLoadingName:   portOfLoadingName,
			PortOfDischargeName: portOfDischargeName,
			ProvinceName:        provinceName,
			DistrictName:        districtName,
			SubdistrictName:     subdistrictName,
			Remark:              order.Remark,
			OrderDetailsURL:     orderDetailsURL,
			Language:            language,
			BaseURL:             baseURL,
		}

		// Send notification
		err = notification.SendOrderSubmissionNotification(notificationData)
		if err != nil {
			log.Printf("Error sending order submission notification: %v", err)
			// Continue with redirect even if notification fails
		} else {
			log.Printf("Order submission notification sent successfully to user ID: %d", userID)
		}
	}

	// After successful insertion
	log.Printf("Order successfully created. Redirecting to dashboard...")

	// Get language from request (check both URL query and form data)
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = r.FormValue("lang") // Check form data
	}

	// Include language parameter in redirect URL if it exists
	redirectURL := "/dashboard"
	if language != "" {
		redirectURL = fmt.Sprintf("/dashboard?lang=%s", language)
	}

	http.Redirect(w, r, redirectURL, http.StatusSeeOther)
}

// Helper functions for converting strings to int/float
func atoi(str string) int {
	val, _ := strconv.Atoi(str)
	return val
}

func atof(str string) float64 {
	val, _ := strconv.ParseFloat(str, 64)
	return val
}

// FetchUserOrders retrieves all orders for a specific user with their current status
func FetchUserOrders(db *sql.DB, userID int, language string) ([]UserOrder, error) {
	// Language-specific field selection
	nameField := "en_name"
	if language == "th" {
		nameField = "th_name"
	}

	query := fmt.Sprintf(`
	SELECT
		o.id, o.marketspace_id, o.market_id, o.submarket_id,
		o.order_type_id, o.matching_type_id, o.contract_type_id,
		o.product_id, o.quality_id, o.price, o.currency_id,
		o.quantity as original_quantity,
		m.quantity as available_quantity,
		o.uom_id, o.packing_id, o.payment_term_id, o.delivery_term_id,
		o.country_id, o.port_of_loading_id, o.port_of_discharge_id,
		o.province_id, o.district_id, o.subdistrict_id,
		o.first_delivery_date, o.last_delivery_date, o.remark,
		m.status_id, o.created_at,
		ms.%[1]s as marketspace_name,
		mk.%[1]s as market_name,
		sm.%[1]s as submarket_name,
		ot.%[1]s as order_type_name,
		mt.%[1]s as matching_type_name,
		ct.%[1]s as contract_type_name,
		p.%[1]s as product_name,
		CONCAT('Standard: ', std.%[1]s, ' - Grade: ', g.%[1]s) as quality_name,
		c.%[1]s as currency_name,
		u.%[1]s as uom_name,
		pk.%[1]s as packing_name,
		pt.%[1]s as payment_term_name,
		dt.%[1]s as delivery_term_name,
		co.%[1]s as country_name,
		pol.%[1]s as port_of_loading_name,
		pod.%[1]s as port_of_discharge_name,
		prov.%[1]s as province_name,
		dist.%[1]s as district_name,
		sd.%[1]s as subdistrict_name,
		s.%[1]s as status_name
	FROM
		orders o
	JOIN
		matchings m ON o.id = m.id
	JOIN
		marketspaces ms ON o.marketspace_id = ms.id
	JOIN
		markets mk ON o.market_id = mk.id
	JOIN
		submarkets sm ON o.submarket_id = sm.id
	JOIN
		order_types ot ON o.order_type_id = ot.id
	JOIN
		matching_types mt ON o.matching_type_id = mt.id
	JOIN
		contract_types ct ON o.contract_type_id = ct.id
	JOIN
		products p ON o.product_id = p.id
	JOIN
		qualities q ON o.quality_id = q.id
	JOIN
		standards std ON q.standard_id = std.id
	JOIN
		grades g ON q.grade_id = g.id
	JOIN
		currencies c ON o.currency_id = c.id
	JOIN
		uoms u ON o.uom_id = u.id
	JOIN
		packings pk ON o.packing_id = pk.id
	JOIN
		payment_terms pt ON o.payment_term_id = pt.id
	JOIN
		delivery_terms dt ON o.delivery_term_id = dt.id
	JOIN
		countries co ON o.country_id = co.id
	LEFT JOIN
		ports pol ON o.port_of_loading_id = pol.id
	LEFT JOIN
		ports pod ON o.port_of_discharge_id = pod.id
	LEFT JOIN
		provinces prov ON o.province_id = prov.id
	LEFT JOIN
		districts dist ON o.district_id = dist.id
	LEFT JOIN
		subdistricts sd ON o.subdistrict_id = sd.id
	JOIN
		statuses s ON m.status_id = s.id
	WHERE
		o.user_id = $1
	ORDER BY
		o.created_at DESC
	`, nameField)

	rows, err := db.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("error querying orders: %w", err)
	}
	defer rows.Close()

	var orders []UserOrder
	for rows.Next() {
		var order UserOrder
		err := rows.Scan(
			&order.ID, &order.MarketspaceID, &order.MarketID, &order.SubmarketID,
			&order.OrderTypeID, &order.MatchingTypeID, &order.ContractTypeID,
			&order.ProductID, &order.QualityID, &order.Price, &order.CurrencyID,
			&order.OriginalQuantity, &order.AvailableQuantity,
			&order.UOMID, &order.PackingID, &order.PaymentTermID, &order.DeliveryTermID,
			&order.CountryID, &order.PortOfLoadingID, &order.PortOfDischargeID,
			&order.ProvinceID, &order.DistrictID, &order.SubdistrictID,
			&order.FirstDeliveryDate, &order.LastDeliveryDate, &order.Remark,
			&order.StatusID, &order.CreatedAt,
			&order.MarketspaceName, &order.MarketName, &order.SubmarketName,
			&order.OrderTypeName, &order.MatchingTypeName, &order.ContractTypeName,
			&order.ProductName, &order.QualityName, &order.CurrencyName,
			&order.UOMName, &order.PackingName, &order.PaymentTermName,
			&order.DeliveryTermName, &order.CountryName,
			&order.PortOfLoadingName, &order.PortOfDischargeName,
			&order.ProvinceName, &order.DistrictName, &order.SubdistrictName,
			&order.StatusName,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning order row: %w", err)
		}
		orders = append(orders, order)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating order rows: %w", err)
	}

	return orders, nil
}

// UpdateOrder updates an existing order with new values
func UpdateOrder(db *sql.DB, orderID int64, userID int, price float64, quantity float64, remark string, firstDeliveryDate, lastDeliveryDate time.Time) error {
	// Start a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("error starting transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Check if the order exists and belongs to the user
	var originalQuantity float64
	var availableQuantity float64
	var statusID int
	err = tx.QueryRow(`
		SELECT o.quantity, m.quantity, m.status_id
		FROM orders o
		JOIN matchings m ON o.id = m.id
		WHERE o.id = $1 AND o.user_id = $2
	`, orderID, userID).Scan(&originalQuantity, &availableQuantity, &statusID)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("order not found or does not belong to user")
		}
		return fmt.Errorf("error checking order: %w", err)
	}

	// Check if the order can be edited (status 13 or 14)
	if statusID != 13 && statusID != 14 {
		return fmt.Errorf("order cannot be edited (status: %d)", statusID)
	}

	// For partially matched orders, ensure quantity is not increased
	if statusID == 14 {
		if quantity > availableQuantity {
			return fmt.Errorf("cannot increase quantity for partially matched orders")
		}
	}

	// Update the orders table with delivery dates
	_, err = tx.Exec(`
		UPDATE orders
		SET price = $1, remark = $2, first_delivery_date = $3, last_delivery_date = $4, updated_at = CURRENT_TIMESTAMP
		WHERE id = $5 AND user_id = $6
	`, price, remark, firstDeliveryDate, lastDeliveryDate, orderID, userID)
	if err != nil {
		return fmt.Errorf("error updating order: %w", err)
	}

	// Update the matchings table
	_, err = tx.Exec(`
		UPDATE matchings
		SET price = $1, quantity = $2, remark = $3, updated_at = CURRENT_TIMESTAMP
		WHERE id = $4 AND user_id = $5 AND status_id IN (13, 14)
	`, price, quantity, remark, orderID, userID)
	if err != nil {
		return fmt.Errorf("error updating matching: %w", err)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("error committing transaction: %w", err)
	}

	return nil
}

// DeleteOrder deletes an order that hasn't been matched yet
func DeleteOrder(db *sql.DB, orderID int64, userID int) error {
	// Start a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("error starting transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Check if the order exists, belongs to the user, and has status 13 (not matched)
	var statusID int
	err = tx.QueryRow(`
		SELECT m.status_id
		FROM orders o
		JOIN matchings m ON o.id = m.id
		WHERE o.id = $1 AND o.user_id = $2
	`, orderID, userID).Scan(&statusID)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("order not found or does not belong to user")
		}
		return fmt.Errorf("error checking order: %w", err)
	}

	// Check if the order can be deleted (status 13 - available, or status 14 - partially matched with completed contract)
	if statusID != 13 && statusID != 14 {
		return fmt.Errorf("only available or partially matched orders with completed contracts can be deleted (status: %d)", statusID)
	}

	// Delete from matchings table first (due to potential foreign key constraints)
	_, err = tx.Exec(`
		DELETE FROM matchings
		WHERE id = $1 AND user_id = $2 AND (status_id = 13 OR status_id = 14)
	`, orderID, userID)
	if err != nil {
		return fmt.Errorf("error deleting from matchings: %w", err)
	}

	// Delete from orders table
	_, err = tx.Exec(`
		DELETE FROM orders
		WHERE id = $1 AND user_id = $2
	`, orderID, userID)
	if err != nil {
		return fmt.Errorf("error deleting from orders: %w", err)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("error committing transaction: %w", err)
	}

	return nil
}

// FetchOrderDetails fetches detailed information about a specific order
func FetchOrderDetails(db *sql.DB, orderID int64, userID int) (*UserOrder, error) {
	// Query to get order details
	query := `
		SELECT
			o.id, o.marketspace_id, o.market_id, o.submarket_id, o.order_type_id,
			o.matching_type_id, o.contract_type_id, o.product_id, o.quality_id,
			o.price, o.currency_id, o.quantity, m.quantity, o.uom_id, o.packing_id,
			o.payment_term_id, o.delivery_term_id, o.country_id, o.port_of_loading_id,
			o.port_of_discharge_id, o.province_id, o.district_id, o.subdistrict_id,
			o.first_delivery_date, o.last_delivery_date, o.remark, m.status_id, o.created_at
		FROM orders o
		JOIN matchings m ON o.id = m.id
		WHERE o.id = $1 AND o.user_id = $2
	`

	var order UserOrder
	var remark sql.NullString
	var portOfLoadingID, portOfDischargeID, provinceID, districtID, subdistrictID sql.NullInt64

	err := db.QueryRow(query, orderID, userID).Scan(
		&order.ID, &order.MarketspaceID, &order.MarketID, &order.SubmarketID, &order.OrderTypeID,
		&order.MatchingTypeID, &order.ContractTypeID, &order.ProductID, &order.QualityID,
		&order.Price, &order.CurrencyID, &order.OriginalQuantity, &order.AvailableQuantity, &order.UOMID, &order.PackingID,
		&order.PaymentTermID, &order.DeliveryTermID, &order.CountryID, &portOfLoadingID,
		&portOfDischargeID, &provinceID, &districtID, &subdistrictID,
		&order.FirstDeliveryDate, &order.LastDeliveryDate, &remark, &order.StatusID, &order.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("order not found or does not belong to user")
		}
		return nil, fmt.Errorf("error fetching order details: %w", err)
	}

	// Assign nullable fields
	order.Remark = remark
	order.PortOfLoadingID = portOfLoadingID
	order.PortOfDischargeID = portOfDischargeID
	order.ProvinceID = provinceID
	order.DistrictID = districtID
	order.SubdistrictID = subdistrictID

	// Get quality name for both English and Thai
	qualityQueryEn := `
		SELECT CONCAT(s.en_name, ' - ', g.en_name) as quality_name
		FROM qualities q
		LEFT JOIN standards s ON q.standard_id = s.id
		LEFT JOIN grades g ON q.grade_id = g.id
		WHERE q.id = $1
	`
	qualityQueryTh := `
		SELECT CONCAT(s.th_name, ' - ', g.th_name) as quality_name
		FROM qualities q
		LEFT JOIN standards s ON q.standard_id = s.id
		LEFT JOIN grades g ON q.grade_id = g.id
		WHERE q.id = $1
	`

	var qualityNameEn, qualityNameTh string
	err = db.QueryRow(qualityQueryEn, order.QualityID).Scan(&qualityNameEn)
	if err != nil {
		log.Printf("Error getting English quality name: %v", err)
		qualityNameEn = "Standard"
	}

	err = db.QueryRow(qualityQueryTh, order.QualityID).Scan(&qualityNameTh)
	if err != nil {
		log.Printf("Error getting Thai quality name: %v", err)
		qualityNameTh = "มาตรฐาน"
	}

	// Set quality name in the order
	order.QualityName = qualityNameEn

	// Add additional fields to the order for Thai support
	order.QualityNameTh = qualityNameTh

	return &order, nil
}

// UpdateOrderFull updates all fields of an available order
func UpdateOrderFull(db *sql.DB, orderID int64, userID int, price float64, quantity float64, remark string,
	marketspaceID, marketID, submarketID, orderTypeID, matchingTypeID, contractTypeID,
	productID, qualityID, currencyID, uomID, packingID, paymentTermID, deliveryTermID,
	countryID int, provinceID, districtID, subdistrictID, portOfLoadingID, portOfDischargeID sql.NullInt64,
	firstDeliveryDate, lastDeliveryDate time.Time) error {

	// Start a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("error starting transaction: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	// Check if the order exists, belongs to the user, and has status 13 (available)
	var statusID int
	err = tx.QueryRow(`
		SELECT m.status_id
		FROM orders o
		JOIN matchings m ON o.id = m.id
		WHERE o.id = $1 AND o.user_id = $2
	`, orderID, userID).Scan(&statusID)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("order not found or does not belong to user")
		}
		return fmt.Errorf("error checking order: %w", err)
	}

	// Check if the order can be fully edited (only status 13 - available)
	if statusID != 13 {
		return fmt.Errorf("only available orders can be fully edited (status: %d)", statusID)
	}

	// Update the orders table with all fields
	_, err = tx.Exec(`
		UPDATE orders
		SET
			marketspace_id = $1, market_id = $2, submarket_id = $3, order_type_id = $4,
			matching_type_id = $5, contract_type_id = $6, product_id = $7, quality_id = $8,
			price = $9, currency_id = $10, quantity = $11, uom_id = $12, packing_id = $13,
			payment_term_id = $14, delivery_term_id = $15, country_id = $16,
			port_of_loading_id = $17, port_of_discharge_id = $18,
			province_id = $19, district_id = $20, subdistrict_id = $21,
			first_delivery_date = $22, last_delivery_date = $23,
			remark = $24, updated_at = CURRENT_TIMESTAMP
		WHERE id = $25 AND user_id = $26
	`,
		marketspaceID, marketID, submarketID, orderTypeID,
		matchingTypeID, contractTypeID, productID, qualityID,
		price, currencyID, quantity, uomID, packingID,
		paymentTermID, deliveryTermID, countryID,
		portOfLoadingID, portOfDischargeID,
		provinceID, districtID, subdistrictID,
		firstDeliveryDate, lastDeliveryDate,
		remark, orderID, userID)
	if err != nil {
		return fmt.Errorf("error updating order: %w", err)
	}

	// Update the matchings table
	_, err = tx.Exec(`
		UPDATE matchings
		SET
			price = $1, quantity = $2, remark = $3, updated_at = CURRENT_TIMESTAMP
		WHERE id = $4 AND user_id = $5 AND status_id = 13
	`, price, quantity, remark, orderID, userID)
	if err != nil {
		return fmt.Errorf("error updating matching: %w", err)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("error committing transaction: %w", err)
	}

	return nil
}
