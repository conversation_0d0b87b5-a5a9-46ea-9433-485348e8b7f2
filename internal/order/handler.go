package order

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/thongsoi/biomassx/database"

	"github.com/golang-jwt/jwt"
)

var jwtKey = []byte("your_secret_key")

// Helper function to parse JWT and return user ID
func getUserIDFromToken(r *http.Request) (int, error) {
	cookie, err := r.<PERSON>("token")
	if err != nil {
		return 0, err
	}

	token, err := jwt.Parse(cookie.Value, func(token *jwt.Token) (interface{}, error) {
		return jwtKey, nil
	})
	if err != nil || !token.Valid {
		return 0, err
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return 0, err
	}
	userID := int(claims["user_id"].(float64))
	return userID, nil
}

func OrderHandler(w http.ResponseWriter, r *http.Request) {
	db := database.GetDB()

	// Get order data
	orderData, err := fetchOrderData(db)
	if err != nil {
		log.Printf("Error fetching order data: %v", err)
		http.Error(w, "Error fetching data", http.StatusInternalServerError)
		return
	}

	// Get language from query
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en"
	}

	// Add today's date to the data
	orderData["today"] = time.Now().Format("2006-01-02")
	orderData["lang"] = language

	var contentFile string
	switch language {
	case "th":
		contentFile = "views/pages/th/order_th.html"
	default:
		contentFile = "views/pages/en/order_en.html"
	}

	// Parse base template and content template
	tmpl, err := template.ParseFiles(
		"views/pages/order.html", // base template
		contentFile,              // content template
	)
	if err != nil {
		log.Printf("Template error: %v", err)
		http.Error(w, "Template error", http.StatusInternalServerError)
		return
	}

	// Execute template with base template name
	err = tmpl.ExecuteTemplate(w, "base", orderData)
	if err != nil {
		log.Printf("Template execution error: %v", err)
		http.Error(w, "Template execution error", http.StatusInternalServerError)
		return
	}
}

func fetchOrderData(db *sql.DB) (map[string]interface{}, error) {

	marketspaces, err := FetchMarketspaces(db)
	if err != nil {
		return nil, err
	}
	markets, err := FetchMarkets(db)
	if err != nil {
		return nil, err
	}
	orderTypes, err := FetchOrderTypes(db)
	if err != nil {
		return nil, err
	}
	matchingTypes, err := FetchMatchingTypes(db)
	if err != nil {
		return nil, err
	}
	contractTypes, err := FetchContractTypes(db)
	if err != nil {
		return nil, err
	}
	currencies, err := FetchCurrency(db)
	if err != nil {
		return nil, err
	}
	uomIDs, err := FetchUom(db)
	if err != nil {
		return nil, err
	}
	packingIDs, err := FetchPacking(db)
	if err != nil {
		return nil, err
	}
	countryIDs, err := FetchCountry(db)
	if err != nil {
		return nil, err
	}
	portOfLoadings, err := FetchPortofloadings(db)
	if err != nil {
		return nil, err
	}
	portOfDischarges, err := FetchPortofdischarges(db)
	if err != nil {
		return nil, err
	}
	provinces, err := FetchProvinces(db)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"Marketspaces":     marketspaces,
		"Markets":          markets,
		"OrderTypes":       orderTypes,
		"MatchingTypes":    matchingTypes,
		"ContractTypes":    contractTypes,
		"Currencies":       currencies,
		"UomIDs":           uomIDs,
		"PackingIDs":       packingIDs,
		"CountryIDs":       countryIDs,
		"Portofloadings":   portOfLoadings,
		"Portofdischarges": portOfDischarges,
		"Provinces":        provinces,
	}, nil
}

// OrderBookHandler handles the order book page
func OrderBookHandler(w http.ResponseWriter, r *http.Request) {
	// Get user ID from token
	userID, err := getUserIDFromToken(r)
	if err != nil {
		log.Printf("Error getting user ID: %v", err)
		http.Redirect(w, r, "/login", http.StatusSeeOther)
		return
	}

	db := database.GetDB()

	// Handle POST requests (edit/delete actions)
	if r.Method == "POST" {
		action := r.FormValue("action")
		orderID, err := strconv.ParseInt(r.FormValue("order_id"), 10, 64)
		if err != nil {
			log.Printf("Invalid order ID: %v", err)
			http.Error(w, "Invalid order ID", http.StatusBadRequest)
			return
		}

		switch action {
		case "delete":
			err = DeleteOrder(db, orderID, userID)
			if err != nil {
				log.Printf("Error deleting order: %v", err)
				http.Error(w, "Error deleting order", http.StatusInternalServerError)
				return
			}
		case "edit":
			// Parse form values
			price, err := strconv.ParseFloat(r.FormValue("price"), 64)
			if err != nil {
				log.Printf("Invalid price: %v", err)
				http.Error(w, "Invalid price", http.StatusBadRequest)
				return
			}

			quantity, err := strconv.ParseFloat(r.FormValue("quantity"), 64)
			if err != nil {
				log.Printf("Invalid quantity: %v", err)
				http.Error(w, "Invalid quantity", http.StatusBadRequest)
				return
			}

			remark := r.FormValue("remark")

			// Check if this is a full edit (for available orders)
			statusID, _ := strconv.Atoi(r.FormValue("status_id"))
			if statusID == 13 {
				// This is a full edit for an available order
				// Parse all the additional fields
				marketspaceID, _ := strconv.Atoi(r.FormValue("marketspaceID"))
				marketID, _ := strconv.Atoi(r.FormValue("marketID"))
				submarketID, _ := strconv.Atoi(r.FormValue("submarketID"))
				orderTypeID, _ := strconv.Atoi(r.FormValue("orderTypeID"))
				matchingTypeID, _ := strconv.Atoi(r.FormValue("matchingTypeID"))
				contractTypeID, _ := strconv.Atoi(r.FormValue("contractTypeID"))
				productID, _ := strconv.Atoi(r.FormValue("productID"))
				qualityID, _ := strconv.Atoi(r.FormValue("quality_id"))
				currencyID, _ := strconv.Atoi(r.FormValue("currency_id"))
				uomID, _ := strconv.Atoi(r.FormValue("uom_id"))
				packingID, _ := strconv.Atoi(r.FormValue("packing_id"))
				paymentTermID, _ := strconv.Atoi(r.FormValue("payment_term_id"))
				deliveryTermID, _ := strconv.Atoi(r.FormValue("delivery_term_id"))
				countryID, _ := strconv.Atoi(r.FormValue("country_id"))

				// Location fields (either province/district/subdistrict or port of loading/discharge)
				var provinceID, districtID, subdistrictID, portOfLoadingID, portOfDischargeID sql.NullInt64

				if marketspaceID == 1 || (marketspaceID == 2 && (deliveryTermID == 5 || deliveryTermID == 9)) {
					// Local market or global market with EXW/FCA delivery terms
					if id, err := strconv.Atoi(r.FormValue("province_id")); err == nil && id > 0 {
						provinceID = sql.NullInt64{Int64: int64(id), Valid: true}
					}
					if id, err := strconv.Atoi(r.FormValue("district_id")); err == nil && id > 0 {
						districtID = sql.NullInt64{Int64: int64(id), Valid: true}
					}
					if id, err := strconv.Atoi(r.FormValue("subdistrict_id")); err == nil && id > 0 {
						subdistrictID = sql.NullInt64{Int64: int64(id), Valid: true}
					}
				} else {
					// Global market with other delivery terms
					if id, err := strconv.Atoi(r.FormValue("port_of_loading_id")); err == nil && id > 0 {
						portOfLoadingID = sql.NullInt64{Int64: int64(id), Valid: true}
					}
					if id, err := strconv.Atoi(r.FormValue("port_of_discharge_id")); err == nil && id > 0 {
						portOfDischargeID = sql.NullInt64{Int64: int64(id), Valid: true}
					}
				}

				// Delivery dates
				var firstDeliveryDate, lastDeliveryDate time.Time
				if date := r.FormValue("first_delivery_date"); date != "" {
					firstDeliveryDate, _ = time.Parse("2006-01-02", date)
				}
				if date := r.FormValue("last_delivery_date"); date != "" {
					lastDeliveryDate, _ = time.Parse("2006-01-02", date)
				}

				// Call the full update function
				err = UpdateOrderFull(db, orderID, userID, price, quantity, remark,
					marketspaceID, marketID, submarketID, orderTypeID, matchingTypeID, contractTypeID,
					productID, qualityID, currencyID, uomID, packingID, paymentTermID, deliveryTermID,
					countryID, provinceID, districtID, subdistrictID, portOfLoadingID, portOfDischargeID,
					firstDeliveryDate, lastDeliveryDate)
			} else {
				// This is a basic edit for a partially matched order
				// Parse delivery dates for partially matched orders
				var firstDeliveryDate, lastDeliveryDate time.Time
				if date := r.FormValue("first_delivery_date"); date != "" {
					firstDeliveryDate, _ = time.Parse("2006-01-02", date)
				}
				if date := r.FormValue("last_delivery_date"); date != "" {
					lastDeliveryDate, _ = time.Parse("2006-01-02", date)
				}

				err = UpdateOrder(db, orderID, userID, price, quantity, remark, firstDeliveryDate, lastDeliveryDate)
			}

			if err != nil {
				log.Printf("Error updating order: %v", err)
				http.Error(w, "Error updating order", http.StatusInternalServerError)
				return
			}
		}

		// Get language from form
		language := r.FormValue("lang")
		if language == "" {
			language = "en"
		}

		// Redirect to prevent form resubmission, preserving language parameter
		http.Redirect(w, r, fmt.Sprintf("/order_book?lang=%s", language), http.StatusSeeOther)
		return
	}

	// Get language from query
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = "en"
	}

	// Fetch user orders
	orders, err := FetchUserOrders(db, userID, language)
	if err != nil {
		log.Printf("Error fetching user orders: %v", err)
		http.Error(w, "Error fetching orders", http.StatusInternalServerError)
		return
	}

	// Get order data for dropdowns
	orderData, err := fetchOrderData(db)
	if err != nil {
		log.Printf("Error fetching order data: %v", err)
		http.Error(w, "Error fetching data", http.StatusInternalServerError)
		return
	}

	// Prepare template data
	data := map[string]interface{}{
		"Orders":           orders,
		"Lang":             language,
		"Marketspaces":     orderData["Marketspaces"],
		"Markets":          orderData["Markets"],
		"OrderTypes":       orderData["OrderTypes"],
		"MatchingTypes":    orderData["MatchingTypes"],
		"ContractTypes":    orderData["ContractTypes"],
		"Currencies":       orderData["Currencies"],
		"UomIDs":           orderData["UomIDs"],
		"PackingIDs":       orderData["PackingIDs"],
		"CountryIDs":       orderData["CountryIDs"],
		"Portofloadings":   orderData["Portofloadings"],
		"Portofdischarges": orderData["Portofdischarges"],
		"Provinces":        orderData["Provinces"],
	}

	// Select template based on language
	var contentFile string
	switch language {
	case "th":
		contentFile = "views/pages/th/order_book_th.html"
	default:
		contentFile = "views/pages/en/order_book_en.html"
	}

	// Parse templates
	tmpl, err := template.ParseFiles(
		"views/pages/order_book.html", // base template
		contentFile,                   // language-specific content
	)
	if err != nil {
		log.Printf("Template error: %v", err)
		http.Error(w, "Template error", http.StatusInternalServerError)
		return
	}

	// Execute template
	err = tmpl.ExecuteTemplate(w, "base", data)
	if err != nil {
		log.Printf("Template execution error: %v", err)
		http.Error(w, "Template execution error", http.StatusInternalServerError)
	}
}

// OrderAPIHandler handles API requests for order details
func OrderAPIHandler(w http.ResponseWriter, r *http.Request) {
	// Get user ID from token
	userID, err := getUserIDFromToken(r)
	if err != nil {
		log.Printf("Error getting user ID: %v", err)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Extract order ID from URL path
	path := r.URL.Path
	parts := strings.Split(path, "/")
	if len(parts) < 3 {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	orderIDStr := parts[len(parts)-1]
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid order ID", http.StatusBadRequest)
		return
	}

	db := database.GetDB()

	// Fetch order details
	order, err := FetchOrderDetails(db, orderID, userID)
	if err != nil {
		log.Printf("Error fetching order details: %v", err)
		http.Error(w, "Error fetching order details", http.StatusInternalServerError)
		return
	}

	// Return order details as JSON
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(order)
}
