package currency

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/thongsoi/biomassx/database"
)

type CurrencyService struct {
	rates  map[string]ExchangeRate
	mutex  sync.RWMutex
	apiKey string
}

// NewCurrencyService creates a new currency service that uses database rates
func NewCurrencyService() *CurrencyService {
	cs := &CurrencyService{
		rates: make(map[string]ExchangeRate),
	}

	// Initialize with first rate fetch
	if _, err := cs.GetRate("USD", "THB"); err != nil {
		log.Printf("Initial rate fetch failed: %v", err)
	}

	return cs
}

func (cs *CurrencyService) GetRate(from, to string) (float64, error) {
	// Get database connection
	db := database.GetDB()

	// For USD to THB, we know currency_id=2 (THB) and base_currency_id=1 (USD)
	if from == "USD" && to == "THB" {
		var rate float64
		err := db.QueryRow(`
			SELECT rate FROM currency_rates 
			WHERE currency_id = 2 AND base_currency_id = 1
			ORDER BY created_at DESC LIMIT 1
		`).Scan(&rate)

		if err != nil {
			log.Printf("Error getting exchange rate from database: %v", err)
			// Fallback to the cached rate if available
			cs.mutex.RLock()
			cachedRate, exists := cs.rates["USD_THB"]
			cs.mutex.RUnlock()

			if exists {
				log.Printf("Using cached exchange rate USD/THB: %.4f", cachedRate.Rate)
				return cachedRate.Rate, nil
			}

			// If no cached rate, use a default value
			log.Printf("Using default exchange rate USD/THB: 36.52")
			return 36.52, nil
		}

		// Update the cache
		cs.mutex.Lock()
		cs.rates["USD_THB"] = ExchangeRate{
			Rate:      rate,
			UpdatedAt: time.Now(),
		}
		cs.mutex.Unlock()

		log.Printf("Database exchange rate USD/THB: %.4f", rate)
		return rate, nil
	}

	// For other currency pairs, we could implement similar logic
	// or fall back to the original API implementation

	// For now, just return an error for unsupported pairs
	return 0, fmt.Errorf("unsupported currency pair: %s/%s", from, to)
}

// func (cs *CurrencyService) updateRate(from, to string) error {
// 	// Increase timeout and add retries
// 	client := &http.Client{
// 		Timeout: 30 * time.Second,
// 		Transport: &http.Transport{
// 			DialContext: (&net.Dialer{
// 				Timeout:   10 * time.Second,
// 				KeepAlive: 30 * time.Second,
// 			}).DialContext,
// 			TLSHandshakeTimeout:   10 * time.Second,
// 			ResponseHeaderTimeout: 10 * time.Second,
// 			ExpectContinueTimeout: 1 * time.Second,
// 			MaxIdleConns:          10,
// 			IdleConnTimeout:       90 * time.Second,
// 		},
// 	}

// 	// Add retry logic
// 	var err error
// 	for retries := 0; retries < 3; retries++ {
// 		url := fmt.Sprintf("https://v6.exchangerate-api.com/v6/%s/pair/%s/%s", cs.apiKey, from, to)

// 		resp, err := client.Get(url)
// 		if err != nil {
// 			log.Printf("Attempt %d failed: %v", retries+1, err)
// 			time.Sleep(time.Second * time.Duration(retries+1))
// 			continue
// 		}
// 		defer resp.Body.Close()

// 		if resp.StatusCode != http.StatusOK {
// 			err = fmt.Errorf("API returned status: %d", resp.StatusCode)
// 			continue
// 		}

// 		var result struct {
// 			ConversionRate float64 `json:"conversion_rate"`
// 		}

// 		if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
// 			continue
// 		}

// 		// Log the rate with 4 decimal places
// 		log.Printf("Retrieved exchange rate %s/%s: %.4f", from, to, result.ConversionRate)

// 		cs.mutex.Lock()
// 		cs.rates[fmt.Sprintf("%s_%s", from, to)] = ExchangeRate{
// 			Rate:      result.ConversionRate,
// 			UpdatedAt: time.Now(),
// 		}
// 		cs.mutex.Unlock()

// 		return nil
// 	}

// 	return fmt.Errorf("failed after 3 retries: %v", err)
// }

// func (cs *CurrencyService) updateRatesPeriodically() {
// 	ticker := time.NewTicker(1 * time.Hour)
// 	for range ticker.C {
// 		if err := cs.updateRate("USD", "THB"); err != nil {
// 			log.Printf("Failed to update USD/THB rate: %v", err)
// 		}
// 	}
// }
