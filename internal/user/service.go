package user

import (
	"crypto/rand"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"net/http"
	"time"

	"fmt"
	"html/template"

	"github.com/thongsoi/biomassx/database"
	"golang.org/x/crypto/bcrypt"
)

// API endpoints to fetch data for cascading forms

// 1. <PERSON><PERSON> for fetching address types
func GetAddressTypesHandler(w http.ResponseWriter, r *http.Request) {
	db := database.GetDB()

	rows, err := db.Query("SELECT id, en_name, th_name FROM address_types ORDER BY en_name")
	if err != nil {
		http.Error(w, "Failed to fetch address types", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var addressTypes []map[string]interface{}
	for rows.Next() {
		var id int
		var enName, thName sql.NullString
		if err := rows.Scan(&id, &enName, &thName); err != nil {
			http.Error(w, "Error processing data", http.StatusInternalServerError)
			return
		}
		addressTypes = append(addressTypes, map[string]interface{}{
			"id":     id,
			"enName": enName.String,
			"thName": thName.String,
		})
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":      true,
		"addressTypes": addressTypes,
	})
}

// 2. Handler for fetching countries
func GetCountriesHandler(w http.ResponseWriter, r *http.Request) {
	db := database.GetDB()

	rows, err := db.Query("SELECT id, en_name, th_name FROM countries ORDER BY en_name")
	if err != nil {
		http.Error(w, "Failed to fetch countries", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var countries []map[string]interface{}
	for rows.Next() {
		var id int
		var enName, thName sql.NullString
		if err := rows.Scan(&id, &enName, &thName); err != nil {
			http.Error(w, "Error processing data", http.StatusInternalServerError)
			return
		}
		countries = append(countries, map[string]interface{}{
			"id":     id,
			"enName": enName.String,
			//"thName": thName.String,
		})
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":   true,
		"countries": countries,
	})
}

// 3. Handler for fetching provinces by country
func GetProvincesHandler(w http.ResponseWriter, r *http.Request) {
	countryID := r.URL.Query().Get("countryId")
	if countryID == "" {
		http.Error(w, "Country ID is required", http.StatusBadRequest)
		return
	}

	db := database.GetDB()
	rows, err := db.Query("SELECT id, en_name, th_name FROM provinces WHERE country_id = $1 ORDER BY en_name", countryID)
	if err != nil {
		http.Error(w, "Failed to fetch provinces", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var provinces []map[string]interface{}
	for rows.Next() {
		var id int
		var enName, thName sql.NullString
		if err := rows.Scan(&id, &enName, &thName); err != nil {
			http.Error(w, "Error processing data", http.StatusInternalServerError)
			return
		}
		provinces = append(provinces, map[string]interface{}{
			"id":     id,
			"enName": enName.String,
			"thName": thName.String,
		})
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":   true,
		"provinces": provinces,
	})
}

// 4. Handler for fetching districts by province
func GetDistrictsHandler(w http.ResponseWriter, r *http.Request) {
	provinceID := r.URL.Query().Get("provinceId")
	if provinceID == "" {
		http.Error(w, "Province ID is required", http.StatusBadRequest)
		return
	}

	db := database.GetDB()

	rows, err := db.Query("SELECT id, en_name, th_name FROM districts WHERE province_id = $1 ORDER BY en_name", provinceID)
	if err != nil {
		http.Error(w, "Failed to fetch districts", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var districts []map[string]interface{}
	for rows.Next() {
		var id int
		var enName, thName sql.NullString
		if err := rows.Scan(&id, &enName, &thName); err != nil {
			http.Error(w, "Error processing data", http.StatusInternalServerError)
			return
		}
		districts = append(districts, map[string]interface{}{
			"id":     id,
			"enName": enName.String,
			"thName": thName.String,
		})
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":   true,
		"districts": districts,
	})
}

// 5. Handler for fetching subdistricts by district
func GetSubdistrictsHandler(w http.ResponseWriter, r *http.Request) {
	districtID := r.URL.Query().Get("districtId")
	if districtID == "" {
		http.Error(w, "District ID is required", http.StatusBadRequest)
		return
	}

	db := database.GetDB()

	rows, err := db.Query("SELECT id, en_name, th_name FROM subdistricts WHERE district_id = $1 ORDER BY en_name", districtID)
	if err != nil {
		http.Error(w, "Failed to fetch subdistricts", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var subdistricts []map[string]interface{}
	for rows.Next() {
		var id int
		var enName, thName sql.NullString
		if err := rows.Scan(&id, &enName, &thName); err != nil {
			http.Error(w, "Error processing data", http.StatusInternalServerError)
			return
		}
		subdistricts = append(subdistricts, map[string]interface{}{
			"id":     id,
			"enName": enName.String,
			"thName": thName.String,
		})
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":      true,
		"subdistricts": subdistricts,
	})
}

// Service contains business logic for user operations.

type Service struct {
	repo *Repository
}

func NewService(repo *Repository) *Service {
	return &Service{repo: repo}
}

func (s *Service) RegisterUser(email, password string) error {
	// Hash with bcrypt
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	hashed := string(hashedBytes)

	// Insert into DB
	return s.repo.CreateUser(email, hashed)
}

// Use bcrypt for login
func (s *Service) LoginUser(email, password string) error {
	u, err := s.repo.GetUserByEmail(email)
	if err != nil || u == nil {
		return errors.New("invalid credentials")
	}
	// Compare with bcrypt
	if err := bcrypt.CompareHashAndPassword([]byte(u.HashedPassword), []byte(password)); err != nil {
		return errors.New("invalid credentials")
	}
	return nil
}

// 1) issue token valid for 1 hour
func (s *Service) IssueResetPasswordToken(email string) (string, error) {
	if _, err := s.repo.GetUserByEmail(email); err != nil {
		return "", errors.New("user not found")
	}
	b := make([]byte, 32)
	if _, err := rand.Read(b); err != nil {
		return "", err
	}
	token := base64.URLEncoding.EncodeToString(b)
	expiry := time.Now().Add(1 * time.Hour)
	return token, s.repo.SaveResetToken(email, token, expiry)
}

// 2) validate token + expiry
func (s *Service) ValidateResetToken(token string) (*UserPassword, error) {
	u, err := s.repo.GetUserByResetToken(token)
	if err != nil || u == nil {
		return nil, errors.New("invalid token")
	}
	if time.Now().After(u.ResetPasswordExpiry.Time) {
		return nil, errors.New("token expired")
	}
	return u, nil
}

// 3) reset password (with checks)
func (s *Service) ResetPasswordWithEmail(email, newPw, confirmPw string) error {
	if newPw != confirmPw {
		return errors.New("new passwords do not match")
	}
	u, err := s.repo.GetUserByEmail(email)
	if err != nil {
		return errors.New("user not found")
	}
	if bcrypt.CompareHashAndPassword([]byte(u.HashedPassword), []byte(newPw)) == nil {
		return errors.New("the password is the same as the previous password")
	}
	hash, _ := bcrypt.GenerateFromPassword([]byte(newPw), bcrypt.DefaultCost)
	return s.repo.UpdateUserPassword(email, string(hash))
}

// Helper Fucntion for Forget & Reset Password Handler
func renderForgot(w http.ResponseWriter, lang, msg string) {
	file := map[string]string{
		"en": "views/pages/en/forgot_password_en.html",
		"th": "views/pages/th/forgot_password_th.html",
	}[lang]
	tmpl, err := template.ParseFiles("views/pages/forgot_password.html", file)
	if err != nil {
		http.Error(w, "Template error", http.StatusInternalServerError)
		return
	}
	data := map[string]string{
		"lang":         lang,
		"ErrorMessage": msg, // shown in red in your HTML
	}
	tmpl.ExecuteTemplate(w, "base", data)
}

func renderReset(w http.ResponseWriter, lang, token, msg string) {
	file := map[string]string{
		"en": "views/pages/en/reset_password_en.html",
		"th": "views/pages/th/reset_password_th.html",
	}[lang]
	tmpl, _ := template.ParseFiles("views/pages/reset_password.html", file)
	data := map[string]string{
		"lang":         lang,
		"Token":        token,
		"ErrorMessage": msg,
	}
	if msg != "" {
		w.WriteHeader(http.StatusBadRequest)
	}
	tmpl.ExecuteTemplate(w, "base", data)
}

func success(lang string, w http.ResponseWriter) {
	msg := map[string]string{
		"en": "Your password has been reset successfully",
		"th": "รีเซ็ตรหัสผ่านของคุณสำเร็จแล้ว",
	}[lang]

	redirectIntro := map[string]string{
		"en": "You will be redirected to the login page in ",
		"th": "จะถูกเปลี่ยนเส้นทางไปยังหน้าเข้าสู่ระบบใน ",
	}[lang]

	redirectSuffix := map[string]string{
		"en": " seconds…",
		"th": " วินาที…",
	}[lang]

	html := fmt.Sprintf(`<!doctype html>
<meta charset="utf-8">
<meta http-equiv="refresh" content="3;URL=/login?lang=%s">
<style>
  body {font-family:sans-serif;text-align:center;margin-top:50px}
  h2   {color:green}
</style>

<h2>%s</h2>
<p>%s<span id="counter">3</span>%s</p>

<script>
let n = 3;
const span = document.getElementById('counter');
const timer = setInterval(() => {
  n--;
  if (n <= 0) { clearInterval(timer); return; }
  span.textContent = n;
}, 1000);
</script>
`, lang, msg, redirectIntro, redirectSuffix)

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// strict Gmail validator
// This function is deprecated and should not be used
func isTrulyGmail(email string) bool {
	// Always return true to allow all email addresses
	return true
}

func (s *Service) GetSegmentsSubsegments() ([]map[string]interface{}, []map[string]interface{}, error) {
	db := database.GetDB()
	// Fetch segments
	rows, err := db.Query("SELECT id, en_name, th_name FROM segments ORDER BY en_name")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to fetch segments: %v", err)
	}
	defer rows.Close()
	var segments []map[string]interface{}
	for rows.Next() {
		var id int
		var enName, thName sql.NullString
		if err := rows.Scan(&id, &enName, &thName); err != nil {
			return nil, nil, fmt.Errorf("error scanning segments: %v", err)
		}
		segments = append(segments, map[string]interface{}{
			"id":      id,
			"en_name": enName.String,
			"th_name": thName.String,
		})
	}

	// Fetch subsegments
	rows, err = db.Query("SELECT id, segment_id, en_name, th_name FROM subsegments ORDER BY en_name")
	if err != nil {
		return nil, nil, fmt.Errorf("failed to fetch subsegments: %v", err)
	}
	defer rows.Close()
	var subsegments []map[string]interface{}
	for rows.Next() {
		var id, segmentID int
		var enName, thName sql.NullString
		if err := rows.Scan(&id, &segmentID, &enName, &thName); err != nil {
			return nil, nil, fmt.Errorf("error scanning subsegments: %v", err)
		}
		subsegments = append(subsegments, map[string]interface{}{
			"id":         id,
			"segment_id": segmentID,
			"en_name":    enName.String,
			"th_name":    thName.String,
		})
	}
	return segments, subsegments, nil
}

// GetUserRoles retrieves all roles for a specific user
func (s *Service) GetUserRoles(userID int) ([]map[string]interface{}, error) {
	db := database.GetDB()
	query := `
        SELECT
            uss.id,
            uss.user_id,
            uss.segment_id,
            uss.subsegment_id,
            seg.en_name as segment_name,
            subseg.en_name as subsegment_name
        FROM
            users_segments_subsegments uss
        JOIN
            segments seg ON uss.segment_id = seg.id
        JOIN
            subsegments subseg ON uss.subsegment_id = subseg.id
        WHERE
            uss.user_id = $1
    `
	rows, err := db.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user roles: %v", err)
	}
	defer rows.Close()

	var roles []map[string]interface{}
	for rows.Next() {
		var id, userID, segmentID, subsegmentID int
		var segmentName, subsegmentName string
		if err := rows.Scan(&id, &userID, &segmentID, &subsegmentID, &segmentName, &subsegmentName); err != nil {
			return nil, fmt.Errorf("failed to scan role row: %v", err)
		}
		role := map[string]interface{}{
			"id":              id,
			"user_id":         userID,
			"segment_id":      segmentID,
			"subsegment_id":   subsegmentID,
			"segment_name":    segmentName,
			"subsegment_name": subsegmentName,
		}
		roles = append(roles, role)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating role rows: %v", err)
	}

	return roles, nil
}

// UpdateUserRoles updates the roles for a specific user
func (s *Service) UpdateUserRoles(userID int, roles []map[string]int) error {
	db := database.GetDB()

	// Start a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to start transaction: %v", err)
	}

	// Delete existing roles for the user
	_, err = tx.Exec("DELETE FROM users_segments_subsegments WHERE user_id = $1", userID)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to delete existing roles: %v", err)
	}

	// Insert new roles
	for _, role := range roles {
		segmentID, hasSegmentID := role["segment_id"]
		subsegmentID, hasSubsegmentID := role["subsegment_id"]

		if !hasSegmentID || !hasSubsegmentID {
			continue
		}

		_, err = tx.Exec(
			"INSERT INTO users_segments_subsegments (user_id, segment_id, subsegment_id) VALUES ($1, $2, $3)",
			userID, segmentID, subsegmentID,
		)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to insert role: %v", err)
		}
	}

	// Commit the transaction
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
}
