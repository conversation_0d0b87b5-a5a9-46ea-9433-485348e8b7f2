package user

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"

	"github.com/golang-jwt/jwt"
	"github.com/thongsoi/biomassx/database"
	"github.com/thongsoi/biomassx/internal/notification"
	"golang.org/x/crypto/bcrypt"
)

var jwtKey = []byte("your_secret_key")

var emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)

func RegisterHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		language := r.URL.Query().Get("lang")
		if language == "" {
			language = getLanguageFromToken(r)
		}
		if language == "" {
			language = "en"
		}
		var contentFile string
		switch language {
		case "th":
			contentFile = "views/pages/th/register_th.html"
		case "en":
			contentFile = "views/pages/en/register_en.html"
		default:
			http.Error(w, "Invalid language", http.StatusBadRequest)
			return
		}
		tmpl, err := template.ParseFiles("views/pages/register.html", contentFile)
		if err != nil {
			log.Println("Error parsing template:", err)
			http.Error(w, "Template not found", http.StatusInternalServerError)
			return
		}
		data := map[string]string{
			"lang": language,
		}
		log.Println("Selected language:", language)
		tmpl.ExecuteTemplate(w, "base", data)
		return
	}

	firstName := r.FormValue("firstName")
	lastName := r.FormValue("lastName")
	organizationName := r.FormValue("organizationName")
	username := r.FormValue("username")
	password := r.FormValue("password")
	phone := r.FormValue("phone")
	email := r.FormValue("email")

	if firstName == "" || lastName == "" || username == "" || password == "" || phone == "" || email == "" {
		http.Error(w, "All fields are required", http.StatusBadRequest)
		return
	}

	if !emailRegex.MatchString(email) {
		http.Error(w, "Invalid email format", http.StatusBadRequest)
		return
	}

	var exists bool
	err := database.DB.QueryRow("SELECT EXISTS(SELECT 1 FROM users WHERE username=$1)", username).Scan(&exists)
	if err != nil {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}
	if exists {
		http.Error(w, "Username already exists", http.StatusConflict)
		return
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		http.Error(w, "Error hashing password", http.StatusInternalServerError)
		return
	}

	createdAt := time.Now()

	_, err = database.DB.Exec(
		"INSERT INTO users (first_name, last_name, organization_name, username, hashed_password, phone, email, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)",
		firstName, lastName, organizationName, username, string(hashedPassword), phone, email, createdAt,
	)
	if err != nil {
		http.Error(w, "Error creating user", http.StatusInternalServerError)
		return
	}

	var userID int64
	err = database.DB.QueryRow("SELECT id FROM users WHERE username = $1", username).Scan(&userID)
	if err != nil {
		log.Printf("Error retrieving user ID: %v", err)
	}

	// Determine the user's language preference
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = getLanguageFromToken(r)
	}
	// Ensure the default language is set to English when the website is accessed for the first time.
	if language == "" {
		language = "en"
	}

	// Ensure the selected language is explicitly passed to all notification functions.
	if userID > 0 {
		// Pass the selected language to the registration notification.
		err = notification.SendRegistrationNotification(
			database.DB,
			userID,
			email,
			firstName,
			lastName,
			username,
			organizationName,
			phone,
			language, // Ensure the selected language is passed here
		)
		if err != nil {
			log.Printf("Error sending registration notification: %v", err)
		} else {
			log.Printf("Registration notification sent to %s in language %s", email, language)
		}

		// Pass the selected language to the profile completion notification.
		notifRepo := notification.NewPostgresRepository(database.DB)
		emailConfig := notification.NewEmailConfig(
			os.Getenv("SMTP_HOST"),
			os.Getenv("SMTP_PORT"),
			os.Getenv("SMTP_USERNAME"),
			os.Getenv("FASTMAIL_APP_PASSWORD"),
			os.Getenv("SMTP_SENDER_EMAIL"),
			"BiomassX Platform",
		)
		notifService := notification.NewEmailService(notifRepo, emailConfig)

		// First check what fields are missing for this user
		completeness, err := notifService.CheckProfileCompleteness(userID)
		if err != nil {
			log.Printf("Error checking profile completeness: %v", err)
		} else {
			// Create field importance map based on language
			var fieldImportance map[string]string
			switch language {
			case "th":
				fieldImportance = map[string]string{
					"First Name":      "จำเป็นสำหรับการปรับแต่งบัญชี",
					"Last Name":       "จำเป็นสำหรับการปรับแต่งบัญชี",
					"Phone":           "จำเป็นสำหรับการติดต่อและยืนยันตัวตน",
					"Street Address":  "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
					"City":            "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
					"Postal Code":     "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
					"Country":         "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
					"Province":        "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
					"District":        "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
					"Subdistrict":     "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
					"Address Details": "จำเป็นสำหรับการจัดส่งและการปฏิบัติตามกฎระเบียบ",
				}
			default:
				fieldImportance = map[string]string{
					"First Name":      "Required for account personalization",
					"Last Name":       "Required for account personalization",
					"Phone":           "Required for contact and verification",
					"Street Address":  "Required for delivery and compliance",
					"City":            "Required for delivery and compliance",
					"Postal Code":     "Required for delivery and compliance",
					"Country":         "Required for delivery and compliance",
					"Province":        "Required for delivery and compliance",
					"District":        "Required for delivery and compliance",
					"Subdistrict":     "Required for delivery and compliance",
					"Address Details": "Required for delivery and compliance",
				}
			}

			// Create a profile incomplete notification with the correct language and missing fields
			profileNotification := notification.ProfileIncompleteNotification{
				UserID:                      userID,
				Email:                       email,
				FirstName:                   firstName,
				LastName:                    lastName,
				Username:                    username,
				MissingBasicFields:          completeness.MissingBasicFields,
				MissingAddressFields:        completeness.MissingAddressFields,
				MissingFields:               append(completeness.MissingBasicFields, completeness.MissingAddressFields...),
				ProfileCompletionPercentage: completeness.CompletionPercentage,
				FieldImportance:             fieldImportance,
				Language:                    language, // Explicitly set the language
				ProfileURL:                  getBaseURL() + "/profile?lang=" + language,
				BasicInfoURL:                getBaseURL() + "/profile?section=basic&lang=" + language,
				AddressURL:                  getBaseURL() + "/profile?section=address&lang=" + language,
			}

			// Add a 3-second delay before sending the profile completion notification
			// This ensures the registration notification is sent and received first
			log.Printf("Adding 3-second delay before sending profile completion notification for user ID: %d", userID)
			time.Sleep(3 * time.Second)

			// Send the profile completion notification
			err = notifService.SendProfileIncompleteNotification(&profileNotification)
			if err != nil {
				log.Printf("Error sending profile completion notification: %v", err)
			} else {
				log.Printf("Profile completion notification sent to user ID: %d in language %s with missing fields: %v, %v",
					userID, language, completeness.MissingBasicFields, completeness.MissingAddressFields)
			}
		}
	}

	w.WriteHeader(http.StatusOK)
	successMsg := map[string]string{
		"en": "Registration successful",
		"th": "ลงทะเบียนสำเร็จ",
	}[language]
	fmt.Fprintf(w, "<div style='color: green;'>%s</div>", successMsg)
}

func getLanguageFromToken(r *http.Request) string {
	cookie, err := r.Cookie("token")
	if err != nil {
		return "en"
	}
	token, err := jwt.Parse(cookie.Value, func(token *jwt.Token) (interface{}, error) {
		return jwtKey, nil
	})
	if err != nil || !token.Valid {
		return "en"
	}
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return "en"
	}
	lang, ok := claims["language"].(string)
	if !ok {
		return "en"
	}
	return lang
}

// getBaseURL returns the base URL for the application from environment variables
func getBaseURL() string {
	baseURL := os.Getenv("BASE_URL")
	if baseURL == "" {
		log.Printf("Warning: BASE_URL environment variable is not set. Please set it in your .env file.")
		// No fallback, rely only on the environment variable
	}
	return baseURL
}

func getUserIDFromToken(r *http.Request) (int, error) {
	log.Println("[getUserIDFromToken] Extracting token from cookies")
	cookie, err := r.Cookie("token")
	if err != nil {
		log.Printf("[getUserIDFromToken] Error retrieving token from cookies: %v", err)
		return 0, err
	}

	log.Println("[getUserIDFromToken] Parsing and validating token")
	token, err := jwt.Parse(cookie.Value, func(token *jwt.Token) (interface{}, error) {
		return jwtKey, nil
	})
	if err != nil || !token.Valid {
		log.Printf("[getUserIDFromToken] Invalid or expired token: %v", err)
		return 0, err
	}

	log.Println("[getUserIDFromToken] Extracting claims from token")
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		log.Println("[getUserIDFromToken] Error converting token claims")
		return 0, err
	}

	userID := int(claims["user_id"].(float64))
	log.Printf("[getUserIDFromToken] Successfully extracted user_id: %d", userID)
	return userID, nil
}

func ProfileHandler(w http.ResponseWriter, r *http.Request) {
	language := r.URL.Query().Get("lang")
	if language == "" {
		language = getLanguageFromToken(r)
	}
	if language == "" {
		language = "en"
	}

	userID, err := getUserIDFromToken(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	db := database.GetDB()

	user, err := fetchUserData(db, userID)
	if err != nil {
		log.Println("Error fetching user:", err)
		http.Error(w, "User not found", http.StatusNotFound)
		return
	}
	if user == nil {
		log.Println("Fetched user data is nil")
		http.Error(w, "User data is invalid", http.StatusInternalServerError)
		return
	}

	var contentFile string
	switch language {
	case "th":
		contentFile = "views/pages/th/profile_th.html"
	case "en":
		contentFile = "views/pages/en/profile_en.html"
	default:
		http.Error(w, "Invalid language", http.StatusBadRequest)
		return
	}

	tmpl, err := template.ParseFiles("views/pages/profile.html", contentFile)
	if err != nil {
		log.Println("Error parsing template:", err)
		http.Error(w, "Template not found", http.StatusInternalServerError)
		return
	}

	data := map[string]interface{}{
		"lang": language,
		"user": user,
	}
	log.Println("Selected language:", language)
	tmpl.ExecuteTemplate(w, "base", data)
}

func ForgotPasswordHandler(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		language := r.URL.Query().Get("lang")
		if language == "" {
			language = getLanguageFromToken(r)
		}
		if language == "" {
			language = "en"
		}

		if r.Method == http.MethodGet {
			renderForgot(w, language, "")
			return
		}

		if r.Method == http.MethodPost {
			if err := r.ParseForm(); err != nil {
				renderForgot(w, language, "Form error")
				return
			}
			email := strings.ToLower(strings.TrimSpace(r.FormValue("email")))
			log.Printf("[ForgotPasswordHandler] received email: %s", email)

			svc := NewService(NewRepository(db))
			userRec, err := svc.repo.GetUserByEmail(email)
			if err != nil || userRec == nil {
				msg := map[string]string{
					"en": "We couldn't find this email in our system. Please register first.",
					"th": "ไม่พบอีเมลนี้ กรุณาสมัครสมาชิก",
				}[language]
				renderForgot(w, language, msg)
				return
			}

			token, err := svc.IssueResetPasswordToken(email)
			if err != nil {
				renderForgot(w, language, "Internal error")
				return
			}
			resetURL := fmt.Sprintf("%s/reset-password?token=%s&lang=%s", getBaseURL(), token, language)

			notifRepo := notification.NewPostgresRepository(db)
			emailConfig := notification.NewEmailConfig(
				os.Getenv("SMTP_HOST"),
				os.Getenv("SMTP_PORT"),
				os.Getenv("SMTP_USERNAME"),
				os.Getenv("FASTMAIL_APP_PASSWORD"),
				os.Getenv("SMTP_SENDER_EMAIL"),
				"BiomassX Platform",
			)
			notifService := notification.NewEmailService(notifRepo, emailConfig)

			resetNotification := notification.PasswordResetNotification{
				UserID:    int64(userRec.ID),
				Email:     email,
				FirstName: "User",
				LastName:  "",
				Username:  email,
				ResetLink: resetURL,
				Language:  language,
			}

			err = notifService.SendPasswordResetEmail(&resetNotification)
			if err != nil {
				log.Printf("Error sending password reset email: %v", err)
			} else {
				log.Printf("Password reset email sent to %s in language %s", email, language)
			}

			msg := map[string]string{
				"en": "We have sent a password reset link to your email address. Please check your inbox.",
				"th": "เราได้ส่งลิงก์รีเซ็ตรหัสผ่านไปยังอีเมลของคุณแล้ว",
			}[language]
			renderForgot(w, language, msg)
			return
		}
		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
	}
}

func CheckProfileCompleteness(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Determine the language from the request (though this might be limited for a cron job)
		language := r.URL.Query().Get("lang")
		if language == "" {
			language = getLanguageFromToken(r)
		}
		if language == "" {
			language = "en"
		}

		log.Printf("CheckProfileCompleteness running with language: %s", language)

		// Initialize the notification handler
		notifRepo := notification.NewPostgresRepository(db)
		emailConfig := notification.NewEmailConfig(
			os.Getenv("SMTP_HOST"),
			os.Getenv("SMTP_PORT"),
			os.Getenv("SMTP_USERNAME"),
			os.Getenv("FASTMAIL_APP_PASSWORD"),
			os.Getenv("SMTP_SENDER_EMAIL"),
			"BiomassX Platform",
		)
		notifService := notification.NewEmailService(notifRepo, emailConfig)
		notifHandler := notification.NewHandler(notifService, notifRepo)

		rows, err := db.Query(`
            SELECT id, email, first_name, last_name, username,
                   organization_name, phone, address
            FROM users
            WHERE
                (address IS NULL OR address = '')
        `)

		if err != nil {
			log.Printf("Error querying for users with incomplete profiles: %v", err)
			http.Error(w, "Database error", http.StatusInternalServerError)
			return
		}
		defer rows.Close()

		var notificationsSent int

		for rows.Next() {
			var (
				userID           int64
				email            string
				firstName        string
				lastName         string
				username         string
				organizationName sql.NullString
				phone            sql.NullString
				address          sql.NullString
			)

			err := rows.Scan(&userID, &email, &firstName, &lastName, &username,
				&organizationName, &phone, &address)
			if err != nil {
				log.Printf("Error scanning user row: %v", err)
				continue
			}

			// Use the CheckProfileCompleteness method from notification/handler.go
			err = notifHandler.CheckProfileCompleteness(userID, email, firstName, lastName, username, language)
			if err != nil {
				log.Printf("Error checking profile completeness for user %s: %v", email, err)
				continue
			}

			notificationsSent++
			log.Printf("Processed profile completion check for %s (%s %s) in language %s",
				email, firstName, lastName, language)
		}

		if err = rows.Err(); err != nil {
			log.Printf("Error iterating through user rows: %v", err)
			http.Error(w, "Database error", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"success": true, "notifications_sent": %d}`, notificationsSent)
	}
}

func ResetPasswordHandler(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		lang := r.URL.Query().Get("lang")
		if lang == "" {
			lang = getLanguageFromToken(r)
		}
		if lang == "" {
			lang = "en"
		}

		token := r.URL.Query().Get("token")
		if token == "" {
			http.Error(w, "missing token", http.StatusBadRequest)
			return
		}

		svc := NewService(NewRepository(db))
		userRec, err := svc.ValidateResetToken(token)
		if err != nil {
			renderReset(w, lang, token,
				map[string]string{
					"en": "Invalid or expired link",
					"th": "ลิงก์ไม่ถูกต้องหรือหมดอายุ",
				}[lang])
			return
		}

		if r.Method == http.MethodGet {
			renderReset(w, lang, token, "")
			return
		}

		if r.Method == http.MethodPost {
			if err := r.ParseForm(); err != nil {
				renderReset(w, lang, token, "form error")
				return
			}

			newPw := r.FormValue("newPassword")
			confirm := r.FormValue("confirmPassword")

			if err := svc.ResetPasswordWithEmail(userRec.Email, newPw, confirm); err != nil {
				renderReset(w, lang, token, err.Error())
				return
			}

			_ = svc.repo.ClearResetToken(userRec.ID)

			var firstName, lastName, username string
			err = db.QueryRow("SELECT first_name, last_name, username FROM users WHERE id = $1", userRec.ID).Scan(&firstName, &lastName, &username)
			if err != nil {
				log.Printf("Error fetching user details for notification: %v", err)
				firstName = "User"
				lastName = ""
				username = userRec.Email
			}

			notifRepo := notification.NewPostgresRepository(db)
			emailConfig := notification.NewEmailConfig(
				os.Getenv("SMTP_HOST"),
				os.Getenv("SMTP_PORT"),
				os.Getenv("SMTP_USERNAME"),
				os.Getenv("FASTMAIL_APP_PASSWORD"),
				os.Getenv("SMTP_SENDER_EMAIL"),
				"BiomassX Platform",
			)
			notifService := notification.NewEmailService(notifRepo, emailConfig)

			resetSuccessNotification := notification.PasswordResetSuccessNotification{
				UserID:    int64(userRec.ID),
				Email:     userRec.Email,
				FirstName: firstName,
				LastName:  lastName,
				Username:  username,
				Language:  lang,
			}

			err = notifService.SendPasswordResetSuccessEmail(&resetSuccessNotification)
			if err != nil {
				log.Printf("Error sending password reset success email: %v", err)
			} else {
				log.Printf("Password reset success email sent to %s in language %s", userRec.Email, lang)
			}

			success(lang, w)
			return
		}

		http.Error(w, "Method Not Allowed", http.StatusMethodNotAllowed)
	}
}

func GetSegmentsSubsegmentsHandler(w http.ResponseWriter, r *http.Request) {
	svc := NewService(NewRepository(database.GetDB()))
	segments, subsegments, err := svc.GetSegmentsSubsegments()
	if err != nil {
		http.Error(w, `{"success": false, "message": "Failed to fetch segments and subsegments"}`, http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":     true,
		"segments":    segments,
		"subsegments": subsegments,
	})
}

func GetUserRolesHandler(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromToken(r)
	if err != nil {
		log.Printf("Authentication error: %v", err)
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}

	service := NewService(NewRepository(database.GetDB()))

	roles, err := service.GetUserRoles(userID)
	if err != nil {
		log.Printf("Error fetching user roles: %v", err)
		http.Error(w, "Failed to fetch user roles: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"roles":   roles,
	})
}

func UpdateUserRolesHandler(w http.ResponseWriter, r *http.Request) {
	userID, err := getUserIDFromToken(r)
	if err != nil {
		log.Printf("Authentication error: %v", err)
		http.Error(w, "User not authenticated", http.StatusUnauthorized)
		return
	}

	var requestData struct {
		Roles []map[string]int `json:"roles"`
	}

	if err := json.NewDecoder(r.Body).Decode(&requestData); err != nil {
		log.Printf("Error parsing request body: %v", err)
		http.Error(w, "Invalid request data: "+err.Error(), http.StatusBadRequest)
		return
	}

	log.Printf("Received roles update request: %+v", requestData)

	service := NewService(NewRepository(database.GetDB()))

	if err := service.UpdateUserRoles(userID, requestData.Roles); err != nil {
		log.Printf("Error updating user roles: %v", err)
		http.Error(w, "Failed to update user roles: "+err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "User roles updated successfully",
	})
}
