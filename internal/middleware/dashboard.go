package middleware

import (
	"html/template"
	"log"
	"net/http"

	"github.com/thongsoi/biomassx/database"
	"github.com/thongsoi/biomassx/internal/invoice"
)

func DashboardHandler(w http.ResponseWriter, r *http.Request) {
	// Get user ID from token
	userID, err := getUserIDFromToken(r)
	if err != nil {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	language := r.URL.Query().Get("lang")
	if language == "" {
		language = getLanguageFromToken(r)
	}
	if language == "" {
		language = "en"
	}

	var contentFile string
	switch language {
	case "th":
		contentFile = "views/pages/th/dashboard_th.html"
	case "en":
		contentFile = "views/pages/en/dashboard_en.html"
	default:
		http.Error(w, "Invalid language", http.StatusBadRequest)
		return
	}

	// Get recent invoices for the user
	db := database.GetDB()
	invoiceService := invoice.NewService(db)
	invoices, err := invoiceService.GetUserInvoices(int64(userID))
	if err != nil {
		log.Printf("Error getting invoices: %v", err)
		// Continue without invoices
		invoices = []invoice.Invoice{}
	}

	// Limit to 5 most recent invoices
	recentInvoices := invoices
	if len(invoices) > 5 {
		recentInvoices = invoices[:5]
	}

	// Parse base template + content template
	tmpl, err := template.ParseFiles("views/pages/dashboard.html", contentFile)
	if err != nil {
		log.Println("Error parsing template:", err)
		http.Error(w, "Template not found", http.StatusInternalServerError)
		return
	}

	data := map[string]interface{}{
		"lang":           language,
		"RecentInvoices": recentInvoices,
		"UserID":         userID,
	}
	log.Println("Selected language:", language)

	tmpl.ExecuteTemplate(w, "base", data)
}
