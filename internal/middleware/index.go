package middleware

import (
	"html/template"
	"log"
	"net/http"

	"github.com/thongsoi/biomassx/database"
	"github.com/thongsoi/biomassx/internal/currency"
	"github.com/thongsoi/biomassx/internal/index"
)

func IndexHandler(w http.ResponseWriter, r *http.Request, currencyService *currency.CurrencyService) {
	// Get database connection
	db := database.GetDB()

	language := r.URL.Query().Get("lang")
	if language == "" {
		language = getLanguageFromToken(r)
	}
	if language == "" {
		language = "en"
	}

	var contentFile string
	switch language {
	case "th":
		contentFile = "views/pages/th/index_th.html"
	case "en":
		contentFile = "views/pages/en/index_en.html"
	default:
		http.Error(w, "Invalid language", http.StatusBadRequest)
		return
	}

	// Parse base template + content template
	tmpl, err := template.ParseFiles("views/pages/index_1.html", contentFile)
	if err != nil {
		log.Println("Error parsing template:", err)
		http.Error(w, "Template not found", http.StatusInternalServerError)
		return
	}

	// Get the latest orders
	orders, err := index.GetLatestOrders(db, language)
	if err != nil {
		http.Error(w, "Error fetching local orders", http.StatusInternalServerError)
		return
	}

	// Get the latest global orders
	globalOrders, err := index.GetLatestGlobalOrders(db, language)
	if err != nil {
		http.Error(w, "Error fetching global orders", http.StatusInternalServerError)
		return
	}

	// Get the top trading product with real-time currency rates
	topProduct, err := index.GetTopTradingProduct(db, language, currencyService)
	if err != nil {
		log.Printf("Error getting top product: %v", err)
		currency := "USD"
		if language == "th" {
			currency = "THB"
		}
		topProduct = &index.ProductIndex{
			ProductName:       "No Active Products",
			OriginalTHBVolume: 0,
			OriginalUSDVolume: 0,
			TotalMarketValue:  0,
			Currency:          currency,
		}
	} else {
		log.Printf("Top Product Market Value: %.2f %s", topProduct.TotalMarketValue, topProduct.Currency)
	}

	// Prepare template data
	data := map[string]interface{}{
		"lang":         language,
		"orders":       orders,
		"globalOrders": globalOrders, // Add global orders to template data
		"TopProduct":   topProduct,
	}

	log.Printf("Selected language: %s, Top Product: %+v", language, topProduct)

	if err := tmpl.ExecuteTemplate(w, "base", data); err != nil {
		log.Printf("Error executing template: %v", err)
		http.Error(w, "Error rendering page", http.StatusInternalServerError)
		return
	}
}
