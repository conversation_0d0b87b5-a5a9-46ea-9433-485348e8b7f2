-- Create the items table (formerly invoice_types)
CREATE TABLE items (
    id SERIAL2 PRIMARY KEY,
    en_name VA<PERSON><PERSON><PERSON>(64) NOT NULL,
    th_name VARCHAR(64) NOT NULL
);

-- Create the invoices table
CREATE TABLE invoices (
    id BIGSERIAL PRIMARY KEY,
    invoice_number VARCHAR(64) NOT NULL,
    item_id INT2 NOT NULL,
    contract_id BIGINT NOT NULL,
    seller_id BIGINT NOT NULL,
    buyer_id BIGINT NOT NULL,
    contract_value NUMERIC(15, 2) NOT NULL,
    contract_fee NUMERIC(15, 2),
    currency_id INT2 NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    status VARCHAR(50) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    payment_term_id INT2,
    status_id INT2,
    buyer_paid_date DATE,
    seller_paid_date DATE,
    payment_slip_buyer VARCHAR(255),
    payment_slip_seller VARCHAR(255),
    CONSTRAINT invoices_item_fk FOREIGN KEY (item_id) REFERENCES items(id),
    CONSTRAINT invoices_contract_fk FOREIGN KEY (contract_id) REFERENCES contracts(id),
    CONSTRAINT invoices_seller_fk FOREIGN KEY (seller_id) REFERENCES users(id),
    CONSTRAINT invoices_buyer_fk FOREIGN KEY (buyer_id) REFERENCES users(id),
    CONSTRAINT invoices_currency_fk FOREIGN KEY (currency_id) REFERENCES currencies(id),
    CONSTRAINT invoices_payment_term_fk FOREIGN KEY (payment_term_id) REFERENCES payment_terms(id),
    CONSTRAINT invoices_status_fk FOREIGN KEY (status_id) REFERENCES statuses(id)
);

-- Create indexes for better performance
CREATE INDEX idx_invoices_status_id ON invoices(status_id);
CREATE INDEX idx_invoices_payment_term_id ON invoices(payment_term_id);
CREATE INDEX idx_invoices_buyer_paid_date ON invoices(buyer_paid_date);
CREATE INDEX idx_invoices_seller_paid_date ON invoices(seller_paid_date);

-- Insert default items (invoice types)
INSERT INTO items (id, en_name, th_name) VALUES 
(1, 'Brokerage fee', 'ค่าธรรมเนียมแพลตฟอร์ม'),
(2, 'Inspection fee', 'ค่าบริการตรวจสอบ'),
(3, 'Analysis fee', 'ค่าบริการวิเคราะห์');
