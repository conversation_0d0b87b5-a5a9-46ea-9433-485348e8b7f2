-- DROP SCHEMA public;

CREATE SCHEMA public AUTHORIZATION postgres;

-- DROP SEQUENCE address_types_id_seq;

CREATE SEQUENCE address_types_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE addresses_id_seq;

CREATE SEQUENCE addresses_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE asset_categories_id_seq;

CREATE SEQUENCE asset_categories_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE asset_subcategories_id_seq;

CREATE SEQUENCE asset_subcategories_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE banks_id_seq;

CREATE SEQUENCE banks_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE categories_id_seq;

CREATE SEQUENCE categories_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE chatgpt_chi_id_seq;

CREATE SEQUENCE chatgpt_chi_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE **********
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE contract_types_id_seq;

CREATE SEQUENCE contract_types_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE **********
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE contracts_id_seq;

CREATE SEQUENCE contracts_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE countries_currencies_id_seq;

CREATE SEQUENCE countries_currencies_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE countries_id_seq;

CREATE SEQUENCE countries_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE countries_marketspaces_currencies_id_seq;

CREATE SEQUENCE countries_marketspaces_currencies_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE currencies_currency_id_seq;

CREATE SEQUENCE currencies_currency_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE currencies_id_seq;

CREATE SEQUENCE currencies_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE currency_rates_id_seq;

CREATE SEQUENCE currency_rates_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE delivery_terms_id_seq;

CREATE SEQUENCE delivery_terms_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE delivery_terms_marketspaces_id_seq;

CREATE SEQUENCE delivery_terms_marketspaces_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE departments_department_id_seq;

CREATE SEQUENCE departments_department_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE **********
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE districts_id_seq;

CREATE SEQUENCE districts_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE employees_employee_id_seq;

CREATE SEQUENCE employees_employee_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE **********
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE grades_id_seq;

CREATE SEQUENCE grades_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE marketplaces_id_seq;

CREATE SEQUENCE marketplaces_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE markets_id_seq;

CREATE SEQUENCE markets_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE marketspaces_id_seq;

CREATE SEQUENCE marketspaces_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE matching_types_id_seq;

CREATE SEQUENCE matching_types_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE **********
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE matchings_id_seq;

CREATE SEQUENCE matchings_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE notification_types_id_seq;

CREATE SEQUENCE notification_types_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE **********
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE notifications_id_seq;

CREATE SEQUENCE notifications_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE **********
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE order_type_id_seq;

CREATE SEQUENCE order_type_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE order_types_id_seq;

CREATE SEQUENCE order_types_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE **********
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE orders_id_seq;

CREATE SEQUENCE orders_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE packings_id_seq;

CREATE SEQUENCE packings_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE payment_terms_id_seq;

CREATE SEQUENCE payment_terms_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE payment_terms_marketspaces_id_seq;

CREATE SEQUENCE payment_terms_marketspaces_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE ports_id_seq;

CREATE SEQUENCE ports_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE product_categories_id_seq;

CREATE SEQUENCE product_categories_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE product_subcategories_id_seq;

CREATE SEQUENCE product_subcategories_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE products_id_seq;

CREATE SEQUENCE products_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE promotions_id_seq;

CREATE SEQUENCE promotions_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE provinces_id_seq;

CREATE SEQUENCE provinces_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE qualities_id_seq;

CREATE SEQUENCE qualities_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE regions_id_seq;

CREATE SEQUENCE regions_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE segments_id_seq;

CREATE SEQUENCE segments_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE service_categories_id_seq;

CREATE SEQUENCE service_categories_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE service_subcategories_id_seq;

CREATE SEQUENCE service_subcategories_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE standards_id_seq;

CREATE SEQUENCE standards_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE status_categories_id_seq;

CREATE SEQUENCE status_categories_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE status_categories_statuses_id_seq;

CREATE SEQUENCE status_categories_statuses_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE statuses_id_seq;

CREATE SEQUENCE statuses_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE subcategories_id_seq;

CREATE SEQUENCE subcategories_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE subdistricts_id_seq;

CREATE SEQUENCE subdistricts_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE submarkets_id_seq;

CREATE SEQUENCE submarkets_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE subsegments_id_seq;

CREATE SEQUENCE subsegments_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE uoms_id_seq;

CREATE SEQUENCE uoms_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 32767
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE users_id_seq;

CREATE SEQUENCE users_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;
-- DROP SEQUENCE users_segments_subsegments_id_seq;

CREATE SEQUENCE users_segments_subsegments_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;-- public.address_types definition

-- Drop table

-- DROP TABLE address_types;

CREATE TABLE address_types (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NULL,
	description varchar(256) NULL,
	CONSTRAINT address_types_pkey PRIMARY KEY (id)
);


-- public.addresses definition

-- Drop table

-- DROP TABLE addresses;

CREATE TABLE addresses (
	id bigserial NOT NULL,
	user_id int4 NOT NULL,
	address_type_id int2 NOT NULL,
	branch_number varchar(5) NULL,
	tax_number varchar(13) NULL,
	country_id int4 NULL,
	province_id int4 NULL,
	district_id int4 NULL,
	subdistrict_id int4 NULL,
	street varchar(128) NULL,
	address varchar(256) NULL,
	postal_code varchar(9) NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT addresses_pkey PRIMARY KEY (id)
);


-- public.asset_categories definition

-- Drop table

-- DROP TABLE asset_categories;

CREATE TABLE asset_categories (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT asset_categories_pkey PRIMARY KEY (id)
);


-- public.asset_subcategories definition

-- Drop table

-- DROP TABLE asset_subcategories;

CREATE TABLE asset_subcategories (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NULL,
	asset_category_id int4 NULL,
	CONSTRAINT asset_subcategories_pkey PRIMARY KEY (id)
);


-- public.banks definition

-- Drop table

-- DROP TABLE banks;

CREATE TABLE banks (
	id smallserial NOT NULL,
	en_name varchar(128) NULL,
	th_name varchar(128) NULL,
	CONSTRAINT banks_pkey PRIMARY KEY (id)
);


-- public.categories definition

-- Drop table

-- DROP TABLE categories;

CREATE TABLE categories (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT categories_pkey PRIMARY KEY (id)
);


-- public.chatgpt_chi definition

-- Drop table

-- DROP TABLE chatgpt_chi;

CREATE TABLE chatgpt_chi (
	id serial4 NOT NULL,
	title text NOT NULL,
	description text NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT chatgpt_chi_pkey PRIMARY KEY (id)
);


-- public.contract_types definition

-- Drop table

-- DROP TABLE contract_types;

CREATE TABLE contract_types (
	id serial4 NOT NULL,
	en_name varchar(50) NOT NULL,
	th_name varchar(50) NULL,
	code varchar(8) NULL,
	description varchar(100) NULL,
	CONSTRAINT contract_types_pkey PRIMARY KEY (id)
);


-- public.contracts definition

-- Drop table

-- DROP TABLE contracts;

CREATE TABLE contracts (
	id bigserial NOT NULL,
	marketspace_id int2 NOT NULL,
	market_id int2 NOT NULL,
	submarket_id int2 NOT NULL,
	contract_date timestamptz NOT NULL,
	contract_type_id int2 NOT NULL,
	seller_matching_id int8 NOT NULL,
	buyer_matching_id int8 NOT NULL,
	product_id int2 NOT NULL,
	quality_id int2 NOT NULL,
	price numeric(9, 2) NOT NULL,
	currency_id int2 NOT NULL,
	quantity numeric(9, 2) NOT NULL,
	uom_id int2 NOT NULL,
	packing_id int2 NOT NULL,
	delivery_term_id int2 NOT NULL,
	seller_country_id int2 NULL,
	buyer_country_id int2 NULL,
	place_of_delivery_province int2 NULL,
	place_of_delivery_district int2 NULL,
	place_of_delivery_subdistrict int2 NULL,
	seller_port_of_loading_id int2 NULL,
	seller_port_of_discharge_id int2 NULL,
	buyer_port_of_loading_id int2 NULL,
	buyer_port_of_discharge_id int2 NULL,
	delivery_status_id int2 NOT NULL,
	start_delivery_date date NOT NULL,
	finish_delivery_date date NOT NULL,
	payment_term_id int2 NOT NULL,
	payment_status_id int2 NOT NULL,
	seller_remark varchar(256) NULL,
	buyer_remark varchar(256) NULL,
	seller_confirmation_status_id int2 NOT NULL,
	buyer_confirmation_status_id int2 NOT NULL,
	contract_status_id int2 NOT NULL,
	CONSTRAINT contracts_pkey PRIMARY KEY (id)
);


-- public.countries definition

-- Drop table

-- DROP TABLE countries;

CREATE TABLE countries (
	id smallserial NOT NULL,
	en_name varchar(128) NULL,
	th_name varchar(128) NULL,
	alpha2 varchar(8) NULL,
	alpha3 varchar(8) NULL,
	country_code varchar(8) NULL,
	iso3166_2 varchar(16) NULL,
	region varchar(16) NULL,
	sub_region varchar(128) NULL,
	intermediate_region varchar(32) NULL,
	region_code varchar(8) NULL,
	sub_region_code varchar(8) NULL,
	intermediate_region_code varchar(8) NULL,
	CONSTRAINT countries_pkey PRIMARY KEY (id)
);


-- public.countries_marketspaces_currencies definition

-- Drop table

-- DROP TABLE countries_marketspaces_currencies;

CREATE TABLE countries_marketspaces_currencies (
	id smallserial NOT NULL,
	country_id int2 NULL,
	marketspace_id int2 NULL,
	currency_id int2 NULL,
	CONSTRAINT countries_marketspaces_currencies_pkey PRIMARY KEY (id)
);


-- public.currencies definition

-- Drop table

-- DROP TABLE currencies;

CREATE TABLE currencies (
	id smallserial NOT NULL,
	code varchar(8) NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT currencies_pkey PRIMARY KEY (id)
);


-- public.currency_rates definition

-- Drop table

-- DROP TABLE currency_rates;

CREATE TABLE currency_rates (
	id smallserial NOT NULL,
	currency_id int2 NULL,
	base_currency_id int2 NULL,
	rate numeric(16, 6) NULL,
	created_at timestamptz NULL,
	CONSTRAINT currency_rates_pkey PRIMARY KEY (id)
);


-- public.delivery_terms definition

-- Drop table

-- DROP TABLE delivery_terms;

CREATE TABLE delivery_terms (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	description varchar(255) NULL,
	CONSTRAINT delivery_terms_pkey PRIMARY KEY (id)
);


-- public.departments definition

-- Drop table

-- DROP TABLE departments;

CREATE TABLE departments (
	id int4 DEFAULT nextval('departments_department_id_seq'::regclass) NOT NULL,
	en_name varchar(100) NOT NULL,
	CONSTRAINT departments_pkey PRIMARY KEY (id)
);


-- public.grades definition

-- Drop table

-- DROP TABLE grades;

CREATE TABLE grades (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NULL,
	CONSTRAINT grades_pkey PRIMARY KEY (id)
);


-- public.marketplaces definition

-- Drop table

-- DROP TABLE marketplaces;

CREATE TABLE marketplaces (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NOT NULL
);


-- public.markets definition

-- Drop table

-- DROP TABLE markets;

CREATE TABLE markets (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NULL,
	CONSTRAINT markets_pkey PRIMARY KEY (id)
);


-- public.marketspaces definition

-- Drop table

-- DROP TABLE marketspaces;

CREATE TABLE marketspaces (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NULL,
	CONSTRAINT marketspaces_pkey PRIMARY KEY (id)
);


-- public.matching_types definition

-- Drop table

-- DROP TABLE matching_types;

CREATE TABLE matching_types (
	id serial4 NOT NULL,
	en_name varchar(32) NOT NULL,
	th_name varchar(50) NULL,
	code varchar(8) NULL,
	description varchar(100) NULL,
	CONSTRAINT matching_types_pkey PRIMARY KEY (id)
);


-- public.matchings definition

-- Drop table

-- DROP TABLE matchings;

CREATE TABLE matchings (
	id bigserial NOT NULL,
	user_id int4 NOT NULL,
	marketspace_id int2 NOT NULL,
	market_id int2 NOT NULL,
	submarket_id int2 NOT NULL,
	order_type_id int2 NOT NULL,
	matching_type_id int2 NOT NULL,
	contract_type_id int2 NOT NULL,
	product_id int2 NOT NULL,
	quality_id int2 NOT NULL,
	price numeric(9, 2) NOT NULL,
	currency_id int2 NOT NULL,
	quantity numeric(9, 2) NOT NULL,
	uom_id int2 NOT NULL,
	packing_id int2 NOT NULL,
	payment_term_id int2 NOT NULL,
	delivery_term_id int2 NOT NULL,
	country_id int2 NOT NULL,
	port_of_loading_id int2 NULL,
	port_of_discharge_id int2 NULL,
	province_id int2 NULL,
	district_id int2 NULL,
	subdistrict_id int2 NULL,
	first_delivery_date date NOT NULL,
	last_delivery_date date NOT NULL,
	remark varchar(128) NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	status_id int2 NULL,
	CONSTRAINT matchings_pkey PRIMARY KEY (id)
);


-- public.notification_types definition

-- Drop table

-- DROP TABLE notification_types;

CREATE TABLE notification_types (
	id serial4 NOT NULL,
	"name" varchar(50) NOT NULL,
	CONSTRAINT notification_types_name_key UNIQUE (name),
	CONSTRAINT notification_types_pkey PRIMARY KEY (id)
);


-- public.order_types definition

-- Drop table

-- DROP TABLE order_types;

CREATE TABLE order_types (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT order_type_pkey PRIMARY KEY (id)
);


-- public.orders definition

-- Drop table

-- DROP TABLE orders;

CREATE TABLE orders (
	id bigserial NOT NULL,
	user_id int4 NOT NULL,
	marketspace_id int2 NOT NULL,
	market_id int2 NOT NULL,
	submarket_id int2 NOT NULL,
	order_type_id int2 NOT NULL,
	matching_type_id int2 NOT NULL,
	contract_type_id int2 NOT NULL,
	product_id int2 NOT NULL,
	quality_id int2 NOT NULL,
	price numeric(9, 2) NOT NULL,
	currency_id int2 NOT NULL,
	quantity numeric(9, 2) NOT NULL,
	uom_id int2 NOT NULL,
	packing_id int2 NOT NULL,
	payment_term_id int2 NOT NULL,
	delivery_term_id int2 NOT NULL,
	country_id int2 NOT NULL,
	port_of_loading_id int2 NULL,
	port_of_discharge_id int2 NULL,
	province_id int2 NULL,
	district_id int2 NULL,
	subdistrict_id int2 NULL,
	first_delivery_date date NOT NULL,
	last_delivery_date date NOT NULL,
	remark varchar(128) NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	status_id int2 NULL,
	CONSTRAINT orders_pkey PRIMARY KEY (id)
);


-- public.packings definition

-- Drop table

-- DROP TABLE packings;

CREATE TABLE packings (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT packings_pkey PRIMARY KEY (id)
);


-- public.payment_terms definition

-- Drop table

-- DROP TABLE payment_terms;

CREATE TABLE payment_terms (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	description varchar(255) NULL,
	CONSTRAINT payment_terms_pkey PRIMARY KEY (id)
);


-- public.ports definition

-- Drop table

-- DROP TABLE ports;

CREATE TABLE ports (
	id smallserial NOT NULL,
	en_name varchar(128) NULL,
	th_name varchar(128) NULL,
	country_id int2 NULL,
	CONSTRAINT ports_pkey PRIMARY KEY (id)
);


-- public.product_categories definition

-- Drop table

-- DROP TABLE product_categories;

CREATE TABLE product_categories (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT product_categories_pkey PRIMARY KEY (id)
);


-- public.promotions definition

-- Drop table

-- DROP TABLE promotions;

CREATE TABLE promotions (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT promotions_pkey PRIMARY KEY (id)
);


-- public.regions definition

-- Drop table

-- DROP TABLE regions;

CREATE TABLE regions (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NOT NULL,
	country_id int4 NULL,
	CONSTRAINT regions_pkey PRIMARY KEY (id)
);


-- public.segments definition

-- Drop table

-- DROP TABLE segments;

CREATE TABLE segments (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT segments_pkey PRIMARY KEY (id)
);


-- public.service_categories definition

-- Drop table

-- DROP TABLE service_categories;

CREATE TABLE service_categories (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT service_categories_pkey PRIMARY KEY (id)
);


-- public.service_subcategories definition

-- Drop table

-- DROP TABLE service_subcategories;

CREATE TABLE service_subcategories (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NULL,
	service_category_id int4 NULL,
	CONSTRAINT service_subcategories_pkey PRIMARY KEY (id)
);


-- public.standards definition

-- Drop table

-- DROP TABLE standards;

CREATE TABLE standards (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NULL,
	product_category_id int2 NULL,
	product_subcategory_id int2 NULL,
	description varchar(255) NULL,
	CONSTRAINT standards_pkey PRIMARY KEY (id)
);


-- public.status_categories definition

-- Drop table

-- DROP TABLE status_categories;

CREATE TABLE status_categories (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT status_categories_pkey PRIMARY KEY (id)
);


-- public.statuses definition

-- Drop table

-- DROP TABLE statuses;

CREATE TABLE statuses (
	id smallserial NOT NULL,
	en_name varchar NOT NULL,
	th_name varchar NOT NULL,
	CONSTRAINT statuses_pkey PRIMARY KEY (id)
);


-- public.subcategories definition

-- Drop table

-- DROP TABLE subcategories;

CREATE TABLE subcategories (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	CONSTRAINT subcategories_pkey PRIMARY KEY (id)
);


-- public.subsegments definition

-- Drop table

-- DROP TABLE subsegments;

CREATE TABLE subsegments (
	id smallserial NOT NULL,
	en_name varchar(64) NULL,
	th_name varchar(64) NULL,
	segment_id int2 NULL,
	CONSTRAINT subsegments_pkey PRIMARY KEY (id)
);


-- public.uoms definition

-- Drop table

-- DROP TABLE uoms;

CREATE TABLE uoms (
	id smallserial NOT NULL,
	en_name varchar(32) NULL,
	th_name varchar(32) NULL,
	description varchar(128) NULL,
	CONSTRAINT uoms_pkey PRIMARY KEY (id)
);


-- public.users definition

-- Drop table

-- DROP TABLE users;

CREATE TABLE users (
	id bigserial NOT NULL,
	first_name varchar(128) NOT NULL,
	last_name varchar(128) NOT NULL,
	organization_name varchar(128) NULL,
	username varchar(64) NOT NULL,
	hashed_password varchar(255) NOT NULL,
	phone varchar(15) NOT NULL,
	email varchar(128) NOT NULL,
	confirmation_code varchar(20) NULL,
	identification_number varchar(24) NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	account_balance numeric(15, 2) NULL,
	employee_id int2 NULL,
	status_id int2 NULL,
	reset_password_token text NULL,
	reset_password_expiry timestamptz NULL,
	CONSTRAINT unique_email UNIQUE (email),
	CONSTRAINT unique_phone UNIQUE (phone),
	CONSTRAINT users_pkey PRIMARY KEY (id)
);


-- public.users_segments_subsegments definition

-- Drop table

-- DROP TABLE users_segments_subsegments;

CREATE TABLE users_segments_subsegments (
	id bigserial NOT NULL,
	user_id int4 NULL,
	segment_id int4 NULL,
	subsegment_id int4 NULL,
	CONSTRAINT users_segments_subsegments_pkey PRIMARY KEY (id)
);


-- public.countries_currencies definition

-- Drop table

-- DROP TABLE countries_currencies;

CREATE TABLE countries_currencies (
	id smallserial NOT NULL,
	country_id int2 NULL,
	currency_id int2 NULL,
	CONSTRAINT countries_currencies_pkey PRIMARY KEY (id),
	CONSTRAINT countries_currencies_countries FOREIGN KEY (country_id) REFERENCES countries(id) ON DELETE CASCADE,
	CONSTRAINT countries_currencies_curencies FOREIGN KEY (currency_id) REFERENCES currencies(id) ON DELETE CASCADE
);


-- public.delivery_terms_marketspaces definition

-- Drop table

-- DROP TABLE delivery_terms_marketspaces;

CREATE TABLE delivery_terms_marketspaces (
	id smallserial NOT NULL,
	delivery_term_id int4 NULL,
	marketspace_id int4 NULL,
	CONSTRAINT delivery_terms_marketspaces_pkey PRIMARY KEY (id),
	CONSTRAINT delivery_terms_marketspaces_delivery_terms_fk FOREIGN KEY (delivery_term_id) REFERENCES delivery_terms(id),
	CONSTRAINT delivery_terms_marketspaces_marketspaces_fk FOREIGN KEY (marketspace_id) REFERENCES marketspaces(id)
);


-- public.employees definition

-- Drop table

-- DROP TABLE employees;

CREATE TABLE employees (
	id int4 DEFAULT nextval('employees_employee_id_seq'::regclass) NOT NULL,
	en_name varchar(100) NOT NULL,
	department_id int4 NULL,
	CONSTRAINT employees_pkey PRIMARY KEY (id),
	CONSTRAINT fk_department FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
);


-- public.notifications definition

-- Drop table

-- DROP TABLE notifications;

CREATE TABLE notifications (
	id serial4 NOT NULL,
	user_id int4 NOT NULL,
	notification_type_id int4 NOT NULL,
	email varchar(255) NOT NULL,
	phone varchar(50) NULL,
	subject text NOT NULL,
	body text NOT NULL,
	status varchar(50) NOT NULL,
	created_at timestamp NOT NULL,
	sent_at timestamp NULL,
	CONSTRAINT notifications_pkey PRIMARY KEY (id),
	CONSTRAINT notifications_notification_type_id_fkey FOREIGN KEY (notification_type_id) REFERENCES notification_types(id),
	CONSTRAINT notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);


-- public.payment_terms_marketspaces definition

-- Drop table

-- DROP TABLE payment_terms_marketspaces;

CREATE TABLE payment_terms_marketspaces (
	id smallserial NOT NULL,
	payment_term_id int4 NULL,
	marketspace_id int4 NULL,
	CONSTRAINT payment_terms_marketspaces_pkey PRIMARY KEY (id),
	CONSTRAINT payment_terms_marketspaces_marketspaces_fk FOREIGN KEY (marketspace_id) REFERENCES marketspaces(id),
	CONSTRAINT payment_terms_marketspaces_payment_terms_fk FOREIGN KEY (payment_term_id) REFERENCES payment_terms(id)
);


-- public.product_subcategories definition

-- Drop table

-- DROP TABLE product_subcategories;

CREATE TABLE product_subcategories (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NULL,
	product_category_id int4 NULL,
	CONSTRAINT product_subcategories_pkey PRIMARY KEY (id),
	CONSTRAINT product_subcategories_product_categories_fk FOREIGN KEY (product_category_id) REFERENCES product_categories(id)
);


-- public.provinces definition

-- Drop table

-- DROP TABLE provinces;

CREATE TABLE provinces (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NOT NULL,
	region_id int2 NULL,
	country_id int2 NULL,
	CONSTRAINT provinces_pkey PRIMARY KEY (id),
	CONSTRAINT provinces_countries_fk FOREIGN KEY (country_id) REFERENCES countries(id),
	CONSTRAINT provinces_regions_fk FOREIGN KEY (region_id) REFERENCES regions(id)
);


-- public.status_categories_statuses definition

-- Drop table

-- DROP TABLE status_categories_statuses;

CREATE TABLE status_categories_statuses (
	id smallserial NOT NULL,
	status_category_id int4 NULL,
	status_id int4 NULL,
	description varchar NULL,
	CONSTRAINT status_categories_statuses_pkey PRIMARY KEY (id),
	CONSTRAINT status_categories_statuses_status_categories_fk FOREIGN KEY (status_category_id) REFERENCES status_categories(id),
	CONSTRAINT status_categories_statuses_statuses_fk FOREIGN KEY (status_id) REFERENCES statuses(id)
);


-- public.submarkets definition

-- Drop table

-- DROP TABLE submarkets;

CREATE TABLE submarkets (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NULL,
	market_id int2 NULL,
	CONSTRAINT submarkets_pkey PRIMARY KEY (id),
	CONSTRAINT submarkets_markets_fk FOREIGN KEY (market_id) REFERENCES markets(id)
);


-- public.districts definition

-- Drop table

-- DROP TABLE districts;

CREATE TABLE districts (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NOT NULL,
	province_id int2 NOT NULL,
	country_id int2 NOT NULL,
	CONSTRAINT districts_pkey PRIMARY KEY (id),
	CONSTRAINT districts_countries_fk FOREIGN KEY (country_id) REFERENCES countries(id),
	CONSTRAINT districts_provinces_fk FOREIGN KEY (province_id) REFERENCES provinces(id)
);


-- public.products definition

-- Drop table

-- DROP TABLE products;

CREATE TABLE products (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NULL,
	product_category_id int2 NULL,
	product_subcategory_id int2 NULL,
	market_id int2 NULL,
	submarket_id int2 NULL,
	CONSTRAINT products_pkey PRIMARY KEY (id),
	CONSTRAINT products_markets_fk FOREIGN KEY (market_id) REFERENCES markets(id),
	CONSTRAINT products_product_categories_fk FOREIGN KEY (product_category_id) REFERENCES product_categories(id),
	CONSTRAINT products_product_subcategories_fk FOREIGN KEY (product_subcategory_id) REFERENCES product_subcategories(id),
	CONSTRAINT products_submarkets_fk FOREIGN KEY (submarket_id) REFERENCES submarkets(id)
);


-- public.qualities definition

-- Drop table

-- DROP TABLE qualities;

CREATE TABLE qualities (
	id smallserial NOT NULL,
	product_id int2 NOT NULL,
	standard_id int2 NOT NULL,
	grade_id int2 NOT NULL,
	CONSTRAINT qualities_pkey PRIMARY KEY (id),
	CONSTRAINT qualities_grades_fk FOREIGN KEY (grade_id) REFERENCES grades(id),
	CONSTRAINT qualities_products_fk FOREIGN KEY (product_id) REFERENCES products(id),
	CONSTRAINT qualities_standards_fk FOREIGN KEY (standard_id) REFERENCES standards(id)
);


-- public.subdistricts definition

-- Drop table

-- DROP TABLE subdistricts;

CREATE TABLE subdistricts (
	id smallserial NOT NULL,
	en_name varchar(64) NOT NULL,
	th_name varchar(64) NOT NULL,
	district_id int2 NULL,
	postal_code varchar(16) NULL,
	CONSTRAINT subdistricts_pkey PRIMARY KEY (id),
	CONSTRAINT subdistricts_districts_fk FOREIGN KEY (district_id) REFERENCES districts(id)
);