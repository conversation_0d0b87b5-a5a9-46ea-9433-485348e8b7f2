<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>BiomassX - Bioenergy Trading Platform</title>
<style>
  /* Reset and base */
  * {
    box-sizing: border-box;
  }
  body {
    margin: 0;
    font-family: Arial, sans-serif;
    background: #fff;
    color: #000;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
  a {
    color: inherit;
    text-decoration: none;
  }
  a:hover {
    text-decoration: underline;
  }
  ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
  }
  button {
    cursor: pointer;
  }

  /* Header */
  header {
    position: sticky;
    top: 0;
    z-index: 50;
    box-shadow: 0 2px 4px rgb(0 0 0 / 0.1);
  }
  nav {
    max-width: 1120px;
    margin: 0 auto;
    background-color: #a6ff4d;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
  }
  .brand {
    font-weight: 800;
    font-size: 1.125rem;
    user-select: none;
  }
  .nav-links {
    display: none;
    align-items: center;
    gap: 16px;
    font-size: 0.875rem;
  }
  .nav-links span {
    user-select: none;
  }
  .nav-links a {
    color: #000;
  }
  .nav-links a:hover {
    text-decoration: underline;
  }
  .lang-select {
    background: #fff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.75rem;
    color: #000;
    padding: 2px 6px;
  }
  .login-btn {
    display: none;
    background-color: #2f9e4e;
    color: #fff;
    font-weight: 600;
    font-size: 0.75rem;
    padding: 4px 12px;
    border: none;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }
  .login-btn:hover {
    background-color: #267a3d;
  }
  .login-icon-btn {
    display: block;
    font-size: 1.25rem;
    color: #000;
    background: none;
    border: none;
  }
  .login-icon-btn:focus {
    outline: 2px solid #a6ff4d;
    outline-offset: 2px;
  }
  .orange-bar {
    background-color: #f97316;
    color: #fff;
    font-weight: 600;
    font-size: 0.75rem;
    text-align: center;
    padding: 6px 0;
    user-select: none;
  }

  /* Main content */
  main {
    flex-grow: 1;
    max-width: 1120px;
    margin: 24px auto;
    padding: 0 16px 48px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  /* Top Metrics Grid */
  .metrics-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
  }
  .metric-card {
    border: 1px solid #e5e7eb;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgb(0 0 0 / 0.05);
  }
  .metric-title {
    font-weight: 800;
    font-size: 1rem;
    margin-bottom: 8px;
  }
  .metric-desc {
    font-size: 0.875rem;
    color: #4b5563;
    margin-bottom: 6px;
  }
  .metric-value {
    font-weight: 800;
    font-size: 1.125rem;
    color: #2f9e4e;
  }

  /* Chart container */
  .chart-container {
    border: 1px solid #e5e7eb;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgb(0 0 0 / 0.05);
    max-width: 720px;
    margin-left: auto;
    margin-right: auto;
  }
  .chart-title {
    font-weight: 800;
    font-size: 1.125rem;
    margin-bottom: 12px;
  }
  canvas {
    width: 100% !important;
    height: 192px !important;
  }

  /* Search input */
  .search-container {
    max-width: 480px;
    margin-left: auto;
    margin-right: auto;
  }
  .search-input {
    width: 100%;
    padding: 10px 12px;
    font-size: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    outline-offset: 2px;
    outline-color: transparent;
    transition: outline-color 0.2s ease;
  }
  .search-input:focus {
    outline-color: #a6ff4d;
  }

  /* Table */
  .table-container {
    max-width: 1120px;
    margin: 0 auto;
    overflow-x: auto;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgb(0 0 0 / 0.05);
  }
  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    text-align: left;
  }
  thead {
    background-color: #a6ff4d;
    font-weight: 700;
    color: #000;
  }
  th, td {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
  }
  tbody tr:hover {
    background-color: #f9fafb;
    cursor: pointer;
  }
  .font-mono {
    font-family: monospace, monospace;
  }
  .price-green {
    color: #2f9e4e;
    font-weight: 700;
  }

  /* Lists for most active and biggest drops */
  .lists-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
    max-width: 1120px;
    margin: 0 auto;
  }
  .list-card {
    border: 1px solid #e5e7eb;
    padding: 16px;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgb(0 0 0 / 0.05);
  }
  .list-title {
    font-weight: 800;
    font-size: 1.125rem;
    margin-bottom: 12px;
  }
  ul.list-items {
    list-style-type: disc;
    padding-left: 20px;
    color: #374151;
    font-size: 0.875rem;
  }
  ul.list-items li {
    margin-bottom: 6px;
  }

  /* Footer */
  footer {
    background-color: #a6ff4d;
    color: #000;
    text-align: center;
    padding: 16px 0;
    user-select: none;
    font-size: 0.875rem;
  }

  /* Responsive */
  @media (min-width: 640px) {
    .nav-links {
      display: flex;
    }
    .login-btn {
      display: inline-block;
    }
    .login-icon-btn {
      display: none;
    }
    .metrics-grid {
      grid-template-columns: repeat(4, 1fr);
    }
    .lists-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
</style>
</head>
<body>

<header>
  <nav>
    <div class="brand">BiomassX</div>
    <ul class="nav-links" role="menubar" aria-label="Primary navigation">
      <li role="none"><a href="#" role="menuitem" tabindex="0">Trade</a></li>
      <li role="none" aria-hidden="true"><span>|</span></li>
      <li role="none"><a href="#" role="menuitem" tabindex="0">Markets</a></li>
      <li role="none" aria-hidden="true"><span>|</span></li>
      <li role="none"><a href="#" role="menuitem" tabindex="0">Products</a></li>
      <li role="none" aria-hidden="true"><span>|</span></li>
      <li role="none"><a href="#" role="menuitem" tabindex="0">Services</a></li>
      <li role="none" aria-hidden="true"><span>|</span></li>
      <li role="none"><a href="#" role="menuitem" tabindex="0">Login</a></li>
    </ul>
    <div>
      <select aria-label="Select language" class="lang-select">
        <option>EN</option>
      </select>
      <button class="login-btn" id="btnLogin">Login / Register</button>
      <button class="login-icon-btn" id="btnLoginMobile" aria-label="Login or Register"><i class="fas fa-user-circle"></i></button>
    </div>
  </nav>
  <div class="orange-bar" aria-live="polite" aria-atomic="true">
    bioenergy exchange platform
  </div>
</header>

<main>

  <section class="metrics-grid" aria-label="Key market metrics">
    <article class="metric-card" role="region" aria-labelledby="priceIndexTitle">
      <h2 id="priceIndexTitle" class="metric-title">Real-time WoodChip Price Index</h2>
      <p class="metric-desc">Current prices of key bioenergy products</p>
      <p class="metric-value" id="priceIndex">USD36.57/MT (EXW) 'avg price'</p>
      <p class="metric-value" id="priceIndex">USD36.57/MT (DAP 'avg price')</p>
    </article>
    <article class="metric-card" role="region" aria-labelledby="tradingVolumeTitle">
      <h2 id="tradingVolumeTitle" class="metric-title">24h Trading Value</h2>
      <p class="metric-desc">Total market activity indicator</p>
      <p class="metric-value" id="tradingVolume">450MT</p>
    </article>
    <article class="metric-card" role="region" aria-labelledby="activeOrdersTitle">
      <h2 id="activeOrdersTitle" class="metric-title">Active Orders Count</h2>
      <p class="metric-desc">Shows market liquidity</p>
      <p class="metric-value" id="activeOrders">87</p>
    </article>
    <article class="metric-card" role="region" aria-labelledby="co2SavedTitle">
      <h2 id="co2SavedTitle" class="metric-title">CO2 Emissions Saved (24h) equation = total_market_Volume * contant</h2>
      <p class="metric-desc">Environmental impact metric</p>
      <p class="metric-value" id="co2Saved">5,012 TCO2eq</p>
    </article>
  </section>



  <section class="table-container" aria-label="Recent market orders">
    <h2 style="padding:16px; font-weight:800; font-size:1.125rem; border-bottom:1px solid #e5e7eb; margin:0;">Recent Market Orders</h2>
    <table>
      <thead>
        <tr>
          <th scope="col">Order ID</th>
          <th scope="col">Product</th>
          <th scope="col">Price (USD/MT)</th>
          <th scope="col">Volume (MT)</th>
          <th scope="col">Type</th>
          <th scope="col">Time</th>
        </tr>
      </thead>
      <tbody id="ordersTableBody">
        <!-- Rows inserted by JS -->
      </tbody>
    </table>
  </section>



</main>

<footer>
  &copy; 2024 BiomassX. All rights reserved.
</footer>

<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/js/all.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Sample dynamic data for demonstration
  const priceIndex = "USD36.57/MT";
  const tradingVolume = "450MT";
  const activeOrders = 87;
  const co2Saved = "5,012 TCO2eq";

  // Update top metrics
  document.getElementById('priceIndex').textContent = priceIndex;
  document.getElementById('tradingVolume').textContent = tradingVolume;
  document.getElementById('activeOrders').textContent = activeOrders;
  document.getElementById('co2Saved').textContent = co2Saved;

  // Chart.js price chart data (last 7 days)
  const ctx = document.getElementById('priceChart').getContext('2d');
  const priceChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: ['6 days ago', '5 days ago', '4 days ago', '3 days ago', '2 days ago', 'Yesterday', 'Today'],
      datasets: [{
        label: 'Price (USD/MT)',
        data: [34, 35, 36, 36.5, 37, 36.8, 36.57],
        borderColor: '#2f9e4e',
        backgroundColor: 'rgba(47,158,78,0.2)',
        fill: true,
        tension: 0.3,
        pointRadius: 4,
        pointHoverRadius: 6,
        pointBackgroundColor: '#2f9e4e',
      }]
    },
    options: {
      responsive: true,
      plugins: {
        legend: { display: false },
        tooltip: { mode: 'index', intersect: false }
      },
      scales: {
        y: {
          beginAtZero: false,
          ticks: { color: '#000' },
          grid: { color: '#e5e7eb' }
        },
        x: {
          ticks: { color: '#000' },
          grid: { color: '#e5e7eb' }
        }
      }
    }
  });

  // Sample recent market orders data
  const recentOrders = [
    { id: 'ORD001', product: 'Woodchip', price: 36.57, volume: 50, type: 'Buy', time: '10:15 AM' },
    { id: 'ORD002', product: 'Pellets', price: 42.10, volume: 30, type: 'Sell', time: '10:20 AM' },
    { id: 'ORD003', product: 'Woodchip', price: 36.60, volume: 20, type: 'Buy', time: '10:25 AM' },
    { id: 'ORD004', product: 'Briquettes', price: 38.00, volume: 40, type: 'Sell', time: '10:30 AM' },
    { id: 'ORD005', product: 'Pellets', price: 41.90, volume: 25, type: 'Buy', time: '10:35 AM' },
    { id: 'ORD006', product: 'Woodchip', price: 36.55, volume: 60, type: 'Sell', time: '10:40 AM' },
    { id: 'ORD007', product: 'Briquettes', price: 37.80, volume: 15, type: 'Buy', time: '10:45 AM' },
  ];

  const ordersTableBody = document.getElementById('ordersTableBody');
  function renderOrdersTable(orders) {
    ordersTableBody.innerHTML = '';
    orders.forEach(order => {
      const tr = document.createElement('tr');
      tr.style.cursor = 'pointer';
      tr.onmouseenter = () => tr.style.backgroundColor = '#f9fafb';
      tr.onmouseleave = () => tr.style.backgroundColor = '';
      tr.innerHTML = `
        <td style="padding:12px 16px; font-family: monospace;">${order.id}</td>
        <td style="padding:12px 16px;">${order.product}</td>
        <td style="padding:12px 16px; font-weight: 700; color: #2f9e4e;">${order.price.toFixed(2)}</td>
        <td style="padding:12px 16px;">${order.volume}</td>
        <td style="padding:12px 16px;">${order.type}</td>
        <td style="padding:12px 16px;">${order.time}</td>
      `;
      ordersTableBody.appendChild(tr);
    });
  }
  renderOrdersTable(recentOrders);

  // Most active products with prices
  const mostActiveProductsData = [
    { name: 'Woodchip', price: 'USD36.57/MT' },
    { name: 'Pellets', price: 'USD42.10/MT' },
    { name: 'Briquettes', price: 'USD38.00/MT' },
  ];
  const mostActiveProducts = document.getElementById('mostActiveProducts');
  mostActiveProductsData.forEach(p => {
    const li = document.createElement('li');
    li.textContent = `${p.name} - ${p.price}`;
    mostActiveProducts.appendChild(li);
  });

  // Products with biggest price drops
  const biggestDropsData = [
    { name: 'Pellets', price: 'USD41.90/MT' },
    { name: 'Briquettes', price: 'USD37.80/MT' },
  ];
  const biggestDrops = document.getElementById('biggestDrops');
  biggestDropsData.forEach(p => {
    const li = document.createElement('li');
    li.textContent = `${p.name} - ${p.price}`;
    biggestDrops.appendChild(li);
  });

  // Search/filter functionality for recent orders
  const searchInput = document.getElementById('searchInput');
  searchInput.addEventListener('input', e => {
    const query = e.target.value.toLowerCase();
    const filteredOrders = recentOrders.filter(order =>
      order.product.toLowerCase().includes(query) ||
      order.id.toLowerCase().includes(query) ||
      order.type.toLowerCase().includes(query)
    );
    renderOrdersTable(filteredOrders);
  });

  // Login/Register button mobile toggle (dummy)
  const btnLoginMobile = document.getElementById('btnLoginMobile');
  btnLoginMobile.addEventListener('click', () => {
    alert('Login/Register modal or redirect would appear here.');
  });
</script>

</body>
</html>