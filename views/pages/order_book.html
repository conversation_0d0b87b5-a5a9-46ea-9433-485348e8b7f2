{{define "base"}}
<!DOCTYPE html>
<html lang="{{if eq .Lang "th"}}th{{else}}en{{end}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{if eq .Lang "th"}}ประวัติคำสั่งซื้อขาย{{else}}Order Book{{end}} - BiomassX</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">
    <link rel="stylesheet" href="/static/css/styles.css">
    <script src="https://unpkg.com/htmx.org@2.0.3"></script>
    <style>
        /* Dropdown menu styles */
        .dropdown-container {
            position: relative;
            display: inline-block;
        }

        .dropdown-trigger {
            cursor: pointer;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 4px;
            overflow: hidden;
        }

        .dropdown-content a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 16px;
            text-align: left;
            margin: 0;
        }

        .dropdown-content a:hover {
            background-color: rgba(178, 255, 89, 0.2);
            color: #ff6d00;
        }

        .dropdown-content.show {
            display: block;
            animation: fadeIn 0.3s;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Adjust main content for top navbar */
        .main-dashboard {
            margin-top: 80px;
            padding: 20px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Mobile responsive styles for dropdown */
        @media screen and (max-width: 768px) {
            .dropdown-container {
                display: block;
                width: 100%;
            }

            .dropdown-trigger {
                display: block;
                width: 100%;
                padding: 15px;
                text-align: center;
                border-bottom: 1px solid rgba(0,0,0,0.1);
            }

            .dropdown-content {
                position: static;
                box-shadow: none;
                width: 100%;
                background-color: rgba(178, 255, 89, 0.1);
            }

            .dropdown-content a {
                text-align: center;
                padding: 12px;
                border-bottom: 1px solid rgba(0,0,0,0.05);
            }

            .dropdown-content.show {
                display: block;
            }
        }
        /* Base styles */
        .order-book-container {
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow-x: auto; /* Enable horizontal scrolling on small screens */
        }

        .order-book-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            table-layout: auto; /* Changed from fixed to auto for better responsiveness */
        }

        /* Set specific widths for columns */
        .order-book-table th:last-child,
        .order-book-table td:last-child {
            width: 150px; /* Fixed width for Actions column */
            min-width: 150px; /* Ensure minimum width */
        }

        /* Define widths for other columns for better consistency */
        .order-book-table th:nth-child(1), /* Order Type */
        .order-book-table td:nth-child(1) {
            width: 12%;
            min-width: 100px;
        }

        .order-book-table th:nth-child(2), /* Product */
        .order-book-table td:nth-child(2) {
            width: 15%;
            min-width: 120px;
        }

        .order-book-table th:nth-child(3), /* Original Quantity */
        .order-book-table td:nth-child(3),
        .order-book-table th:nth-child(4), /* Available Quantity */
        .order-book-table td:nth-child(4) {
            width: 12%;
            min-width: 100px;
        }

        .order-book-table th:nth-child(5), /* Price */
        .order-book-table td:nth-child(5) {
            width: 12%;
            min-width: 100px;
        }

        .order-book-table th:nth-child(6), /* Status */
        .order-book-table td:nth-child(6) {
            width: 15%;
            min-width: 120px;
        }

        .order-book-table th:nth-child(7), /* Created Date */
        .order-book-table td:nth-child(7) {
            width: 12%;
            min-width: 100px;
        }

        /* Filter styles */
        .order-filter-container {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap; /* Allow wrapping on small screens */
        }

        .order-filter-label {
            font-weight: 500;
            color: #495057;
            margin-right: 5px;
        }

        .order-filter-select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            min-width: 150px;
            font-size: 14px;
            cursor: pointer;
        }

        .order-filter-select:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
            outline: none;
        }

        .order-book-table th, .order-book-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            height: 60px; /* Fixed height for all cells */
            vertical-align: middle; /* Center content vertically */
            box-sizing: border-box; /* Include padding in height calculation */
            line-height: 1.5; /* Standardize line height */
        }

        .order-book-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .order-book-table tr {
            height: 60px; /* Ensure consistent row height */
        }

        .order-book-table tr:hover {
            background-color: #f5f5f5;
        }

        /* Status styles */
        .status-available {
            color: #28a745;
            font-weight: bold;
        }

        .status-partial {
            color: #fd7e14;
            font-weight: bold;
        }

        .status-matched {
            color: #007bff;
            font-weight: bold;
        }

        .status-icon {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-available .status-icon {
            background-color: #28a745;
        }

        .status-partial .status-icon {
            background-color: #fd7e14;
        }

        .status-matched .status-icon {
            background-color: #007bff;
        }

        /* Button styles */
        .action-buttons {
            display: flex;
            gap: 8px;
            justify-content: flex-start;
            align-items: center;
            height: 100%;
            flex-wrap: nowrap; /* Prevent buttons from wrapping */
            white-space: nowrap; /* Keep buttons on one line */
            min-height: 40px; /* Minimum height to ensure consistency */
            box-sizing: border-box; /* Include padding in height calculation */
        }

        /* Empty action buttons cell should maintain height */
        .action-buttons:empty {
            min-height: 40px;
            display: block; /* Ensure it takes up space even when empty */
        }

        .edit-btn, .delete-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            height: 32px; /* Fixed height for buttons */
            line-height: 1; /* Ensure text is vertically centered */
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 60px; /* Ensure minimum touch target size */
            margin: 0; /* Remove any default margins */
            box-sizing: border-box; /* Include padding in height calculation */
        }

        .edit-btn {
            background-color: #b2ff59;
            color: #333;
        }

        .delete-btn {
            background-color: #ff6d00;
            color: white;
        }

        .edit-btn:hover {
            background-color: #ff6d00;
            color: white;
        }

        .delete-btn:hover {
            background-color: #d45c00;
        }

        /* Card view for mobile devices */
        .order-cards {
            display: none; /* Hidden by default, shown on mobile */
        }

        .order-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px;
            margin-bottom: 15px;
        }

        .order-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .order-card-title {
            font-weight: bold;
            font-size: 16px;
        }

        .order-card-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .order-card-status.available {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .order-card-status.partial {
            background-color: rgba(253, 126, 20, 0.1);
            color: #fd7e14;
        }

        .order-card-status.matched {
            background-color: rgba(0, 123, 255, 0.1);
            color: #007bff;
        }

        .order-card-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .order-card-label {
            color: #6c757d;
            font-size: 14px;
        }

        .order-card-value {
            font-weight: 500;
            font-size: 14px;
        }

        .order-card-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 15px;
            border-top: 1px solid #eee;
            padding-top: 15px;
            min-height: 50px; /* Ensure consistent height */
            align-items: center; /* Center buttons vertically */
            box-sizing: border-box; /* Include padding in height calculation */
        }

        /* Responsive styles */
        @media (max-width: 1024px) {
            .order-book-table th, .order-book-table td {
                padding: 10px;
                font-size: 14px;
            }

            .edit-btn, .delete-btn {
                padding: 5px 10px;
                font-size: 13px;
            }

            /* Ensure action column has enough width */
            .order-book-table th:last-child,
            .order-book-table td:last-child {
                width: 120px; /* Slightly reduced but still adequate */
                min-width: 120px; /* Ensure minimum width */
            }
        }

        @media (max-width: 768px) {
            .order-book-container {
                padding: 15px;
            }

            .order-filter-container {
                flex-direction: column;
                align-items: flex-start;
            }

            .order-filter-select {
                width: 100%;
            }

            /* Hide some table columns on tablets */
            .order-book-table th:nth-child(4), /* Available Quantity */
            .order-book-table td:nth-child(4),
            .order-book-table th:nth-child(7), /* Created Date */
            .order-book-table td:nth-child(7) {
                display: none;
            }

            /* Ensure action column has enough width */
            .order-book-table th:last-child,
            .order-book-table td:last-child {
                width: 110px; /* Further reduced but still functional */
                min-width: 110px;
                padding: 5px 8px; /* Reduce padding to save space */
            }

            /* Adjust action buttons for tablet */
            .action-buttons {
                gap: 5px; /* Reduce gap between buttons */
            }

            .edit-btn, .delete-btn {
                min-width: 50px; /* Slightly smaller buttons */
                padding: 5px 8px;
            }
        }

        @media (max-width: 576px) {
            /* Hide table and show cards on mobile */
            .order-book-table {
                display: none;
            }

            .order-cards {
                display: block;
            }

            .main-dashboard {
                padding: 15px;
            }

            h3 {
                font-size: 20px;
            }

            /* Ensure card action buttons are properly sized and spaced */
            .order-card-actions {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                margin-top: 15px;
                padding-top: 15px;
                border-top: 1px solid #eee;
            }

            .order-card-actions .edit-btn,
            .order-card-actions .delete-btn {
                min-width: 70px; /* Larger touch targets for mobile */
                height: 36px; /* Taller buttons for easier tapping */
                padding: 8px 12px;
                font-size: 14px;
            }
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            position: relative;
            background-color: #fff;
            margin: 2% auto;
            padding: 24px;
            border: none;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 8px 24px rgba(0,0,0,0.15);
            border-radius: 8px;
        }

        .close {
            color: #6c757d;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.2s;
            line-height: 1;
            padding: 5px; /* Larger touch target */
        }

        .close:hover,
        .close:focus {
            color: #343a40;
            text-decoration: none;
        }

        .modal-header {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 16px;
            color: #212529;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* Responsive modal styles */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                padding: 16px;
                margin: 5% auto;
            }

            .modal-header {
                font-size: 18px;
                padding-bottom: 12px;
                margin-bottom: 16px;
            }
        }

        @media (max-width: 576px) {
            .modal-content {
                width: 95%;
                padding: 12px;
                margin: 10% auto;
            }

            .close {
                font-size: 24px;
                padding: 8px; /* Even larger touch target for mobile */
            }
        }

        /* Form styles to exactly match order submission form */
        .order-form {
            display: flex;
            flex-direction: column;
            max-width: 600px;
            margin: 0 auto;
        }

        .form-group {
            display: flex;
            align-items: flex-start; /* Changed from center to flex-start for better mobile layout */
            margin-bottom: 10px;
            flex-wrap: wrap; /* Allow wrapping on small screens */
        }

        .form-group label {
            width: 150px;
            text-align: left;
            margin-right: 10px;
            line-height: 1.5;
            padding-top: 8px; /* Align with input */
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            flex: 1;
            padding: 8px 10px; /* Increased padding for better touch targets */
            box-sizing: border-box;
            height: 40px; /* Increased height for better touch targets */
            border: 1px solid #ddd;
            border-radius: 4px;
            min-width: 200px; /* Ensure inputs have a minimum width */
        }

        .form-group select {
            background-color: white;
            padding-right: 25px; /* Space for dropdown arrow */
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23343a40' d='M.5 1.5l3 3 3-3'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 8px 8px;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .form-group textarea {
            height: auto;
            min-height: 80px;
            resize: vertical;
        }

        /* Add <br> after each form-group to match original form */
        .form-group + br {
            display: block;
            content: "";
            margin-bottom: 10px;
        }

        .modal-footer {
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid #dee2e6;
            text-align: right;
            display: flex;
            justify-content: flex-end;
            flex-wrap: wrap; /* Allow wrapping on small screens */
            gap: 10px; /* Use gap instead of margin for better spacing when wrapped */
        }

        .modal-footer button {
            padding: 10px 16px; /* Increased padding for better touch targets */
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
            transition: background-color 0.2s;
            min-width: 80px; /* Ensure minimum button width */
            min-height: 40px; /* Ensure minimum button height */
        }

        .modal-footer button:hover {
            background-color: #0069d9;
        }

        .modal-footer button[type="button"],
        .modal-footer button:first-child {
            background-color: #6c757d;
            color: white;
        }

        .modal-footer button[type="button"]:hover,
        .modal-footer button:first-child:hover {
            background-color: #5a6268;
        }

        /* Responsive form styles */
        @media (max-width: 768px) {
            .form-group {
                flex-direction: column;
                align-items: stretch;
            }

            .form-group label {
                width: 100%;
                margin-bottom: 5px;
                padding-top: 0;
            }

            .form-group input,
            .form-group select,
            .form-group textarea {
                width: 100%;
            }

            .modal-footer {
                justify-content: space-between;
            }

            .modal-footer button {
                flex: 1;
                margin-left: 0;
            }
        }

        @media (max-width: 576px) {
            .order-form {
                padding: 0;
            }

            .form-group {
                margin-bottom: 15px;
            }

            .modal-footer {
                flex-direction: column-reverse;
                gap: 10px;
            }

            .modal-footer button {
                width: 100%;
                margin-left: 0;
            }
        }

        /* Form validation styles */
        .invalid-field {
            border: 1px solid #ff0000 !important;
            background-color: #fff8f8 !important;
        }

        .field-error-message {
            color: #ff0000;
            font-size: 12px;
            margin-top: 4px;
        }

        /* Improved styles for partially matched orders */
        .partially-matched-notice {
            background-color: #fff3e0;
            border-left: 4px solid #ff6d00;
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 4px;
        }

        .partially-matched-notice h4 {
            margin-top: 0;
            margin-bottom: 8px;
            color: #ff6d00;
        }

        .partially-matched-notice p {
            margin: 0;
            line-height: 1.5;
        }

        .editable-field {
            /* Removed green border */
            border: 1px solid #ced4da !important;
        }

        .field-tooltip {
            position: relative;
            display: inline-block;
            margin-left: 5px;
            cursor: help;
        }

        .field-tooltip .tooltip-icon {
            color: #ff6d00;
            font-size: 16px;
            font-weight: bold;
        }

        .field-tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .field-tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        /* Loading indicator styles */
        #loading-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.9);
            z-index: 10;
            border-radius: 8px;
        }

        .loading-spinner {
            border: 3px solid #e9ecef;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            animation: spin 0.8s linear infinite;
            margin-bottom: 12px;
        }

        #loading-indicator p {
            color: #495057;
            font-size: 14px;
            font-weight: 500;
            margin: 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Error message styles */
        .error-message {
            background-color: #ffebee;
            color: #c62828;
            padding: 10px 15px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
            border-radius: 4px;
        }

        /* Custom alert dialog styles */
        .custom-alert {
            position: fixed;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
            z-index: 1100;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .alert-content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            max-width: 500px;
            width: 80%;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .alert-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
            font-weight: bold;
            font-size: 18px;
            color: #ff6d00;
        }

        .alert-message {
            margin-bottom: 15px;
            white-space: pre-line;
            line-height: 1.5;
        }

        .alert-button {
            padding: 8px 16px;
            background-color: #ff6d00;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            float: right;
            font-weight: bold;
        }

        /* Confirmation modal styles */
        #confirmationModal {
            display: none;
            position: fixed;
            z-index: 1100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }

        #modal-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 20px;
            border: 1px solid #ddd;
            width: 80%;
            max-width: 600px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        #modal-content ul {
            list-style-type: none;
            padding: 0;
        }

        #modal-content li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        #modal-content li:last-child {
            border-bottom: none;
        }

        /* Unified styling for the edit modal */
        #editModal .modal-content {
            max-width: 800px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        /* Common form section styling */
        #basic-edit-fields,
        #full-edit-fields {
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 6px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        #full-edit-fields {
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
            margin-top: 15px;
        }

        /* Form group styling */
        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 6px;
            display: inline-block;
        }

        /* Input and select styling */
        .form-group input,
        .form-group select,
        .form-group textarea {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px 12px;
            width: 100%;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
            outline: none;
        }

        /* Date inputs */
        input[type="date"] {
            padding: 7px 12px;
        }

        /* Notice styling */
        .partially-matched-notice {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 12px 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .partially-matched-notice h4 {
            color: #856404;
            margin-top: 0;
            margin-bottom: 8px;
            font-size: 16px;
            font-weight: 600;
        }

        .partially-matched-notice p {
            margin: 0;
            color: #5d4037;
            font-size: 14px;
            line-height: 1.5;
        }

        /* Editable field highlighting */
        .editable-field {
            border: 1px solid #ced4da !important;
            background-color: #ffffff !important;
            transition: all 0.3s ease;
        }

        .editable-field:focus {
            border-color: #80bdff !important;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25) !important;
        }

        /* Tooltip styling */
        .field-tooltip {
            position: relative;
            display: inline-block;
            margin-left: 8px;
            vertical-align: middle;
        }

        .tooltip-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 18px;
            height: 18px;
            background-color: #6c757d;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            cursor: help;
            transition: background-color 0.2s;
        }

        .tooltip-icon:hover {
            background-color: #5a6268;
        }

        .field-tooltip .tooltip-text {
            width: 220px;
            background-color: #343a40;
            color: white;
            text-align: center;
            border-radius: 4px;
            padding: 8px 12px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.5;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        /* Error message styling */
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px 15px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
            border-radius: 4px;
            font-size: 14px;
        }

        /* Invalid field styling */
        .invalid-field {
            border-color: #dc3545 !important;
            background-color: #fff8f8 !important;
        }

        .invalid-field:focus {
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25) !important;
        }

        .field-error-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 4px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .form-group {
                flex-direction: column;
                align-items: flex-start;
            }

            .form-group label {
                width: 100%;
                margin-bottom: 5px;
                line-height: 1.5;
            }

            .form-group input,
            .form-group select,
            .form-group textarea {
                width: 100%;
            }

            .modal-content {
                width: 95%;
                margin: 5% auto;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    {{template "content" .}}

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEditModal()">&times;</span>
            <div class="modal-header">{{if eq .Lang "th"}}แก้ไขคำสั่งซื้อขาย{{else}}Edit Order{{end}}</div>
            <form id="editForm" method="POST" action="/order_book" onsubmit="return false;" class="order-form">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="lang" value="{{ .Lang }}">
                <input type="hidden" id="edit_order_id" name="order_id" value="">
                <input type="hidden" id="order_status_id" name="status_id" value="">

                <!-- Basic fields for partially matched orders -->
                <div id="basic-edit-fields">
                    <!-- Notice for partially matched orders -->
                    <div class="partially-matched-notice">
                        <h4>{{if eq .Lang "th"}}คำสั่งที่จับคู่บางส่วนแล้ว{{else}}Partially Matched Order{{end}}</h4>
                        <p>{{if eq .Lang "th"}}คำสั่งนี้ได้รับการจับคู่บางส่วนแล้ว คุณสามารถแก้ไขได้เฉพาะฟิลด์ที่ไฮไลท์ไว้เท่านั้น{{else}}This order has been partially matched. You can only edit the highlighted fields.{{end}}</p>
                    </div>

                    <div class="form-group">
                        <label for="edit_price">{{if eq .Lang "th"}}ราคา{{else}}Price{{end}}:</label>
                        <input type="number" id="edit_price" name="price" step="0.01" required class="editable-field">
                    </div>

                    <div class="form-group">
                        <label for="edit_quantity">{{if eq .Lang "th"}}ปริมาณ{{else}}Quantity{{end}}:</label>
                        <input type="number" id="edit_quantity" name="quantity" step="0.01" required min="0.01" max="" class="editable-field">
                        <input type="hidden" id="max_quantity" value="">
                        <div id="quantity_error" style="color: red; display: none;">
                            {{if eq .Lang "th"}}ไม่สามารถเพิ่มปริมาณมากกว่าที่มีอยู่ได้{{else}}Cannot increase quantity beyond available amount{{end}}
                        </div>
                    </div>

                    <div id="quantity_restriction" style="color: #856404; background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 8px 12px; margin: 10px 0; border-radius: 4px; font-size: 14px; display: none;">
                        {{if eq .Lang "th"}}หมายเหตุ: สำหรับคำสั่งที่จับคู่บางส่วนแล้ว คุณสามารถลดปริมาณได้เท่านั้น ไม่สามารถเพิ่มเกินปริมาณที่เหลืออยู่ได้{{else}}Note: For partially matched orders, you can only decrease the quantity, not increase beyond the available amount{{end}}
                    </div>

                    <div class="form-group">
                        <label for="edit_first_delivery_date_basic">{{if eq .Lang "th"}}วันที่ส่งมอบแรก{{else}}First Delivery Date{{end}}:</label>
                        <input type="date" id="edit_first_delivery_date_basic" name="first_delivery_date" class="editable-field">
                        <div id="first_date_error" class="error-message" style="color: red; display: none;">
                            {{if eq .Lang "th"}}วันที่ส่งมอบแรกต้องไม่มากกว่าวันที่ส่งมอบสุดท้าย{{else}}First delivery date cannot be after last delivery date{{end}}
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_last_delivery_date_basic">{{if eq .Lang "th"}}วันที่ส่งมอบสุดท้าย{{else}}Last Delivery Date{{end}}:</label>
                        <input type="date" id="edit_last_delivery_date_basic" name="last_delivery_date" class="editable-field">
                        <div id="last_date_error" class="error-message" style="color: red; display: none;">
                            {{if eq .Lang "th"}}วันที่ส่งมอบสุดท้ายต้องไม่ก่อนวันที่ส่งมอบแรก{{else}}Last delivery date cannot be before first delivery date{{end}}
                        </div>
                    </div>

                    <input type="hidden" id="edit_currency_basic" name="currency_id">
                </div>

                <!-- Full fields for available orders -->
                <div id="full-edit-fields" style="display: none;">
                    <div class="form-group">
                        <label for="edit_marketspace">{{if eq .Lang "th"}}ตลาด{{else}}Marketspace{{end}}:</label>
                        <select id="edit_marketspace" name="marketspaceID" hx-get="/payment?lang={{ .Lang }}" hx-trigger="change"
                            hx-target="#edit_payment_term" hx-include="[name='marketspaceID']" hx-swap="innerHTML"
                            hx-on="htmx:afterRequest: this.closest('form').querySelector('#edit_delivery_term').dispatchEvent(new Event('change'));">
                            <option value="">--{{if eq .Lang "th"}}เลือกตลาด{{else}}select local/global market{{end}}--</option>
                            {{range .Marketspaces}}
                            <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                            {{end}}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_market">{{if eq .Lang "th"}}ประเภทตลาด{{else}}Market{{end}}:</label>
                        <select id="edit_market" name="marketID" hx-get="/submarkets?lang={{ .Lang }}" hx-target="#edit_submarket" hx-trigger="change"
                            hx-include="[name='marketID']" hx-on::after-request="htmx.trigger('#edit_submarket', 'change')">
                            <option value="">--{{if eq .Lang "th"}}เลือกประเภทตลาด{{else}}select a market{{end}}--</option>
                            {{range .Markets}}
                            <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                            {{end}}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_submarket">{{if eq .Lang "th"}}ตลาดย่อย{{else}}Submarket{{end}}:</label>
                        <select id="edit_submarket" name="submarketID" hx-get="/products?lang={{ .Lang }}" hx-target="#edit_product"
                            hx-trigger="change" hx-include="[name='submarketID']"
                            hx-on::after-request="htmx.trigger('#edit_product', 'change'); logDropdownState('After submarket change');"
                            onchange="this.setAttribute('data-selected-submarket-id', this.value)">
                            <option value="">--{{if eq .Lang "th"}}เลือกตลาดย่อย{{else}}select a submarket{{end}}--</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_order_type">{{if eq .Lang "th"}}ประเภทคำสั่ง{{else}}Order type{{end}}:</label>
                        <select id="edit_order_type" name="orderTypeID" hx-target="#edit_order_type" hx-trigger="load">
                            <option value="">--{{if eq .Lang "th"}}เลือกประเภทคำสั่ง{{else}}select buy/sell order{{end}}--</option>
                            {{range .OrderTypes}}
                            <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                            {{end}}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_matching_type">{{if eq .Lang "th"}}ประเภทการจับคู่{{else}}Matching type{{end}}:</label>
                        <select id="edit_matching_type" name="matchingTypeID" hx-target="#edit_matching_type" hx-trigger="load">
                            <option value="">--{{if eq .Lang "th"}}เลือกประเภทการจับคู่{{else}}select a matching algorithm{{end}}--</option>
                            {{range .MatchingTypes}}
                            <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                            {{end}}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_contract_type">{{if eq .Lang "th"}}ประเภทสัญญา{{else}}Contract type{{end}}:</label>
                        <select id="edit_contract_type" name="contractTypeID" hx-target="#edit_contract_type" hx-trigger="load">
                            <option value="">--{{if eq .Lang "th"}}เลือกประเภทสัญญา{{else}}select a contract type{{end}}--</option>
                            {{range .ContractTypes}}
                            <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                            {{end}}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_product">{{if eq .Lang "th"}}สินค้า{{else}}Product{{end}}:</label>
                        <select id="edit_product" name="productID"
                            onchange="updateQualityOptions(this.value)"
                            hx-on::after-request="logDropdownState('After product change')">
                            <option value="">--{{if eq .Lang "th"}}เลือกสินค้า{{else}}select a product{{end}}--</option>
                        </select>
                        <script>
                            // Function to update quality options based on product ID
                            function updateQualityOptions(productId) {
                                if (!productId) return;

                                console.log("Updating quality options for product ID:", productId);

                                // Get the quality ID from the order data
                                const qualityId = window.currentOrderData ? window.currentOrderData.QualityID : null;
                                console.log("Quality ID from order data:", qualityId);

                                const qualitySelect = document.getElementById('edit_quality');
                                if (!qualitySelect) return;

                                // Get current language
                                const currentLang = window.currentLang || "{{ .Lang }}";
                                const isThai = currentLang === "th";

                                // Keep the first option (placeholder)
                                const firstOption = qualitySelect.options[0];

                                // Check if we have cached qualities for this product
                                if (dropdownCache.qualities[productId] && dropdownCache.qualities[productId].length > 0) {
                                    console.log("Using cached qualities for product ID:", productId);

                                    // Build options from cache
                                    let optionsHtml = '';
                                    dropdownCache.qualities[productId].forEach(quality => {
                                        // Format quality name based on available properties and language
                                        let qualityName = '';
                                        if (quality.StandardID && quality.GradeID) {
                                            if (isThai) {
                                                qualityName = `มาตรฐาน: ${quality.StandardID} - เกรด: ${quality.GradeID}`;
                                            } else {
                                                qualityName = `Standard: ${quality.StandardID} - Grade: ${quality.GradeID}`;
                                            }
                                        } else if (quality.EnName || quality.ThName) {
                                            qualityName = isThai && quality.ThName ? quality.ThName : quality.EnName;
                                        } else {
                                            qualityName = `Quality ID: ${quality.ID}`;
                                        }

                                        optionsHtml += `<option value="${quality.ID}">${qualityName}</option>`;
                                    });

                                    qualitySelect.innerHTML = firstOption.outerHTML + optionsHtml;
                                    console.log("Quality options loaded from cache:", Array.from(qualitySelect.options).map(opt => ({ value: opt.value, text: opt.text })));

                                    // Set the quality value from the order data
                                    if (qualityId) {
                                        console.log(`Setting quality value to: ${qualityId}`);
                                        qualitySelect.value = qualityId;

                                        // Store the quality ID in a data attribute for reference
                                        qualitySelect.setAttribute('data-quality-id', qualityId);

                                        // If the value wasn't set, try again after a short delay
                                        setTimeout(() => {
                                            if (qualitySelect.value !== qualityId.toString()) {
                                                console.log(`Retry setting quality value to: ${qualityId}`);
                                                qualitySelect.value = qualityId;

                                                // Highlight the field to indicate it's been set
                                                qualitySelect.classList.add('editable-field');
                                            }
                                        }, 100);
                                    }
                                } else {
                                    // Fallback to fetch if not in cache
                                    console.log("Qualities not in cache, fetching from server");
                                    fetch(`/qualities?productID=${productId}&lang=${currentLang}`)
                                        .then(response => {
                                            if (!response.ok) {
                                                throw new Error(`Failed to fetch qualities: ${response.status}`);
                                            }
                                            return response.text();
                                        })
                                        .then(html => {
                                            // Store the original HTML for debugging
                                            const originalHtml = html;
                                            console.log("Received qualities HTML:", originalHtml.substring(0, 200) + (originalHtml.length > 200 ? '...' : ''));

                                            // Set the HTML to the select element
                                            qualitySelect.innerHTML = firstOption.outerHTML + html;

                                            // Log available options for debugging
                                            console.log("Quality options after loading:", Array.from(qualitySelect.options).map(opt => ({ value: opt.value, text: opt.text })));

                                            // Set the quality value from the order data
                                            if (qualityId) {
                                                console.log(`Setting quality value to: ${qualityId}`);
                                                qualitySelect.value = qualityId;

                                                // Store the quality ID in a data attribute for reference
                                                qualitySelect.setAttribute('data-quality-id', qualityId);

                                                // If the value wasn't set, try again after a short delay
                                                setTimeout(() => {
                                                    if (qualitySelect.value !== qualityId.toString()) {
                                                        console.log(`Retry setting quality value to: ${qualityId}`);
                                                        qualitySelect.value = qualityId;

                                                        // If still not set, try to find the option and add it if missing
                                                        if (qualitySelect.value !== qualityId.toString()) {
                                                            console.log("Quality option not found, attempting to add it manually");

                                                            // Check if we have the quality name in the order data
                                                            if (window.currentOrderData && window.currentOrderData.QualityName) {
                                                                const qualityName = window.currentOrderData.QualityName;
                                                                console.log(`Adding quality option manually: ID=${qualityId}, Name=${qualityName}`);

                                                                // Create and add the option
                                                                const option = document.createElement('option');
                                                                option.value = qualityId;
                                                                option.text = qualityName;
                                                                option.selected = true;
                                                                qualitySelect.appendChild(option);
                                                            }
                                                        }

                                                        // Highlight the field to indicate it's been set
                                                        qualitySelect.classList.add('editable-field');
                                                    }
                                                }, 100);
                                            }
                                        })
                                        .catch(error => {
                                            console.error('Error fetching qualities:', error);

                                            // If fetch fails but we have quality ID and name, create the option manually
                                            if (qualityId && window.currentOrderData && window.currentOrderData.QualityName) {
                                                const qualityName = window.currentOrderData.QualityName;
                                                console.log(`Fetch failed. Adding quality option manually: ID=${qualityId}, Name=${qualityName}`);

                                                // Create and add the option
                                                const option = document.createElement('option');
                                                option.value = qualityId;
                                                option.text = qualityName;
                                                option.selected = true;
                                                qualitySelect.appendChild(option);

                                                // Highlight the field
                                                qualitySelect.classList.add('editable-field');
                                            }
                                        });
                                }
                            }
                        </script>
                    </div>

                    <div class="form-group">
                        <label for="edit_quality">{{if eq .Lang "th"}}คุณภาพ{{else}}Quality{{end}}:</label>
                        <select id="edit_quality" name="quality_id" data-quality-id="">
                            <option value="">--{{if eq .Lang "th"}}เลือกคุณภาพ{{else}}select a quality{{end}}--</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_price_full">{{if eq .Lang "th"}}ราคาต่อหน่วย{{else}}Price{{end}}:</label>
                        <input type="number" id="edit_price_full" name="price" step="0.01">
                    </div>

                    <div class="form-group">
                        <label for="edit_currency">{{if eq .Lang "th"}}สกุลเงิน{{else}}Currency{{end}}:</label>
                        <select id="edit_currency" name="currency_id" hx-target="#edit_currency" hx-trigger="load">
                            <option value="">--{{if eq .Lang "th"}}เลือกสกุลเงิน{{else}}select a currency{{end}}--</option>
                            {{range .Currencies}}
                            <option value="{{.ID}}">{{.Code}}</option>
                            {{end}}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_quantity_full">{{if eq .Lang "th"}}จำนวน{{else}}Quantity{{end}}:</label>
                        <input type="number" id="edit_quantity_full" name="quantity" step="0.01">
                    </div>


                    <div class="form-group">
                        <label for="edit_uom">{{if eq .Lang "th"}}หน่วย{{else}}Unit{{end}}:</label>
                        <select id="edit_uom" name="uom_id" hx-target="#edit_uom" hx-trigger="load">
                            <option value="">--{{if eq .Lang "th"}}เลือกหน่วย{{else}}select a unit{{end}}--</option>
                            {{range .UomIDs}}
                            <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                            {{end}}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_packing">{{if eq .Lang "th"}}บรรจุภัณฑ์{{else}}Packing{{end}}:</label>
                        <select id="edit_packing" name="packing_id" hx-target="#edit_packing" hx-trigger="load">
                            <option value="">--{{if eq .Lang "th"}}เลือกบรรจุภัณฑ์{{else}}select a packing{{end}}--</option>
                            {{range .PackingIDs}}
                            <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                            {{end}}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_payment_term">{{if eq .Lang "th"}}เงื่อนไขการชำระเงิน{{else}}Payment term{{end}}:</label>
                        <select id="edit_payment_term" name="payment_term_id">
                            <option value="">--{{if eq .Lang "th"}}เลือกเงื่อนไขการชำระเงิน{{else}}select a payment term{{end}}--</option>
                            <!-- Payment terms will be loaded via HTMX -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_delivery_term">{{if eq .Lang "th"}}เงื่อนไขการส่งมอบ{{else}}Delivery term{{end}}:</label>
                        <select id="edit_delivery_term" name="delivery_term_id" hx-get="/delivery?lang={{ .Lang }}"
                            hx-trigger="change from:#edit_marketspace" hx-target="this" hx-include="[name='marketspaceID']"
                            hx-swap="innerHTML">
                            <option value="">--{{if eq .Lang "th"}}เลือกเงื่อนไขการส่งมอบ{{else}}select a delivery term{{end}}--</option>
                            <!-- Delivery terms will be loaded via HTMX -->
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_country">{{if eq .Lang "th"}}ประเทศ{{else}}Country{{end}}:</label>
                        <select id="edit_country" name="country_id" hx-target="#edit_country" hx-trigger="load">
                            <option value="">--{{if eq .Lang "th"}}เลือกประเทศ{{else}}select a country{{end}}--</option>
                            {{range .CountryIDs}}
                            <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                            {{end}}
                        </select>
                    </div>

                    <!-- Location fields (shown based on marketspace) -->
                    <div id="edit-location-fields" style="display: none;">
                        <div class="form-group">
                            <label for="edit_province">{{if eq .Lang "th"}}จังหวัด{{else}}Province/State{{end}}:</label>
                            <select id="edit_province" name="province_id" hx-get="/district?lang={{ .Lang }}" hx-trigger="change"
                                hx-target="#edit_district" hx-include="[name='province_id']" hx-on::after-request="htmx.trigger('#edit_district', 'change')">
                                <option value="">--{{if eq .Lang "th"}}เลือกจังหวัด{{else}}select a province/state{{end}}--</option>
                                {{range .Provinces}}
                                <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                                {{end}}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_district">{{if eq .Lang "th"}}อำเภอ{{else}}District/City{{end}}:</label>
                            <select id="edit_district" name="district_id" hx-get="/subdistrict?lang={{ .Lang }}" hx-trigger="change"
                                hx-target="#edit_subdistrict" hx-include="[name='district_id']">
                                <option value="">--{{if eq .Lang "th"}}เลือกอำเภอ{{else}}select a district/city{{end}}--</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_subdistrict">{{if eq .Lang "th"}}ตำบล{{else}}Subdistrict{{end}}:</label>
                            <select id="edit_subdistrict" name="subdistrict_id" hx-trigger="change from:#edit_district">
                                <option value="">--{{if eq .Lang "th"}}เลือกตำบล{{else}}select a subdistrict{{end}}--</option>
                            </select>
                        </div>
                    </div>

                    <!-- Port fields (shown based on marketspace) -->
                    <div id="edit-port-fields" style="display: none;">
                        <div class="form-group">
                            <label for="edit_port_loading">{{if eq .Lang "th"}}ท่าเรือต้นทาง{{else}}Port of loading{{end}}:</label>
                            <select id="edit_port_loading" name="port_of_loading_id" hx-target="#edit_port_loading" hx-trigger="load">
                                <option value="">--{{if eq .Lang "th"}}เลือกท่าเรือต้นทาง{{else}}select a port of loading{{end}}--</option>
                                {{range .Portofloadings}}
                                <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                                {{end}}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_port_discharge">{{if eq .Lang "th"}}ท่าเรือปลายทาง{{else}}Port of discharge{{end}}:</label>
                            <select id="edit_port_discharge" name="port_of_discharge_id" hx-target="#edit_port_discharge" hx-trigger="load">
                                <option value="">--{{if eq .Lang "th"}}เลือกท่าเรือปลายทาง{{else}}select a port of discharge{{end}}--</option>
                                {{range .Portofdischarges}}
                                <option value="{{.ID}}">{{if eq $.Lang "th"}}{{.ThName}}{{else}}{{.EnName}}{{end}}</option>
                                {{end}}
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_first_delivery_date">{{if eq .Lang "th"}}วันที่ส่งมอบแรก{{else}}First Delivery Date{{end}}:</label>
                        <input type="date" id="edit_first_delivery_date" name="first_delivery_date">
                        <div id="first_date_error_full" class="error-message" style="color: red; display: none;">
                            {{if eq .Lang "th"}}วันที่ส่งมอบแรกต้องไม่มากกว่าวันที่ส่งมอบสุดท้าย{{else}}First delivery date cannot be after last delivery date{{end}}
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_last_delivery_date">{{if eq .Lang "th"}}วันที่ส่งมอบสุดท้าย{{else}}Last Delivery Date{{end}}:</label>
                        <input type="date" id="edit_last_delivery_date" name="last_delivery_date">
                        <div id="last_date_error_full" class="error-message" style="color: red; display: none;">
                            {{if eq .Lang "th"}}วันที่ส่งมอบสุดท้ายต้องไม่ก่อนวันที่ส่งมอบแรก{{else}}Last delivery date cannot be before first delivery date{{end}}
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="edit_remark">{{if eq .Lang "th"}}หมายเหตุ{{else}}Remark{{end}}:</label>
                    <textarea id="edit_remark" name="remark" rows="3"></textarea>
                </div>

                <div class="modal-footer">
                    <button type="button" onclick="closeEditModal()">{{if eq .Lang "th"}}ยกเลิก{{else}}Cancel{{end}}</button>
                    <button type="button" onclick="handleSaveButtonClick()">{{if eq .Lang "th"}}บันทึก{{else}}Save{{end}}</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeDeleteModal()">&times;</span>
            <div class="modal-header">{{if eq .Lang "th"}}ยืนยันการลบ{{else}}Confirm Delete{{end}}</div>
            <p>{{if eq .Lang "th"}}คุณแน่ใจหรือไม่ว่าต้องการลบคำสั่งซื้อขายนี้?{{else}}Are you sure you want to delete this order?{{end}}</p>
            <form id="deleteForm" method="POST" action="/order_book">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" id="delete_order_id" name="order_id" value="">
                <input type="hidden" name="lang" value="{{ .Lang }}">

                <div class="modal-footer">
                    <button type="button" onclick="closeDeleteModal()">{{if eq .Lang "th"}}ยกเลิก{{else}}Cancel{{end}}</button>
                    <button type="submit">{{if eq .Lang "th"}}ลบ{{else}}Delete{{end}}</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Confirmation Modal -->
    <div id="confirmationModal">
        <div id="modal-content"></div>
    </div>

    <script>
        // Define global variables
        let editMarketspaceSelect;
        let editDeliveryTermSelect;

        // Cache for dropdown options to ensure immediate display
        const dropdownCache = {
            submarkets: {},  // Indexed by marketID
            products: {},    // Indexed by submarketID
            qualities: {},   // Indexed by productID
            districts: {},   // Indexed by provinceID
            subdistricts: {}, // Indexed by districtID
            payment: {},     // Indexed by marketspaceID
            delivery: {}     // Indexed by marketspaceID
        };

        // Initialize global variables after DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            editMarketspaceSelect = document.getElementById('edit_marketspace');
            editDeliveryTermSelect = document.getElementById('edit_delivery_term');

            // Pre-fetch and cache common dropdown options
            preloadDropdownOptions();

            // Initialize tooltips
            initializeTooltips();

            // Initialize order type filter if it exists
            const orderTypeFilter = document.getElementById('order-type-filter');
            if (orderTypeFilter) {
                orderTypeFilter.addEventListener('change', filterOrdersByType);

                // Apply saved filter if exists
                applySavedFilter();
            }
        });

        // Function to filter orders by type
        function filterOrdersByType() {
            const filterValue = document.getElementById('order-type-filter').value.toLowerCase();
            const tableRows = document.querySelectorAll('.order-book-table tbody tr');

            tableRows.forEach(row => {
                const orderTypeCell = row.querySelector('td:first-child');
                if (!orderTypeCell) return;

                const orderTypeText = orderTypeCell.textContent.toLowerCase();

                // Handle both English and Thai languages
                if (filterValue === 'all') {
                    // Show all orders
                    row.style.display = '';
                } else if (filterValue === 'buy') {
                    // Check for buy orders in both languages
                    if (orderTypeText.includes('buy') || orderTypeText.includes('ซื้อ')) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                } else if (filterValue === 'sell') {
                    // Check for sell orders in both languages
                    if (orderTypeText.includes('sell') || orderTypeText.includes('ขาย')) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                } else {
                    // Default case - should not happen
                    row.style.display = '';
                }
            });

            // Save filter preference to localStorage
            localStorage.setItem('orderBookFilter', filterValue);
        }

        // Function to apply saved filter on page load
        function applySavedFilter() {
            const savedFilter = localStorage.getItem('orderBookFilter');
            if (savedFilter) {
                const filterSelect = document.getElementById('order-type-filter');
                if (filterSelect) {
                    filterSelect.value = savedFilter;
                    filterOrdersByType();
                }
            }
        }

        // Function to initialize tooltips
        function initializeTooltips() {
            const tooltipIcons = document.querySelectorAll('.tooltip-icon');

            tooltipIcons.forEach(icon => {
                // Show tooltip on hover
                icon.addEventListener('mouseenter', function() {
                    const tooltip = this.nextElementSibling;
                    if (tooltip && tooltip.classList.contains('tooltip-text')) {
                        tooltip.style.visibility = 'visible';
                        tooltip.style.opacity = '1';
                    }
                });

                // Hide tooltip when mouse leaves
                icon.addEventListener('mouseleave', function() {
                    const tooltip = this.nextElementSibling;
                    if (tooltip && tooltip.classList.contains('tooltip-text')) {
                        tooltip.style.visibility = 'hidden';
                        tooltip.style.opacity = '0';
                    }
                });
            });
        }

        // Function to handle marketspace change in edit form (similar to submit order form)
        function handleEditMarketspaceChange(preserveValues = false) {
            const marketspaceSelect = document.getElementById('edit_marketspace');
            const deliveryTermSelect = document.getElementById('edit_delivery_term');
            const selectedMarketspace = marketspaceSelect ? marketspaceSelect.value : '';
            const selectedDeliveryTerm = deliveryTermSelect ? deliveryTermSelect.value : '';

            console.log(`handleEditMarketspaceChange - Marketspace: ${selectedMarketspace}, Delivery Term: ${selectedDeliveryTerm}, Preserve Values: ${preserveValues}`);

            // Function to hide fields without clearing values or removing required attributes
            const hideFieldsOnly = (fieldsContainer) => {
                if (fieldsContainer) {
                    fieldsContainer.style.display = 'none';
                }
            };

            // Function to hide fields and remove required attributes without clearing values
            const hideFields = (fieldsContainer, fieldIds) => {
                if (fieldsContainer) {
                    fieldsContainer.style.display = 'none';
                    fieldIds.forEach(id => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.removeAttribute('required');
                        }
                    });
                }
            };

            // Function to reset fields (clear values)
            const resetFields = (fieldsContainer, fieldIds) => {
                if (fieldsContainer) {
                    fieldsContainer.style.display = 'none';
                    fieldIds.forEach(id => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.removeAttribute('required');
                            if (!preserveValues) {
                                if (element.tagName === 'SELECT') {
                                    element.selectedIndex = 0;
                                } else {
                                    element.value = '';
                                }
                            }
                        }
                    });
                }
            };

            // Get the field containers
            const portFields = document.getElementById('edit-port-fields');
            const locationFields = document.getElementById('edit-location-fields');

            // Just hide both containers first without affecting the fields
            hideFieldsOnly(portFields);
            hideFieldsOnly(locationFields);

            // Update payment and delivery terms based on marketspace
            if (selectedMarketspace && !preserveValues) {
                // Fetch payment terms for the selected marketspace
                fetch(`/payment?marketspaceID=${selectedMarketspace}&lang=${window.currentLang || 'en'}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Failed to fetch payment terms: ${response.status}`);
                        }
                        return response.text();
                    })
                    .then(html => {
                        const paymentSelect = document.getElementById('edit_payment_term');
                        if (paymentSelect) {
                            const firstOption = paymentSelect.options[0];
                            paymentSelect.innerHTML = firstOption.outerHTML + html;
                        }
                    })
                    .catch(error => console.error('Error fetching payment terms:', error));

                // Fetch delivery terms for the selected marketspace
                fetch(`/delivery?marketspaceID=${selectedMarketspace}&lang=${window.currentLang || 'en'}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Failed to fetch delivery terms: ${response.status}`);
                        }
                        return response.text();
                    })
                    .then(html => {
                        const deliverySelect = document.getElementById('edit_delivery_term');
                        if (deliverySelect) {
                            const firstOption = deliverySelect.options[0];
                            deliverySelect.innerHTML = firstOption.outerHTML + html;

                            // If we're switching from local to global, reset the delivery term
                            // This matches the behavior in the submit order form
                            if (selectedMarketspace === '2') {
                                deliverySelect.selectedIndex = 0;
                                deliverySelect.dispatchEvent(new Event('change'));
                            }
                        }
                    })
                    .catch(error => console.error('Error fetching delivery terms:', error));
            }

            // Apply requirements based on conditions
            if (selectedMarketspace === '1') { // Local market
                console.log("Showing location fields for local market");
                locationFields.style.display = 'block';

                // Make location fields required
                ['edit_province', 'edit_district', 'edit_subdistrict'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.setAttribute('required', '');
                    }
                });
            } else if (selectedMarketspace === '2') { // Global market
                if (selectedDeliveryTerm === '5' || selectedDeliveryTerm === '9') {
                    console.log("Showing location fields for global market with delivery terms 5 or 9");
                    locationFields.style.display = 'block';

                    // Make location fields required
                    ['edit_province', 'edit_district', 'edit_subdistrict'].forEach(id => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.setAttribute('required', '');
                        }
                    });
                } else {
                    console.log("Showing port fields for global market with other delivery terms");
                    portFields.style.display = 'block';
                    // We don't make port fields required as validation will handle this
                }
            }

            // Log the current state of important fields for debugging
            console.log("Current field values after handleEditMarketspaceChange:");
            const provinceSelect = document.getElementById('edit_province');
            const districtSelect = document.getElementById('edit_district');
            const subdistrictSelect = document.getElementById('edit_subdistrict');
            const polSelect = document.getElementById('edit_port_loading');
            const podSelect = document.getElementById('edit_port_discharge');

            console.log(`Province: ${provinceSelect ? provinceSelect.value : 'not found'}`);
            console.log(`District: ${districtSelect ? districtSelect.value : 'not found'}`);
            console.log(`Subdistrict: ${subdistrictSelect ? subdistrictSelect.value : 'not found'}`);
            console.log(`Port of Loading: ${polSelect ? polSelect.value : 'not found'}`);
            console.log(`Port of Discharge: ${podSelect ? podSelect.value : 'not found'}`);
            console.log(`Location fields display: ${locationFields.style.display}`);
            console.log(`Port fields display: ${portFields.style.display}`);
        }

        // Edit modal functions
        // Function to fetch order details for editing
        async function fetchOrderDetails(orderId) {
            try {
                const response = await fetch(`/api/order/${orderId}`);
                if (!response.ok) {
                    throw new Error(`Failed to fetch order details: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error("Error fetching order details:", error);
                return null;
            }
        }

        // Function to populate select options
        async function populateSelectOptions(selectId, endpoint, selectedValue) {
            try {
                const select = document.getElementById(selectId);
                if (!select) return;

                const response = await fetch(endpoint);
                if (!response.ok) {
                    throw new Error(`Failed to fetch options for ${selectId}: ${response.status}`);
                }

                const options = await response.json();
                options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.id;
                    optionElement.textContent = option.name;
                    select.appendChild(optionElement);
                });

                if (selectedValue) {
                    select.value = selectedValue;
                }
            } catch (error) {
                console.error(`Error populating ${selectId}:`, error);
            }
        }

        function openEditModal(orderId, price, quantity, maxQuantity, remark, statusId) {
            console.log("Opening edit modal with values:", {orderId, price, quantity, maxQuantity, remark, statusId});

            try {
                // Show the modal first
                document.getElementById('editModal').style.display = 'block';

                // Show loading indicator
                showLoadingIndicator();

                // Log initial state of dropdowns
                setTimeout(() => {
                    logDropdownState("Initial state before loading order data");
                }, 100);

                // Get current language - make it available globally for all fetch calls
                window.currentLang = "{{ .Lang }}";
                const currentLang = window.currentLang;
                const isThai = currentLang === "th";

                // Reset form and validation styles
                document.getElementById('editForm').reset();
                resetValidationStyles();

                // Enable all fields initially
                document.getElementById('edit_price').disabled = false;
                document.getElementById('edit_quantity').disabled = false;
                document.getElementById('edit_first_delivery_date_basic').disabled = false;
                document.getElementById('edit_last_delivery_date_basic').disabled = false;
                document.getElementById('edit_price_full').disabled = false;
                document.getElementById('edit_quantity_full').disabled = false;
                document.getElementById('edit_first_delivery_date').disabled = false;
                document.getElementById('edit_last_delivery_date').disabled = false;

                // Set basic values
                document.getElementById('edit_order_id').value = orderId;
                document.getElementById('edit_price').value = parseFloat(price) || 0;

                // Make sure quantity is properly displayed
                const quantityValue = parseFloat(quantity) || 0;
                document.getElementById('edit_quantity').value = quantityValue;
                console.log("Setting quantity value:", quantityValue);

                document.getElementById('max_quantity').value = parseFloat(maxQuantity) || 0;
                document.getElementById('order_status_id').value = parseInt(statusId) || 13;

                // Handle remark which might be null or undefined
                if (remark === "null" || remark === "undefined" || !remark) {
                    document.getElementById('edit_remark').value = '';
                } else {
                    document.getElementById('edit_remark').value = remark;
                }

                // Show or hide quantity error message based on order status
                const orderStatusId = parseInt(document.getElementById('order_status_id').value);
                console.log("Order status ID:", orderStatusId);

                // We'll load dropdown options directly for available orders

                // For partially matched orders, fetch currency data
                if (orderStatusId === 14) {
                    fetch(`/api/order/${orderId}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`Failed to fetch order details: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(orderData => {
                            if (orderData && orderData.currencyID) {
                                // Set the hidden currency field
                                document.getElementById('edit_currency_basic').value = orderData.currencyID;
                            }
                        })
                        .catch(error => {
                            console.error("Error fetching order details:", error);
                        });
                }

                if (orderStatusId === 14) {
                    // For partially matched orders, show quantity restriction message and only basic fields
                    document.getElementById('quantity_restriction').style.display = 'block';
                    document.getElementById('basic-edit-fields').style.display = 'block';
                    document.getElementById('full-edit-fields').style.display = 'none';

                    // Set the max attribute to prevent increasing quantity beyond available amount
                    const maxQuantityValue = parseFloat(document.getElementById('max_quantity').value);
                    const quantityInput = document.getElementById('edit_quantity');

                    // Make sure quantity is properly displayed again
                    if (!quantityInput.value || quantityInput.value === "0") {
                        const originalQuantity = parseFloat(quantity) || 0;
                        quantityInput.value = originalQuantity;
                        console.log("Re-setting quantity value:", originalQuantity);
                    }

                    quantityInput.setAttribute('max', maxQuantityValue);
                    console.log("Showing quantity restriction for partially matched order. Max quantity set to:", maxQuantityValue);

                    // Fetch order details to get delivery dates for partially matched orders
                    fetch(`/api/order/${orderId}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`Failed to fetch order details: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(orderData => {
                            console.log("Order data for partially matched order:", orderData);

                            // Store the order data for reference
                            window.currentPartialOrderData = orderData;

                            // Set delivery dates if available - check both camelCase and PascalCase properties
                            // (API might return either format)
                            const firstDeliveryDate = orderData.firstDeliveryDate || orderData.FirstDeliveryDate;
                            const lastDeliveryDate = orderData.lastDeliveryDate || orderData.LastDeliveryDate;

                            if (firstDeliveryDate) {
                                try {
                                    const formattedDate = new Date(firstDeliveryDate).toISOString().split('T')[0];
                                    document.getElementById('edit_first_delivery_date_basic').value = formattedDate;
                                    console.log("Set first delivery date:", formattedDate);
                                } catch (e) {
                                    console.error("Error formatting first delivery date:", e);
                                }
                            }

                            if (lastDeliveryDate) {
                                try {
                                    const formattedDate = new Date(lastDeliveryDate).toISOString().split('T')[0];
                                    document.getElementById('edit_last_delivery_date_basic').value = formattedDate;
                                    console.log("Set last delivery date:", formattedDate);
                                } catch (e) {
                                    console.error("Error formatting last delivery date:", e);
                                }
                            }

                            // Set currency if available
                            const currencyID = orderData.currencyID || orderData.CurrencyID;
                            if (currencyID) {
                                document.getElementById('edit_currency_basic').value = currencyID;
                            }

                            // Add validation for delivery dates
                            setupDateValidation();

                            // Hide loading indicator after data is loaded
                            hideLoadingIndicator();
                        })
                        .catch(error => {
                            console.error("Error fetching order details for dates:", error);

                            // Hide loading indicator and show error message
                            hideLoadingIndicator();
                            const errorMsg = window.currentLang === "th" ?
                                "ไม่สามารถโหลดข้อมูลวันที่ส่งมอบได้ กรุณาลองใหม่อีกครั้ง" :
                                "Could not load delivery dates. Please try again.";
                            showErrorMessage(errorMsg);
                        });

                    // Function to set up date validation for partially matched orders
                    function setupDateValidation() {
                        const firstDateInput = document.getElementById('edit_first_delivery_date_basic');
                        const lastDateInput = document.getElementById('edit_last_delivery_date_basic');
                        const firstDateError = document.getElementById('first_date_error');
                        const lastDateError = document.getElementById('last_date_error');

                        // Calculate today's date in YYYY-MM-DD format
                        const today = new Date().toISOString().split('T')[0];

                        // Set the min attribute for both fields to today's date automatically
                        firstDateInput.setAttribute('min', today);
                        lastDateInput.setAttribute('min', today);

                        // Clear any previous event listeners to avoid duplicates
                        firstDateInput.removeEventListener('change', updateDateConstraints);
                        lastDateInput.removeEventListener('change', updateDateConstraints);

                        // Attach event listeners to update constraints when either field changes
                        firstDateInput.addEventListener('change', updateDateConstraints);
                        lastDateInput.addEventListener('change', updateDateConstraints);

                        // Initial validation
                        updateDateConstraints();

                        // Function to update constraints when dates change
                        function updateDateConstraints() {
                            // Hide error messages
                            firstDateError.style.display = 'none';
                            lastDateError.style.display = 'none';

                            // Update constraints when the first delivery date changes
                            if (firstDateInput.value) {
                                // Set the minimum allowed value for the last delivery date to the selected first date
                                lastDateInput.min = firstDateInput.value;

                                // If the last delivery date is already set but is before the new first date, clear it
                                if (lastDateInput.value && lastDateInput.value < firstDateInput.value) {
                                    lastDateError.style.display = 'block';
                                    setTimeout(() => {
                                        lastDateInput.value = "";
                                        lastDateError.style.display = 'none';
                                    }, 2000);
                                }
                            } else {
                                // If the first date is cleared, reset the last date's min to today's date
                                lastDateInput.min = today;
                            }

                            // Update constraints when the last delivery date changes
                            if (lastDateInput.value) {
                                // Set the maximum allowed value for the first delivery date to the selected last date
                                firstDateInput.max = lastDateInput.value;

                                // If the first delivery date is set but is later than the new last date, clear it
                                if (firstDateInput.value && firstDateInput.value > lastDateInput.value) {
                                    firstDateError.style.display = 'block';
                                    setTimeout(() => {
                                        firstDateInput.value = "";
                                        firstDateError.style.display = 'none';
                                    }, 2000);
                                }

                                // Check if dates are the same
                                if (firstDateInput.value && firstDateInput.value === lastDateInput.value) {
                                    const errorMsg = window.currentLang === "th" ?
                                        "วันที่ส่งมอบสุดท้ายต้องไม่เป็นวันเดียวกับวันที่ส่งมอบแรก" :
                                        "Last delivery date cannot be the same as first delivery date";
                                    showErrorMessage(errorMsg);

                                    // Clear the last date
                                    setTimeout(() => {
                                        lastDateInput.value = "";
                                    }, 100);
                                }
                            } else {
                                // If the last date is cleared, remove the max constraint from the first date
                                firstDateInput.removeAttribute('max');
                            }
                        }
                    }

                    // Function to set up date validation for available orders (full edit form)
                    function setupFullDateValidation() {
                        const firstDateInput = document.getElementById('edit_first_delivery_date');
                        const lastDateInput = document.getElementById('edit_last_delivery_date');
                        const firstDateError = document.getElementById('first_date_error_full');
                        const lastDateError = document.getElementById('last_date_error_full');

                        // Calculate today's date in YYYY-MM-DD format
                        const today = new Date().toISOString().split('T')[0];

                        // Set the min attribute for both fields to today's date automatically
                        firstDateInput.setAttribute('min', today);
                        lastDateInput.setAttribute('min', today);

                        // Clear any previous event listeners to avoid duplicates
                        firstDateInput.removeEventListener('change', updateDateConstraints);
                        lastDateInput.removeEventListener('change', updateDateConstraints);

                        // Attach event listeners to update constraints when either field changes
                        firstDateInput.addEventListener('change', updateDateConstraints);
                        lastDateInput.addEventListener('change', updateDateConstraints);

                        // Initial validation
                        updateDateConstraints();

                        // Function to update constraints when dates change
                        function updateDateConstraints() {
                            // Hide error messages
                            firstDateError.style.display = 'none';
                            lastDateError.style.display = 'none';

                            // Update constraints when the first delivery date changes
                            if (firstDateInput.value) {
                                // Set the minimum allowed value for the last delivery date to the selected first date
                                lastDateInput.min = firstDateInput.value;

                                // If the last delivery date is already set but is before the new first date, clear it
                                if (lastDateInput.value && lastDateInput.value < firstDateInput.value) {
                                    lastDateError.style.display = 'block';
                                    setTimeout(() => {
                                        lastDateInput.value = "";
                                        lastDateError.style.display = 'none';
                                    }, 2000);
                                }
                            } else {
                                // If the first date is cleared, reset the last date's min to today's date
                                lastDateInput.min = today;
                            }

                            // Update constraints when the last delivery date changes
                            if (lastDateInput.value) {
                                // Set the maximum allowed value for the first delivery date to the selected last date
                                firstDateInput.max = lastDateInput.value;

                                // If the first delivery date is set but is later than the new last date, clear it
                                if (firstDateInput.value && firstDateInput.value > lastDateInput.value) {
                                    firstDateError.style.display = 'block';
                                    setTimeout(() => {
                                        firstDateInput.value = "";
                                        firstDateError.style.display = 'none';
                                    }, 2000);
                                }

                                // Check if dates are the same
                                if (firstDateInput.value && firstDateInput.value === lastDateInput.value) {
                                    const errorMsg = window.currentLang === "th" ?
                                        "วันที่ส่งมอบสุดท้ายต้องไม่เป็นวันเดียวกับวันที่ส่งมอบแรก" :
                                        "Last delivery date cannot be the same as first delivery date";
                                    showErrorMessage(errorMsg);

                                    // Clear the last date
                                    setTimeout(() => {
                                        lastDateInput.value = "";
                                    }, 100);
                                }
                            } else {
                                // If the last date is cleared, remove the max constraint from the first date
                                firstDateInput.removeAttribute('max');
                            }
                        }
                    }
                } else if (orderStatusId === 13) {
                    // For available orders, hide quantity restriction message and show all fields
                    document.getElementById('quantity_restriction').style.display = 'none';
                    document.getElementById('basic-edit-fields').style.display = 'none';
                    document.getElementById('full-edit-fields').style.display = 'block';
                    console.log("Showing full edit form for available order");

                    // Copy values to full form fields
                    document.getElementById('edit_price_full').value = document.getElementById('edit_price').value;
                    document.getElementById('edit_quantity_full').value = document.getElementById('edit_quantity').value;

                    // For available orders, we need to fetch all details and populate all fields
                    // First, fetch the order details
                    fetch(`/api/order/${orderId}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`Failed to fetch order details: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(orderData => {
                            // Store the order data globally so we can use it after dropdowns are loaded
                            window.currentOrderData = orderData;
                            console.log("Order data received:", orderData);

                            // Now fetch all dropdown options and wait for them to complete
                            const promises = [];

                            // Set marketspace and market directly
                            if (orderData.MarketspaceID) {
                                const marketspaceSelect = document.getElementById('edit_marketspace');
                                if (marketspaceSelect) {
                                    marketspaceSelect.value = orderData.MarketspaceID;
                                    // Trigger change event to update dependent fields
                                    marketspaceSelect.dispatchEvent(new Event('change'));
                                }
                            }

                            if (orderData.MarketID) {
                                const marketSelect = document.getElementById('edit_market');
                                if (marketSelect) {
                                    marketSelect.value = orderData.MarketID;
                                    // Trigger change event to update dependent fields
                                    marketSelect.dispatchEvent(new Event('change'));
                                }
                            }

                            // First, manually set the marketspace and market values
                            if (orderData.MarketspaceID) {
                                const marketspaceSelect = document.getElementById('edit_marketspace');
                                if (marketspaceSelect) {
                                    marketspaceSelect.value = orderData.MarketspaceID;
                                }
                            }

                            if (orderData.MarketID) {
                                const marketSelect = document.getElementById('edit_market');
                                if (marketSelect) {
                                    marketSelect.value = orderData.MarketID;
                                }
                            }

                            // Now fetch the dependent dropdowns in sequence
                            // First, fetch payment terms based on marketspace
                            promises.push(
                                fetch(`/payment?marketspaceID=${orderData.MarketspaceID}&lang=${window.currentLang || 'en'}`)
                                    .then(response => {
                                        if (!response.ok) {
                                            throw new Error(`Failed to fetch payment terms: ${response.status}`);
                                        }
                                        return response.text();
                                    })
                                    .then(html => {
                                        const paymentSelect = document.getElementById('edit_payment_term');
                                        if (paymentSelect) {
                                            const firstOption = paymentSelect.options[0];
                                            paymentSelect.innerHTML = firstOption.outerHTML + html;

                                            if (orderData.PaymentTermID) {
                                                paymentSelect.value = orderData.PaymentTermID;
                                            }
                                        }
                                    })
                                    .catch(error => console.error('Error fetching payment terms:', error))
                            );

                            // Fetch delivery terms based on marketspace
                            promises.push(
                                fetch(`/delivery?marketspaceID=${orderData.MarketspaceID}&lang=${window.currentLang || 'en'}`)
                                    .then(response => {
                                        if (!response.ok) {
                                            throw new Error(`Failed to fetch delivery terms: ${response.status}`);
                                        }
                                        return response.text();
                                    })
                                    .then(html => {
                                        const deliverySelect = document.getElementById('edit_delivery_term');
                                        if (deliverySelect) {
                                            const firstOption = deliverySelect.options[0];
                                            deliverySelect.innerHTML = firstOption.outerHTML + html;

                                            if (orderData.DeliveryTermID) {
                                                deliverySelect.value = orderData.DeliveryTermID;
                                            }
                                        }
                                    })
                                    .catch(error => console.error('Error fetching delivery terms:', error))
                            );

                            // Fetch submarkets based on selected market
                            promises.push(
                                fetch(`/submarkets?marketID=${orderData.MarketID}&lang=${window.currentLang || 'en'}`)
                                    .then(response => {
                                        if (!response.ok) {
                                            throw new Error(`Failed to fetch submarkets: ${response.status}`);
                                        }
                                        return response.text();
                                    })
                                    .then(html => {
                                        // Parse HTML options into select element
                                        const submarketSelect = document.getElementById('edit_submarket');
                                        if (submarketSelect) {
                                            // Keep the first option (placeholder)
                                            const firstOption = submarketSelect.options[0];
                                            submarketSelect.innerHTML = firstOption.outerHTML + html;

                                            // Store the submarket ID for reference
                                            const submarketID = orderData.SubmarketID;

                                            // Store the submarket ID in a data attribute
                                            if (submarketID) {
                                                submarketSelect.setAttribute('data-selected-submarket-id', submarketID);
                                            }

                                            // Log the available options for debugging
                                            console.log("Submarkets HTML received");
                                            const options = Array.from(submarketSelect.options).map(opt => ({
                                                value: opt.value,
                                                text: opt.text
                                            }));
                                            console.log("Submarket options after loading:", options);

                                            // Set selected value with a delay to ensure options are fully loaded
                                            setTimeout(() => {
                                                if (submarketID) {
                                                    console.log(`Setting submarket value to: ${submarketID}`);
                                                    submarketSelect.value = submarketID;

                                                    // No need for visual styling

                                                    // Trigger change event to update dependent fields
                                                    submarketSelect.dispatchEvent(new Event('change'));

                                                    // Log the state after setting submarket
                                                    logDropdownState("After setting submarket value");
                                                }
                                            }, 500); // Increased delay to ensure options are fully loaded
                                        }
                                    })
                                    .catch(error => console.error('Error fetching submarkets:', error))
                            );

                            // Set order type directly
                            if (orderData.OrderTypeID) {
                                const orderTypeSelect = document.getElementById('edit_order_type');
                                if (orderTypeSelect) {
                                    orderTypeSelect.value = orderData.OrderTypeID;
                                }
                            }

                            // Set matching type directly
                            if (orderData.MatchingTypeID) {
                                const matchingTypeSelect = document.getElementById('edit_matching_type');
                                if (matchingTypeSelect) {
                                    matchingTypeSelect.value = orderData.MatchingTypeID;
                                }
                            }

                            // Set contract type directly
                            if (orderData.ContractTypeID) {
                                const contractTypeSelect = document.getElementById('edit_contract_type');
                                if (contractTypeSelect) {
                                    contractTypeSelect.value = orderData.ContractTypeID;
                                }
                            }

                            // Load products based on selected submarket
                            if (orderData.SubmarketID) {
                                const submarketID = orderData.SubmarketID;
                                const productSelect = document.getElementById('edit_product');

                                if (productSelect) {
                                    // Get current language
                                    const currentLang = window.currentLang || "{{ .Lang }}";
                                    const isThai = currentLang === "th";

                                    // Keep the first option (placeholder)
                                    const firstOption = productSelect.options[0];

                                    // Store the product and quality IDs for later use
                                    const productID = orderData.ProductID;
                                    const qualityID = orderData.QualityID;

                                    // Store quality ID in the quality select for reference
                                    const qualitySelect = document.getElementById('edit_quality');
                                    if (qualitySelect && qualityID) {
                                        console.log(`Storing quality ID in data attribute: ${qualityID}`);
                                        qualitySelect.setAttribute('data-quality-id', qualityID);
                                    }

                                    // Check if we have cached products for this submarket
                                    if (dropdownCache.products[submarketID] && dropdownCache.products[submarketID].length > 0) {
                                        console.log("Using cached products for submarket ID:", submarketID);

                                        // Build options from cache
                                        let optionsHtml = '';
                                        dropdownCache.products[submarketID].forEach(product => {
                                            const name = isThai && product.ThName ? product.ThName : product.EnName;
                                            optionsHtml += `<option value="${product.ID}">${name}</option>`;
                                        });

                                        productSelect.innerHTML = firstOption.outerHTML + optionsHtml;
                                        console.log("Product options loaded from cache:", Array.from(productSelect.options).map(opt => ({ value: opt.value, text: opt.text })));

                                        // Set product value immediately
                                        if (productID) {
                                            console.log(`Setting product value to: ${productID}`);
                                            productSelect.value = productID;

                                            // Manually trigger the updateQualityOptions function
                                            updateQualityOptions(productID);
                                        }
                                    } else {
                                        // Fallback to fetch if not in cache
                                        console.log("Products not in cache, fetching from server");
                                        promises.push(
                                            fetch(`/products?submarketID=${submarketID}&lang=${currentLang}`)
                                                .then(response => {
                                                    if (!response.ok) {
                                                        throw new Error(`Failed to fetch products: ${response.status}`);
                                                    }
                                                    return response.text();
                                                })
                                                .then(html => {
                                                    productSelect.innerHTML = firstOption.outerHTML + html;

                                                    // Set selected value with a delay to ensure options are fully loaded
                                                    setTimeout(() => {
                                                        if (productID) {
                                                            console.log(`Setting product value to: ${productID}`);
                                                            productSelect.value = productID;

                                                            // Manually trigger the updateQualityOptions function
                                                            updateQualityOptions(productID);

                                                            // Store quality name for reference
                                                            if (orderData.QualityName) {
                                                                console.log(`Storing quality name: ${orderData.QualityName}`);
                                                                if (window.currentLang === "th" && orderData.QualityNameTh) {
                                                                    console.log(`Also storing Thai quality name: ${orderData.QualityNameTh}`);
                                                                }
                                                            }
                                                        }
                                                    }, 100);
                                                })
                                                .catch(error => console.error('Error fetching products:', error))
                                        );
                                    }

                                    // Double-check quality value after a delay with multiple retries
                                    let retryCount = 0;
                                    const maxRetries = 3;
                                    const checkQualityValue = () => {
                                        if (qualitySelect && qualityID) {
                                            console.log(`Check #${retryCount+1}: Quality select value: ${qualitySelect.value}, Expected: ${qualityID}`);

                                            if (qualitySelect.value !== qualityID.toString()) {
                                                console.log(`Retry #${retryCount+1}: Setting quality value to: ${qualityID}`);

                                                // Force update quality options
                                                updateQualityOptions(productID);

                                                // Directly set the value after a short delay
                                                setTimeout(() => {
                                                    qualitySelect.value = qualityID;
                                                    console.log(`After direct set: Quality value is now: ${qualitySelect.value}`);

                                                    // If still not set, try to add the option manually
                                                    if (qualitySelect.value !== qualityID.toString()) {
                                                        // Get the appropriate quality name based on language
                                                        let qualityName = orderData.QualityName;
                                                        if (window.currentLang === "th" && orderData.QualityNameTh) {
                                                            qualityName = orderData.QualityNameTh;
                                                        }

                                                        if (qualityName) {
                                                            console.log(`Adding quality option manually: ${qualityID} - ${qualityName}`);
                                                            const option = document.createElement('option');
                                                            option.value = qualityID;
                                                            option.text = qualityName;
                                                            option.selected = true;
                                                            qualitySelect.appendChild(option);

                                                            // Store the quality name for reference
                                                            window.currentOrderData.QualityName = qualityName;
                                                        }
                                                    }
                                                }, 100);

                                                // Try again if we haven't reached max retries
                                                retryCount++;
                                                if (retryCount < maxRetries) {
                                                    setTimeout(checkQualityValue, 300);
                                                }
                                            } else {
                                                console.log(`Quality value successfully set to: ${qualityID}`);
                                            }
                                        }
                                    };

                                    // Start the retry process
                                    setTimeout(checkQualityValue, 500);
                                }
                            }

                            // Note: Quality fetch is now handled in the product fetch callback
                            // to ensure proper sequencing of the dependent dropdowns

                            // Set currency directly
                            if (orderData.CurrencyID) {
                                const currencySelect = document.getElementById('edit_currency');
                                if (currencySelect) {
                                    currencySelect.value = orderData.CurrencyID;
                                }
                            }

                            // Set UOM directly
                            if (orderData.UOMID) {
                                const uomSelect = document.getElementById('edit_uom');
                                if (uomSelect) {
                                    uomSelect.value = orderData.UOMID;
                                }
                            }

                            // Set packing directly
                            if (orderData.PackingID) {
                                const packingSelect = document.getElementById('edit_packing');
                                if (packingSelect) {
                                    packingSelect.value = orderData.PackingID;
                                }
                            }

                            // Set payment term directly
                            if (orderData.PaymentTermID) {
                                const paymentTermSelect = document.getElementById('edit_payment_term');
                                if (paymentTermSelect) {
                                    paymentTermSelect.value = orderData.PaymentTermID;
                                }
                            }

                            // Set delivery term directly
                            if (orderData.DeliveryTermID) {
                                const deliveryTermSelect = document.getElementById('edit_delivery_term');
                                if (deliveryTermSelect) {
                                    deliveryTermSelect.value = orderData.DeliveryTermID;
                                    // Trigger change event to update dependent fields
                                    deliveryTermSelect.dispatchEvent(new Event('change'));
                                }
                            }

                            // Set country directly
                            if (orderData.CountryID) {
                                const countrySelect = document.getElementById('edit_country');
                                if (countrySelect) {
                                    countrySelect.value = orderData.CountryID;
                                }
                            }

                            // Now wait for all promises to complete
                            Promise.all(promises).then(() => {
                                console.log("All dropdowns loaded, now setting values");

                                // Log the complete order data for debugging
                                console.log("Complete order data:", JSON.stringify(orderData, null, 2));

                                // Store the order data in a global variable for reference
                                window.currentOrderData = orderData;

                                // Set delivery dates
                                if (orderData.FirstDeliveryDate) {
                                    document.getElementById('edit_first_delivery_date').value =
                                        new Date(orderData.FirstDeliveryDate).toISOString().split('T')[0];
                                }
                                if (orderData.LastDeliveryDate) {
                                    document.getElementById('edit_last_delivery_date').value =
                                        new Date(orderData.LastDeliveryDate).toISOString().split('T')[0];
                                }

                                // Setup date validation for full edit form
                                setupFullDateValidation();

                                // Use our centralized function to handle field visibility
                                // Pass true to preserve field values when loading existing data
                                handleEditMarketspaceChange(true);
                            });

                            // End of fetch order details

                            // Set province and fetch districts if valid
                            if (orderData.ProvinceID && orderData.ProvinceID.Valid) {
                                const provinceSelect = document.getElementById('edit_province');
                                if (provinceSelect) {
                                    provinceSelect.value = orderData.ProvinceID.Int64;

                                    // Fetch districts based on province
                                    promises.push(
                                        fetch(`/district?province_id=${orderData.ProvinceID.Int64}&lang=${window.currentLang || 'en'}`)
                                            .then(response => {
                                                if (!response.ok) {
                                                    throw new Error(`Failed to fetch districts: ${response.status}`);
                                                }
                                                return response.text();
                                            })
                                            .then(html => {
                                                const districtSelect = document.getElementById('edit_district');
                                                if (districtSelect) {
                                                    const firstOption = districtSelect.options[0];
                                                    districtSelect.innerHTML = firstOption.outerHTML + html;

                                                    // Set district if valid
                                                    if (orderData.DistrictID && orderData.DistrictID.Valid) {
                                                        districtSelect.value = orderData.DistrictID.Int64;

                                                        // Fetch subdistricts based on district
                                                        fetch(`/subdistrict?district_id=${orderData.DistrictID.Int64}&lang=${window.currentLang || 'en'}`)
                                                            .then(response => {
                                                                if (!response.ok) {
                                                                    throw new Error(`Failed to fetch subdistricts: ${response.status}`);
                                                                }
                                                                return response.text();
                                                            })
                                                            .then(html => {
                                                                const subdistrictSelect = document.getElementById('edit_subdistrict');
                                                                if (subdistrictSelect) {
                                                                    const firstOption = subdistrictSelect.options[0];
                                                                    subdistrictSelect.innerHTML = firstOption.outerHTML + html;

                                                                    // Set subdistrict if valid
                                                                    if (orderData.SubdistrictID && orderData.SubdistrictID.Valid) {
                                                                        subdistrictSelect.value = orderData.SubdistrictID.Int64;
                                                                    }
                                                                }
                                                            })
                                                            .catch(error => console.error('Error fetching subdistricts:', error));
                                                    }
                                                }
                                            })
                                            .catch(error => console.error('Error fetching districts:', error))
                                    );
                                }
                            }

                            // Set port of loading if valid
                            if (orderData.PortOfLoadingID && orderData.PortOfLoadingID.Valid) {
                                const portLoadingSelect = document.getElementById('edit_port_loading');
                                if (portLoadingSelect) {
                                    portLoadingSelect.value = orderData.PortOfLoadingID.Int64;
                                }
                            }

                            // Set port of discharge if valid
                            if (orderData.PortOfDischargeID && orderData.PortOfDischargeID.Valid) {
                                const portDischargeSelect = document.getElementById('edit_port_discharge');
                                if (portDischargeSelect) {
                                    portDischargeSelect.value = orderData.PortOfDischargeID.Int64;
                                }
                            }

                            // We'll use htmx to handle the cascading dropdowns
                            // But we still need to wait for all promises to complete
                            Promise.all(promises)
                                .then(() => {
                                    console.log("All dropdowns loaded, now setting values");

                                    // Log the entire order data to see what we're working with
                                    console.log("Complete order data:", JSON.stringify(orderData, null, 2));

                                    // Add a small delay to ensure all DOM updates are complete
                                    setTimeout(() => {
                                        // Set values for all form fields based on the order data
                                        // Set marketspace
                                        if (orderData.MarketspaceID) {
                                            const marketspaceSelect = document.getElementById('edit_marketspace');
                                            if (marketspaceSelect) {
                                                marketspaceSelect.value = orderData.MarketspaceID;
                                                // Trigger change event to update dependent fields
                                                marketspaceSelect.dispatchEvent(new Event('change'));
                                            }
                                        }

                                        // Set market
                                        if (orderData.MarketID) {
                                            const marketSelect = document.getElementById('edit_market');
                                            if (marketSelect) {
                                                marketSelect.value = orderData.MarketID;
                                                // Trigger change event to update dependent fields
                                                marketSelect.dispatchEvent(new Event('change'));
                                            }
                                        }

                                        // Set order type
                                        if (orderData.OrderTypeID) {
                                            const orderTypeSelect = document.getElementById('edit_order_type');
                                            if (orderTypeSelect) {
                                                orderTypeSelect.value = orderData.OrderTypeID;
                                            }
                                        }

                                        // Set matching type
                                        if (orderData.MatchingTypeID) {
                                            const matchingTypeSelect = document.getElementById('edit_matching_type');
                                            if (matchingTypeSelect) {
                                                matchingTypeSelect.value = orderData.MatchingTypeID;
                                            }
                                        }

                                        // Set contract type
                                        if (orderData.ContractTypeID) {
                                            const contractTypeSelect = document.getElementById('edit_contract_type');
                                            if (contractTypeSelect) {
                                                contractTypeSelect.value = orderData.ContractTypeID;
                                            }
                                        }

                                        // Note: We don't set submarket, product, and quality values here
                                        // because they're set with delays in their respective fetch callbacks

                                        // Set currency
                                        if (orderData.CurrencyID) {
                                            const currencySelect = document.getElementById('edit_currency');
                                            if (currencySelect) {
                                                currencySelect.value = orderData.CurrencyID;
                                            }
                                        }

                                        // Set UOM
                                        if (orderData.UOMID) {
                                            const uomSelect = document.getElementById('edit_uom');
                                            if (uomSelect) {
                                                uomSelect.value = orderData.UOMID;
                                            }
                                        }

                                        // Set packing
                                        if (orderData.PackingID) {
                                            const packingSelect = document.getElementById('edit_packing');
                                            if (packingSelect) {
                                                packingSelect.value = orderData.PackingID;
                                            }
                                        }

                                        // Set payment term
                                        if (orderData.PaymentTermID) {
                                            const paymentTermSelect = document.getElementById('edit_payment_term');
                                            if (paymentTermSelect) {
                                                paymentTermSelect.value = orderData.PaymentTermID;
                                            }
                                        }

                                        // Set delivery term
                                        if (orderData.DeliveryTermID) {
                                            const deliveryTermSelect = document.getElementById('edit_delivery_term');
                                            if (deliveryTermSelect) {
                                                deliveryTermSelect.value = orderData.DeliveryTermID;
                                                // We'll trigger the change event after marketspace is set
                                                // to ensure proper field visibility
                                            }
                                        }

                                        // Set country
                                        if (orderData.CountryID) {
                                            const countrySelect = document.getElementById('edit_country');
                                            if (countrySelect) {
                                                countrySelect.value = orderData.CountryID;
                                            }
                                        }

                                        // Set province if valid
                                        if (orderData.ProvinceID && orderData.ProvinceID.Valid) {
                                            const provinceSelect = document.getElementById('edit_province');
                                            if (provinceSelect) {
                                                provinceSelect.value = orderData.ProvinceID.Int64;
                                                // Trigger change event to update dependent fields
                                                provinceSelect.dispatchEvent(new Event('change'));
                                            }
                                        }

                                        // Set district if valid
                                        if (orderData.DistrictID && orderData.DistrictID.Valid) {
                                            const districtSelect = document.getElementById('edit_district');
                                            if (districtSelect) {
                                                // Wait a bit for the district options to load
                                                setTimeout(() => {
                                                    districtSelect.value = orderData.DistrictID.Int64;
                                                    // Trigger change event to update dependent fields
                                                    districtSelect.dispatchEvent(new Event('change'));
                                                }, 500);
                                            }
                                        }

                                        // Set subdistrict if valid
                                        if (orderData.SubdistrictID && orderData.SubdistrictID.Valid) {
                                            const subdistrictSelect = document.getElementById('edit_subdistrict');
                                            if (subdistrictSelect) {
                                                // Wait a bit for the subdistrict options to load
                                                setTimeout(() => {
                                                    subdistrictSelect.value = orderData.SubdistrictID.Int64;
                                                }, 1000);
                                            }
                                        }

                                        // Set port of loading if valid
                                        if (orderData.PortOfLoadingID && orderData.PortOfLoadingID.Valid) {
                                            const portLoadingSelect = document.getElementById('edit_port_loading');
                                            if (portLoadingSelect) {
                                                portLoadingSelect.value = orderData.PortOfLoadingID.Int64;
                                            }
                                        }

                                        // Set port of discharge if valid
                                        if (orderData.PortOfDischargeID && orderData.PortOfDischargeID.Valid) {
                                            const portDischargeSelect = document.getElementById('edit_port_discharge');
                                            if (portDischargeSelect) {
                                                portDischargeSelect.value = orderData.PortOfDischargeID.Int64;
                                            }
                                        }

                                        // Set delivery dates
                                        if (orderData.FirstDeliveryDate) {
                                            document.getElementById('edit_first_delivery_date').value =
                                                new Date(orderData.FirstDeliveryDate).toISOString().split('T')[0];
                                        }
                                        if (orderData.LastDeliveryDate) {
                                            document.getElementById('edit_last_delivery_date').value =
                                                new Date(orderData.LastDeliveryDate).toISOString().split('T')[0];
                                        }

                                        // Use our centralized function to handle field visibility
                                        // Pass true to preserve field values when loading existing data
                                        handleEditMarketspaceChange(true);

                                        // Now that fields are properly displayed, trigger delivery term change
                                        // to ensure proper field visibility based on delivery term
                                        const deliveryTermSelect = document.getElementById('edit_delivery_term');
                                        if (deliveryTermSelect) {
                                            deliveryTermSelect.dispatchEvent(new Event('change'));
                                        }

                                        // Log the final state of important fields
                                        console.log("Final field state after all setup:");
                                        const provinceSelect = document.getElementById('edit_province');
                                        const districtSelect = document.getElementById('edit_district');
                                        const subdistrictSelect = document.getElementById('edit_subdistrict');
                                        const polSelect = document.getElementById('edit_port_loading');
                                        const podSelect = document.getElementById('edit_port_discharge');
                                        const locationFields = document.getElementById('edit-location-fields');
                                        const portFields = document.getElementById('edit-port-fields');

                                        console.log(`Province: ${provinceSelect ? provinceSelect.value : 'not found'}`);
                                        console.log(`District: ${districtSelect ? districtSelect.value : 'not found'}`);
                                        console.log(`Subdistrict: ${subdistrictSelect ? subdistrictSelect.value : 'not found'}`);
                                        console.log(`Port of Loading: ${polSelect ? polSelect.value : 'not found'}`);
                                        console.log(`Port of Discharge: ${podSelect ? podSelect.value : 'not found'}`);
                                        console.log(`Location fields display: ${locationFields ? locationFields.style.display : 'not found'}`);
                                        console.log(`Port fields display: ${portFields ? portFields.style.display : 'not found'}`);
                                    }, 500);
                                });
                        })
                        .catch(error => {
                            console.error("Error fetching order details:", error);
                            // Show a simplified form if we can't fetch all details
                            document.getElementById('full-edit-fields').style.display = 'none';

                            // Hide loading indicator and show error message
                            hideLoadingIndicator();
                            const errorMsg = window.currentLang === "th" ?
                                "ไม่สามารถโหลดข้อมูลคำสั่งได้ กรุณาลองใหม่อีกครั้ง" :
                                "Could not load order details. Please try again.";
                            showErrorMessage(errorMsg);
                        });
                } else {
                    // For other statuses, hide both
                    document.getElementById('quantity_restriction').style.display = 'none';
                    document.getElementById('full-edit-fields').style.display = 'none';

                    // Hide loading indicator since we're done
                    hideLoadingIndicator();
                }

                // Log the state of dropdowns after everything is loaded
                setTimeout(() => {
                    logDropdownState("Final state after all loading is complete");

                    // Hide loading indicator after everything is loaded
                    hideLoadingIndicator();
                }, 2000);
            } catch (error) {
                console.error("Error in openEditModal:", error);

                // Hide loading indicator and show error message
                hideLoadingIndicator();
                const errorMsg = window.currentLang === "th" ?
                    "เกิดข้อผิดพลาดในการโหลดข้อมูล กรุณาลองใหม่อีกครั้ง" :
                    "An error occurred while loading data. Please try again.";
                showErrorMessage(errorMsg);
            }
        }



        // Debug function to log the state of important dropdowns
        function logDropdownState(message) {
            const submarketSelect = document.getElementById('edit_submarket');
            const productSelect = document.getElementById('edit_product');
            const qualitySelect = document.getElementById('edit_quality');

            console.log(`${message}:`);
            console.log(`  Submarket: ${submarketSelect ? submarketSelect.value : 'not found'}`);
            console.log(`  Product: ${productSelect ? productSelect.value : 'not found'}`);
            console.log(`  Quality: ${qualitySelect ? qualitySelect.value : 'not found'}`);

            // Log options for debugging
            if (submarketSelect) {
                const submarketOptions = Array.from(submarketSelect.options).map(opt => ({value: opt.value, text: opt.text}));
                console.log(`  Submarket options: ${JSON.stringify(submarketOptions)}`);

                // Check if submarket value is lost and restore it if needed
                const storedSubmarketId = submarketSelect.getAttribute('data-selected-submarket-id');
                if (storedSubmarketId && !submarketSelect.value && submarketOptions.some(opt => opt.value === storedSubmarketId)) {
                    console.log(`Restoring submarket value to: ${storedSubmarketId}`);
                    submarketSelect.value = storedSubmarketId;
                    // No need for visual styling
                }
            }

            if (qualitySelect) {
                const qualityOptions = Array.from(qualitySelect.options).map(opt => ({value: opt.value, text: opt.text}));
                console.log(`  Quality options: ${JSON.stringify(qualityOptions)}`);

                // Check if quality value is lost and restore it if needed
                const storedQualityId = qualitySelect.getAttribute('data-selected-quality-id');
                if (storedQualityId && !qualitySelect.value && qualityOptions.some(opt => opt.value === storedQualityId)) {
                    console.log(`Restoring quality value to: ${storedQualityId}`);
                    qualitySelect.value = storedQualityId;
                    // No need for visual styling
                }
            }
        }

        // Preload all dropdown options when the page loads
        function preloadDropdownOptions() {
            console.log("Preloading dropdown options...");

            // Get current language and store it globally
            const currentLang = "{{ .Lang }}";
            window.currentLang = currentLang;
            const isThai = currentLang === "th";

            // Initialize dropdown cache if not already done
            if (!window.dropdownCache) {
                window.dropdownCache = {
                    submarkets: {},
                    products: {},
                    qualities: {},
                    payment: {},
                    delivery: {}
                };
            }

            // Reference to the cache for easier access
            const dropdownCache = window.dropdownCache;

            // Use Promise.all to batch API requests and improve performance
            const preloadPromises = [];

            // Function to preload qualities for a product
            const preloadQualitiesForProduct = (productId) => {
                if (!productId) return;

                // Skip if already cached
                if (dropdownCache.qualities[productId] && dropdownCache.qualities[productId].length > 0) {
                    console.log(`Qualities for product ${productId} already cached`);
                    return;
                }

                preloadPromises.push(
                    fetch(`/qualities?productID=${productId}&lang=${currentLang}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`Failed to fetch qualities: ${response.status}`);
                            }
                            return response.text();
                        })
                        .then(html => {
                            // Parse HTML to extract quality data
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = html;
                            const options = Array.from(tempDiv.querySelectorAll('option'));

                            // Store in cache
                            dropdownCache.qualities[productId] = options.map(opt => ({
                                ID: opt.value,
                                StandardID: opt.textContent.split('-')[0].trim(),
                                GradeID: opt.textContent.split('-')[1]?.trim() || '',
                                EnName: opt.textContent,
                                ThName: opt.textContent // Will be correct based on language of request
                            }));

                            console.log(`Preloaded ${options.length} qualities for product ID ${productId}`);
                        })
                        .catch(error => console.error(`Error preloading qualities for product ${productId}:`, error))
                );
            };

            // Fetch all markets (already loaded in the template, but we'll cache them for consistency)
            const marketSelect = document.getElementById('edit_market');
            if (marketSelect) {
                Array.from(marketSelect.options).forEach(option => {
                    if (option.value) {
                        // Cache market options
                        const marketId = option.value;
                        const marketName = option.textContent;

                        // Preload submarkets for this market
                        preloadPromises.push(
                            fetch(`/submarkets?marketID=${marketId}&lang=${currentLang}`)
                                .then(response => response.text())
                                .then(html => {
                                    // Store the HTML directly in the cache
                                    if (!dropdownCache.submarkets[marketId]) {
                                        dropdownCache.submarkets[marketId] = html;
                                    }
                                })
                                .catch(error => console.error(`Error preloading submarkets for market ${marketId}:`, error))
                        );
                    }
                });
            }

            // Preload payment terms for all marketspaces
            [1, 2].forEach(marketspaceID => {
                preloadPromises.push(
                    fetch(`/payment?marketspaceID=${marketspaceID}&lang=${currentLang}`)
                        .then(response => response.text())
                        .then(html => {
                            dropdownCache.payment[marketspaceID] = html;
                        })
                        .catch(error => console.error(`Error preloading payment terms for marketspace ${marketspaceID}:`, error))
                );
            });

            // Preload delivery terms for all marketspaces
            [1, 2].forEach(marketspaceID => {
                preloadPromises.push(
                    fetch(`/delivery?marketspaceID=${marketspaceID}&lang=${currentLang}`)
                        .then(response => response.text())
                        .then(html => {
                            dropdownCache.delivery[marketspaceID] = html;
                        })
                        .catch(error => console.error(`Error preloading delivery terms for marketspace ${marketspaceID}:`, error))
                );
            });

            // Preload countries data
            preloadPromises.push(
                fetch(`/submarkets?type=countries&lang=${currentLang}`)
                    .then(response => response.json())
                    .then(data => {
                        console.log("Countries data preloaded successfully");
                        // Store in global variable for immediate access
                        window.countriesData = data;

                        // Also populate the dropdown immediately
                        const countrySelect = document.getElementById('edit_country');
                        if (countrySelect) {
                            countrySelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกประเทศ' : 'select a country') + '--</option>';
                            data.forEach(country => {
                                const option = document.createElement('option');
                                option.value = country.ID;
                                option.textContent = isThai ? country.ThName : country.EnName;
                                countrySelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => console.error('Error preloading countries:', error))
            );

            // Preload products and qualities for common products
            // This helps ensure quality data is available when editing orders
            fetch('/products?lang=${currentLang}')
                .then(response => response.json())
                .then(data => {
                    console.log(`Preloading qualities for ${data.length} products`);
                    // For each product, preload its qualities
                    data.forEach(product => {
                        if (product.ID) {
                            preloadQualitiesForProduct(product.ID);
                        }
                    });
                })
                .catch(error => console.error('Error preloading products for quality preloading:', error));

            // Execute all preload promises
            Promise.all(preloadPromises)
                .then(() => {
                    console.log("All dropdown options preloaded successfully");
                })
                .catch(error => {
                    console.error("Error during preloading dropdown options:", error);
                });
        }

        // Fetch dropdown options for all select fields
        function fetchDropdownOptions() {
            console.log("Fetching dropdown options...");

            // Get current language and store it globally
            const currentLang = "{{ .Lang }}";
            window.currentLang = currentLang;
            const isThai = currentLang === "th";

            // Marketspaces already loaded in the template

            // Markets
            fetch('/submarkets')
                .then(response => response.json())
                .then(data => {
                    const marketSelect = document.getElementById('edit_market');
                    if (marketSelect) {
                        marketSelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกตลาด' : 'select a market') + '--</option>';
                        data.forEach(market => {
                            const option = document.createElement('option');
                            option.value = market.ID;
                            option.textContent = isThai ? market.ThName : market.EnName;
                            marketSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching markets:', error));

            // Order types
            fetch('/submarkets?type=order_types')
                .then(response => response.json())
                .then(data => {
                    const orderTypeSelect = document.getElementById('edit_order_type');
                    if (orderTypeSelect) {
                        orderTypeSelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกประเภทคำสั่ง' : 'select buy/sell order') + '--</option>';
                        data.forEach(type => {
                            const option = document.createElement('option');
                            option.value = type.ID;
                            option.textContent = isThai ? type.ThName : type.EnName;
                            orderTypeSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching order types:', error));

            // Matching types
            fetch('/submarkets?type=matching_types')
                .then(response => response.json())
                .then(data => {
                    const matchingTypeSelect = document.getElementById('edit_matching_type');
                    if (matchingTypeSelect) {
                        matchingTypeSelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกประเภทการจับคู่' : 'select a matching algorithm') + '--</option>';
                        data.forEach(type => {
                            const option = document.createElement('option');
                            option.value = type.ID;
                            option.textContent = isThai ? type.ThName : type.EnName;
                            matchingTypeSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching matching types:', error));

            // Contract types
            fetch('/submarkets?type=contract_types')
                .then(response => response.json())
                .then(data => {
                    const contractTypeSelect = document.getElementById('edit_contract_type');
                    if (contractTypeSelect) {
                        contractTypeSelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกประเภทสัญญา' : 'select a contract type') + '--</option>';
                        data.forEach(type => {
                            const option = document.createElement('option');
                            option.value = type.ID;
                            option.textContent = isThai ? type.ThName : type.EnName;
                            contractTypeSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching contract types:', error));

            // Products
            fetch('/products')
                .then(response => response.json())
                .then(data => {
                    const productSelect = document.getElementById('edit_product');
                    if (productSelect) {
                        productSelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกสินค้า' : 'select a product') + '--</option>';
                        data.forEach(product => {
                            const option = document.createElement('option');
                            option.value = product.ID;
                            option.textContent = isThai ? product.ThName : product.EnName;
                            productSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching products:', error));

            // Qualities - fetch all qualities for the specific product
            fetch(`/qualities?productID=${orderData.ProductID}&lang=${currentLang}`)
                .then(response => response.text())
                .then(html => {
                    const qualitySelect = document.getElementById('edit_quality');
                    if (qualitySelect) {
                        // Keep the first option (placeholder)
                        const firstOption = '<option value="">--' + (isThai ? 'เลือกคุณภาพ' : 'select a quality') + '--</option>';
                        qualitySelect.innerHTML = firstOption + html;

                        // Set the quality value directly
                        if (orderData.QualityID) {
                            console.log(`Setting quality value directly to: ${orderData.QualityID}`);
                            qualitySelect.value = orderData.QualityID;

                            // If the value wasn't set, try again after a short delay
                            setTimeout(() => {
                                if (qualitySelect.value !== orderData.QualityID.toString()) {
                                    console.log(`Retry setting quality value to: ${orderData.QualityID}`);
                                    qualitySelect.value = orderData.QualityID;
                                }
                            }, 200);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching qualities:', error);

                    // Fallback to generic qualities if product-specific fetch fails
                    fetch('/qualities')
                        .then(response => response.json())
                        .then(data => {
                            const qualitySelect = document.getElementById('edit_quality');
                            if (qualitySelect) {
                                qualitySelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกคุณภาพ' : 'select a quality') + '--</option>';
                                data.forEach(quality => {
                                    const option = document.createElement('option');
                                    option.value = quality.ID;
                                    option.textContent = quality.StandardID + ' - Grade: ' + quality.GradeID;
                                    qualitySelect.appendChild(option);
                                });

                                // Set the quality value
                                if (orderData.QualityID) {
                                    qualitySelect.value = orderData.QualityID;
                                }
                            }
                        })
                        .catch(error => console.error('Error fetching fallback qualities:', error));
                });

            // Currencies
            fetch('/submarkets?type=currencies')
                .then(response => response.json())
                .then(data => {
                    // Populate both currency selects (basic and full form)
                    const currencySelect = document.getElementById('edit_currency');
                    const currencySelectBasic = document.getElementById('edit_currency_basic');

                    if (currencySelect) {
                        currencySelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกสกุลเงิน' : 'select a currency') + '--</option>';
                        data.forEach(currency => {
                            const option = document.createElement('option');
                            option.value = currency.ID;
                            option.textContent = currency.Code;
                            currencySelect.appendChild(option);
                        });
                    }

                    if (currencySelectBasic) {
                        currencySelectBasic.innerHTML = '<option value="">--' + (isThai ? 'เลือกสกุลเงิน' : 'select a currency') + '--</option>';
                        data.forEach(currency => {
                            const option = document.createElement('option');
                            option.value = currency.ID;
                            option.textContent = currency.Code;
                            currencySelectBasic.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching currencies:', error));

            // UOMs
            fetch('/submarkets?type=uoms')
                .then(response => response.json())
                .then(data => {
                    const uomSelect = document.getElementById('edit_uom');
                    if (uomSelect) {
                        uomSelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกหน่วย' : 'select a unit') + '--</option>';
                        data.forEach(uom => {
                            const option = document.createElement('option');
                            option.value = uom.ID;
                            option.textContent = isThai ? uom.ThName : uom.EnName;
                            uomSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching UOMs:', error));

            // Packings
            fetch('/submarkets?type=packings')
                .then(response => response.json())
                .then(data => {
                    const packingSelect = document.getElementById('edit_packing');
                    if (packingSelect) {
                        packingSelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกบรรจุภัณฑ์' : 'select a packing') + '--</option>';
                        data.forEach(packing => {
                            const option = document.createElement('option');
                            option.value = packing.ID;
                            option.textContent = isThai ? packing.ThName : packing.EnName;
                            packingSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching packings:', error));

            // Provinces
            fetch(`/api/provinces?countryId=221&lang=${currentLang}`)
                .then(response => response.text())
                .then(html => {
                    const provinceSelect = document.getElementById('edit_province');
                    if (provinceSelect) {
                        // Keep the first option (placeholder)
                        const firstOption = '<option value="">--' + (isThai ? 'เลือกจังหวัด' : 'select a province/state') + '--</option>';
                        provinceSelect.innerHTML = firstOption + html;
                    }
                })
                .catch(error => console.error('Error fetching provinces:', error));

            // Payment terms
            fetch('/payment')
                .then(response => response.json())
                .then(data => {
                    const paymentSelect = document.getElementById('edit_payment_term');
                    if (paymentSelect) {
                        paymentSelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกเงื่อนไขการชำระเงิน' : 'select a payment term') + '--</option>';
                        data.forEach(payment => {
                            const option = document.createElement('option');
                            option.value = payment.ID;
                            option.textContent = isThai ? payment.ThName : payment.EnName;
                            paymentSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching payment terms:', error));

            // Delivery terms
            fetch('/delivery')
                .then(response => response.json())
                .then(data => {
                    const deliverySelect = document.getElementById('edit_delivery_term');
                    if (deliverySelect) {
                        deliverySelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกเงื่อนไขการส่งมอบ' : 'select a delivery term') + '--</option>';
                        data.forEach(delivery => {
                            const option = document.createElement('option');
                            option.value = delivery.ID;
                            option.textContent = isThai ? delivery.ThName : delivery.EnName;
                            deliverySelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching delivery terms:', error));

            // Countries - use preloaded data if available
            if (window.countriesData) {
                console.log("Using preloaded countries data");
                const countrySelect = document.getElementById('edit_country');
                if (countrySelect) {
                    countrySelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกประเทศ' : 'select a country') + '--</option>';
                    window.countriesData.forEach(country => {
                        const option = document.createElement('option');
                        option.value = country.ID;
                        option.textContent = isThai ? country.ThName : country.EnName;
                        countrySelect.appendChild(option);
                    });
                }
            } else {
                // Fallback to fetch if preloaded data is not available
                fetch(`/submarkets?type=countries&lang=${currentLang}`)
                    .then(response => response.json())
                    .then(data => {
                        const countrySelect = document.getElementById('edit_country');
                        if (countrySelect) {
                            countrySelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกประเทศ' : 'select a country') + '--</option>';
                            data.forEach(country => {
                                const option = document.createElement('option');
                                option.value = country.ID;
                                option.textContent = isThai ? country.ThName : country.EnName;
                                countrySelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => console.error('Error fetching countries:', error));
            }

            // Ports of Loading
            fetch(`/submarkets?type=ports&lang=${currentLang}`)
                .then(response => response.json())
                .then(data => {
                    const polSelect = document.getElementById('edit_port_loading');
                    if (polSelect) {
                        polSelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกท่าเรือต้นทาง' : 'select a port of loading') + '--</option>';
                        data.forEach(port => {
                            const option = document.createElement('option');
                            option.value = port.ID;
                            option.textContent = isThai ? port.ThName : port.EnName;
                            polSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching ports of loading:', error));

            // Ports of Discharge
            fetch(`/submarkets?type=ports&lang=${currentLang}`)
                .then(response => response.json())
                .then(data => {
                    const podSelect = document.getElementById('edit_port_discharge');
                    if (podSelect) {
                        podSelect.innerHTML = '<option value="">--' + (isThai ? 'เลือกท่าเรือปลายทาง' : 'select a port of discharge') + '--</option>';
                        data.forEach(port => {
                            const option = document.createElement('option');
                            option.value = port.ID;
                            option.textContent = isThai ? port.ThName : port.EnName;
                            podSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error fetching ports of discharge:', error));
        }


        // The fetchDropdownOptions function is called when the edit modal is opened

        // Function to update quality options based on selected product
        function updateQualityOptions(productId) {
            if (!productId) return;

            console.log(`Updating quality options for product ID: ${productId}`);
            const qualitySelect = document.getElementById('edit_quality');
            if (!qualitySelect) return;

            // Get current language
            const currentLang = window.currentLang || "{{ .Lang }}";
            const isThai = currentLang === "th";

            // Keep the first option (placeholder)
            const firstOption = qualitySelect.options[0];

            // Get the quality ID from the order data
            const qualityId = window.currentOrderData ? window.currentOrderData.QualityID : null;
            const storedQualityId = qualitySelect.getAttribute('data-quality-id') || qualityId;

            console.log(`Quality ID from order data: ${qualityId}`);
            console.log(`Stored quality ID: ${storedQualityId}`);

            // Fetch qualities for this product
            fetch(`/qualities?productID=${productId}&lang=${currentLang}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Failed to fetch qualities: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    // Update the dropdown
                    qualitySelect.innerHTML = firstOption.outerHTML + html;
                    console.log(`Quality options loaded for product ${productId}`);

                    // Set the quality value from the order data
                    if (qualityId) {
                        console.log(`Setting quality value to: ${qualityId}`);
                        qualitySelect.value = qualityId;

                        // Store the quality ID in a data attribute for reference
                        qualitySelect.setAttribute('data-quality-id', qualityId);

                        // If the value wasn't set, try again after a short delay
                        setTimeout(() => {
                            if (qualitySelect.value !== qualityId.toString()) {
                                console.log(`Retry setting quality value to: ${qualityId}`);
                                qualitySelect.value = qualityId;

                                // If still not set, try to add the option manually
                                if (qualitySelect.value !== qualityId.toString()) {
                                    // Get the appropriate quality name based on language
                                    let qualityName = window.currentOrderData.QualityName;
                                    if (isThai && window.currentOrderData.QualityNameTh) {
                                        qualityName = window.currentOrderData.QualityNameTh;
                                    }

                                    if (qualityName) {
                                        console.log(`Adding quality option manually: ${qualityId} - ${qualityName}`);
                                        const option = document.createElement('option');
                                        option.value = qualityId;
                                        option.text = qualityName;
                                        option.selected = true;
                                        qualitySelect.appendChild(option);
                                    }
                                }
                            }
                        }, 200);
                    }
                })
                .catch(error => {
                    console.error('Error fetching qualities:', error);

                    // If API call fails, try to add the quality option manually
                    if (qualityId && window.currentOrderData) {
                        let qualityName = window.currentOrderData.QualityName;
                        if (isThai && window.currentOrderData.QualityNameTh) {
                            qualityName = window.currentOrderData.QualityNameTh;
                        }

                        if (qualityName) {
                            console.log(`Adding quality option manually after API error: ${qualityId} - ${qualityName}`);
                            const option = document.createElement('option');
                            option.value = qualityId;
                            option.text = qualityName;
                            qualitySelect.appendChild(option);
                            qualitySelect.value = qualityId;
                        }
                    }
                });
        }

        // Event listener for product change to ensure productID is not empty
        const productSelect = document.getElementById('edit_product');
        if (productSelect) {
            productSelect.addEventListener('change', function() {
                const productId = this.value;
                console.log("Product changed to:", productId);

                // Log dropdown state after product change
                logDropdownState("After product change event");

                // If product ID is empty, clear the quality dropdown
                if (!productId) {
                    const qualitySelect = document.getElementById('edit_quality');
                    if (qualitySelect) {
                        // Keep only the first option (placeholder)
                        const firstOption = qualitySelect.options[0];
                        qualitySelect.innerHTML = firstOption.outerHTML;
                    }
                } else {
                    // Update quality options based on selected product
                    updateQualityOptions(productId);
                }
            });
        }

        // Event listeners for dependent dropdowns
        if (editMarketspaceSelect) {
            editMarketspaceSelect.addEventListener('change', function() {
                console.log(`Marketspace select changed to: ${this.value}`);
                // Call the centralized function to handle marketspace change
                // When user changes marketspace, we want to reset fields for the new selection
                handleEditMarketspaceChange(false);
            });
        }

        // Market change event to load submarkets
        const marketSelect = document.getElementById('edit_market');
        if (marketSelect) {
            marketSelect.addEventListener('change', function() {
                const marketId = this.value;
                if (marketId) {
                    // Get current language
                    const currentLang = "{{ .Lang }}";
                    const isThai = currentLang === "th";

                    console.log("Market changed to:", marketId);

                    const submarketSelect = document.getElementById('edit_submarket');
                    if (submarketSelect) {
                        // Keep the first option (placeholder)
                        const firstOption = '<option value="">--' + (isThai ? 'เลือกตลาดย่อย' : 'select a submarket') + '--</option>';

                        // Check if we have cached submarkets for this market
                        if (dropdownCache.submarkets[marketId]) {
                            console.log("Using cached submarkets for market ID:", marketId);

                            // Use the cached HTML directly
                            submarketSelect.innerHTML = firstOption + dropdownCache.submarkets[marketId];
                            console.log("Submarket options loaded from cache:", Array.from(submarketSelect.options).map(opt => ({ value: opt.value, text: opt.text })));
                        } else {
                            // Fallback to fetch if not in cache
                            console.log("Submarkets not in cache, fetching from server");
                            fetch(`/submarkets?marketID=${marketId}&lang=${currentLang}`)
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`Failed to fetch submarkets: ${response.status}`);
                                    }
                                    return response.text();
                                })
                                .then(html => {
                                    console.log("Submarkets HTML received");
                                    submarketSelect.innerHTML = firstOption + html;
                                    console.log("Submarket options after loading:", Array.from(submarketSelect.options).map(opt => ({ value: opt.value, text: opt.text })));
                                })
                                .catch(error => console.error('Error fetching submarkets:', error));
                        }
                    }
                }
            });
        }

        if (editDeliveryTermSelect) {
            editDeliveryTermSelect.addEventListener('change', function() {
                console.log(`Delivery Term select changed to: ${this.value}`);
                // Call the centralized function to handle marketspace change
                // This will also handle delivery term changes
                // When user changes delivery term, we want to reset fields for the new selection
                handleEditMarketspaceChange(false);
            });
        }

        // Province change event
        const provinceSelect = document.getElementById('edit_province');
        if (provinceSelect) {
            provinceSelect.addEventListener('change', function() {
                const provinceId = this.value;
                if (provinceId) {
                    // Get current language
                    const currentLang = "{{ .Lang }}";
                    const isThai = currentLang === "th";

                    // Fetch districts for the selected province
                    fetch(`/district?province_id=${provinceId}&lang=${window.currentLang || currentLang}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`Failed to fetch districts: ${response.status}`);
                            }
                            return response.text();
                        })
                        .then(html => {
                            const districtSelect = document.getElementById('edit_district');
                            // Keep the first option (placeholder)
                            const firstOption = '<option value="">--' + (isThai ? 'เลือกอำเภอ' : 'select a district/city') + '--</option>';
                            districtSelect.innerHTML = firstOption + html;
                        })
                        .catch(error => console.error('Error fetching districts:', error));
                }
            });
        }

        // District change event
        const districtSelect = document.getElementById('edit_district');
        if (districtSelect) {
            districtSelect.addEventListener('change', function() {
                const districtId = this.value;
                if (districtId) {
                    // Get current language
                    const currentLang = "{{ .Lang }}";
                    const isThai = currentLang === "th";

                    // Fetch subdistricts for the selected district
                    fetch(`/subdistrict?district_id=${districtId}&lang=${window.currentLang || currentLang}`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`Failed to fetch subdistricts: ${response.status}`);
                            }
                            return response.text();
                        })
                        .then(html => {
                            const subdistrictSelect = document.getElementById('edit_subdistrict');
                            // Keep the first option (placeholder)
                            const firstOption = '<option value="">--' + (isThai ? 'เลือกตำบล' : 'select a subdistrict') + '--</option>';
                            subdistrictSelect.innerHTML = firstOption + html;
                        })
                        .catch(error => console.error('Error fetching subdistricts:', error));
                }
            });
        }


        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
            document.getElementById('quantity_error').style.display = 'none';
        }

        // Delete modal functions
        function openDeleteModal(orderId) {
            document.getElementById('delete_order_id').value = orderId;
            document.getElementById('deleteModal').style.display = 'block';
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Custom alert function to match order submission form
        function customAlert(message) {
            // Create a custom modal dialog
            const modal = document.createElement('div');
            modal.className = 'custom-alert';

            // Create the modal content
            const content = document.createElement('div');
            content.className = 'alert-content';

            // Create the header with BiomassX branding
            const header = document.createElement('div');
            header.className = 'alert-header';
            header.textContent = 'BiomassX';

            // Create the message area
            const messageArea = document.createElement('div');
            messageArea.className = 'alert-message';
            messageArea.textContent = message;

            // Create the OK button
            const button = document.createElement('button');
            button.className = 'alert-button';
            button.textContent = 'OK';
            button.onclick = function() {
                document.body.removeChild(modal);
            };

            // Assemble the modal
            content.appendChild(header);
            content.appendChild(messageArea);
            content.appendChild(button);
            modal.appendChild(content);

            // Add to the document
            document.body.appendChild(modal);
        }

        // Price validation and synchronization for basic form
        document.getElementById('edit_price').addEventListener('input', function() {
            // Sync with full form field
            document.getElementById('edit_price_full').value = this.value;
        });

        // Price validation and synchronization for full form
        document.getElementById('edit_price_full').addEventListener('input', function() {
            // Sync with basic form field
            document.getElementById('edit_price').value = this.value;
        });

        // Quantity validation for basic form
        document.getElementById('edit_quantity').addEventListener('input', function() {
            const quantity = parseFloat(this.value);
            const maxQuantity = parseFloat(document.getElementById('max_quantity').value);
            const statusId = parseInt(document.getElementById('order_status_id').value);
            const error = document.getElementById('quantity_error');

            // Only apply max quantity validation for partially matched orders (status 14)
            if (statusId === 14) {
                if (quantity > maxQuantity) {
                    error.style.display = 'block';
                    document.querySelector('#editForm button[type="submit"]').disabled = true;
                    // Reset the value to the maximum allowed
                    this.value = maxQuantity;
                } else {
                    error.style.display = 'none';
                    document.querySelector('#editForm button[type="submit"]').disabled = false;
                }
            } else {
                error.style.display = 'none';
                document.querySelector('#editForm button[type="submit"]').disabled = false;
            }

            // Sync with full form field
            document.getElementById('edit_quantity_full').value = this.value;
        });

        // Quantity validation for full form
        document.getElementById('edit_quantity_full').addEventListener('input', function() {
            const quantity = parseFloat(this.value);
            const maxQuantity = parseFloat(document.getElementById('max_quantity').value);
            const statusId = parseInt(document.getElementById('order_status_id').value);
            const error = document.getElementById('quantity_error');

            // Only apply max quantity validation for partially matched orders (status 14)
            if (statusId === 14) {
                if (quantity > maxQuantity) {
                    error.style.display = 'block';
                    document.querySelector('#editForm button[type="submit"]').disabled = true;
                    // Reset the value to the maximum allowed
                    this.value = maxQuantity;
                } else {
                    error.style.display = 'none';
                    document.querySelector('#editForm button[type="submit"]').disabled = false;
                }
            } else {
                error.style.display = 'none';
                document.querySelector('#editForm button[type="submit"]').disabled = false;
            }

            // Sync with basic form field
            document.getElementById('edit_quantity').value = this.value;
        });

        // Generate confirmation dialog before submitting the edit form
        function generateEditConfirmation() {
            const form = document.getElementById('editForm');
            const statusId = parseInt(document.getElementById('order_status_id').value);

            // Validate form first
            const invalidFields = validateEditFormFields();
            if (invalidFields.length > 0) {
                customAlert('Please fill in the following required fields:\n\n' + invalidFields.join('\n'));
                return false;
            }

            try {
                // Create confirmation content based on form values
                let confirmationDetails = `<div class="modal-header">{{if eq .Lang "th"}}ยืนยันการแก้ไข{{else}}Confirm Edit{{end}}</div><ul>`;

                // Always include basic fields
                const price = document.getElementById('edit_price').value;
                const quantity = document.getElementById('edit_quantity').value;
                const remark = document.getElementById('edit_remark').value || '';
                const statusId = parseInt(document.getElementById('order_status_id').value);

                confirmationDetails += `
                    <li><b>{{if eq .Lang "th"}}ราคาต่อหน่วย{{else}}Price per Unit{{end}}:</b> ${price}</li>
                    <li><b>{{if eq .Lang "th"}}ปริมาณ{{else}}Quantity{{end}}:</b> ${quantity}</li>
                `;

                // For partially matched orders, include delivery dates
                if (statusId === 14) {
                    const firstDeliveryDate = document.getElementById('edit_first_delivery_date_basic').value || '';
                    const lastDeliveryDate = document.getElementById('edit_last_delivery_date_basic').value || '';

                    confirmationDetails += `
                        <li><b>{{if eq .Lang "th"}}วันที่ส่งมอบแรก{{else}}First Delivery Date{{end}}:</b> ${firstDeliveryDate}</li>
                        <li><b>{{if eq .Lang "th"}}วันที่ส่งมอบสุดท้าย{{else}}Last Delivery Date{{end}}:</b> ${lastDeliveryDate}</li>
                    `;
                }

                // For available orders, include all fields
                if (statusId === 13 && document.getElementById('full-edit-fields').style.display === 'block') {
                    const marketspace = document.getElementById('edit_marketspace').options[document.getElementById('edit_marketspace').selectedIndex]?.text || '';
                    const market = document.getElementById('edit_market').options[document.getElementById('edit_market').selectedIndex]?.text || '';
                    const submarket = document.getElementById('edit_submarket').options[document.getElementById('edit_submarket').selectedIndex]?.text || '';
                    const orderType = document.getElementById('edit_order_type').options[document.getElementById('edit_order_type').selectedIndex]?.text || '';
                    const matchingType = document.getElementById('edit_matching_type').options[document.getElementById('edit_matching_type').selectedIndex]?.text || '';
                    const contractType = document.getElementById('edit_contract_type').options[document.getElementById('edit_contract_type').selectedIndex]?.text || '';
                    const product = document.getElementById('edit_product').options[document.getElementById('edit_product').selectedIndex]?.text || '';
                    const quality = document.getElementById('edit_quality').options[document.getElementById('edit_quality').selectedIndex]?.text || '';
                    const currency = document.getElementById('edit_currency').options[document.getElementById('edit_currency').selectedIndex]?.text || '';
                    const uom = document.getElementById('edit_uom').options[document.getElementById('edit_uom').selectedIndex]?.text || '';
                    const packing = document.getElementById('edit_packing').options[document.getElementById('edit_packing').selectedIndex]?.text || '';
                    const paymentTerm = document.getElementById('edit_payment_term').options[document.getElementById('edit_payment_term').selectedIndex]?.text || '';
                    const deliveryTerm = document.getElementById('edit_delivery_term').options[document.getElementById('edit_delivery_term').selectedIndex]?.text || '';
                    const country = document.getElementById('edit_country').options[document.getElementById('edit_country').selectedIndex]?.text || '';
                    const firstDeliveryDate = document.getElementById('edit_first_delivery_date').value || '';
                    const lastDeliveryDate = document.getElementById('edit_last_delivery_date').value || '';

                    confirmationDetails += `
                        <li><b>{{if eq .Lang "th"}}ตลาด{{else}}Marketspace{{end}}:</b> ${marketspace}</li>
                        <li><b>{{if eq .Lang "th"}}ตลาด{{else}}Market{{end}}:</b> ${market}</li>
                        <li><b>{{if eq .Lang "th"}}ตลาดย่อย{{else}}Submarket{{end}}:</b> ${submarket}</li>
                        <li><b>{{if eq .Lang "th"}}ประเภทคำสั่งซื้อขาย{{else}}Order Type{{end}}:</b> ${orderType}</li>
                        <li><b>{{if eq .Lang "th"}}ประเภทการจับคู่{{else}}Matching Type{{end}}:</b> ${matchingType}</li>
                        <li><b>{{if eq .Lang "th"}}ประเภทสัญญา{{else}}Contract Type{{end}}:</b> ${contractType}</li>
                        <li><b>{{if eq .Lang "th"}}สินค้า{{else}}Product{{end}}:</b> ${product}</li>
                        <li><b>{{if eq .Lang "th"}}คุณภาพ{{else}}Quality{{end}}:</b> ${quality}</li>
                        <li><b>{{if eq .Lang "th"}}สกุลเงิน{{else}}Currency{{end}}:</b> ${currency}</li>
                        <li><b>{{if eq .Lang "th"}}หน่วย{{else}}Unit{{end}}:</b> ${uom}</li>
                        <li><b>{{if eq .Lang "th"}}บรรจุภัณฑ์{{else}}Packing{{end}}:</b> ${packing}</li>
                        <li><b>{{if eq .Lang "th"}}เงื่อนไขการชำระเงิน{{else}}Payment Term{{end}}:</b> ${paymentTerm}</li>
                        <li><b>{{if eq .Lang "th"}}เงื่อนไขการส่งมอบ{{else}}Delivery Term{{end}}:</b> ${deliveryTerm}</li>
                        <li><b>{{if eq .Lang "th"}}ประเทศ{{else}}Country{{end}}:</b> ${country}</li>
                    `;

                    // Add location or port fields based on marketspace and delivery term
                    const marketspaceValue = document.getElementById('edit_marketspace').value;
                    const deliveryTermValue = document.getElementById('edit_delivery_term').value;

                    if (marketspaceValue === '1' || (marketspaceValue === '2' && (deliveryTermValue === '5' || deliveryTermValue === '9'))) {
                        // Show location fields for local market or global market with EXW/FCA
                        const province = document.getElementById('edit_province').options[document.getElementById('edit_province').selectedIndex]?.text || '';
                        const district = document.getElementById('edit_district').options[document.getElementById('edit_district').selectedIndex]?.text || '';
                        const subdistrict = document.getElementById('edit_subdistrict').options[document.getElementById('edit_subdistrict').selectedIndex]?.text || '';

                        confirmationDetails += `
                            <li><b>{{if eq .Lang "th"}}จังหวัด{{else}}Province{{end}}:</b> ${province}</li>
                            <li><b>{{if eq .Lang "th"}}อำเภอ{{else}}District{{end}}:</b> ${district}</li>
                            <li><b>{{if eq .Lang "th"}}ตำบล{{else}}Subdistrict{{end}}:</b> ${subdistrict}</li>
                        `;
                    } else if (marketspaceValue === '2') {
                        // Show port fields for global market with other delivery terms
                        const portLoading = document.getElementById('edit_port_loading').options[document.getElementById('edit_port_loading').selectedIndex]?.text || '';
                        const portDischarge = document.getElementById('edit_port_discharge').options[document.getElementById('edit_port_discharge').selectedIndex]?.text || '';

                        if (portLoading) {
                            confirmationDetails += `<li><b>{{if eq .Lang "th"}}ท่าเรือต้นทาง{{else}}Port of Loading{{end}}:</b> ${portLoading}</li>`;
                        }
                        if (portDischarge) {
                            confirmationDetails += `<li><b>{{if eq .Lang "th"}}ท่าเรือปลายทาง{{else}}Port of Discharge{{end}}:</b> ${portDischarge}</li>`;
                        }
                    }

                    confirmationDetails += `
                        <li><b>{{if eq .Lang "th"}}วันที่ส่งมอบแรก{{else}}First Delivery Date{{end}}:</b> ${firstDeliveryDate}</li>
                        <li><b>{{if eq .Lang "th"}}วันที่ส่งมอบสุดท้าย{{else}}Last Delivery Date{{end}}:</b> ${lastDeliveryDate}</li>
                    `;
                }

                confirmationDetails += `<li><b>{{if eq .Lang "th"}}หมายเหตุ{{else}}Remark{{end}}:</b> ${remark}</li></ul>`;

                // Add buttons
                confirmationDetails += `
                <div class="modal-footer">
                    <button type="button" onclick="submitEditForm()">{{if eq .Lang "th"}}ยืนยันและบันทึก{{else}}Confirm and Save{{end}}</button>
                    <button type="button" onclick="closeConfirmationModal()">{{if eq .Lang "th"}}ยกเลิก{{else}}Cancel{{end}}</button>
                </div>`;

                // Create confirmation modal if it doesn't exist
                let confirmationModal = document.getElementById('confirmationModal');
                if (!confirmationModal) {
                    confirmationModal = document.createElement('div');
                    confirmationModal.id = 'confirmationModal';
                    document.body.appendChild(confirmationModal);
                }

                // Create modal content if it doesn't exist
                let modalContent = document.getElementById('modal-content');
                if (!modalContent) {
                    modalContent = document.createElement('div');
                    modalContent.id = 'modal-content';
                    confirmationModal.appendChild(modalContent);
                }

                // Set content and show modal
                modalContent.innerHTML = confirmationDetails;
                confirmationModal.style.display = 'block';

                return false; // Prevent form submission
            } catch (error) {
                console.error('Error generating confirmation:', error);
                return true; // Allow form submission if there's an error
            }
        }

        // Close confirmation modal
        function closeConfirmationModal() {
            const confirmationModal = document.getElementById('confirmationModal');
            if (confirmationModal) {
                confirmationModal.style.display = 'none';
            }
        }

        // Submit the edit form after confirmation
        function submitEditForm() {
            try {
                console.log("Starting submitEditForm function");
                closeConfirmationModal();

                // Create a FormData object
                const formData = new FormData();

                // Helper function to safely add form fields
                const addFieldIfExists = (fieldName, elementId) => {
                    const element = document.getElementById(elementId);
                    if (element && element.value !== undefined) {
                        formData.append(fieldName, element.value || '');
                        console.log(`Added field ${fieldName} with value ${element.value}`);
                    }
                };

                // Add common fields
                formData.append('action', 'edit');

                // Always use the current language from the page
                formData.append('lang', "{{ .Lang }}");

                addFieldIfExists('order_id', 'edit_order_id');
                addFieldIfExists('status_id', 'order_status_id');
                addFieldIfExists('remark', 'edit_remark');

                // Get order status ID with fallback
                let orderStatusId = 13; // Default to available order
                const statusElement = document.getElementById('order_status_id');
                if (statusElement && statusElement.value) {
                    orderStatusId = parseInt(statusElement.value);
                }

                if (orderStatusId === 13) {
                    // For available orders, use the full form values with null checks
                    addFieldIfExists('price', 'edit_price_full');
                    addFieldIfExists('quantity', 'edit_quantity_full');
                    addFieldIfExists('first_delivery_date', 'edit_first_delivery_date');
                    addFieldIfExists('last_delivery_date', 'edit_last_delivery_date');
                    // Add other fields from the full form

                // Add all fields with camelCase naming as expected by the server
                addFieldIfExists('marketspaceID', 'edit_marketspace');
                addFieldIfExists('marketID', 'edit_market');
                addFieldIfExists('submarketID', 'edit_submarket');
                addFieldIfExists('orderTypeID', 'edit_order_type');
                addFieldIfExists('matchingTypeID', 'edit_matching_type');
                addFieldIfExists('contractTypeID', 'edit_contract_type');
                addFieldIfExists('productID', 'edit_product');
                addFieldIfExists('quality_id', 'edit_quality');
                addFieldIfExists('currency_id', 'edit_currency');
                addFieldIfExists('uom_id', 'edit_uom');
                addFieldIfExists('packing_id', 'edit_packing');
                addFieldIfExists('payment_term_id', 'edit_payment_term');
                addFieldIfExists('delivery_term_id', 'edit_delivery_term');
                addFieldIfExists('country_id', 'edit_country');
                addFieldIfExists('province_id', 'edit_province');
                addFieldIfExists('district_id', 'edit_district');
                addFieldIfExists('subdistrict_id', 'edit_subdistrict');
                addFieldIfExists('port_of_loading_id', 'edit_port_loading');
                addFieldIfExists('port_of_discharge_id', 'edit_port_discharge');
            } else if (orderStatusId === 14) {
                // For partially matched orders, use the basic form values with null checks
                addFieldIfExists('price', 'edit_price');
                addFieldIfExists('quantity', 'edit_quantity');
                addFieldIfExists('first_delivery_date', 'edit_first_delivery_date_basic');
                addFieldIfExists('last_delivery_date', 'edit_last_delivery_date_basic');
                addFieldIfExists('currency_id', 'edit_currency_basic');
            }

            // Create a hidden form and submit it directly
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/order_book';
            form.style.display = 'none';

            // Add all form data to the form
            for (const [key, value] of formData.entries()) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
                console.log(`Adding field ${key} with value ${value}`);
            }

            // Add the form to the document and submit it
            document.body.appendChild(form);

            // Log the complete form data for debugging
            const formDataObj = {};
            for (const [key, value] of formData.entries()) {
                formDataObj[key] = value;
            }
            console.log('Submitting form with data:', formDataObj);

            // Submit the form
            form.submit();
            } catch (error) {
                console.error('Exception in submitEditForm:', error);
                customAlert('{{if eq .Lang "th"}}เกิดข้อผิดพลาดในการบันทึกข้อมูล กรุณาลองใหม่อีกครั้ง{{else}}An error occurred while processing your request. Please try again.{{end}}');
            }
        }

        // Validate edit form fields
        function validateEditFormFields() {
            try {
                // Get status ID with fallback
                let statusId = 13; // Default to available order
                const statusElement = document.getElementById('order_status_id');
                if (statusElement && statusElement.value) {
                    statusId = parseInt(statusElement.value);
                }

                // Get quantity with fallback
                let quantity = 0;
                const quantityElement = document.getElementById('edit_quantity');
                if (quantityElement && quantityElement.value) {
                    quantity = parseFloat(quantityElement.value);
                }

                // Get max quantity with fallback
                let maxQuantity = 999999;
                const maxQuantityElement = document.getElementById('max_quantity');
                if (maxQuantityElement && maxQuantityElement.value) {
                    maxQuantity = parseFloat(maxQuantityElement.value);
                }

                const missingFields = [];

                // Basic validation for all orders
                const priceElement = document.getElementById('edit_price');
                if (!priceElement || !priceElement.value) {
                    missingFields.push('{{if eq .Lang "th"}}ราคาต่อหน่วย{{else}}Price per Unit{{end}}');
                }

                if (!quantity) {
                    missingFields.push('{{if eq .Lang "th"}}ปริมาณ{{else}}Quantity{{end}}');
                }

            // Validation for partially matched orders (status 14)
            if (statusId === 14) {
                // Check quantity limit
                if (quantity > maxQuantity) {
                    customAlert('{{if eq .Lang "th"}}สำหรับคำสั่งซื้อขายที่จับคู่บางส่วนแล้ว คุณไม่สามารถเพิ่มปริมาณเกินกว่าจำนวนที่เหลืออยู่ (' + maxQuantity + '){{else}}For partially matched orders, you cannot increase the quantity beyond the available amount (' + maxQuantity + '){{end}}');
                    return ['quantity']; // Return a non-empty array to prevent form submission
                }

                // Check delivery dates
                const firstDeliveryDate = document.getElementById('edit_first_delivery_date_basic').value;
                const lastDeliveryDate = document.getElementById('edit_last_delivery_date_basic').value;

                // Check if both dates are provided
                if (!firstDeliveryDate) {
                    missingFields.push('{{if eq .Lang "th"}}วันที่ส่งมอบแรก{{else}}First Delivery Date{{end}}');
                }

                if (!lastDeliveryDate) {
                    missingFields.push('{{if eq .Lang "th"}}วันที่ส่งมอบสุดท้าย{{else}}Last Delivery Date{{end}}');
                }

                // If both dates are provided, make sure first date is not after last date
                if (firstDeliveryDate && lastDeliveryDate) {
                    const firstDate = new Date(firstDeliveryDate);
                    const lastDate = new Date(lastDeliveryDate);

                    if (firstDate > lastDate) {
                        customAlert('{{if eq .Lang "th"}}วันที่ส่งมอบแรกต้องไม่มากกว่าวันที่ส่งมอบสุดท้าย{{else}}First delivery date cannot be after last delivery date{{end}}');
                        return ['delivery_dates']; // Return a non-empty array to prevent form submission
                    }

                    // Check if dates are in the past
                    const today = new Date();
                    today.setHours(0, 0, 0, 0); // Reset time to start of day

                    if (firstDate < today) {
                        customAlert('{{if eq .Lang "th"}}วันที่ส่งมอบแรกต้องไม่เป็นวันในอดีต{{else}}First delivery date cannot be in the past{{end}}');
                        return ['delivery_dates']; // Return a non-empty array to prevent form submission
                    }

                    if (lastDate < today) {
                        customAlert('{{if eq .Lang "th"}}วันที่ส่งมอบสุดท้ายต้องไม่เป็นวันในอดีต{{else}}Last delivery date cannot be in the past{{end}}');
                        return ['delivery_dates']; // Return a non-empty array to prevent form submission
                    }

                    // Check if dates are the same
                    if (firstDate.getTime() === lastDate.getTime()) {
                        customAlert('{{if eq .Lang "th"}}วันที่ส่งมอบสุดท้ายต้องไม่เป็นวันเดียวกับวันที่ส่งมอบแรก{{else}}Last delivery date cannot be the same as first delivery date{{end}}');
                        return ['delivery_dates']; // Return a non-empty array to prevent form submission
                    }
                }
            }

            // Additional validation for available orders (status 13) with full edit form
            if (statusId === 13 && document.getElementById('full-edit-fields').style.display === 'block') {
                // Check delivery dates for full edit form
                const firstDeliveryDate = document.getElementById('edit_first_delivery_date').value;
                const lastDeliveryDate = document.getElementById('edit_last_delivery_date').value;

                // Check if both dates are provided
                if (!firstDeliveryDate) {
                    missingFields.push('{{if eq .Lang "th"}}วันที่ส่งมอบแรก{{else}}First Delivery Date{{end}}');
                }

                if (!lastDeliveryDate) {
                    missingFields.push('{{if eq .Lang "th"}}วันที่ส่งมอบสุดท้าย{{else}}Last Delivery Date{{end}}');
                }

                // If both dates are provided, make sure first date is not after last date
                if (firstDeliveryDate && lastDeliveryDate) {
                    const firstDate = new Date(firstDeliveryDate);
                    const lastDate = new Date(lastDeliveryDate);

                    if (firstDate > lastDate) {
                        customAlert('{{if eq .Lang "th"}}วันที่ส่งมอบแรกต้องไม่มากกว่าวันที่ส่งมอบสุดท้าย{{else}}First delivery date cannot be after last delivery date{{end}}');
                        return ['delivery_dates']; // Return a non-empty array to prevent form submission
                    }

                    // Check if dates are in the past
                    const today = new Date();
                    today.setHours(0, 0, 0, 0); // Reset time to start of day

                    if (firstDate < today) {
                        customAlert('{{if eq .Lang "th"}}วันที่ส่งมอบแรกต้องไม่เป็นวันในอดีต{{else}}First delivery date cannot be in the past{{end}}');
                        return ['delivery_dates']; // Return a non-empty array to prevent form submission
                    }

                    if (lastDate < today) {
                        customAlert('{{if eq .Lang "th"}}วันที่ส่งมอบสุดท้ายต้องไม่เป็นวันในอดีต{{else}}Last delivery date cannot be in the past{{end}}');
                        return ['delivery_dates']; // Return a non-empty array to prevent form submission
                    }

                    // Check if dates are the same
                    if (firstDate.getTime() === lastDate.getTime()) {
                        customAlert('{{if eq .Lang "th"}}วันที่ส่งมอบสุดท้ายต้องไม่เป็นวันเดียวกับวันที่ส่งมอบแรก{{else}}Last delivery date cannot be the same as first delivery date{{end}}');
                        return ['delivery_dates']; // Return a non-empty array to prevent form submission
                    }
                }

                const requiredFields = [
                    { id: 'edit_marketspace', label: '{{if eq .Lang "th"}}ตลาด{{else}}Marketspace{{end}}' },
                    { id: 'edit_market', label: '{{if eq .Lang "th"}}ตลาด{{else}}Market{{end}}' },
                    { id: 'edit_submarket', label: '{{if eq .Lang "th"}}ตลาดย่อย{{else}}Submarket{{end}}' },
                    { id: 'edit_order_type', label: '{{if eq .Lang "th"}}ประเภทคำสั่งซื้อขาย{{else}}Order Type{{end}}' },
                    { id: 'edit_matching_type', label: '{{if eq .Lang "th"}}ประเภทการจับคู่{{else}}Matching Type{{end}}' },
                    { id: 'edit_contract_type', label: '{{if eq .Lang "th"}}ประเภทสัญญา{{else}}Contract Type{{end}}' },
                    { id: 'edit_product', label: '{{if eq .Lang "th"}}สินค้า{{else}}Product{{end}}' },
                    { id: 'edit_quality', label: '{{if eq .Lang "th"}}คุณภาพ{{else}}Quality{{end}}' },
                    { id: 'edit_currency', label: '{{if eq .Lang "th"}}สกุลเงิน{{else}}Currency{{end}}' },
                    { id: 'edit_uom', label: '{{if eq .Lang "th"}}หน่วย{{else}}Unit{{end}}' },
                    { id: 'edit_packing', label: '{{if eq .Lang "th"}}บรรจุภัณฑ์{{else}}Packing{{end}}' },
                    { id: 'edit_payment_term', label: '{{if eq .Lang "th"}}เงื่อนไขการชำระเงิน{{else}}Payment Term{{end}}' },
                    { id: 'edit_delivery_term', label: '{{if eq .Lang "th"}}เงื่อนไขการส่งมอบ{{else}}Delivery Term{{end}}' },
                    { id: 'edit_country', label: '{{if eq .Lang "th"}}ประเทศ{{else}}Country{{end}}' }
                ];

                requiredFields.forEach(field => {
                    const element = document.getElementById(field.id);
                    if (element && !element.value) {
                        missingFields.push(field.label);
                    }
                });

                // Check location fields based on marketspace
                const marketspaceValue = document.getElementById('edit_marketspace').value;
                const deliveryTermValue = document.getElementById('edit_delivery_term').value;

                if (marketspaceValue === '1') {
                    // Local market - check province, district, subdistrict
                    const locationFields = [
                        { id: 'edit_province', label: '{{if eq .Lang "th"}}จังหวัด{{else}}Province{{end}}' },
                        { id: 'edit_district', label: '{{if eq .Lang "th"}}อำเภอ{{else}}District{{end}}' },
                        { id: 'edit_subdistrict', label: '{{if eq .Lang "th"}}ตำบล{{else}}Subdistrict{{end}}' }
                    ];

                    locationFields.forEach(field => {
                        const element = document.getElementById(field.id);
                        if (element && !element.value) {
                            missingFields.push(field.label);
                        }
                    });
                } else if (marketspaceValue === '2') {
                    if (deliveryTermValue === '5' || deliveryTermValue === '9') {
                        // Global market with EXW/FCA - check province, district, subdistrict
                        const locationFields = [
                            { id: 'edit_province', label: '{{if eq .Lang "th"}}จังหวัด{{else}}Province{{end}}' },
                            { id: 'edit_district', label: '{{if eq .Lang "th"}}อำเภอ{{else}}District{{end}}' },
                            { id: 'edit_subdistrict', label: '{{if eq .Lang "th"}}ตำบล{{else}}Subdistrict{{end}}' }
                        ];

                        locationFields.forEach(field => {
                            const element = document.getElementById(field.id);
                            if (element && !element.value) {
                                missingFields.push(field.label);
                            }
                        });
                    } else {
                        // Global market with other delivery terms - check at least one port
                        const pol = document.getElementById('edit_port_loading');
                        const pod = document.getElementById('edit_port_discharge');

                        if ((!pol?.value && !pod?.value)) {
                            missingFields.push('{{if eq .Lang "th"}}ต้องระบุท่าเรือต้นทางหรือท่าเรือปลายทางอย่างน้อยหนึ่งแห่ง{{else}}At least one port (Port of Loading or Port of Discharge) is required{{end}}');
                        }
                    }
                }
            }

            return missingFields;
            } catch (error) {
                console.error('Exception in validateEditFormFields:', error);
                return ['{{if eq .Lang "th"}}เกิดข้อผิดพลาดในการตรวจสอบข้อมูล{{else}}Error validating form data{{end}}'];
            }
        }

        // Validate edit form before submission
        function validateEditForm() {
            try {
                // Reset all validation styles first
                resetValidationStyles();

                // Validate the form fields
                const invalidFields = validateEditFormFields();
                if (invalidFields.length > 0) {
                    // Highlight invalid fields with red border
                    highlightInvalidFields(invalidFields);

                    // Show detailed error message
                    const errorMessage = '{{if eq .Lang "th"}}กรุณากรอกข้อมูลในฟิลด์ต่อไปนี้ให้ครบถ้วน:{{else}}Please fill in the following required fields:{{end}}\n\n' +
                        invalidFields.map(field => `• ${field}`).join('\n');

                    customAlert(errorMessage);
                    return false;
                }

                // Show confirmation dialog
                return generateEditConfirmation();
            } catch (error) {
                console.error('Exception in validateEditForm:', error);
                customAlert('{{if eq .Lang "th"}}เกิดข้อผิดพลาดในการตรวจสอบข้อมูล กรุณาลองใหม่อีกครั้ง{{else}}An error occurred while validating the form. Please try again.{{end}}');
                return false;
            }
        }

        // Reset validation styles for all form fields
        function resetValidationStyles() {
            const form = document.getElementById('editForm');
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                input.classList.remove('invalid-field');

                // Remove any existing error message
                const errorMsg = input.parentNode.querySelector('.field-error-message');
                if (errorMsg) {
                    input.parentNode.removeChild(errorMsg);
                }
            });
        }

        // Highlight invalid fields with red border and error message
        function highlightInvalidFields(invalidFields) {
            const form = document.getElementById('editForm');
            const currentLang = "{{ .Lang }}";
            const isThai = currentLang === "th";

            invalidFields.forEach(fieldName => {
                // Convert field name to input ID
                let inputId = '';
                const statusId = parseInt(document.getElementById('order_status_id').value);

                // Map field names to input IDs based on form type (basic or full)
                if (statusId === 14) {
                    // Basic form for partially matched orders
                    if (fieldName.includes('Price')) inputId = 'edit_price';
                    else if (fieldName.includes('Quantity')) inputId = 'edit_quantity';
                    else if (fieldName.includes('First Delivery') || fieldName.includes('วันเริ่มต้นส่งมอบ')) inputId = 'edit_first_delivery_date_basic';
                    else if (fieldName.includes('Last Delivery') || fieldName.includes('วันสิ้นสุดส่งมอบ')) inputId = 'edit_last_delivery_date_basic';
                } else {
                    // Full form for available orders
                    if (fieldName.includes('Marketspace') || fieldName.includes('ขอบเขตตลาด')) inputId = 'edit_marketspace';
                    else if (fieldName.includes('Market') || fieldName.includes('ตลาด')) inputId = 'edit_market';
                    else if (fieldName.includes('Submarket') || fieldName.includes('ตลาดย่อย')) inputId = 'edit_submarket';
                    else if (fieldName.includes('Order type') || fieldName.includes('ประเภทคำสั่ง')) inputId = 'edit_order_type';
                    else if (fieldName.includes('Matching type') || fieldName.includes('ประเภทการจับคู่')) inputId = 'edit_matching_type';
                    else if (fieldName.includes('Contract type') || fieldName.includes('ประเภทสัญญา')) inputId = 'edit_contract_type';
                    else if (fieldName.includes('Product') || fieldName.includes('สินค้า')) inputId = 'edit_product';
                    else if (fieldName.includes('Quality') || fieldName.includes('คุณภาพ')) inputId = 'edit_quality';
                    else if (fieldName.includes('Price') || fieldName.includes('ราคา')) inputId = 'edit_price_full';
                    else if (fieldName.includes('Quantity') || fieldName.includes('ปริมาณ')) inputId = 'edit_quantity_full';
                    else if (fieldName.includes('Currency') || fieldName.includes('สกุลเงิน')) inputId = 'edit_currency';
                    else if (fieldName.includes('Unit') || fieldName.includes('หน่วย')) inputId = 'edit_uom';
                    else if (fieldName.includes('Packing') || fieldName.includes('บรรจุภัณฑ์')) inputId = 'edit_packing';
                    else if (fieldName.includes('Payment term') || fieldName.includes('เงื่อนไขการชำระเงิน')) inputId = 'edit_payment_term';
                    else if (fieldName.includes('Delivery term') || fieldName.includes('เงื่อนไขการส่งมอบ')) inputId = 'edit_delivery_term';
                    else if (fieldName.includes('Country') || fieldName.includes('ประเทศ')) inputId = 'edit_country';
                    else if (fieldName.includes('First Delivery') || fieldName.includes('วันเริ่มต้นส่งมอบ')) inputId = 'edit_first_delivery_date';
                    else if (fieldName.includes('Last Delivery') || fieldName.includes('วันสิ้นสุดส่งมอบ')) inputId = 'edit_last_delivery_date';
                }

                if (inputId) {
                    const input = document.getElementById(inputId);
                    if (input) {
                        // Add red border
                        input.classList.add('invalid-field');

                        // Add error message below the field
                        const errorMsg = document.createElement('div');
                        errorMsg.className = 'field-error-message';
                        errorMsg.textContent = isThai ? 'กรุณากรอกข้อมูลในฟิลด์นี้' : 'This field is required';
                        errorMsg.style.color = '#ff0000';
                        errorMsg.style.fontSize = '12px';
                        errorMsg.style.marginTop = '4px';

                        // Insert error message after the input
                        input.parentNode.appendChild(errorMsg);
                    }
                }
            });
        }

        // Show loading indicator
        function showLoadingIndicator() {
            // Check if loading indicator already exists
            if (!document.getElementById('loading-indicator')) {
                const loadingDiv = document.createElement('div');
                loadingDiv.id = 'loading-indicator';
                loadingDiv.innerHTML = `
                    <div class="loading-spinner"></div>
                    <p>{{if eq .Lang "th"}}กำลังโหลดข้อมูล...{{else}}Loading data...{{end}}</p>
                `;
                document.querySelector('.modal-content').appendChild(loadingDiv);
            } else {
                document.getElementById('loading-indicator').style.display = 'flex';
            }
        }

        // Hide loading indicator
        function hideLoadingIndicator() {
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        }

        // Show error message
        function showErrorMessage(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;

            // Add to modal content
            const modalContent = document.querySelector('.modal-content');
            modalContent.appendChild(errorDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }

        // Function to handle the Save button click
        function handleSaveButtonClick() {
            try {
                if (validateEditForm()) {
                    submitEditForm();
                }
            } catch (error) {
                console.error('Exception in handleSaveButtonClick:', error);
                customAlert('{{if eq .Lang "th"}}เกิดข้อผิดพลาดในการบันทึกข้อมูล กรุณาลองใหม่อีกครั้ง{{else}}An error occurred while saving. Please try again.{{end}}');
            }
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            const editModal = document.getElementById('editModal');
            const deleteModal = document.getElementById('deleteModal');
            const confirmationModal = document.getElementById('confirmationModal');

            if (event.target === editModal) {
                closeEditModal();
            }

            if (event.target === deleteModal) {
                closeDeleteModal();
            }

            if (event.target === confirmationModal) {
                closeConfirmationModal();
            }
        };

        // Initialize dropdown options when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Get current language and store it globally
            const currentLang = "{{ .Lang }}";
            window.currentLang = currentLang;

            // Preload dropdown options
            preloadDropdownOptions();

            // Set up order type filter for both table and card views
            const orderTypeFilter = document.getElementById('order-type-filter');
            if (orderTypeFilter) {
                orderTypeFilter.addEventListener('change', function() {
                    const selectedType = this.value;

                    // Filter table rows (desktop view)
                    const tableRows = document.querySelectorAll('.order-book-table tbody tr');
                    tableRows.forEach(row => {
                        const orderType = row.querySelector('td:first-child').textContent.trim().toLowerCase();

                        if (selectedType === 'all' ||
                            (selectedType === 'buy' && orderType.includes('buy')) ||
                            (selectedType === 'sell' && orderType.includes('sell'))) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });

                    // Filter cards (mobile view)
                    const cards = document.querySelectorAll('.order-card');
                    cards.forEach(card => {
                        const orderType = card.getAttribute('data-order-type').toLowerCase();

                        if (selectedType === 'all' ||
                            (selectedType === 'buy' && orderType.includes('buy')) ||
                            (selectedType === 'sell' && orderType.includes('sell'))) {
                            card.style.display = '';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            }

            // Add event listener for marketspace change in edit form
            const editMarketspaceSelect = document.getElementById('edit_marketspace');
            const editDeliveryTermSelect = document.getElementById('edit_delivery_term');
            let lastKnownMarketspace = "";

            if (editMarketspaceSelect) {
                editMarketspaceSelect.addEventListener('change', function() {
                    const changingToGlobal = lastKnownMarketspace === "1" && editMarketspaceSelect.value === "2";
                    lastKnownMarketspace = editMarketspaceSelect.value;

                    // If changing from local to global and delivery term is EXW (5) or FCA (9),
                    // reset the delivery term to match the submit order form behavior
                    if (changingToGlobal && editDeliveryTermSelect &&
                        (editDeliveryTermSelect.value === '5' || editDeliveryTermSelect.value === '9')) {
                        editDeliveryTermSelect.selectedIndex = 0;
                        editDeliveryTermSelect.dispatchEvent(new Event('change'));
                    }

                    handleEditMarketspaceChange();
                });

                // Initialize the last known marketspace
                lastKnownMarketspace = editMarketspaceSelect.value;
            }

            // Add event listener for delivery term change in edit form
            if (editDeliveryTermSelect) {
                editDeliveryTermSelect.addEventListener('change', function() {
                    // Get the current marketspace value
                    const marketspaceSelect = document.getElementById('edit_marketspace');
                    const selectedMarketspace = marketspaceSelect ? marketspaceSelect.value : '';
                    const selectedDeliveryTerm = this.value;

                    console.log(`Delivery term changed to: ${selectedDeliveryTerm} in marketspace: ${selectedMarketspace}`);

                    // Only handle field visibility for global market
                    if (selectedMarketspace === '2') {
                        const portFields = document.getElementById('edit-port-fields');
                        const locationFields = document.getElementById('edit-location-fields');

                        // For EXW (5) and FCA (9), show location fields, otherwise show port fields
                        if (selectedDeliveryTerm === '5' || selectedDeliveryTerm === '9') {
                            console.log("Showing location fields for global market with delivery terms 5 or 9");
                            portFields.style.display = 'none';
                            locationFields.style.display = 'block';

                            // Make location fields required
                            ['edit_province', 'edit_district', 'edit_subdistrict'].forEach(id => {
                                const element = document.getElementById(id);
                                if (element) {
                                    element.setAttribute('required', '');
                                }
                            });

                            // Remove required from port fields
                            ['edit_port_loading', 'edit_port_discharge'].forEach(id => {
                                const element = document.getElementById(id);
                                if (element) {
                                    element.removeAttribute('required');
                                }
                            });
                        } else {
                            console.log("Showing port fields for global market with other delivery terms");
                            locationFields.style.display = 'none';
                            portFields.style.display = 'block';

                            // Make port fields required
                            ['edit_port_loading', 'edit_port_discharge'].forEach(id => {
                                const element = document.getElementById(id);
                                if (element) {
                                    element.setAttribute('required', '');
                                }
                            });

                            // Remove required from location fields
                            ['edit_province', 'edit_district', 'edit_subdistrict'].forEach(id => {
                                const element = document.getElementById(id);
                                if (element) {
                                    element.removeAttribute('required');
                                }
                            });
                        }
                    }
                });
            }

            // Check contract status for all partially matched orders
            checkAllPartiallyMatchedOrders();

            // Set up periodic refresh for order status updates
            setInterval(refreshOrderStatuses, 10000); // Check every 10 seconds
        });

        // Function to check contract status for all partially matched orders
        function checkAllPartiallyMatchedOrders() {
            // Find all partially matched orders (status 14)
            const partiallyMatchedOrders = document.querySelectorAll('[data-order-id]');

            partiallyMatchedOrders.forEach(element => {
                const orderId = element.getAttribute('data-order-id');
                if (orderId) {
                    checkContractStatus(orderId);
                }
            });
        }

        // Function to check if a contract exists for this order
        function checkContractStatus(orderId) {
            fetch(`/api/order/${orderId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Failed to fetch order details: ${response.status}`);
                    }
                    return response.json();
                })
                .then(orderData => {
                    // Check if this order has a completed contract
                    // We'll use the available quantity as a proxy - if it's less than original quantity
                    // and the status is still 14, it means a contract has been signed and the remaining
                    // quantity is back in the marketplace
                    if (orderData.AvailableQuantity < orderData.OriginalQuantity &&
                        orderData.StatusID === 14) {

                        // This is a partially matched order with a completed contract
                        // Show both edit and delete buttons (desktop view)
                        const editBtn = document.getElementById(`edit-btn-${orderId}`);
                        const deleteBtn = document.getElementById(`delete-btn-${orderId}`);

                        if (editBtn) {
                            editBtn.style.display = 'inline-flex';
                        }

                        if (deleteBtn) {
                            deleteBtn.style.display = 'inline-flex';
                        }

                        // Also update mobile view buttons
                        const mobileEditBtn = document.getElementById(`mobile-edit-btn-${orderId}`);
                        const mobileDeleteBtn = document.getElementById(`mobile-delete-btn-${orderId}`);

                        if (mobileEditBtn) {
                            mobileEditBtn.style.display = 'inline-flex';
                        }

                        if (mobileDeleteBtn) {
                            mobileDeleteBtn.style.display = 'inline-flex';
                        }
                    } else {
                        // This is a partially matched order in contract process
                        // Hide both edit and delete buttons (desktop view)
                        const editBtn = document.getElementById(`edit-btn-${orderId}`);
                        const deleteBtn = document.getElementById(`delete-btn-${orderId}`);

                        if (editBtn) {
                            editBtn.style.display = 'none';
                        }

                        if (deleteBtn) {
                            deleteBtn.style.display = 'none';
                        }

                        // Also update mobile view buttons
                        const mobileEditBtn = document.getElementById(`mobile-edit-btn-${orderId}`);
                        const mobileDeleteBtn = document.getElementById(`mobile-delete-btn-${orderId}`);

                        if (mobileEditBtn) {
                            mobileEditBtn.style.display = 'none';
                        }

                        if (mobileDeleteBtn) {
                            mobileDeleteBtn.style.display = 'none';
                        }
                    }
                })
                .catch(error => {
                    console.error(`Error checking contract status for order ${orderId}:`, error);
                });
        }

        // Function to check contract status before editing
        function checkContractStatusBeforeEdit(orderId, price, quantity, maxQuantity, remark, statusId) {
            fetch(`/api/order/${orderId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Failed to fetch order details: ${response.status}`);
                    }
                    return response.json();
                })
                .then(orderData => {
                    // Check if this order has a completed contract
                    if (orderData.AvailableQuantity < orderData.OriginalQuantity &&
                        orderData.StatusID === 14) {

                        // This is a partially matched order with a completed contract
                        // Allow editing
                        openEditModal(orderId, price, quantity, maxQuantity, remark, statusId);
                    } else {
                        // This is a partially matched order in contract process
                        // Show alert that editing is not allowed
                        const message = window.currentLang === "th" ?
                            "ไม่สามารถแก้ไขคำสั่งซื้อขายที่อยู่ในกระบวนการทำสัญญาได้" :
                            "Cannot edit an order that is in the contract process";
                        customAlert(message);
                    }
                })
                .catch(error => {
                    console.error(`Error checking contract status for order ${orderId}:`, error);
                    const message = window.currentLang === "th" ?
                        "เกิดข้อผิดพลาดในการตรวจสอบสถานะสัญญา กรุณาลองใหม่อีกครั้ง" :
                        "Error checking contract status. Please try again.";
                    customAlert(message);
                });
        }

        // Function to check contract status before deleting
        function checkContractStatusBeforeDelete(orderId) {
            fetch(`/api/order/${orderId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Failed to fetch order details: ${response.status}`);
                    }
                    return response.json();
                })
                .then(orderData => {
                    // Check if this order has a completed contract
                    if (orderData.AvailableQuantity < orderData.OriginalQuantity &&
                        orderData.StatusID === 14) {

                        // This is a partially matched order with a completed contract
                        // Allow deletion
                        openDeleteModal(orderId);
                    } else {
                        // This is a partially matched order in contract process
                        // Show alert that deletion is not allowed
                        const message = window.currentLang === "th" ?
                            "ไม่สามารถลบคำสั่งซื้อขายที่อยู่ในกระบวนการทำสัญญาได้" :
                            "Cannot delete an order that is in the contract process";
                        customAlert(message);
                    }
                })
                .catch(error => {
                    console.error(`Error checking contract status for order ${orderId}:`, error);
                    const message = window.currentLang === "th" ?
                        "เกิดข้อผิดพลาดในการตรวจสอบสถานะสัญญา กรุณาลองใหม่อีกครั้ง" :
                        "Error checking contract status. Please try again.";
                    customAlert(message);
                });
        }

        // Function to refresh order statuses in real-time
        function refreshOrderStatuses() {
            // Get all order elements (both table rows and mobile cards)
            const orderElements = document.querySelectorAll('[data-order-id]');

            // For each order element, check its current status
            orderElements.forEach(element => {
                const orderId = element.getAttribute('data-order-id');
                if (!orderId) return;

                fetch(`/api/order/${orderId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Failed to fetch order details: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(orderData => {
                        // Check if this is a table row or a card
                        const isTableRow = element.tagName.toLowerCase() === 'tr';

                        if (isTableRow) {
                            // Desktop view (table row)
                            // Get the current status cell and update it if needed
                            const statusCell = element.querySelector('.status-cell');
                            if (statusCell) {
                                const currentStatusId = parseInt(statusCell.getAttribute('data-status-id') || '0');

                                // If status has changed, update the UI
                                if (currentStatusId !== orderData.StatusID) {
                                    // Update status cell
                                    updateStatusCell(statusCell, orderData.StatusID, orderData.StatusName);

                                    // Update quantity cell if needed
                                    const quantityCell = element.querySelector('.quantity-cell');
                                    if (quantityCell) {
                                        if (orderData.StatusID === 14) {
                                            // For partially matched orders, show both available and original quantity
                                            quantityCell.textContent = `${orderData.AvailableQuantity}/${orderData.OriginalQuantity} ${orderData.UOMName}`;
                                        } else {
                                            quantityCell.textContent = `${orderData.OriginalQuantity} ${orderData.UOMName}`;
                                        }
                                    }

                                    // Update action buttons based on new status
                                    updateActionButtons(element, orderData);
                                }
                                // Even if status hasn't changed, check if it's a partially matched order
                                // and update the edit/delete buttons visibility
                                else if (orderData.StatusID === 14) {
                                    checkContractStatus(orderId);
                                }
                            }
                        } else {
                            // Mobile view (card)
                            // Update card status
                            const statusElement = element.querySelector('.order-card-status');
                            if (statusElement) {
                                // Remove all status classes
                                statusElement.classList.remove('available', 'partial', 'matched');

                                // Update status text and class
                                if (orderData.StatusID === 13) {
                                    statusElement.classList.add('available');
                                    statusElement.textContent = window.currentLang === "th" ? 'เปิดประมูล' : 'Available';
                                } else if (orderData.StatusID === 14) {
                                    statusElement.classList.add('partial');
                                    statusElement.textContent = window.currentLang === "th" ? 'จับคู่แล้วบางส่วน' : 'Partially Matched';
                                } else if (orderData.StatusID === 15) {
                                    statusElement.classList.add('matched');
                                    statusElement.textContent = window.currentLang === "th" ? 'จับคู่แล้วแล้วทั้งหมด' : 'Fully Matched';
                                }
                            }

                            // Update quantity values
                            const originalQuantityValue = element.querySelector('.order-card-detail:nth-child(4) .order-card-value');
                            const availableQuantityValue = element.querySelector('.order-card-detail:nth-child(5) .order-card-value');

                            if (originalQuantityValue) {
                                originalQuantityValue.textContent = `${orderData.OriginalQuantity} ${orderData.UOMName}`;
                            }

                            if (availableQuantityValue) {
                                availableQuantityValue.textContent = `${orderData.AvailableQuantity} ${orderData.UOMName}`;
                            }

                            // Update action buttons
                            const actionsContainer = element.querySelector('.order-card-actions');
                            if (actionsContainer) {
                                // Clear existing buttons
                                actionsContainer.innerHTML = '';

                                if (orderData.StatusID === 13) {
                                    // Available orders - show both edit and delete buttons
                                    const editBtn = document.createElement('button');
                                    editBtn.className = 'edit-btn';
                                    editBtn.textContent = window.currentLang === "th" ? 'แก้ไข' : 'Edit';
                                    editBtn.onclick = function() {
                                        openEditModal(orderId, orderData.Price, orderData.AvailableQuantity,
                                            '999999', orderData.Remark ? orderData.Remark.String : '', orderData.StatusID);
                                    };

                                    const deleteBtn = document.createElement('button');
                                    deleteBtn.className = 'delete-btn';
                                    deleteBtn.textContent = window.currentLang === "th" ? 'ลบ' : 'Delete';
                                    deleteBtn.onclick = function() {
                                        openDeleteModal(orderId);
                                    };

                                    actionsContainer.appendChild(editBtn);
                                    actionsContainer.appendChild(deleteBtn);
                                }
                                else if (orderData.StatusID === 14) {
                                    // Partially matched orders - add buttons with hidden state initially
                                    const editBtn = document.createElement('button');
                                    editBtn.className = 'edit-btn';
                                    editBtn.id = `mobile-edit-btn-${orderId}`;
                                    editBtn.setAttribute('data-order-id', orderId);
                                    editBtn.textContent = window.currentLang === "th" ? 'แก้ไข' : 'Edit';
                                    editBtn.style.display = 'none';
                                    editBtn.onclick = function() {
                                        checkContractStatusBeforeEdit(orderId, orderData.Price, orderData.AvailableQuantity,
                                            orderData.AvailableQuantity, orderData.Remark ? orderData.Remark.String : '', orderData.StatusID);
                                    };

                                    const deleteBtn = document.createElement('button');
                                    deleteBtn.className = 'delete-btn';
                                    deleteBtn.id = `mobile-delete-btn-${orderId}`;
                                    deleteBtn.setAttribute('data-order-id', orderId);
                                    deleteBtn.textContent = window.currentLang === "th" ? 'ลบ' : 'Delete';
                                    deleteBtn.style.display = 'none';
                                    deleteBtn.onclick = function() {
                                        checkContractStatusBeforeDelete(orderId);
                                    };

                                    actionsContainer.appendChild(editBtn);
                                    actionsContainer.appendChild(deleteBtn);

                                    // Check if this is a partially matched order with completed contract
                                    if (orderData.AvailableQuantity < orderData.OriginalQuantity) {
                                        editBtn.style.display = 'inline-flex';
                                        deleteBtn.style.display = 'inline-flex';
                                    }
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.error(`Error refreshing order status for order ${orderId}:`, error);
                    });
            });
        }

        // Helper function to update status cell
        function updateStatusCell(statusCell, statusId, statusName) {
            // Update data attribute
            statusCell.setAttribute('data-status-id', statusId);

            // Update class and content based on status
            statusCell.className = 'status-cell';

            if (statusId === 13) {
                statusCell.classList.add('status-available');
                statusCell.innerHTML = '<span class="status-icon">🟢</span> ' +
                    (window.currentLang === "th" ? 'พร้อมขาย' : 'Available');
            } else if (statusId === 14) {
                statusCell.classList.add('status-partial');
                statusCell.innerHTML = '<span class="status-icon">🟠</span> ' +
                    (window.currentLang === "th" ? 'จับคู่บางส่วน' : 'Partially Matched');
            } else if (statusId === 15) {
                statusCell.classList.add('status-matched');
                statusCell.innerHTML = '<span class="status-icon">🔵</span> ' +
                    (window.currentLang === "th" ? 'จับคู่แล้ว' : 'Fully Matched');
            } else {
                statusCell.innerHTML = statusName;
            }
        }

        // Helper function to update action buttons
        function updateActionButtons(row, orderData) {
            const orderId = orderData.ID;
            const statusId = orderData.StatusID;

            // Get action buttons cell
            const actionCell = row.querySelector('.action-buttons');
            if (!actionCell) return;

            // Clear existing buttons
            actionCell.innerHTML = '';

            if (statusId === 13) {
                // Available orders - show both edit and delete buttons
                const editBtn = document.createElement('button');
                editBtn.className = 'edit-btn';
                editBtn.textContent = window.currentLang === "th" ? 'แก้ไข' : 'Edit';
                editBtn.onclick = function() {
                    openEditModal(orderId, orderData.Price, orderData.AvailableQuantity,
                        '999999', orderData.Remark ? orderData.Remark.String : '', statusId);
                };

                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'delete-btn';
                deleteBtn.textContent = window.currentLang === "th" ? 'ลบ' : 'Delete';
                deleteBtn.onclick = function() {
                    openDeleteModal(orderId);
                };

                actionCell.appendChild(editBtn);
                actionCell.appendChild(deleteBtn);
            }
            else if (statusId === 14) {
                // Partially matched orders - add buttons with hidden state initially
                const editBtn = document.createElement('button');
                editBtn.className = 'edit-btn';
                editBtn.id = `edit-btn-${orderId}`;
                editBtn.setAttribute('data-order-id', orderId);
                editBtn.textContent = window.currentLang === "th" ? 'แก้ไข' : 'Edit';
                editBtn.style.display = 'none';
                editBtn.onclick = function() {
                    checkContractStatusBeforeEdit(orderId, orderData.Price, orderData.AvailableQuantity,
                        orderData.AvailableQuantity, orderData.Remark ? orderData.Remark.String : '', statusId);
                };

                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'delete-btn';
                deleteBtn.id = `delete-btn-${orderId}`;
                deleteBtn.setAttribute('data-order-id', orderId);
                deleteBtn.textContent = window.currentLang === "th" ? 'ลบ' : 'Delete';
                deleteBtn.style.display = 'none';
                deleteBtn.onclick = function() {
                    checkContractStatusBeforeDelete(orderId);
                };

                actionCell.appendChild(editBtn);
                actionCell.appendChild(deleteBtn);

                // Check if this is a partially matched order with completed contract
                if (orderData.AvailableQuantity < orderData.OriginalQuantity) {
                    editBtn.style.display = 'inline-flex';
                    deleteBtn.style.display = 'inline-flex';
                }
            }
            else if (statusId === 15) {
                // For fully matched orders (status 15), add a hidden placeholder to maintain height
                const placeholder = document.createElement('div');
                placeholder.style.height = '32px'; // Same height as buttons
                placeholder.style.width = '1px';
                placeholder.style.visibility = 'hidden';
                placeholder.style.display = 'inline-block';
                actionCell.appendChild(placeholder);
            }
        }
    </script>
</body>
</html>
{{end}}
