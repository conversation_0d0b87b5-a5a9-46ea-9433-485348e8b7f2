{{ define "content" }}
<nav class="index-nav">
    <div class="index-nav-container">
        <a href="/?lang={{ .lang }}" class="index-nav-brand">BiomassX</a>
        <div class="index-nav-links">
            <a href="/order?lang={{ .lang }}">เสนอซื้อขาย</a>
            <a href="/markets?lang={{ .lang }}">ตลาด</a>
            <a href="/productspage?lang={{ .lang }}">สินค้า</a>
            <a href="/services?lang={{ .lang }}">บริการ</a>
            <a href="/login?lang={{ .lang }}">เข้าสู่ระบบ</a>
            <div class="language-selector">
                <form hx-get="/" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">เลือกภาษา:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>อังกฤษ</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>ไทย</option>
                        <option value="en" {{ if eq .lang "kr" }}selected{{ end }}>เกาหลี</option>
                        <option value="en" {{ if eq .lang "jp" }}selected{{ end }}>ญี่ปุ่น</option>
                        <option value="en" {{ if eq .lang "vi" }}selected{{ end }}>เวียดนาม</option>
                    </select>
                </form>
            </div>
        </div>
        <div class="desktop-lang-selector index-lang-selector">
            <form hx-get="/" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>อังกฤษ</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>ไทย</option>
                        <option value="en" {{ if eq .lang "kr" }}selected{{ end }}>เกาหลี</option>
                        <option value="en" {{ if eq .lang "jp" }}selected{{ end }}>ญี่ปุ่น</option>
                        <option value="en" {{ if eq .lang "vi" }}selected{{ end }}>เวียดนาม</option>
                </select>
            </form>
        </div>
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="main-logo">
    <a>แพลตฟอร์มพลังงานชีวภาพ</a>
</main>

<div class="content-wrapper">
    <main class="content">
        <div class="container">
            <div class="grid">
                <div class="card">
                    <h2 id="top-product-container" hx-get="/api/top-product-name?lang=th" hx-trigger="load, every 5s"
                        hx-swap="innerHTML">
                        ดัชนีราคา {{ .TopProduct.ProductName }}
                    </h2>
                    <table class="biomass-price">
                        <thead>
                            <tr>
                                <th>เงื่อนไขการส่งมอบ</th>
                                <th>ซื้อ(บาท)</th>
                                <th>ขาย(บาท)</th>
                            </tr>
                        </thead>
                        <tbody hx-get="/api/product-prices?lang=th" hx-trigger="load, every 5s" hx-swap="innerHTML">
                            <tr>
                                <td colspan="3" class="loading-text">โหลดข้อมูลราคา...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="card">
                    <h2>ปริมาณการซื้อขาย</h2>
                    <div id="biomass-price">
                        <table class="biomass-price">
                            <thead>
                                <tr>
                                    <th>เงื่อนไขการส่งมอบ</th>
                                    <th>ซื้อ(บาท)</th>
                                    <th>ขาย(บาท)</th>
                                </tr>
                            </thead>
                            <tbody hx-get="/api/market-volume?lang=th" hx-trigger="load, every 5s" hx-swap="innerHTML">
                                <tr>
                                    <td colspan="3" class="loading-text">โหลดข้อมูลปริมาณการซื้อขาย...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card">
                    <h2>จำนวนคำสั่งเสนอซื้อ/เสนอขาย</h2>
                    <div class="card">
                        <table class="biomass-price">
                            <thead>
                                <tr>
                                    <th>เงื่อนไขการส่งมอบ</th>
                                    <th>ซื้อ(คำสั่ง)</th>
                                    <th>ขาย(คำสั่ง)</th>
                                    <th>ขอบเขตตลาด</th>
                                    <th>ตลาดหลัก</th>
                                </tr>
                            </thead>
                            <tbody hx-get="/api/active-orders?lang={{ .lang }}" hx-trigger="load, every 5s" hx-swap="innerHTML">
                                <tr>
                                    <td colspan="5" class="loading-text">โหลดข้อมูล...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card">
                    <h2>ลดการปล่อยก๊าซ CO2</h2>
                    <div class="co2-stats" hx-get="/api/co2-saved?lang={{ .lang }}" hx-trigger="load, every 5s" hx-swap="innerHTML">
                        <div class="co2-stat">
                            <div class="co2-label">รายเดือน</div>
                            <div class="co2-value">Loading... TCO2eq</div>
                        </div>
                        <div class="co2-stat">
                            <div class="co2-label">รายปี</div>
                            <div class="co2-value">Loading... TCO2eq</div>
                        </div>
                        <div class="co2-stat">
                            <div class="co2-label">ทั้งหมด</div>
                            <div class="co2-value">Loading... TCO2eq</div>
                        </div>
                    </div>
                    <!--<div class="co2-description">ลดการปล่อยก๊าซ Co2</div>-->
                </div>
            </div>
            <div class="card" style="margin-top: 20px; padding-bottom: 50px;">
                <h2>คำสั่งเสนอซื้อขายล่าสุด</h2>
                <table id="transactions-table">
                    <thead>
                        <tr>
                            <th>คำสั่ง</th>
                            <th>สินค้า</th>
                            <th>จำนวน(หน่วยนับ)</th>
                            <th>คุณภาพ</th>
                            <th>บรรจุภัณฑ์</th>
                            <th>ตลาดหลัก</th>
                            <th>ตลาดย่อย</th>
                            <th>ประเภทสัญญา</th>
                            <th>เงื่อนไขการส่งมอบ</th>
                            <th>ประเทศ</th>
                            <th>จังหวัด</th>
                            <th>เงื่อนไขการชำระเงิน</th>
                        </tr>
                    </thead>
                    <tbody hx-get="/api/recent-market-orders?lang=th" hx-trigger="every 5s" hx-swap="innerHTML">
                        {{ range .orders }}
                        <tr>
                            <td>{{ .Order }}</td>
                            <td>{{ .Product }}</td>
                            <td>{{ .QuantityUnit }}</td>
                            <td>{{ .Quality }}</td>
                            <td>{{ .Packaging }}</td>
                            <td>{{ .Market }}</td>
                            <td>{{ .SubMarket }}</td>
                            <td>{{ .ContractType }}</td>
                            <td>{{ .DeliveryTerm }}</td>
                            <td>{{ .Country }}</td>
                            <td>{{ .ProvinceState }}</td>
                            <td>{{ .PaymentTerm }}</td>
                        </tr>
                        {{ end }}
                    </tbody>
                </table>
            </div>

            <div class="card" style="margin-top: 20px; padding-bottom: 50px;">
                <h2>รายการซื้อขายล่าสุด (ตลาดต่างประเทศ)</h2>
                <table id="transactions-table-global">
                    <thead>
                        <tr>
                            <th>คำสั่ง</th>
                            <th>สินค้า</th>
                            <th>ปริมาณ</th>
                            <th>คุณภาพ</th>
                            <th>บรรจุภัณฑ์</th>
                            <th>ตลาดหลัก</th>
                            <th>ตลาดย่อย</th>
                            <th>ประเภทสัญญา</th>
                            <th>เงื่อนไขการส่งมอบ</th>
                            <th>ภูมิภาค</th>
                            <th>ประเทศ</th>
                            <th>ท่าเรือต้นทาง</th>
                            <th>ท่าเรือปลายทาง</th>
                            <th>เงื่อนไขการชำระเงิน</th>
                        </tr>
                    </thead>
                    <tbody hx-get="/api/recent-market-orders-global?lang=th"
                           hx-trigger="every 5s"
                           hx-swap="innerHTML">
                        {{ range .globalOrders }}
                        <tr>
                            <td>{{ .Order }}</td>
                            <td>{{ .Product }}</td>
                            <td>{{ .QuantityUnit }}</td>
                            <td>{{ .Quality }}</td>
                            <td>{{ .Packaging }}</td>
                            <td>{{ .Market }}</td>
                            <td>{{ .SubMarket }}</td>
                            <td>{{ .ContractType }}</td>
                            <td>{{ .DeliveryTerm }}</td>
                            <td>{{ .Region }}</td>
                            <td>{{ .Country }}</td>
                            <td>{{ .PortOfLoading }}</td>
                            <td>{{ .PortOfDischarge }}</td>
                            <td>{{ .PaymentTerm }}</td>
                        </tr>
                        {{ end }}
                    </tbody>
                </table>
            </div>

            <script>
                // Initialize price chart
                const ctx = document.getElementById('priceChart').getContext('2d');
                const priceChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['7 days ago', '6 days ago', '5 days ago', '4 days ago', '3 days ago', '2 days ago', 'Yesterday', 'Today'],
                        datasets: [{
                            label: 'Woodchip price (THB/MT)',
                            data: [1451.86, 1495.87, 1344.16, 1243.89, 1250.67, 1350.02, 1290.78, 1250.67],
                            borderColor: 'rgb(75, 192, 192)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: false
                            }
                        }
                    }
                });
            </script>
        </div>
    </main>

    <footer class="footer">
        <br>
        <a href="/about?lang={{ .lang }}">เกี่ยวกับเรา</a> | <a href="/faqs?lang={{ .lang }}">คำถามพบบ่อย</a> | <a
            href="/contact?lang={{ .lang }}">ติดต่อเรา</a><br>
        <p>บริษัท ไบโอแมส เอ็กซ์เชนจ์ จำกัด</p>
        <br>
    </footer>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.index-nav-links');
        const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
        const dropdownContents = document.querySelectorAll('.dropdown-content');
        const body = document.body;

        // Mobile detection
        const isMobile = () => window.innerWidth <= 768;

        // Toggle hamburger menu
        if (hamburger && navLinks) {
            hamburger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle active classes
                hamburger.classList.toggle('active');
                navLinks.classList.toggle('active');

                // Manage body scroll
                if (navLinks.classList.contains('active')) {
                    body.style.overflow = 'hidden';
                } else {
                    body.style.overflow = '';

                    // Reset all dropdown triggers and contents
                    dropdownTriggers.forEach(trigger => {
                        trigger.classList.remove('active');
                    });

                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });
        }

        // Handle dropdown triggers in mobile view
        dropdownTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                if (isMobile()) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Remove active class from all triggers
                    dropdownTriggers.forEach(t => {
                        if (t !== this) {
                            t.classList.remove('active');
                        }
                    });

                    // Hide all dropdowns first
                    dropdownContents.forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });

                    // Toggle active class on current trigger
                    const wasActive = this.classList.contains('active');
                    this.classList.toggle('active');

                    // Show dropdown content if trigger is active
                    const content = this.nextElementSibling;
                    if (!wasActive) {
                        content.style.display = 'block';
                    }
                }
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!isMobile()) return;

            const isClickInsideNav = navLinks && navLinks.contains(event.target);
            const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

            if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset all dropdown triggers and contents
                dropdownTriggers.forEach(trigger => {
                    trigger.classList.remove('active');
                });

                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                // Reset styles for desktop view
                if (navLinks) navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset all dropdown triggers and contents
                dropdownTriggers.forEach(trigger => {
                    trigger.classList.remove('active');
                });

                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Add click event to regular nav links to close menu when clicked
        const regularLinks = navLinks.querySelectorAll('a:not(.dropdown-trigger)');
        regularLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (isMobile()) {
                    navLinks.classList.remove('active');
                    hamburger.classList.remove('active');
                    body.style.overflow = '';
                }
            });
        });
    });
    </script>
    {{ end }}