{{ define "content" }}
<nav class="navigator">
    <a href="/?lang={{ .lang }}" class="nav-brand">BiomassX</a>
    <a href="/order?lang={{ .lang }}">เสนอซื้อขาย</a>
    <a href="/markets?lang={{ .lang }}">ตลาด</a>
    <a href="/productspage?lang={{ .lang }}">สินค้า</a>
    <a href="/services?lang={{ .lang }}">บริการ</a>
    <a href="/login?lang={{ .lang }}">เข้าสู่ระบบ</a>
</nav>

<main class="main-dashboard">
  <h2>ตลาด</h2>

  <!-- Improved search bar with essential filters -->
  <div class="search-container">
    <div class="search-header">
      <h3>ค้นหาข้อมูลตลาด</h3>
      <p>เลือกสินค้าและใช้ตัวกรองเพื่อดูข้อมูลตลาด</p>
    </div>

    <div class="search-main">
      <!-- Product search with improved styling -->
      <div class="input-group">
        <label for="productSearch">สินค้า</label>
        <div class="search-input-wrapper">
          <input type="text" id="productSearch" class="form-control" placeholder="พิมพ์เพื่อค้นหาสินค้า...">
          <div id="selectedProductIndicator" class="selected-product-indicator" style="display: none;">✓</div>
        </div>
        <div id="searchResults" class="search-results"></div>
      </div>

      <!-- Improved filter layout -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-group">
            <label for="marketFilter">ตลาด</label>
            <select class="filter-select" id="marketFilter">
              <option value="">ตลาดทั้งหมด</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="localFilter">พื้นที่ตลาด</label>
            <select class="filter-select" id="localFilter">
              <option value="">พื้นที่ตลาดทั้งหมด</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="qualityFilter">คุณภาพ</label>
            <select class="filter-select" id="qualityFilter" disabled>
              <option value="">คุณภาพทั้งหมด</option>
            </select>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter-group">
            <label for="deliveryTermFilter">เงื่อนไขการส่งมอบ</label>
            <select class="filter-select" id="deliveryTermFilter">
              <option value="">เงื่อนไขการส่งมอบทั้งหมด</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="paymentTermFilter">เงื่อนไขการชำระเงิน</label>
            <select class="filter-select" id="paymentTermFilter">
              <option value="">เงื่อนไขการชำระเงินทั้งหมด</option>
            </select>
          </div>

          <div class="filter-group">
            <!-- Empty group for spacing -->
          </div>
        </div>
      </div>

      <!-- Search action button -->
      <div class="search-action">
        <button class="search-btn" id="searchSubmitBtn">ค้นหา</button>
      </div>
    </div>
  </div>

   <!-- Add product overview section after the Price History Analysis -->
  <div class="analytics-card product-overview-container">
    <h3>ภาพรวมเปลือกเมล็ดปาล์ม (PKS)</h3>
    <div class="product-details">
      <div class="product-header">
        <span class="product-market-tag">รายละเอียดสินค้า</span>
      </div>
      <div class="product-info-grid">
        <div class="product-info-item">
          <span class="info-label">ชื่อ:</span>
          <span class="info-value">เปลือกเมล็ดปาล์ม (PKS)</span>
        </div>
        <div class="product-info-item">
          <span class="info-label">ตลาด:</span>
          <span class="info-value">ไบโอแมส</span>
        </div>
        <div class="product-info-item">
          <span class="info-label">คุณภาพที่มี:</span>
          <span class="info-value">EN 14961-1 - มาตรฐาน
           <br> ISO 17225-1 - มาตรฐาน</span>
        </div>
      </div>
    </div>
  </div>

  <div class="analytics-card">
    <h3>ราคาปิดตลาด</h3>
    <h5>กำลังโหลดวันที่...</h5>

    <div class="time-filter-container">
      <button class="time-filter-btn active" data-days="1">1 วัน</button>
      <button class="time-filter-btn" data-days="7">1 สัปดาห์</button>
      <button class="time-filter-btn" data-days="30">1 เดือน</button>
      <button class="time-filter-btn" data-days="365">1 ปี</button>
    </div>

    <div class="futures-table-wrapper">
      <table class="futures-table">
        <thead>
          <tr>
            <th>ระยะเวลาสัญญา</th>
            <th>เปิด</th>
            <th>สูงสุด</th>
            <th>ต่ำสุด</th>
            <th>ปริมาณเสนอซื้อ</th>
            <th>ราคาเสนอซื้อ</th>
            <th>เปลี่ยนแปลง</th>
            <th>ส่วนต่าง</th>
            <th>เปิด</th>
            <th>สูงสุด</th>
            <th>ต่ำสุด</th>
            <th>ปริมาณเสนอขาย</th>
            <th>ราคาเสนอขาย</th>
            <th>เปลี่ยนแปลง</th>
            <th>ส่วนต่าง</th>
          </tr>
        </thead>
        <tbody>
          <tr>

          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="analytics-card">
    <h3>การวิเคราะห์ประวัติราคา</h3>
    <div class="chart-container">
      <canvas id="priceHistoryChart"></canvas>
      <div id="priceHistoryLoading" class="chart-loading" style="display: none;">
        <div class="spinner"></div>
        <p>กำลังโหลดข้อมูลราคา...</p>
      </div>
      <div id="priceHistoryNoData" class="chart-no-data" style="display: none;">
        <p>ไม่มีข้อมูลประวัติราคาสำหรับเงื่อนไขที่เลือก</p>
      </div>
    </div>
    <div class="time-filter">
      <button class="time-btn active" data-days="1">1ว</button>
      <button class="time-btn" data-days="7">1ส</button>
      <button class="time-btn" data-days="30">1ด</button>
      <button class="time-btn" data-days="365">1ป</button>
    </div>
  </div>

</main>

<footer class="footer">
  <br>
  <a href="/about?lang={{ .lang }}">เกี่ยวกับเรา</a> | <a href="/faqs?lang={{ .lang }}">คำถามพบบ่อย</a> | <a href="/contact?lang={{ .lang }}">ติดต่อเรา</a><br>
  <p>บริษัท ไบโอแมส เอ็กซ์เชนจ์ จำกัด</p>
  <br>
</footer>
{{ end }}