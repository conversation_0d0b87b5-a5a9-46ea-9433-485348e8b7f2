{{ define "content" }}
<style>
    .role-segment {
        margin-bottom: 1.5rem;
    }
    .role-segment h3 {
        font-size: 1.2rem;
        color: #333;
        margin-bottom: 0.5rem;
        border-bottom: 1px solid #ddd;
        padding-bottom: 0.3rem;
    }
    .role-segment label {
        display: block;
        margin: 0.3rem 0;
        font-size: 1rem;
    }
    .role-segment input[type="checkbox"] {
        margin-right: 0.5rem;
    }
    #roles-display ul {
        list-style: none;
        padding: 0;
    }
    #roles-display li {
        margin: 0.3rem 0;
        font-size: 0.95rem;
    }
    .success-message {
        color: green;
        font-weight: bold;
    }
    .error-message {
        color: red;
        font-weight: bold;
    }
    .address-types-summary {
        margin: 20px 0;
        padding: 15px;
        background-color: #f9f9f9;
        border-radius: 5px;
        border: 1px solid #eee;
    }
    .address-types-summary h3 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 1.1rem;
        color: #333;
    }
    .address-type-indicator {
        display: inline-block;
        margin-right: 15px;
        margin-bottom: 5px;
    }
    .address-type-indicator span {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.9rem;
        margin-right: 5px;
    }
    .address-type-indicator.has-address span {
        background-color: #e6f7ff;
        color: #0066cc;
        border: 1px solid #0066cc;
    }
    .address-type-indicator.no-address span {
        background-color: #f5f5f5;
        color: #999;
        border: 1px solid #ddd;
    }
    .address-type-status {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-left: 5px;
        vertical-align: middle;
    }
    .status-set {
        background-color: #52c41a;
    }
    .status-not-set {
        background-color: #f5222d;
    }
    .roles-section {
        background-color: #f8f8f8;
        border-radius: 4px;
        padding: 15px;
        margin: 20px 0;
        border: 1px solid #e0e0e0;
    }
    .roles-section h2 {
        margin-top: 0;
        color: #333;
        font-size: 1.2rem;
        margin-bottom: 15px;
    }
    .roles-section p {
        color: #555;
        margin-bottom: 15px;
        line-height: 1.4;
    }
    .roles-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }
    .role-segment {
        flex: 1;
        min-width: 200px;
        background-color: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 12px;
        margin-bottom: 10px;
    }
    .role-segment h3 {
        color: #333;
        font-size: 1rem;
        margin-top: 0;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #eee;
    }
    .role-segment ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .role-segment li {
        padding: 4px 0;
        font-size: 0.9rem;
        color: #444;
    }
    .role-tag {
        display: inline-block;
        background-color: #f0f0f0;
        color: #333;
        border-radius: 3px;
        padding: 3px 8px;
        margin: 2px 0;
        font-size: 0.85rem;
    }
    #roles-edit-modal .modal-content {
        max-width: 700px;
        width: 95%;
        border-radius: 3px;
        padding: 15px;
        overflow-y: auto;
        max-height: 85vh;
        margin: 5% auto;
    }
    .role-options-container {
        background-color: #fff;
        border-radius: 3px;
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid #e0e0e0;
    }
    .role-options-container h3 {
        color: #333;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 5px;
        margin-top: 0;
        font-size: 0.9rem;
        margin-bottom: 8px;
        font-weight: 600;
    }
    .role-options {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 6px;
    }
    .role-checkbox {
        display: flex;
        align-items: center;
        background-color: #f8f8f8;
        border-radius: 3px;
        padding: 4px 6px;
        transition: all 0.15s ease;
        cursor: pointer;
        border: 1px solid #e0e0e0;
        min-height: 30px; /* Smaller height */
    }
    .role-checkbox:hover {
        background-color: #f0f0f0;
        border-color: #ccc;
    }
    .role-checkbox input[type="checkbox"] {
        margin-right: 6px;
        min-width: 14px;
        min-height: 14px;
    }
    .role-label {
        font-size: 0.8rem;
        color: #444;
        line-height: 1.2;
    }
    .role-checkbox input[type="checkbox"]:checked + .role-label {
        font-weight: 500;
        color: #333;
    }
    .role-checkbox:has(input[type="checkbox"]:checked) {
        background-color: #f0f0f0;
        border-color: #ccc;
    }
    .no-roles-message {
        color: #555;
        font-size: 0.95rem;
        margin: 10px 0;
    }

    /* Mobile responsive styles for role edit */
    @media screen and (max-width: 480px) {
        .role-options {
            grid-template-columns: repeat(2, 1fr);
            gap: 5px;
        }

        .role-checkbox {
            padding: 4px 5px;
            min-height: 28px;
        }

        .role-checkbox input[type="checkbox"] {
            min-width: 14px;
            min-height: 14px;
            margin-right: 4px;
        }

        .role-label {
            font-size: 0.75rem;
        }
    }

    /* Small mobile responsive styles */
    @media screen and (max-width: 360px) {
        .role-options-container {
            padding: 8px;
            margin-bottom: 8px;
        }

        .role-options-container h3 {
            font-size: 0.85rem;
            padding-bottom: 4px;
            margin-bottom: 6px;
        }

        .role-options {
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
        }

        .role-checkbox {
            padding: 3px 4px;
            min-height: 26px;
        }

        .role-checkbox input[type="checkbox"] {
            min-width: 12px;
            min-height: 12px;
            margin-right: 3px;
        }

        .role-label {
            font-size: 0.7rem;
        }
    }
    #delete-address-modal .modal-content {
        max-width: 400px;
        border-radius: 4px;
    }
    #delete-address-modal .modal-content p {
        margin: 15px 0;
        font-size: 1rem;
        color: #333;
    }
    #delete-address-modal .button-group {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    #delete-address-modal .button-group button {
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 0.95rem;
        cursor: pointer;
        border: none;
    }
    #delete-address-modal .button-group .delete-btn {
        background-color: #f5222d;
        color: white;
    }
    #delete-address-modal .button-group .delete-btn:hover {
        background-color: #d61f26;
    }
    #delete-address-modal .button-group .cancel-btn {
        background-color: #f0f0f0;
        color: #333;
    }
    #delete-address-modal .button-group .cancel-btn:hover {
        background-color: #e0e0e0;
    }
</style>

<nav class="dash-nav">
    <div class="dash-nav-container">
        <a href="/?lang={{ .lang }}" class="dash-nav-brand">BiomassX</a>
        <div class="dash-nav-links">
            <a href="/dashboard?lang={{ .lang }}">หน้าหลักของฉัน</a>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">เสนอซื้อ/เสนอขาย</a>
                <div class="dropdown-content">
                    <a href="/order?lang={{ .lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">สมุดคำสั่งซื้อ/ขาย</a>
                <div class="dropdown-content">
                    <a href="/order_book?lang={{ .lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">สัญญาซื้อขาย</a>
                <div class="dropdown-content">
                    <a href="/contract?lang={{ .lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">ตั้งค่าใช้งาน</a>
                <div class="dropdown-content">
                    <a href="/profile?lang={{ .lang }}">ข้อมูลผู้ใช้</a>
                </div>
            </div>

            <a href="/logout?lang={{ .lang }}" hx-post="/logout?lang={{ .lang }}" hx-redirect="/login?lang={{ .lang }}">ออกจากระบบ</a>

            <!-- <div class="language-selector">
                <form hx-get="/profile" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">เลือกภาษา:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>ไทย</option>
                    </select>
                </form>
            </div> -->
        </div>
        <!-- <div class="desktop-lang-selector">
            <form hx-get="/profile" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                    <option value="en" {{ if eq .lang "en" }}selected{{ end }}>EN</option>
                    <option value="th" {{ if eq .lang "th" }}selected{{ end }}>ไทย</option>
                </select>
            </form>
        </div> -->
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="main-dashboard profile-page">
    <div id="response"></div>
    <div class="profile-container">
        <h2>ข้อมูลผู้ใช้</h2>

        <!-- User Information -->
        <div class="user-info">
            <p><strong>ชื่อ-นามสกุล:</strong>
                <span id="first_name">{{if .user.FirstName.Valid}}{{.user.FirstName.String}}{{end}}</span>
                <span id="last_name">{{if .user.LastName.Valid}}{{.user.LastName.String}}{{end}}</span>
            </p>
            <p><strong>อีเมล:</strong> <span id="email">{{if .user.Email.Valid}}{{.user.Email.String}}{{end}}</span></p>
            <p><strong>โทรศัพท์:</strong> <span id="phone">{{if .user.Phone.Valid}}{{.user.Phone.String}}{{end}}</span></p>
            <button class="edit-btn" onclick="toggleEdit('user')">แก้ไข</button>
        </div>

        <!-- Address Types Summary -->
        <div class="address-types-summary">
            <h3>ประเภทที่อยู่</h3>
            <div id="address-types-indicators">
                <p>กำลังโหลดประเภทที่อยู่...</p>
            </div>
        </div>

        <!-- Role Selection -->
        <h2>บทบาทผู้ใช้</h2>
        <div class="roles-section">
            <p>เลือกบทบาทของคุณในระบบ BiomassX:</p>
            <button class="edit-btn" onclick="toggleEdit('roles')">แก้ไขบทบาท</button>
            <div id="roles-display">
                <p>กำลังโหลดบทบาท...</p>
            </div>
        </div>

        <!-- Displaying Addresses -->
        {{if .user.Addresses}}
        <h2>ที่อยู่</h2>
        <div class="address-table-container">
            <table class="address-table">
                <thead>
                    <tr>
                        <th>ประเภทที่อยู่</th>
                        <th>สาขาที่</th>
                        <th>เลขประจำตัวผู้เสียภาษี</th>
                        <th>ที่อยู่</th>
                        <th>ถนน</th>
                        <th>ตำบล/แขวง</th>
                        <th>อำเภอ/เขต</th>
                        <th>จังหวัด</th>
                        <th>ประเทศ</th>
                        <th>รหัสไปรษณีย์</th>
                        <th>การดำเนินการ</th>
                    </tr>
                </thead>
                <tbody>
                    {{range $index, $address := .user.Addresses}}
                    <tr data-address-id="{{$address.ID.Int64}}">
                        <td>{{if $address.AddressType.ThName.Valid}}{{$address.AddressType.ThName.String}}{{end}}</td>
                        <td>{{if $address.BranchNumber.Valid}}{{$address.BranchNumber.String}}{{end}}</td>
                        <td>{{if $address.Taxnumber.Valid}}{{$address.Taxnumber.String}}{{end}}</td>
                        <td>{{if $address.Address.Valid}}{{$address.Address.String}}{{end}}</td>
                        <td>{{if $address.Street.Valid}}{{$address.Street.String}}{{end}}</td>
                        <td>{{if $address.Subdistrict.ThName.Valid}}{{$address.Subdistrict.ThName.String}}{{end}}</td>
                        <td>{{if $address.District.ThName.Valid}}{{$address.District.ThName.String}}{{end}}</td>
                        <td>{{if $address.Province.ThName.Valid}}{{$address.Province.ThName.String}}{{end}}</td>
                        <td>{{if $address.Country.EnName.Valid}}{{$address.Country.EnName.String}}{{end}}</td>
                        <td>{{if $address.PostalCode.Valid}}{{$address.PostalCode.String}}{{end}}</td>
                        <td>
                            <button class="edit-btn" onclick="toggleEdit('address-{{$index}}')">แก้ไข</button>
                            <button class="delete-btn" onclick="showDeleteConfirmation('{{$address.ID.Int64}}')">ลบข้อมูล</button>
                        </td>
                    </tr>
                    {{end}}
                </tbody>
            </table>
        </div>
        {{else}}
        <h3>ไม่มีที่อยู่</h3>
        {{end}}

        <!-- Add Address Button -->
        <button class="add-address-btn" onclick="toggleEdit('add-address')">เพิ่มที่อยู่</button>
    </div>

    <!-- User Edit Modal -->
    <div id="user-edit-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('user-edit-modal')">×</span>
            <h2>แก้ไขข้อมูลผู้ใช้</h2>
            <form id="profile-form">
                <input type="hidden" name="_csrf" value="{{.csrfToken}}">
                <div class="form-group">
                    <label for="first_name">ชื่อ:</label>
                    <input type="text" id="edit_first_name" name="firstName" value="{{if .user.FirstName.Valid}}{{.user.FirstName.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="last_name">นามสกุล:</label>
                    <input type="text" id="edit_last_name" name="lastName" value="{{if .user.LastName.Valid}}{{.user.LastName.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="organization_name">องค์กร:</label>
                    <input type="text" id="edit_organization_name" name="organizationName" value="{{if .user.Organization.Valid}}{{.user.Organization.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="username">ชื่อผู้ใช้:</label>
                    <input type="text" id="edit_username" name="username" value="{{if .user.Username.Valid}}{{.user.Username.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="email">อีเมล:</label>
                    <input type="email" id="edit_email" name="email" value="{{if .user.Email.Valid}}{{.user.Email.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="phone">โทรศัพท์:</label>
                    <input type="text" id="edit_phone" name="phone" value="{{if .user.Phone.Valid}}{{.user.Phone.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="password">รหัสผ่านใหม่:</label>
                    <input type="password" id="edit_password" name="password">
                    <small>เว้นว่างหากไม่ต้องการเปลี่ยนรหัสผ่าน</small>
                </div>
                <div class="form-group">
                    <label for="confirm_password">ยืนยันรหัสผ่านใหม่:</label>
                    <input type="password" id="edit_confirm_password" name="confirmPassword">
                </div>
                <div class="button-group">
                    <button type="button" onclick="saveUserProfile()">บันทึก</button>
                    <button type="button" onclick="closeModal('user-edit-modal')">ยกเลิก</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Roles Edit Modal -->
    <div id="roles-edit-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('roles-edit-modal')">×</span>
            <h2>แก้ไขบทบาท</h2>
            <form id="roles-form">
                <input type="hidden" name="_csrf" value="{{.csrfToken}}">
                <div id="roles-checkboxes">
                    <!-- Populated dynamically via JavaScript -->
                </div>
                <div class="button-group">
                    <button type="button" onclick="saveUserRoles()">บันทึก</button>
                    <button type="button" onclick="closeModal('roles-edit-modal')">ยกเลิก</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Address Modal -->
    <div id="add-address-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('add-address-modal')">×</span>
            <h2>เพิ่มที่อยู่</h2>
            <form id="add-address-form">
                <input type="hidden" name="_csrf" value="{{.csrfToken}}">
                <div class="form-group">
                    <label for="address_type_id">ประเภทที่อยู่:</label>
                    <select id="address_type_id" name="address_type_id" required>
                        <option value="">-- เลือกประเภทที่อยู่ --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="branch_number">สาขาที่:</label>
                    <input type="text" id="branch_number" name="branch_number" maxlength="5">
                </div>
                <div class="form-group">
                    <label for="tax_number">เลขประจำตัวผู้เสียภาษี:</label>
                    <input type="text" id="tax_number" name="tax_number" maxlength="13">
                </div>
                <div class="form-group">
                    <label for="country_id">ประเทศ:</label>
                    <select id="country_id" name="country_id" required>
                        <option value="">-- เลือกประเทศ --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="province_id">จังหวัด:</label>
                    <select id="province_id" name="province_id" required>
                        <option value="">-- เลือกจังหวัด --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="district_id">อำเภอ/เขต:</label>
                    <select id="district_id" name="district_id" required>
                        <option value="">-- เลือกอำเภอ/เขต --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="subdistrict_id">ตำบล/แขวง:</label>
                    <select id="subdistrict_id" name="subdistrict_id" required>
                        <option value="">-- เลือกตำบล/แขวง --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="street">ถนน:</label>
                    <input type="text" id="street" name="street">
                </div>
                <div class="form-group">
                    <label for="address">ที่อยู่:</label>
                    <textarea id="address" name="address" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="postal_code">รหัสไปรษณีย์:</label>
                    <input type="text" id="postal_code" name="postal_code" maxlength="5" required>
                </div>
                <div class="button-group">
                    <button type="button" onclick="saveAddress()">บันทึก</button>
                    <button type="button" onclick="closeModal('add-address-modal')">ยกเลิก</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Address Modal -->
    <div id="edit-address-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('edit-address-modal')">×</span>
            <h2>แก้ไขที่อยู่</h2>
            <form id="edit-address-form">
                <input type="hidden" name="_csrf" value="{{.csrfToken}}">
                <input type="hidden" id="edit_address_id" name="address_id">
                <div class="form-group">
                    <label for="edit_address_type_id">ประเภทที่อยู่:</label>
                    <select id="edit_address_type_id" name="address_type_id" required>
                        <option value="">-- เลือกประเภทที่อยู่ --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_branch_number">สาขาที่:</label>
                    <input type="text" id="edit_branch_number" name="branch_number" maxlength="5">
                </div>
                <div class="form-group">
                    <label for="edit_taxnumber">เลขประจำตัวผู้เสียภาษี:</label>
                    <input type="text" id="edit_taxnumber" name="tax_number" maxlength="13">
                </div>
                <div class="form-group">
                    <label for="edit_country_id">ประเทศ:</label>
                    <select id="edit_country_id" name="country_id" required>
                        <option value="">-- เลือกประเทศ --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_province_id">จังหวัด:</label>
                    <select id="edit_province_id" name="province_id" required>
                        <option value="">-- เลือกจังหวัด --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_district_id">อำเภอ/เขต:</label>
                    <select id="edit_district_id" name="district_id" required>
                        <option value="">-- เลือกอำเภอ/เขต --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_subdistrict_id">ตำบล/แขวง:</label>
                    <select id="edit_subdistrict_id" name="subdistrict_id" required>
                        <option value="">-- เลือกตำบล/แขวง --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_street">ถนน:</label>
                    <input type="text" id="edit_street" name="street">
                </div>
                <div class="form-group">
                    <label for="edit_address">ที่อยู่:</label>
                    <textarea id="edit_address" name="address" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="edit_postal_code">รหัสไปรษณีย์:</label>
                    <input type="text" id="edit_postal_code" name="postal_code" maxlength="5" required>
                </div>
                <div class="button-group">
                    <button type="button" onclick="updateAddress()">บันทึก</button>
                    <button type="button" onclick="closeModal('edit-address-modal')">ยกเลิก</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-address-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('delete-address-modal')">×</span>
            <h2>ยืนยันการลบ - BiomassX</h2>
            <p>คุณแน่ใจหรือไม่ว่าต้องการลบที่อยู่นี้?</p>
            <div class="button-group">
                <button type="button" class="delete-btn" onclick="confirmDeleteAddress()">ลบข้อมูล</button>
                <button type="button" class="cancel-btn" onclick="closeModal('delete-address-modal')">ยกเลิก</button>
            </div>
        </div>
    </div>
</main>

<script>
// Toggle edit modals
window.toggleEdit = function(formType) {
    if (formType === 'user') {
        document.getElementById('user-edit-modal').style.display = 'block';
    } else if (formType === 'roles') {
        document.getElementById('roles-edit-modal').style.display = 'block';
        loadRoles();
    } else if (formType === 'add-address') {
        document.getElementById('add-address-modal').style.display = 'block';
        loadAddressTypes();
        loadCountries();
    } else if (formType.startsWith('address-')) {
        const index = formType.split('-')[1];
        const addressRow = document.querySelectorAll('.address-table tbody tr')[parseInt(index)];
        const addressId = addressRow.getAttribute('data-address-id');
        document.getElementById('edit-address-modal').style.display = 'block';
        loadAddressForEditing(addressId);
    }
};

// Close modals and reset forms
window.closeModal = function(modalId) {
    const modal = document.getElementById(modalId);
    modal.style.display = 'none';
    if (modalId === 'add-address-modal') {
        document.getElementById('add-address-form').reset();
    } else if (modalId === 'edit-address-modal') {
        document.getElementById('edit-address-form').reset();
    } else if (modalId === 'roles-edit-modal') {
        document.getElementById('roles-form').reset();
    } else if (modalId === 'user-edit-modal') {
        document.getElementById('profile-form').reset();
    }
};

// Show messages
function showMessage(message, className) {
    const responseDiv = document.getElementById('response');
    responseDiv.className = className;
    responseDiv.textContent = message;
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setTimeout(() => {
        responseDiv.textContent = '';
        responseDiv.className = '';
    }, 3000);
}

// Load roles and populate checkboxes
function loadRoles() {
    const rolesContainer = document.getElementById('roles-checkboxes');
    rolesContainer.innerHTML = '<p>กำลังโหลดบทบาท...</p>';

    fetch('/api/segments-subsegments')
        .then(response => {
            if (!response.ok) throw new Error('ไม่สามารถโหลดข้อมูลกลุ่มและบทบาทย่อย');
            return response.json();
        })
        .then(data => {
            if (data.success && data.segments) {
                // Override segment_id for specific subsegments
                const subsegmentOverrides = {
                    11: { segment_id: 11, segment_name: 'ผู้ผลิต (ชีวมวล/เชื้อเพลิงชีวภาพ/พลังงานชีวภาพ)' },
                    29: { segment_id: 11, segment_name: 'ผู้ผลิต (ชีวมวล/เชื้อเพลิงชีวภาพ/พลังงานชีวภาพ)' }
                };

                // Apply overrides to subsegments
                data.subsegments = data.subsegments.map(sub => ({
                    ...sub,
                    segment_id: subsegmentOverrides[sub.id]?.segment_id || sub.segment_id
                }));

                let html = '<div class="roles-grid">';

                // Map English segment names to Thai
                const segmentNameMap = {
                    'Producer (biomass/biofuels/bioenergy)': 'ผู้ผลิต (ชีวมวล/เชื้อเพลิงชีวภาพ/พลังงานชีวภาพ)',
                    'Broker': 'นายหน้า',
                    'Consultant': 'ที่ปรึกษา',
                    'Consumer': 'ผู้บริโภค',
                    'Government': 'หน่วยงานรัฐ',
                    'Investor': 'นักลงทุน',
                    'Financial institution': 'สถาบันการเงิน',
                    'Service provider': 'ผู้ให้บริการ',
                    'Media': 'สื่อ',
                    'Association': 'สมาคม',
                    'Contractor': 'ผู้รับเหมา',
                    'Biomass producer': 'ผู้ผลิตวัตถุดิบชีวภาพ',
                    'Research': 'หน่วยงานวิจัย',
                    'Trader': 'ผู้ค้าคนกลาง',
                    'Waste processor': 'ผู้ให้บริการกำจัดกากอุตสาหกรรม',
                    'Other': 'อื่น ๆ',
                    'Non-governmental organization': 'องค์กรพัฒนาเอกชน',
                    'Logistics service (trasport/storage/shipping/port/inspection)': 'บริการโลจิสติกส์ (ขนส่ง/คลังสินค้า/การขนส่งทางเรือ/ท่าเรือ/การตรวจสอบ)'
                };

                data.segments.forEach(segment => {
                    const subsegments = data.subsegments.filter(sub => sub.segment_id === segment.id);
                    if (subsegments.length === 0) return;

                    // Use Thai name if available, otherwise try to translate from English name
                    let segmentDisplayName = segment.th_name;
                    if (!segmentDisplayName || segmentDisplayName.trim() === '') {
                        segmentDisplayName = segmentNameMap[segment.en_name] || segment.en_name || 'อื่น ๆ';
                    }

                    html += `
                        <div class="role-segment">
                            <h3>${segmentDisplayName}</h3>
                            <div class="role-options">
                    `;

                    // Map English subsegment names to Thai
                    const subsegmentNameMap = {
                        'Agricultural consumer': 'ผู้บริโภค (การเกษตร)',
                        'Association': 'สมาคม',
                        'Biomass producer': 'ผู้ผลิตวัตถุดิบชีวภาพ',
                        'Broker': 'นายหน้า',
                        'Certified body': 'ผู้ให้บริการรับรอง',
                        'Commercial consumer': 'ผู้บริโภค (ภาคพาณิชย์)',
                        'Consultant': 'ที่ปรึกษา',
                        'Container yard': 'ลานตู้คอนเทนเนอร์',
                        'EPC contractor': 'ผู้ให้บริการ EPC',
                        'Fumigator': 'ผู้ให้บริการรมยา',
                        'Gaseous biofuel producer': 'ผู้ผลิตเชื้อเพลิงชีวภาพก๊าช',
                        'Government agency': 'หน่วยงานรัฐ',
                        'Government consumer': 'ผู้บริโภค (ภาครัฐ)',
                        'Harvester': 'ผู้ให้บริการเก็บเกี่ยวพืชพลังงาน',
                        'Industrial consumer': 'ผู้บริโภค (อุตสาหกรรม)',
                        'Inspector and surveyor': 'ผู้ให้บริการตรวจสอบคุณภาพและปริมาณ',
                        'Investor': 'นักลงทุน',
                        'Landowner': 'เจ้าของที่ดิน',
                        'Lender and banker': 'ธนาคารและสถาบันการเงิน',
                        'Liquid biofuel producer': 'ผู้ผลิตเชื้อเพลิงชีวภาพเหลว',
                        'Media': 'สื่อ',
                        'O&M contractor': 'ผู้ให้บริการ O&M',
                        'Planting service provider': 'ผู้ให้บรการปลูกพืชพลังงาน',
                        'Port': 'ท่าเรือ',
                        'Power consumer': 'ผู้บริโภค (ภาคการผลิตไฟฟ้า)',
                        'Researcher': 'หน่วยงานวิจัย/นักวิจัย',
                        'Residential consumer': 'ผู้บริโภค (ภาคครัวเรือน)',
                        'Shipper and liner': 'ผู้ให้บริการ shipping/ขนส่งทางเรือ',
                        'Solid biofuel producer': 'ผู้ผลิตเชื้อเพลิงชีวภาพแข็ง',
                        'Technology and equipment supplier': 'ผู้จำหน่ายเครื่องจักร/อุปกรณ์',
                        'Testing and analysis provider': 'ผู้ให้บริการทดสอบวิเคราะห์',
                        'Trader': 'ผู้ค้าคนกลาง',
                        'Transportation consumer': 'ผู้บริโภค (ภาคขนส่ง)',
                        'Transporter': 'ผู้ให้บริการขนส่ง',
                        'Warehousing and storage provider': 'ผู้ให้บริการโกดัง/ไซโล',
                        'Waste processor': 'ผู้ให้บริการกำจัดกากอุตสาหกรรม',
                        'Wharf supplier': 'ผู้ให้บริการท่าเรือโป๊ะ'
                    };

                    subsegments.forEach(subsegment => {
                        // Use Thai name if available, otherwise try to translate from English name, or use English name as fallback
                        let displayName = subsegment.th_name;
                        if (!displayName || displayName.trim() === '') {
                            displayName = subsegmentNameMap[subsegment.en_name] || subsegment.en_name || 'ไม่ทราบ';
                        }

                        html += `
                            <label class="role-checkbox">
                                <input type="checkbox" name="subsegment_ids" value="${subsegment.id}"
                                        data-segment-id="${subsegment.segment_id}">
                                <span class="role-label">${displayName}</span>
                            </label>
                        `;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                });

                html += '</div>';
                rolesContainer.innerHTML = html;

                // Fetch existing user roles
                fetch('/api/user-roles')
                    .then(response => {
                        if (!response.ok) throw new Error('ไม่สามารถโหลดข้อมูลบทบาทผู้ใช้');
                        return response.json();
                    })
                    .then(rolesData => {
                        if (rolesData.success && rolesData.roles) {
                            rolesData.roles.forEach(role => {
                                const checkbox = document.querySelector(`input[name="subsegment_ids"][value="${role.subsegment_id}"]`);
                                if (checkbox) checkbox.checked = true;
                            });
                        }
                        updateRolesDisplay();
                    })
                    .catch(error => {
                        showMessage('เกิดข้อผิดพลาดในการโหลดข้อมูลบทบาทผู้ใช้: ' + error.message, 'error-message');
                    });
            }
        })
        .catch(error => {
            showMessage('เกิดข้อผิดพลาดในการโหลดข้อมูลบทบาท: ' + error.message, 'error-message');
        });
}

// Update displayed roles
function updateRolesDisplay() {
    const rolesDisplay = document.getElementById('roles-display');
    fetch('/api/user-roles')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.roles) {
                if (data.roles.length === 0) {
                    rolesDisplay.innerHTML = '<p class="no-roles-message">คุณยังไม่ได้เลือกบทบาทใด ๆ คลิก "แก้ไขบทบาท" เพื่อเพิ่มบทบาทของคุณ</p>';
                    return;
                }

                // Map English segment names to Thai
                const segmentNameMap = {
                    'Producer (biomass/biofuels/bioenergy)': 'ผู้ผลิต (ชีวมวล/เชื้อเพลิงชีวภาพ/พลังงานชีวภาพ)',
                    'Broker': 'นายหน้า',
                    'Consultant': 'ที่ปรึกษา',
                    'Consumer': 'ผู้บริโภค',
                    'Government': 'หน่วยงานรัฐ',
                    'Investor': 'นักลงทุน',
                    'Financial institution': 'สถาบันการเงิน',
                    'Service provider': 'ผู้ให้บริการ',
                    'Media': 'สื่อ',
                    'Association': 'สมาคม',
                    'Contractor': 'ผู้รับเหมา',
                    'Biomass producer': 'ผู้ผลิตวัตถุดิบชีวภาพ',
                    'Research': 'หน่วยงานวิจัย',
                    'Trader': 'ผู้ค้าคนกลาง',
                    'Waste processor': 'ผู้ให้บริการกำจัดกากอุตสาหกรรม',
                    'Other': 'อื่น ๆ',
                    'Non-governmental organization': 'องค์กรพัฒนาเอกชน',
                    'Logistics service (trasport/storage/shipping/port/inspection)': 'บริการโลจิสติกส์ (ขนส่ง/คลังสินค้า/การขนส่งทางเรือ/ท่าเรือ/การตรวจสอบ)'
                };

                // Override segment names for specific subsegments
                const segmentOverrides = {
                    11: 'ผู้ผลิต (ชีวมวล/เชื้อเพลิงชีวภาพ/พลังงานชีวภาพ)',
                    29: 'ผู้ผลิต (ชีวมวล/เชื้อเพลิงชีวภาพ/พลังงานชีวภาพ)'
                };

                // Map English subsegment names to Thai
                const subsegmentNameMap = {
                    'Agricultural consumer': 'ผู้บริโภค (การเกษตร)',
                    'Association': 'สมาคม',
                    'Biomass producer': 'ผู้ผลิตวัตถุดิบชีวภาพ',
                    'Broker': 'นายหน้า',
                    'Certified body': 'ผู้ให้บริการรับรอง',
                    'Commercial consumer': 'ผู้บริโภค (ภาคพาณิชย์)',
                    'Consultant': 'ที่ปรึกษา',
                    'Container yard': 'ลานตู้คอนเทนเนอร์',
                    'EPC contractor': 'ผู้ให้บริการ EPC',
                    'Fumigator': 'ผู้ให้บริการรมยา',
                    'Gaseous biofuel producer': 'ผู้ผลิตเชื้อเพลิงชีวภาพก๊าช',
                    'Government agency': 'หน่วยงานรัฐ',
                    'Government consumer': 'ผู้บริโภค (ภาครัฐ)',
                    'Harvester': 'ผู้ให้บริการเก็บเกี่ยวพืชพลังงาน',
                    'Industrial consumer': 'ผู้บริโภค (อุตสาหกรรม)',
                    'Inspector and surveyor': 'ผู้ให้บริการตรวจสอบคุณภาพและปริมาณ',
                    'Investor': 'นักลงทุน',
                    'Landowner': 'เจ้าของที่ดิน',
                    'Lender and banker': 'ธนาคารและสถาบันการเงิน',
                    'Liquid biofuel producer': 'ผู้ผลิตเชื้อเพลิงชีวภาพเหลว',
                    'Media': 'สื่อ',
                    'O&M contractor': 'ผู้ให้บริการ O&M',
                    'Planting service provider': 'ผู้ให้บรการปลูกพืชพลังงาน',
                    'Port': 'ท่าเรือ',
                    'Power consumer': 'ผู้บริโภค (ภาคการผลิตไฟฟ้า)',
                    'Researcher': 'หน่วยงานวิจัย/นักวิจัย',
                    'Residential consumer': 'ผู้บริโภค (ภาคครัวเรือน)',
                    'Shipper and liner': 'ผู้ให้บริการ shipping/ขนส่งทางเรือ',
                    'Solid biofuel producer': 'ผู้ผลิตเชื้อเพลิงชีวภาพแข็ง',
                    'Technology and equipment supplier': 'ผู้จำหน่ายเครื่องจักร/อุปกรณ์',
                    'Testing and analysis provider': 'ผู้ให้บริการทดสอบวิเคราะห์',
                    'Trader': 'ผู้ค้าคนกลาง',
                    'Transportation consumer': 'ผู้บริโภค (ภาคขนส่ง)',
                    'Transporter': 'ผู้ให้บริการขนส่ง',
                    'Warehousing and storage provider': 'ผู้ให้บริการโกดัง/ไซโล',
                    'Waste processor': 'ผู้ให้บริการกำจัดกากอุตสาหกรรม',
                    'Wharf supplier': 'ผู้ให้บริการท่าเรือโป๊ะ'
                };

                // Group roles by segment
                const rolesBySegment = {};
                data.roles.forEach(role => {
                    // First try to use the override for specific subsegment IDs
                    // Then try to use the Thai segment name from the API
                    // Then try to translate the English segment name to Thai
                    // Finally fall back to 'อื่น ๆ' (Other)
                    let segmentName = segmentOverrides[role.subsegment_id];

                    if (!segmentName && role.segment_th_name) {
                        segmentName = role.segment_th_name;
                    } else if (!segmentName && role.segment_name) {
                        segmentName = segmentNameMap[role.segment_name] || role.segment_name;
                    }

                    if (!segmentName) {
                        segmentName = 'อื่น ๆ';
                    }

                    if (!rolesBySegment[segmentName]) {
                        rolesBySegment[segmentName] = [];
                    }

                    // First try to use the Thai name from the API
                    // Then try to translate the English name using our mapping
                    // Finally fall back to the English name or 'ไม่ทราบ'
                    let subsegmentName = role.subsegment_th_name;
                    if (!subsegmentName || subsegmentName.trim() === '') {
                        subsegmentName = subsegmentNameMap[role.subsegment_name] || role.subsegment_name || 'ไม่ทราบ';
                    }

                    rolesBySegment[segmentName].push(subsegmentName);
                });

                let html = '<div class="roles-list">';

                for (const segment in rolesBySegment) {
                    html += `
                        <div class="role-segment">
                            <h3>${segment}</h3>
                            <ul>
                                ${rolesBySegment[segment].map(subsegment =>
                                    `<li><span class="role-tag">${subsegment}</span></li>`
                                ).join('')}
                            </ul>
                        </div>
                    `;
                }

                html += '</div>';
                rolesDisplay.innerHTML = html;
            }
        })
        .catch(error => {
            console.error('เกิดข้อผิดพลาดในการอัปเดตการแสดงบทบาท:', error);
            rolesDisplay.innerHTML = '<p class="error-message">เกิดข้อผิดพลาดในการโหลดข้อมูลบทบาท กรุณาลองใหม่</p>';
        });
}

// Save user roles
window.saveUserRoles = function() {
    const checkboxes = document.querySelectorAll('input[name="subsegment_ids"]:checked');
    const subsegmentIds = Array.from(checkboxes).map(cb => ({
        subsegment_id: parseInt(cb.value),
        segment_id: parseInt(cb.getAttribute('data-segment-id'))
    }));

    if (subsegmentIds.length === 0) {
        showMessage('กรุณาเลือกอย่างน้อยหนึ่งบทบาท', 'error-message');
        return;
    }

    const csrfToken = document.querySelector('#roles-form input[name="_csrf"]').value;
    const requestData = { roles: subsegmentIds, _csrf: csrfToken };

    fetch('/api/update-user-roles', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken
        },
        body: JSON.stringify(requestData),
        credentials: 'same-origin'
    })
        .then(response => {
            if (!response.ok) throw new Error('ไม่สามารถอัปเดตบทบาท: ' + response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('อัปเดตบทบาทเรียบร้อยแล้ว', 'success-message');
                closeModal('roles-edit-modal');
                updateRolesDisplay();
                triggerProfileCompletenessCheck();
            } else {
                throw new Error(data.message || 'ไม่สามารถอัปเดตบทบาท');
            }
        })
        .catch(error => {
            showMessage('เกิดข้อผิดพลาดในการอัปเดตบทบาท: ' + error.message, 'error-message');
        });
};

// Trigger profile completeness check
function triggerProfileCompletenessCheck() {
    const csrfToken = document.querySelector('#profile-form input[name="_csrf"]').value;
    fetch('/api/check-profile-completeness', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken
        },
        credentials: 'same-origin'
    })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.warn('การตรวจสอบความสมบูรณ์ของโปรไฟล์ล้มเหลว:', data.message);
            }
        })
        .catch(error => {
            console.error('เกิดข้อผิดพลาดในการตรวจสอบความสมบูรณ์ของโปรไฟล์:', error);
        });
}

// Validate profile form
function validateForm() {
    const password = document.getElementById('edit_password').value;
    const confirmPassword = document.getElementById('edit_confirm_password').value;
    if (password && password !== confirmPassword) {
        showMessage('รหัสผ่านไม่ตรงกัน กรุณาตรวจสอบอีกครั้ง', 'error-message');
        return false;
    }
    return true;
}

// Save user profile
window.saveUserProfile = function() {
    if (!validateForm()) return;

    const userData = {
        firstName: document.getElementById('edit_first_name').value,
        lastName: document.getElementById('edit_last_name').value,
        organizationName: document.getElementById('edit_organization_name').value,
        username: document.getElementById('edit_username').value,
        email: document.getElementById('edit_email').value,
        phone: document.getElementById('edit_phone').value,
        password: document.getElementById('edit_password').value,
        _csrf: document.querySelector('#profile-form input[name="_csrf"]').value
    };

    Object.keys(userData).forEach(key => {
        if (!userData[key] && key !== '_csrf') delete userData[key];
    });

    fetch('/api/update-profile', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': userData._csrf
        },
        body: JSON.stringify(userData),
        credentials: 'same-origin'
    })
        .then(response => {
            if (!response.ok) throw new Error('เซิร์ฟเวอร์ตอบกลับด้วยสถานะ: ' + response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('บันทึกข้อมูลส่วนตัวเรียบร้อยแล้ว', 'success-message');
                if (userData.firstName) document.getElementById('first_name').textContent = userData.firstName;
                if (userData.lastName) document.getElementById('last_name').textContent = userData.lastName;
                if (userData.email) document.getElementById('email').textContent = userData.email;
                if (userData.phone) document.getElementById('phone').textContent = userData.phone;
                closeModal('user-edit-modal');
                triggerProfileCompletenessCheck();
            } else {
                throw new Error(data.message || 'ไม่สามารถอัปเดตโปรไฟล์');
            }
        })
        .catch(error => {
            showMessage('เกิดข้อผิดพลาดในการบันทึกข้อมูล: ' + error.message, 'error-message');
        });
};

// Show delete confirmation modal
let addressIdToDelete = null;
window.showDeleteConfirmation = function(addressId) {
    addressIdToDelete = addressId;
    document.getElementById('delete-address-modal').style.display = 'block';
};

// Confirm address deletion
window.confirmDeleteAddress = function() {
    if (!addressIdToDelete) return;

    const requestData = {
        addressId: parseInt(addressIdToDelete),
        _csrf: document.querySelector('#profile-form input[name="_csrf"]').value
    };
    fetch('/api/delete-address', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': requestData._csrf
        },
        body: JSON.stringify(requestData),
        credentials: 'same-origin'
    })
        .then(response => {
            if (!response.ok) throw new Error('เซิร์ฟเวอร์ตอบกลับด้วยสถานะ: ' + response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('ลบที่อยู่เรียบร้อยแล้ว', 'success-message');
                const addressRow = document.querySelector(`tr[data-address-id="${addressIdToDelete}"]`);
                if (addressRow) addressRow.remove();
                updateAddressTypesSummary();
                closeModal('delete-address-modal');
                addressIdToDelete = null;
                triggerProfileCompletenessCheck();
            } else {
                throw new Error(data.message || 'ไม่สามารถลบที่อยู่');
            }
        })
        .catch(error => {
            showMessage('เกิดข้อผิดพลาดในการลบที่อยู่: ' + error.message, 'error-message');
            closeModal('delete-address-modal');
            addressIdToDelete = null;
        });
};

// Load address types
function loadAddressTypes() {
    const select = document.getElementById('address_type_id');
    select.innerHTML = '<option value="">-- เลือกประเภทที่อยู่ --</option>';
    select.disabled = true;

    fetch('/api/address-types')
        .then(response => {
            if (!response.ok) throw new Error('เกิดข้อผิดพลาดในการโหลดประเภทที่อยู่');
            return response.json();
        })
        .then(data => {
            if (data.success && data.addressTypes) {
                data.addressTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.thName;
                    select.appendChild(option);
                });
                select.disabled = false;
            }
        })
        .catch(error => {
            showMessage('ไม่สามารถโหลดข้อมูลประเภทที่อยู่ได้: ' + error.message, 'error-message');
        });
}

// Load countries
function loadCountries() {
    const select = document.getElementById('country_id');
    select.innerHTML = '<option value="">-- เลือกประเทศ --</option>';
    select.disabled = true;

    fetch('/api/countries')
        .then(response => {
            if (!response.ok) throw new Error('เกิดข้อผิดพลาดในการโหลดข้อมูลประเทศ');
            return response.json();
        })
        .then(data => {
            if (data.success && data.countries) {
                data.countries.forEach(country => {
                    const option = document.createElement('option');
                    option.value = country.id;
                    option.textContent = country.enName;
                    select.appendChild(option);
                });
                select.disabled = false;
            }
        })
        .catch(error => {
            showMessage('ไม่สามารถโหลดข้อมูลประเทศได้: ' + error.message, 'error-message');
        });
}

// Load provinces
function loadProvinces(countryId) {
    const provinceSelect = document.getElementById('province_id');
    const districtSelect = document.getElementById('district_id');
    const subdistrictSelect = document.getElementById('subdistrict_id');
    provinceSelect.innerHTML = '<option value="">-- เลือกจังหวัด --</option>';
    districtSelect.innerHTML = '<option value="">-- เลือกอำเภอ/เขต --</option>';
    subdistrictSelect.innerHTML = '<option value="">-- เลือกตำบล/แขวง --</option>';
    provinceSelect.disabled = true;
    districtSelect.disabled = true;
    subdistrictSelect.disabled = true;

    if (!countryId) return;

    fetch(`/api/provinces?countryId=${countryId}`)
        .then(response => {
            if (!response.ok) throw new Error('เกิดข้อผิดพลาดในการโหลดข้อมูลจังหวัด');
            return response.json();
        })
        .then(data => {
            if (data.success && data.provinces) {
                data.provinces.forEach(province => {
                    const option = document.createElement('option');
                    option.value = province.id;
                    option.textContent = province.thName;
                    provinceSelect.appendChild(option);
                });
                provinceSelect.disabled = false;
            }
        })
        .catch(error => {
            showMessage('ไม่สามารถโหลดข้อมูลจังหวัดได้: ' + error.message, 'error-message');
        });
}

// Load districts
function loadDistricts(provinceId) {
    const districtSelect = document.getElementById('district_id');
    const subdistrictSelect = document.getElementById('subdistrict_id');
    districtSelect.innerHTML = '<option value="">-- เลือกอำเภอ/เขต --</option>';
    subdistrictSelect.innerHTML = '<option value="">-- เลือกตำบล/แขวง --</option>';
    districtSelect.disabled = true;
    subdistrictSelect.disabled = true;

    if (!provinceId) return;

    fetch(`/api/districts?provinceId=${provinceId}`)
        .then(response => {
            if (!response.ok) throw new Error('เกิดข้อผิดพลาดในการโหลดข้อมูลอำเภอ/เขต');
            return response.json();
        })
        .then(data => {
            if (data.success && data.districts) {
                data.districts.forEach(district => {
                    const option = document.createElement('option');
                    option.value = district.id;
                    option.textContent = district.thName;
                    districtSelect.appendChild(option);
                });
                districtSelect.disabled = false;
            }
        })
        .catch(error => {
            showMessage('ไม่สามารถโหลดข้อมูลอำเภอ/เขตได้: ' + error.message, 'error-message');
        });
}

// Load subdistricts
function loadSubdistricts(districtId) {
    const subdistrictSelect = document.getElementById('subdistrict_id');
    subdistrictSelect.innerHTML = '<option value="">-- เลือกตำบล/แขวง --</option>';
    subdistrictSelect.disabled = true;

    if (!districtId) return;

    fetch(`/api/subdistricts?districtId=${districtId}`)
        .then(response => {
            if (!response.ok) throw new Error('เกิดข้อผิดพลาดในการโหลดข้อมูลตำบล/แขวง');
            return response.json();
        })
        .then(data => {
            if (data.success && data.subdistricts) {
                data.subdistricts.forEach(subdistrict => {
                    const option = document.createElement('option');
                    option.value = subdistrict.id;
                    option.textContent = subdistrict.thName;
                    subdistrictSelect.appendChild(option);
                });
                subdistrictSelect.disabled = false;
            }
        })
        .catch(error => {
            showMessage('ไม่สามารถโหลดข้อมูลตำบล/แขวงได้: ' + error.message, 'error-message');
        });
}

// Save address
function saveAddress() {
    const form = document.getElementById('add-address-form');
    if (!form.checkValidity()) {
        showMessage('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน', 'error-message');
        return;
    }
    const addressData = {
        address_type_id: parseInt(document.getElementById('address_type_id').value),
        branch_number: document.getElementById('branch_number').value.trim(),
        tax_number: document.getElementById('tax_number').value.trim(),
        country_id: parseInt(document.getElementById('country_id').value),
        province_id: parseInt(document.getElementById('province_id').value),
        district_id: parseInt(document.getElementById('district_id').value),
        subdistrict_id: parseInt(document.getElementById('subdistrict_id').value),
        street: document.getElementById('street').value.trim(),
        address: document.getElementById('address').value.trim(),
        postal_code: document.getElementById('postal_code').value.trim(),
        _csrf: document.querySelector('#add-address-form input[name="_csrf"]').value
    };
    fetch('/api/add-address', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': addressData._csrf
        },
        body: JSON.stringify(addressData),
        credentials: 'same-origin'
    })
        .then(response => {
            if (!response.ok) throw new Error('เซิร์ฟเวอร์ตอบกลับด้วยสถานะ: ' + response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('เพิ่มที่อยู่เรียบร้อยแล้ว', 'success-message');
                closeModal('add-address-modal');
                setTimeout(() => window.location.reload(), 1500);
                triggerProfileCompletenessCheck();
            } else {
                throw new Error(data.message || 'ไม่สามารถเพิ่มที่อยู่');
            }
        })
        .catch(error => {
            showMessage('เกิดข้อผิดพลาดในการเพิ่มที่อยู่: ' + error.message, 'error-message');
        });
}

// Load address types for edit form
function loadAddressTypesForEdit() {
    const select = document.getElementById('edit_address_type_id');
    select.innerHTML = '<option value="">-- เลือกประเภทที่อยู่ --</option>';
    select.disabled = true;

    fetch('/api/address-types')
        .then(response => {
            if (!response.ok) throw new Error('เกิดข้อผิดพลาดในการโหลดประเภทที่อยู่');
            return response.json();
        })
        .then(data => {
            if (data.success && data.addressTypes) {
                data.addressTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.thName;
                    select.appendChild(option);
                });
                select.disabled = false;
            }
        })
        .catch(error => {
            showMessage('ไม่สามารถโหลดข้อมูลประเภทที่อยู่ได้: ' + error.message, 'error-message');
        });
}

// Load countries for edit form
function loadCountriesForEdit() {
    const select = document.getElementById('edit_country_id');
    select.innerHTML = '<option value="">-- เลือกประเทศ --</option>';
    select.disabled = true;

    fetch('/api/countries')
        .then(response => {
            if (!response.ok) throw new Error('เกิดข้อผิดพลาดในการโหลดข้อมูลประเทศ');
            return response.json();
        })
        .then(data => {
            if (data.success && data.countries) {
                data.countries.forEach(country => {
                    const option = document.createElement('option');
                    option.value = country.id;
                    option.textContent = country.enName;
                    select.appendChild(option);
                });
                select.disabled = false;
            }
        })
        .catch(error => {
            showMessage('ไม่สามารถโหลดข้อมูลประเทศได้: ' + error.message, 'error-message');
        });
}

// Load provinces for edit form
function loadProvincesForEdit(countryId) {
    return new Promise((resolve, reject) => {
        const provinceSelect = document.getElementById('edit_province_id');
        const districtSelect = document.getElementById('edit_district_id');
        const subdistrictSelect = document.getElementById('edit_subdistrict_id');
        provinceSelect.innerHTML = '<option value="">-- เลือกจังหวัด --</option>';
        districtSelect.innerHTML = '<option value="">-- เลือกอำเภอ/เขต --</option>';
        subdistrictSelect.innerHTML = '<option value="">-- เลือกตำบล/แขวง --</option>';
        provinceSelect.disabled = true;
        districtSelect.disabled = true;
        subdistrictSelect.disabled = true;

        if (!countryId) {
            resolve();
            return;
        }

        fetch(`/api/provinces?countryId=${countryId}`)
            .then(response => {
                if (!response.ok) throw new Error('เกิดข้อผิดพลาดในการโหลดข้อมูลจังหวัด');
                return response.json();
            })
            .then(data => {
                if (data.success && data.provinces) {
                    data.provinces.forEach(province => {
                        const option = document.createElement('option');
                        option.value = province.id;
                        option.textContent = province.thName;
                        provinceSelect.appendChild(option);
                    });
                    provinceSelect.disabled = false;
                    resolve();
                }
            })
            .catch(error => {
                showMessage('ไม่สามารถโหลดข้อมูลจังหวัดได้: ' + error.message, 'error-message');
                reject(error);
            });
    });
}

// Load districts for edit form
function loadDistrictsForEdit(provinceId) {
    return new Promise((resolve, reject) => {
        const districtSelect = document.getElementById('edit_district_id');
        const subdistrictSelect = document.getElementById('edit_subdistrict_id');
        districtSelect.innerHTML = '<option value="">-- เลือกอำเภอ/เขต --</option>';
        subdistrictSelect.innerHTML = '<option value="">-- เลือกตำบล/แขวง --</option>';
        districtSelect.disabled = true;
        subdistrictSelect.disabled = true;

        if (!provinceId) {
            resolve();
            return;
        }

        fetch(`/api/districts?provinceId=${provinceId}`)
            .then(response => {
                if (!response.ok) throw new Error('เกิดข้อผิดพลาดในการโหลดข้อมูลอำเภอ/เขต');
                return response.json();
            })
            .then(data => {
                if (data.success && data.districts) {
                    data.districts.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.id;
                        option.textContent = district.thName;
                        districtSelect.appendChild(option);
                    });
                    districtSelect.disabled = false;
                    resolve();
                }
            })
            .catch(error => {
                showMessage('ไม่สามารถโหลดข้อมูลอำเภอ/เขตได้: ' + error.message, 'error-message');
                reject(error);
            });
    });
}

// Load subdistricts for edit form
function loadSubdistrictsForEdit(districtId) {
    return new Promise((resolve, reject) => {
        const subdistrictSelect = document.getElementById('edit_subdistrict_id');
        subdistrictSelect.innerHTML = '<option value="">-- เลือกตำบล/แขวง --</option>';
        subdistrictSelect.disabled = true;

        if (!districtId) {
            resolve();
            return;
        }

        fetch(`/api/subdistricts?districtId=${districtId}`)
            .then(response => {
                if (!response.ok) throw new Error('เกิดข้อผิดพลาดในการโหลดข้อมูลตำบล/แขวง');
                return response.json();
            })
            .then(data => {
                if (data.success && data.subdistricts) {
                    data.subdistricts.forEach(subdistrict => {
                        const option = document.createElement('option');
                        option.value = subdistrict.id;
                        option.textContent = subdistrict.thName;
                        subdistrictSelect.appendChild(option);
                    });
                    subdistrictSelect.disabled = false;
                    resolve();
                }
            })
            .catch(error => {
                showMessage('ไม่สามารถโหลดข้อมูลตำบล/แขวงได้: ' + error.message, 'error-message');
                reject(error);
            });
    });
}

// Load address for editing
function loadAddressForEditing(addressId) {
    document.getElementById('edit_address_id').value = addressId;

    const addressRow = document.querySelector(`tr[data-address-id="${addressId}"]`);
    if (!addressRow) {
        showMessage('ไม่พบข้อมูลที่อยู่', 'error-message');
        return;
    }

    const cells = addressRow.querySelectorAll('td');

    loadAddressTypesForEdit();
    loadCountriesForEdit();

    setTimeout(() => {
        const addressTypeText = cells[0].textContent.trim();
        const addressTypeSelect = document.getElementById('edit_address_type_id');
        for (let i = 0; i < addressTypeSelect.options.length; i++) {
            if (addressTypeSelect.options[i].text === addressTypeText) {
                addressTypeSelect.value = addressTypeSelect.options[i].value;
                break;
            }
        }

        document.getElementById('edit_branch_number').value = cells[1].textContent.trim();
        document.getElementById('edit_taxnumber').value = cells[2].textContent.trim();

        document.getElementById('edit_address').value = cells[3].textContent.trim();
        document.getElementById('edit_street').value = cells[4].textContent.trim();

        document.getElementById('edit_postal_code').value = cells[9].textContent.trim();

        const countryText = cells[8].textContent.trim();
        const provinceText = cells[7].textContent.trim();
        const districtText = cells[6].textContent.trim();
        const subdistrictText = cells[5].textContent.trim();

        const countrySelect = document.getElementById('edit_country_id');
        for (let i = 0; i < countrySelect.options.length; i++) {
            if (countrySelect.options[i].text === countryText) {
                countrySelect.value = countrySelect.options[i].value;
                break;
            }
        }

        if (countrySelect.value) {
            loadProvincesForEdit(countrySelect.value)
                .then(() => {
                    const provinceSelect = document.getElementById('edit_province_id');
                    for (let i = 0; i < provinceSelect.options.length; i++) {
                        if (provinceSelect.options[i].text === provinceText) {
                            provinceSelect.value = provinceSelect.options[i].value;
                            break;
                        }
                    }

                    if (provinceSelect.value) {
                        return loadDistrictsForEdit(provinceSelect.value);
                    }
                })
                .then(() => {
                    const districtSelect = document.getElementById('edit_district_id');
                    for (let i = 0; i < districtSelect.options.length; i++) {
                        if (districtSelect.options[i].text === districtText) {
                            districtSelect.value = districtSelect.options[i].value;
                            break;
                        }
                    }

                    if (districtSelect.value) {
                        return loadSubdistrictsForEdit(districtSelect.value);
                    }
                })
                .then(() => {
                    const subdistrictSelect = document.getElementById('edit_subdistrict_id');
                    for (let i = 0; i < subdistrictSelect.options.length; i++) {
                        if (subdistrictSelect.options[i].text === subdistrictText) {
                            subdistrictSelect.value = subdistrictSelect.options[i].value;
                            break;
                        }
                    }
                })
                .catch(error => {
                    console.error('เกิดข้อผิดพลาดในการตั้งค่าตัวเลือกสถานที่:', error);
                });
        }
    }, 500);
}

// Update address
function updateAddress() {
    const form = document.getElementById('edit-address-form');
    if (!form.checkValidity()) {
        showMessage('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน', 'error-message');
        return;
    }
    const addressData = {
        address_id: parseInt(document.getElementById('edit_address_id').value),
        address_type_id: parseInt(document.getElementById('edit_address_type_id').value),
        branch_number: document.getElementById('edit_branch_number').value.trim(),
        tax_number: document.getElementById('edit_taxnumber').value.trim(),
        country_id: parseInt(document.getElementById('edit_country_id').value),
        province_id: parseInt(document.getElementById('edit_province_id').value),
        district_id: parseInt(document.getElementById('edit_district_id').value),
        subdistrict_id: parseInt(document.getElementById('edit_subdistrict_id').value),
        street: document.getElementById('edit_street').value.trim(),
        address: document.getElementById('edit_address').value.trim(),
        postal_code: document.getElementById('edit_postal_code').value.trim(),
        _csrf: document.querySelector('#edit-address-form input[name="_csrf"]').value
    };
    fetch('/api/update-address', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': addressData._csrf
        },
        body: JSON.stringify(addressData),
        credentials: 'same-origin'
    })
        .then(response => {
            if (!response.ok) throw new Error('เซิร์ฟเวอร์ตอบกลับด้วยสถานะ: ' + response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('แก้ไขที่อยู่เรียบร้อยแล้ว', 'success-message');
                closeModal('edit-address-modal');
                setTimeout(() => window.location.reload(), 1500);
                triggerProfileCompletenessCheck();
            } else {
                throw new Error(data.message || 'ไม่สามารถอัปเดตที่อยู่');
            }
        })
        .catch(error => {
            showMessage('เกิดข้อผิดพลาดในการแก้ไขที่อยู่: ' + error.message, 'error-message');
        });
}

// Event listeners for cascading dropdowns
document.addEventListener('DOMContentLoaded', function() {
    const countrySelect = document.getElementById('country_id');
    if (countrySelect) {
        countrySelect.addEventListener('change', function() {
            loadProvinces(this.value);
        });
    }

    const provinceSelect = document.getElementById('province_id');
    if (provinceSelect) {
        provinceSelect.addEventListener('change', function() {
            loadDistricts(this.value);
        });
    }

    const districtSelect = document.getElementById('district_id');
    if (districtSelect) {
        districtSelect.addEventListener('change', function() {
            loadSubdistricts(this.value);
        });
    }

    const editCountrySelect = document.getElementById('edit_country_id');
    if (editCountrySelect) {
        editCountrySelect.addEventListener('change', function() {
            loadProvincesForEdit(this.value);
        });
    }

    const editProvinceSelect = document.getElementById('edit_province_id');
    if (editProvinceSelect) {
        editProvinceSelect.addEventListener('change', function() {
            loadDistrictsForEdit(this.value);
        });
    }

    const editDistrictSelect = document.getElementById('edit_district_id');
    if (editDistrictSelect) {
        editDistrictSelect.addEventListener('change', function() {
            loadSubdistrictsForEdit(this.value);
        });
    }
});

// Function to update address types summary
function updateAddressTypesSummary() {
    const indicatorsContainer = document.getElementById('address-types-indicators');
    if (!indicatorsContainer) return;

    const addressTable = document.querySelector('.address-table');
    if (!addressTable) {
        const addressTypes = {
            1: { name: 'ที่อยู่ออกใบเสร็จ', count: 0 },
            2: { name: 'ที่อยู่สาขา', count: 0 },
            3: { name: 'ที่อยู่จัดส่ง', count: 0 }
        };

        updateAddressTypesDisplay(addressTypes);
        return;
    }

    const addressRows = addressTable.querySelectorAll('tbody tr');
    const addressTypes = {
        1: { name: 'ที่อยู่ออกใบเสร็จ', count: 0 },
        2: { name: 'ที่อยู่สาขา', count: 0 },
        3: { name: 'ที่อยู่จัดส่ง', count: 0 }
    };

    // Get all address types from the database
    fetch('/api/address-types')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.addressTypes) {
                // Create a mapping of address type IDs to names
                const addressTypeMap = {};
                data.addressTypes.forEach(type => {
                    addressTypeMap[type.id] = type.thName;
                });

                // Count addresses by type
                addressRows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length > 0) {
                        const addressTypeText = cells[0].textContent.trim();

                        // Check for billing address
                        if (addressTypeText.includes('ออกใบเสร็จ') ||
                            addressTypeText.toLowerCase().includes('billing') ||
                            addressTypeText === addressTypeMap[1]) {
                            addressTypes[1].count++;
                        }

                        // Check for branch address
                        if (addressTypeText.includes('สาขา') ||
                            addressTypeText.toLowerCase().includes('branch') ||
                            addressTypeText === addressTypeMap[2]) {
                            addressTypes[2].count++;
                        }

                        // Check for shipping address
                        if (addressTypeText.includes('จัดส่ง') ||
                            addressTypeText.toLowerCase().includes('shipping') ||
                            addressTypeText === addressTypeMap[3]) {
                            addressTypes[3].count++;
                        }
                    }
                });

                updateAddressTypesDisplay(addressTypes);
            }
        })
        .catch(error => {
            console.error('Error fetching address types:', error);
            // Fallback to basic checking if API fails
            addressRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    const addressTypeText = cells[0].textContent.trim();

                    if (addressTypeText.includes('ออกใบเสร็จ') || addressTypeText.toLowerCase().includes('billing')) {
                        addressTypes[1].count++;
                    } else if (addressTypeText.includes('สาขา') || addressTypeText.toLowerCase().includes('branch')) {
                        addressTypes[2].count++;
                    } else if (addressTypeText.includes('จัดส่ง') || addressTypeText.toLowerCase().includes('shipping')) {
                        addressTypes[3].count++;
                    }
                }
            });

            updateAddressTypesDisplay(addressTypes);
        });
}

// Helper function to update the address types display
function updateAddressTypesDisplay(addressTypes) {
    const indicatorsContainer = document.getElementById('address-types-indicators');
    if (!indicatorsContainer) return;

    let html = '';

    for (const typeId in addressTypes) {
        const type = addressTypes[typeId];
        const hasAddress = type.count > 0;
        const statusClass = hasAddress ? 'status-set' : 'status-not-set';
        const indicatorClass = hasAddress ? 'has-address' : 'no-address';

        html += `
            <div class="address-type-indicator ${indicatorClass}">
                <span>${type.name}</span>
                <div class="address-type-status ${statusClass}" title="${hasAddress ? 'ตั้งค่าแล้ว' : 'ยังไม่ได้ตั้งค่า'}"></div>
                ${hasAddress ? ` (${type.count})` : ''}
            </div>
        `;
    }

    indicatorsContainer.innerHTML = html;
}

// Function to load and display user roles
function loadAndDisplayUserRoles() {
    const rolesDisplay = document.getElementById('roles-display');
    if (!rolesDisplay) return;

    fetch('/api/user-roles')
        .then(response => {
            if (!response.ok) throw new Error('ไม่สามารถโหลดข้อมูลบทบาทผู้ใช้');
            return response.json();
        })
        .then(data => {
            if (data.success) {
                if (!data.roles || data.roles.length === 0) {
                    rolesDisplay.innerHTML = '<p class="no-roles-message">คุณยังไม่ได้เลือกบทบาทใด ๆ คลิก "แก้ไขบทบาท" เพื่อเพิ่มบทบาทของคุณ</p>';
                    return;
                }

                // Map English segment names to Thai
                const segmentNameMap = {
                    'Producer (biomass/biofuels/bioenergy)': 'ผู้ผลิต (ชีวมวล/เชื้อเพลิงชีวภาพ/พลังงานชีวภาพ)',
                    'Broker': 'นายหน้า',
                    'Consultant': 'ที่ปรึกษา',
                    'Consumer': 'ผู้บริโภค',
                    'Government': 'หน่วยงานรัฐ',
                    'Investor': 'นักลงทุน',
                    'Financial institution': 'สถาบันการเงิน',
                    'Service provider': 'ผู้ให้บริการ',
                    'Media': 'สื่อ',
                    'Association': 'สมาคม',
                    'Contractor': 'ผู้รับเหมา',
                    'Biomass producer': 'ผู้ผลิตวัตถุดิบชีวภาพ',
                    'Research': 'หน่วยงานวิจัย',
                    'Trader': 'ผู้ค้าคนกลาง',
                    'Waste processor': 'ผู้ให้บริการกำจัดกากอุตสาหกรรม',
                    'Other': 'อื่น ๆ',
                    'Non-governmental organization': 'องค์กรพัฒนาเอกชน',
                    'Logistics service (trasport/storage/shipping/port/inspection)': 'บริการโลจิสติกส์ (ขนส่ง/คลังสินค้า/การขนส่งทางเรือ/ท่าเรือ/การตรวจสอบ)'
                };

                // Override segment names for specific subsegments
                const segmentOverrides = {
                    11: 'ผู้ผลิต (ชีวมวล/เชื้อเพลิงชีวภาพ/พลังงานชีวภาพ)',
                    29: 'ผู้ผลิต (ชีวมวล/เชื้อเพลิงชีวภาพ/พลังงานชีวภาพ)'
                };

                // Map English subsegment names to Thai
                const subsegmentNameMap = {
                    'Agricultural consumer': 'ผู้บริโภค (การเกษตร)',
                    'Association': 'สมาคม',
                    'Biomass producer': 'ผู้ผลิตวัตถุดิบชีวภาพ',
                    'Broker': 'นายหน้า',
                    'Certified body': 'ผู้ให้บริการรับรอง',
                    'Commercial consumer': 'ผู้บริโภค (ภาคพาณิชย์)',
                    'Consultant': 'ที่ปรึกษา',
                    'Container yard': 'ลานตู้คอนเทนเนอร์',
                    'EPC contractor': 'ผู้ให้บริการ EPC',
                    'Fumigator': 'ผู้ให้บริการรมยา',
                    'Gaseous biofuel producer': 'ผู้ผลิตเชื้อเพลิงชีวภาพก๊าช',
                    'Government agency': 'หน่วยงานรัฐ',
                    'Government consumer': 'ผู้บริโภค (ภาครัฐ)',
                    'Harvester': 'ผู้ให้บริการเก็บเกี่ยวพืชพลังงาน',
                    'Industrial consumer': 'ผู้บริโภค (อุตสาหกรรม)',
                    'Inspector and surveyor': 'ผู้ให้บริการตรวจสอบคุณภาพและปริมาณ',
                    'Investor': 'นักลงทุน',
                    'Landowner': 'เจ้าของที่ดิน',
                    'Lender and banker': 'ธนาคารและสถาบันการเงิน',
                    'Liquid biofuel producer': 'ผู้ผลิตเชื้อเพลิงชีวภาพเหลว',
                    'Media': 'สื่อ',
                    'O&M contractor': 'ผู้ให้บริการ O&M',
                    'Planting service provider': 'ผู้ให้บรการปลูกพืชพลังงาน',
                    'Port': 'ท่าเรือ',
                    'Power consumer': 'ผู้บริโภค (ภาคการผลิตไฟฟ้า)',
                    'Researcher': 'หน่วยงานวิจัย/นักวิจัย',
                    'Residential consumer': 'ผู้บริโภค (ภาคครัวเรือน)',
                    'Shipper and liner': 'ผู้ให้บริการ shipping/ขนส่งทางเรือ',
                    'Solid biofuel producer': 'ผู้ผลิตเชื้อเพลิงชีวภาพแข็ง',
                    'Technology and equipment supplier': 'ผู้จำหน่ายเครื่องจักร/อุปกรณ์',
                    'Testing and analysis provider': 'ผู้ให้บริการทดสอบวิเคราะห์',
                    'Trader': 'ผู้ค้าคนกลาง',
                    'Transportation consumer': 'ผู้บริโภค (ภาคขนส่ง)',
                    'Transporter': 'ผู้ให้บริการขนส่ง',
                    'Warehousing and storage provider': 'ผู้ให้บริการโกดัง/ไซโล',
                    'Waste processor': 'ผู้ให้บริการกำจัดกากอุตสาหกรรม',
                    'Wharf supplier': 'ผู้ให้บริการท่าเรือโป๊ะ'
                };

                // Group roles by segment
                const rolesBySegment = {};
                data.roles.forEach(role => {
                    // First try to use the override for specific subsegment IDs
                    // Then try to use the Thai segment name from the API
                    // Then try to translate the English segment name to Thai
                    // Finally fall back to 'อื่น ๆ' (Other)
                    let segmentName = segmentOverrides[role.subsegment_id];

                    if (!segmentName && role.segment_th_name) {
                        segmentName = role.segment_th_name;
                    } else if (!segmentName && role.segment_name) {
                        segmentName = segmentNameMap[role.segment_name] || role.segment_name;
                    }

                    if (!segmentName) {
                        segmentName = 'อื่น ๆ';
                    }

                    if (!rolesBySegment[segmentName]) {
                        rolesBySegment[segmentName] = [];
                    }

                    // First try to use the Thai name from the API
                    // Then try to translate the English name using our mapping
                    // Finally fall back to the English name or 'ไม่ทราบ'
                    let subsegmentName = role.subsegment_th_name;
                    if (!subsegmentName || subsegmentName.trim() === '') {
                        subsegmentName = subsegmentNameMap[role.subsegment_name] || role.subsegment_name || 'ไม่ทราบ';
                    }

                    rolesBySegment[segmentName].push(subsegmentName);
                });

                let html = '<div class="roles-list">';

                for (const segment in rolesBySegment) {
                    html += `
                        <div class="role-segment">
                            <h3>${segment}</h3>
                            <ul>
                                ${rolesBySegment[segment].map(subsegment =>
                                    `<li><span class="role-tag">${subsegment}</span></li>`
                                ).join('')}
                            </ul>
                        </div>
                    `;
                }

                html += '</div>';
                rolesDisplay.innerHTML = html;
            } else {
                rolesDisplay.innerHTML = '<p class="error-message">เกิดข้อผิดพลาดในการโหลดข้อมูลบทบาท กรุณาลองใหม่</p>';
            }
        })
        .catch(error => {
            console.error('เกิดข้อผิดพลาดในการโหลดข้อมูลบทบาทผู้ใช้:', error);
            rolesDisplay.innerHTML = '<p class="error-message">เกิดข้อผิดพลาดในการโหลดข้อมูลบทบาท กรุณาลองใหม่</p>';
        });
}

// Call the function when the page loads
document.addEventListener('DOMContentLoaded', function() {
    loadAndDisplayUserRoles();
    updateAddressTypesSummary();

    // Elements
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.dash-nav-links');
    const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
    const dropdownContents = document.querySelectorAll('.dropdown-content');
    const body = document.body;

    // Mobile detection
    const isMobile = () => window.innerWidth <= 768;

    // Toggle hamburger menu
    if (hamburger && navLinks) {
        hamburger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Toggle active classes
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');

            // Manage body scroll
            if (navLinks.classList.contains('active')) {
                body.style.overflow = 'hidden';

                // Show all dropdown contents in mobile view
                if (isMobile()) {
                    dropdownContents.forEach(content => {
                        content.style.display = 'block';
                    });
                }
            } else {
                body.style.overflow = '';

                // Hide all dropdown contents when closing menu
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });
    }

    // Handle dropdown triggers in mobile view
    dropdownTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            if (isMobile()) {
                e.preventDefault();
                e.stopPropagation();

                const content = this.nextElementSibling;
                const isVisible = content.style.display === 'block';

                // Hide all dropdowns first
                dropdownContents.forEach(dropdown => {
                    dropdown.style.display = 'none';
                });

                // Toggle current dropdown
                if (!isVisible) {
                    content.style.display = 'block';
                }
            }
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        if (!isMobile()) return;

        const isClickInsideNav = navLinks && navLinks.contains(event.target);
        const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

        if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
            navLinks.classList.remove('active');
            if (hamburger) hamburger.classList.remove('active');
            body.style.overflow = '';

            // Hide all dropdown contents
            dropdownContents.forEach(content => {
                content.style.display = '';
            });
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            // Reset styles for desktop view
            if (navLinks) navLinks.classList.remove('active');
            if (hamburger) hamburger.classList.remove('active');
            body.style.overflow = '';

            // Reset dropdown display
            dropdownContents.forEach(content => {
                content.style.display = '';
            });
        }
    });
});

</script>
<div style="margin-bottom: 200px;"></div>
<footer class="footer">
    <br>
    <a href="/about?lang={{ .lang }}">เกี่ยวกับเรา</a> | <a href="/faqs?lang={{ .lang }}">คำถามที่พบบ่อย</a> | <a href="/contact?lang={{ .lang }}">ติดต่อเรา</a><br>
    <p>BIOMASS EXCHANGE CO., LTD.</p>
    <br>
</footer>
{{ end }}