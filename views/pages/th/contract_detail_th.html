{{ define "content" }}
<nav class="dash-nav">
    <div class="dash-nav-container">
        <a href="/?lang={{ .Lang }}" class="dash-nav-brand">BiomassX</a>
        <div class="dash-nav-links">
            <a href="/dashboard?lang={{ .Lang }}">หน้าหลักของฉัน</a>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">เสนอซื้อ/เสนอขาย</a>
                <div class="dropdown-content">
                    <a href="/order?lang={{ .Lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">ประวัติคำสั่งซื้อขาย</a>
                <div class="dropdown-content">
                    <a href="/order_book?lang={{ .Lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">สัญญาซื้อขาย</a>
                <div class="dropdown-content">
                    <a href="/contract?lang={{ .Lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">ตั้งค่าใช้งาน</a>
                <div class="dropdown-content">
                    <a href="/profile?lang={{ .Lang }}">ข้อมูลผู้ใช้</a>
                </div>
            </div>

            <a href="/logout?lang={{ .Lang }}" hx-post="/logout?lang={{ .Lang }}" hx-redirect="/login?lang={{ .Lang }}">ออกจากระบบ</a>

            <!-- <div class="language-selector">
                <form hx-get="/contract/{{ .Contract.ID }}" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">ภาษา:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                    </select>
                </form>
            </div> -->
        </div>
        <!-- <div class="desktop-lang-selector">
            <form hx-get="/contract/{{ .Contract.ID }}" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                    <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                    <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                </select>
            </form>
        </div> -->
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

  <main class="main-dashboard">
    <div class="dashboard-content">
        <h2 class="contract-title">สัญญาเลขที่: {{.Contract.ID}}</h2>
        <p class="contract-date">วันที่สัญญา: {{if .Contract.ContractDate.IsZero}}N/A{{else}}{{.Contract.ContractDate.Format "02-01-2006"}}{{end}}</p>

        <section class="contract-section">
            <h2>คู่สัญญา</h2>
            <p>ผู้ขาย: {{.Contract.SellerFirstName.String}} {{.Contract.SellerLastName.String}}</p>
            <p>ชื่อองค์กร: {{if .Contract.SellerOrgName.Valid}}{{.Contract.SellerOrgName.String}}{{else}}-{{end}}</p>
            <p>ผู้ซื้อ: {{.Contract.BuyerFirstName.String}} {{.Contract.BuyerLastName.String}}</p>
            <p>ชื่อองค์กร: {{if .Contract.BuyerOrgName.Valid}}{{.Contract.BuyerOrgName.String}}{{else}}-{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>ข้อมูลสินค้า</h2>
            <p>สินค้า: {{if .Contract.ProductThName.Valid}}{{.Contract.ProductThName.String}}{{else}}N/A{{end}}</p>
            <p>มาตรฐาน: {{if .Contract.StandardThName.Valid}}{{.Contract.StandardThName.String}}{{else}}N/A{{end}}</p>
            <p>เกรด: {{if .Contract.GradeThName.Valid}}{{.Contract.GradeThName.String}}{{else}}N/A{{end}}</p>
            <p>จำนวน: {{.Contract.Quantity}} {{if .Contract.UomTh.Valid}}{{.Contract.UomTh.String}}{{else}}N/A{{end}}</p>
            <p>บรรจุภัณฑ์: {{if .Contract.PackingTh.Valid}}{{.Contract.PackingTh.String}}{{else}}N/A{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>เงื่อนไขการชำระเงิน</h2>
            <p>ราคา: {{.Contract.Price}} {{if .Contract.CurrencyThName.Valid}}{{.Contract.CurrencyThName.String}}{{else}}N/A{{end}}</p>
            <p>เงื่อนไขการชำระเงิน: {{if .Contract.PaymentTermTh.Valid}}{{.Contract.PaymentTermTh.String}}{{else}}N/A{{end}}</p>
            <p>สถานะการชำระเงิน: {{if .Contract.PaymentStatusTh.Valid}}{{.Contract.PaymentStatusTh.String}}{{else}}N/A{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>เงื่อนไขการส่งมอบ</h2>
            <p>เงื่อนไขการส่งมอบ: {{if .Contract.DeliveryTermTh.Valid}}{{.Contract.DeliveryTermTh.String}}{{else}}N/A{{end}}</p>
            <p>วันที่ส่งมอบเริ่มต้น: {{if .Contract.StartDelivery.Valid}}{{.Contract.StartDelivery.Time.Format "02-01-2006"}}{{else}}N/A{{end}}</p>
            <p>วันที่ส่งมอบสิ้นสุด: {{if .Contract.FinishDelivery.Valid}}{{.Contract.FinishDelivery.Time.Format "02-01-2006"}}{{else}}N/A{{end}}</p>
            <p><strong>สถานที่จัดส่ง:</strong></p>
            <p>ประเทศผู้ขาย: {{.Contract.SellerCountryTh.String}}</p>
            <p>ประเทศผู้ซื้อ: {{.Contract.BuyerCountryTh.String}}</p>
            <p>จังหวัด: {{if .Contract.PodProvinceTh.Valid}}{{.Contract.PodProvinceTh.String}}{{else}}N/A{{end}}</p>
            <p>อำเภอ: {{if .Contract.PodDistrictTh.Valid}}{{.Contract.PodDistrictTh.String}}{{else}}N/A{{end}}</p>
            <p>ตำบล: {{if .Contract.PodSubdistrictTh.Valid}}{{.Contract.PodSubdistrictTh.String}}{{else}}N/A{{end}}</p>
            <p><strong>ท่าเรือขนส่งสินค้า:</strong></p>
            <p>ท่าเรือผู้ขาย: {{if .Contract.SellerPolTh.Valid}}{{.Contract.SellerPolTh.String}}{{else}}N/A{{end}}</p>
            <p>ท่าเรือผู้ขาย: {{if .Contract.SellerPodTh.Valid}}{{.Contract.SellerPodTh.String}}{{else}}N/A{{end}}</p>
            <p>ท่าเรือผู้ซื้อ: {{if .Contract.BuyerPolTh.Valid}}{{.Contract.BuyerPolTh.String}}{{else}}N/A{{end}}</p>
            <p>ท่าเรือผู้ซื้อ: {{if .Contract.BuyerPodTh.Valid}}{{.Contract.BuyerPodTh.String}}{{else}}N/A{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>สถานะสัญญา</h2>
            <p>ผู้ขายลงนามตกลง: {{if .Contract.SellerConfirmStatusTh.Valid}}{{.Contract.SellerConfirmStatusTh.String}}{{else}}N/A{{end}}</p>
            <p>ผู้ซื้อลงนามตกลง: {{if .Contract.BuyerConfirmStatusTh.Valid}}{{.Contract.BuyerConfirmStatusTh.String}}{{else}}N/A{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>หมายเหตุ</h2>
            <p>ผู้ขาย: {{if .Contract.SellerRemark.Valid}}{{.Contract.SellerRemark.String}}{{else}}N/A{{end}}</p>
            <p>ผู้ซื้อ: {{if .Contract.BuyerRemark.Valid}}{{.Contract.BuyerRemark.String}}{{else}}N/A{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>ตกลงปฏิบัติตามสัญญา</h2>
            <div class="button-stack">
                <button
                    class="sign-button"
                    hx-post="/sign"
                    hx-vals='{"contract_id": "{{.Contract.ID}}"}'
                >
                    ลงนามในสัญญา
                </button>

                <button
                    class="sign-button reject-button"
                    hx-post="/reject"
                    hx-vals='{"contract_id": "{{.Contract.ID}}"}'
                    style="background-color: #f44336; margin-left: 10px;"
                >
                    ปฏิเสธสัญญา
                </button>

                {{if and (eq .Contract.SellerConfirmStatusTh.String "ลงนามแล้ว") (eq .Contract.BuyerConfirmStatusTh.String "ลงนามแล้ว") (eq .Contract.ContractStatusTh.String "ลงนามแล้ว")}}
                <a href="/download-pdfTh/{{.Contract.ID}}" class="sign-button download-btn">
                    ดาวน์โหลด PDF
                </a>
                {{end}}
            </div>
        </section>

        {{if and (eq .Contract.SellerConfirmStatusTh.String "ลงนามแล้ว") (eq .Contract.BuyerConfirmStatusTh.String "ลงนามแล้ว")}}
        <section class="contract-section">
            <h2>ใบแจ้งหนี้</h2>
            {{if .Invoices}}
            <div class="invoice-container">
                <table class="invoice-list">
                    <thead>
                        <tr>
                            <th>เลขที่ใบแจ้งหนี้</th>
                            <th>ประเภท</th>
                            <th>วันที่</th>
                            <th>จำนวนเงิน</th>
                            <th>สถานะ</th>
                            <th>การดำเนินการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{range .Invoices}}
                        <tr class="invoice-row {{if eq .ItemID 1}}type-product{{else}}type-fee{{end}} {{if eq .SellerID $.UserID}}as-seller{{else}}as-buyer{{end}}">
                            <td>{{.InvoiceNumber}}</td>
                            <td>{{.ItemThName}}</td>
                            <td>{{.InvoiceDate.Format "02/01/2006"}}</td>
                            <td>{{printf "%.2f" .ContractValue}} {{.CurrencyCode}}</td>
                            <td>
                                <span class="invoice-status status-{{.Status}}">
                                    {{if eq .Status "draft"}}ร่าง{{end}}
                                    {{if eq .Status "sent"}}รอการชำระเงิน{{end}}
                                    {{if eq .Status "viewed"}}รอการชำระเงิน{{end}}
                                    {{if eq .Status "paid"}}✓ ชำระเงินแล้ว{{end}}
                                    {{if eq .Status "overdue"}}⚠️ เกินกำหนดชำระ{{end}}
                                    {{if eq .Status "canceled"}}ยกเลิก{{end}}
                                    {{if eq .Status "disputed"}}อยู่ระหว่างการตรวจสอบ{{end}}
                                </span>
                            </td>
                            <td>
                                <a href="/invoice/{{.ID}}?lang=th" class="action-button view-button">ดู</a>
                                <a href="/invoice/{{.ID}}/download?lang=th" class="action-button download-button">ดาวน์โหลด</a>
                            </td>
                        </tr>
                        {{end}}
                    </tbody>
                </table>
            </div>
            {{else}}
            <p>ไม่พบใบแจ้งหนี้สำหรับสัญญานี้</p>
            <button
                class="action-button"
                hx-post="/api/invoice/generate"
                hx-vals='{"contract_id": {{.Contract.ID}}}'
                hx-swap="outerHTML"
                hx-target="closest section"
            >
                สร้างใบแจ้งหนี้
            </button>
            {{end}}
            <div class="view-all-link">
                <a href="/invoice?lang=th">ดูใบแจ้งหนี้ทั้งหมด</a>
            </div>
        </section>
        {{end}}
    </div>
</main>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.dash-nav-links');
        const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
        const dropdownContents = document.querySelectorAll('.dropdown-content');
        const body = document.body;

        // Mobile detection
        const isMobile = () => window.innerWidth <= 768;

        // Toggle hamburger menu
        if (hamburger && navLinks) {
            hamburger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle active classes
                hamburger.classList.toggle('active');
                navLinks.classList.toggle('active');

                // Manage body scroll
                if (navLinks.classList.contains('active')) {
                    body.style.overflow = 'hidden';

                    // Show all dropdown contents in mobile view
                    if (isMobile()) {
                        dropdownContents.forEach(content => {
                            content.style.display = 'block';
                        });
                    }
                } else {
                    body.style.overflow = '';

                    // Hide all dropdown contents when closing menu
                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });
        }

        // Handle dropdown triggers in mobile view
        dropdownTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                if (isMobile()) {
                    e.preventDefault();
                    e.stopPropagation();

                    const content = this.nextElementSibling;
                    const isVisible = content.style.display === 'block';

                    // Hide all dropdowns first
                    dropdownContents.forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });

                    // Toggle current dropdown
                    if (!isVisible) {
                        content.style.display = 'block';
                    }
                }
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!isMobile()) return;

            const isClickInsideNav = navLinks && navLinks.contains(event.target);
            const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

            if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Hide all dropdown contents
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                // Reset styles for desktop view
                if (navLinks) navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset dropdown display
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });
    });
</script>
{{end}}