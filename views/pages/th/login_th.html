{{ define "content" }}
<nav class="index-nav">
    <div class="index-nav-container">
        <a href="/?lang={{ .lang }}" class="index-nav-brand">BiomassX</a>
        <div class="index-nav-links">
            <a href="/order?lang={{ .lang }}">เสนอซื้อขาย</a>
            <a href="/markets?lang={{ .lang }}">ตลาด</a>
            <a href="/productspage?lang={{ .lang }}">สินค้า</a>
            <a href="/services?lang={{ .lang }}">บริการ</a>
            <div class="language-selector">
                <form hx-get="/login" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">เลือกภาษา:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>อังกฤษ</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>ไทย</option>
                        <option value="en" {{ if eq .lang "kr" }}selected{{ end }}>เกาหลี</option>
                        <option value="en" {{ if eq .lang "jp" }}selected{{ end }}>ญี่ปุ่น</option>
                        <option value="en" {{ if eq .lang "vi" }}selected{{ end }}>เวียดนาม</option>
                    </select>
                </form>
            </div>
        </div>
        <div class="desktop-lang-selector index-lang-selector">
            <form hx-get="/login" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>อังกฤษ</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>ไทย</option>
                        <option value="en" {{ if eq .lang "kr" }}selected{{ end }}>เกาหลี</option>
                        <option value="en" {{ if eq .lang "jp" }}selected{{ end }}>ญี่ปุ่น</option>
                        <option value="en" {{ if eq .lang "vi" }}selected{{ end }}>เวียดนาม</option>
                </select>
            </form>
        </div>
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="main-dashboard">
<form hx-post="/login?lang={{ .lang }}" hx-target="#message" hx-swap="innerHTML" >
    <h2>เข้าสู่ระบบ</h2>
    <div id="message"></div>
        <div class="loginform-group">
            <label for="username">ชื่อผู้ใช้ : </label>
            <input type="text" id="username" name="username" required>
        </div>
        <div class="loginform-group">
            <label for="password">รหัสผ่าน : </label>
            <input type="password" id="password" name="password" required>
        </div>
            <button type="submit">เข้าสู่ระบบ</button>
            <p> <a href="/forgot-password?lang={{ .lang }}">ลืมรหัสผ่าน?</a></p>
            <p>หากไม่มีบัญชีผู้ใช้? <a href="/register?lang={{ .lang }}"> สมัครบัญชีผู้ใช้</a></p>
</form>
<div id="message"></div>
</main>

<footer class="footer">
    <br>
    <a href="/about?lang={{ .lang }}">เกี่ยวกับเรา</a> | <a href="/faqs?lang={{ .lang }}">คำถามพบบ่อย</a> | <a href="/contact?lang={{ .lang }}">ติดต่อเรา</a><br>
    <p>บริษัท ไบโอแมส เอ็กซ์เชนจ์ จำกัด</p>
    <br>
</footer>

<script>
    // Elements
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.index-nav-links');
    const body = document.body;

    // Mobile detection
    const isMobile = () => window.innerWidth <= 768;

    // Toggle hamburger menu
    if (hamburger && navLinks) {
        hamburger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Toggle active classes
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');

            // Manage body scroll
            if (navLinks.classList.contains('active')) {
                body.style.overflow = 'hidden';
            } else {
                body.style.overflow = '';
            }
        });
    }

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        if (!isMobile()) return;

        const isClickInsideNav = navLinks && navLinks.contains(event.target);
        const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

        if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
            navLinks.classList.remove('active');
            if (hamburger) hamburger.classList.remove('active');
            body.style.overflow = '';
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            // Reset styles for desktop view
            if (navLinks) navLinks.classList.remove('active');
            if (hamburger) hamburger.classList.remove('active');
            body.style.overflow = '';
        }
    });
</script>
{{ end }}