{{define "content"}}
<div class="page-header">
    <h1>รายละเอียดใบแจ้งหนี้</h1>
</div>

<div class="invoice-detail">
    <!-- Status message section removed to fix template error -->
    <div class="invoice-header">
        <div class="invoice-title">ใบแจ้งหนี้</div>
        <div class="invoice-number">{{.Invoice.InvoiceNumber}}</div>
    </div>

    <div class="invoice-dates">
        <div class="invoice-date">
            <strong>วันที่ออกใบแจ้งหนี้:</strong> {{.InvoiceDate}}
        </div>
        <div class="due-date">
            <strong>วันครบกำหนด:</strong> {{.DueDate}}
        </div>
    </div>

    <div class="invoice-parties">
        <div class="seller-info">
            <h3>จาก</h3>
            <p>
                {{.Invoice.SellerName}}
            </p>
        </div>
        <div class="buyer-info">
            <h3>ถึง</h3>
            <p>
                {{.Invoice.BuyerName}}
            </p>
        </div>
    </div>

    <div class="invoice-container">
        <table class="invoice-items">
            <thead>
                <tr>
                    <th>รายละเอียด</th>
                    <th>จำนวน</th>
                    <th>ราคาต่อหน่วย</th>
                    <th>จำนวนเงิน</th>
                </tr>
            </thead>
            <tbody>
                {{if eq .Invoice.ItemID 1}}
                <tr>
                    <td>ค่าธรรมเนียมแพลตฟอร์ม (0.5%)</td>
                    <td>1</td>
                    <td>{{printf "%.2f" .Invoice.ContractValue}} {{.Invoice.CurrencyCode}}</td>
                    <td>{{printf "%.2f" .Invoice.ContractValue}} {{.Invoice.CurrencyCode}}</td>
                </tr>
                {{if .Invoice.ContractFee.Valid}}
                <tr class="fee-calculation">
                    <td colspan="4" class="fee-note">
                        <small>* ค่าธรรมเนียมนี้ ({{printf "%.2f" .Invoice.ContractValue}} {{.Invoice.CurrencyCode}}) คำนวณจาก 0.5% ของมูลค่าสัญญา ({{printf "%.2f" (multiply .Invoice.ContractValue 200)}} {{.Invoice.CurrencyCode}})</small>
                    </td>
                </tr>
                {{end}}
                {{else}}
                <tr>
                    <td>สินค้า: {{.Invoice.ProductName}}</td>
                    <td>{{.Invoice.Quantity}} {{.Invoice.UnitOfMeasure}}</td>
                    <td>{{printf "%.2f" .UnitPrice}} {{.Invoice.CurrencyCode}}</td>
                    <td>{{printf "%.2f" .Invoice.ContractValue}} {{.Invoice.CurrencyCode}}</td>
                </tr>
                {{end}}
            </tbody>
        </table>
    </div>

    <div class="invoice-total">
        <p>รวมทั้งสิ้น: {{printf "%.2f" .Invoice.ContractValue}} {{.Invoice.CurrencyCode}}</p>
    </div>

    <div class="invoice-status-display prominent">
        <strong>สถานะการชำระเงิน:</strong>
        <span class="invoice-status status-{{.Invoice.Status}}">
            {{if eq .Invoice.Status "draft"}}ร่าง{{end}}
            {{if eq .Invoice.Status "sent"}}รอการชำระเงิน{{end}}
            {{if eq .Invoice.Status "viewed"}}รอการชำระเงิน{{end}}
            {{if eq .Invoice.Status "paid"}}✓ ชำระเงินแล้ว{{end}}
            {{if eq .Invoice.Status "overdue"}}⚠️ เกินกำหนดชำระ{{end}}
            {{if eq .Invoice.Status "canceled"}}ยกเลิก{{end}}
            {{if eq .Invoice.Status "disputed"}}อยู่ระหว่างการตรวจสอบ{{end}}
        </span>
    </div>

    {{if eq .Invoice.ItemID 1}} <!-- Platform Fee Invoice Specific Section -->
    <div class="platform-fee-summary">
        <h4>สถานะการชำระค่าธรรมเนียมแพลตฟอร์มของคุณ (สัญญา: {{.Invoice.ContractNumber}})</h4>
        <div class="fee-status-item">
            <strong>ค่าธรรมเนียมแพลตฟอร์มของคุณ (0.5%):</strong>
            {{if eq .MyFeeStatus "paid"}}
                <span class="status-paid">ชำระแล้ว</span>
                {{if .MyFeeProofPath.Valid}}
                    (<a href="/{{.MyFeeProofPath.String}}" target="_blank">ดูหลักฐานของคุณ</a>)
                {{end}}
            {{else if .MyFeeProofPath.Valid}}
                <span class="status-viewed">ส่งหลักฐานแล้ว</span>
                 (<a href="/{{.MyFeeProofPath.String}}" target="_blank">ดูหลักฐานของคุณ</a>)
            {{else if .MyFeeStatus}}
                <span class="status-sent">รอการชำระเงิน</span>
            {{end}}
        </div>
        <div class="overall-fee-status">
            <strong>สถานะค่าธรรมเนียมแพลตฟอร์มโดยรวม:</strong>
            {{if and (eq .MyFeeStatus "paid") (eq .Invoice.OtherPartyFeeStatus.String "paid")}}
                <span class="status-paid">ค่าธรรมเนียมแพลตฟอร์มทั้งหมดชำระแล้ว</span>
            {{else}}
                <span class="status-sent">ค่าธรรมเนียมแพลตฟอร์มชำระบางส่วนหรือรอการชำระเงิน</span>
            {{end}}
        </div>
    </div>
    {{end}}

    {{if eq .Invoice.Status "paid"}}
    <div class="payment-history">
        <h3>ข้อมูลการชำระเงิน</h3>
        {{if .Invoice.DatePaid.Valid}}
        <p>วันที่ชำระเงิน: {{.Invoice.DatePaid.Time.Format "02/01/2006"}}</p>
        {{end}}

        {{if eq .Invoice.ItemID 1}} <!-- Platform Fee Invoice - Proof for THIS invoice -->
            {{if .Invoice.PaymentSlipBuyer.Valid}}
            <div class="payment-proof">
                <h4>หลักฐานการชำระเงินสำหรับใบแจ้งหนี้นี้ (ชำระโดย {{.Invoice.BuyerName}})</h4>
                <a href="/{{.Invoice.PaymentSlipBuyer.String}}" target="_blank" class="view-proof-button">ดูหลักฐานการชำระเงิน</a>
            </div>
            {{end}}
        {{else}} <!-- Product Invoice (between actual buyer and seller) or other types -->
            {{if eq .UserID .Invoice.BuyerID}}
                {{if .Invoice.PaymentSlipBuyer.Valid}}
                <div class="payment-proof">
                    <h4>หลักฐานการชำระเงิน</h4>
                    <a href="/{{.Invoice.PaymentSlipBuyer.String}}" target="_blank" class="view-proof-button">ดูหลักฐานการชำระเงินของคุณ</a>
                </div>
                {{end}}
                {{if .Invoice.PaymentSlipSeller.Valid}}
                <div class="payment-proof-other">
                    <p>หลักฐานการชำระเงินถูกส่งโดยผู้ขาย</p>
                </div>
                {{end}}
            {{else if eq .UserID .Invoice.SellerID}}
                {{if .Invoice.PaymentSlipSeller.Valid}}
                <div class="payment-proof">
                    <h4>หลักฐานการชำระเงิน</h4>
                    <a href="/{{.Invoice.PaymentSlipSeller.String}}" target="_blank" class="view-proof-button">ดูหลักฐานการชำระเงินของคุณ</a>
                </div>
                {{end}}
                {{if .Invoice.PaymentSlipBuyer.Valid}}
                <div class="payment-proof-other">
                    <p>หลักฐานการชำระเงินถูกส่งโดยผู้ซื้อ</p>
                </div>
                {{end}}
            {{end}}
        {{end}}
        <p>ขอบคุณสำหรับการชำระเงินของคุณ!</p>
    </div>
    {{else if or (eq .Invoice.Status "sent") (eq .Invoice.Status "viewed") (eq .Invoice.Status "overdue")}}
    <!-- This section is for invoices that are NOT YET PAID -->
    <!-- We will show payment instructions and upload form -->

    {{if eq .Invoice.ItemID 1}} <!-- Fee Invoice (Platform Fee) - Upload form for THIS invoice -->
        {{if and (eq .UserID .Invoice.BuyerID) .Invoice.PaymentSlipBuyer.Valid}} <!-- Show if current user is buyer of THIS invoice and proof submitted -->
        <div class="payment-proof-submitted">
            <h4>หลักฐานการชำระเงินของคุณที่ส่งแล้ว</h4>
            <p>คุณได้อัปโหลดหลักฐานการชำระเงินสำหรับค่าธรรมเนียมนี้แล้ว ทีมงานของเราจะตรวจสอบในไม่ช้า</p>
            <a href="/{{.Invoice.PaymentSlipBuyer.String}}" target="_blank" class="view-proof-button">ดูหลักฐานที่คุณส่ง</a>
            <!-- <p>หากคุณต้องการอัปโหลดหลักฐานใหม่ โปรดติดต่อฝ่ายสนับสนุน</p> -->
        </div>
        {{else if and (eq .UserID .Invoice.BuyerID) (not .Invoice.PaymentSlipBuyer.Valid)}} <!-- Show if current user is buyer of THIS invoice and NO proof submitted -->
        <!-- Upload form for Fee Invoice (current user is the buyer of the fee) -->
        <div class="payment-proof-upload">
            <h4>อัปโหลดหลักฐานการชำระเงิน</h4>
            <p class="payment-instructions">กรุณาอัปโหลดสลิปการชำระเงินเพื่อยืนยันว่าคุณได้ชำระค่าธรรมเนียมแพลตฟอร์มนี้แล้ว สถานะใบแจ้งหนี้ของคุณจะถูกอัปเดตเมื่อหลักฐานการชำระเงินของคุณถูกส่งและตรวจสอบแล้ว</p>
            <form action="/invoice/upload-payment-proof?lang={{.Lang}}" method="post" enctype="multipart/form-data">
                <input type="hidden" name="invoice_id" value="{{.Invoice.ID}}">
                <div class="form-group">
                    <label for="payment_slip">สลิปการชำระเงิน (รูปภาพหรือ PDF):</label>
                    <input type="file" id="payment_slip" name="payment_slip" accept="image/*,.pdf" required>
                </div>
                <button type="submit" class="action-button">ส่งหลักฐานการชำระเงิน</button>
            </form>
        </div>
        {{end}}
    {{else}} <!-- Product Invoice (or other types not ItemID 1) -->
        {{if eq .UserID .Invoice.BuyerID}}
            {{if .Invoice.PaymentSlipBuyer.Valid}}
            <div class="payment-proof-submitted">
                <h4>หลักฐานการชำระเงินของคุณที่ส่งแล้ว</h4>
                <p>คุณได้อัปโหลดหลักฐานการชำระเงินแล้ว ทีมงานของเราจะตรวจสอบในไม่ช้า</p>
                <a href="/{{.Invoice.PaymentSlipBuyer.String}}" target="_blank" class="view-proof-button">ดูหลักฐานที่คุณส่ง</a>
            </div>
            {{else}}
            <!-- Upload form for Buyer of product/service -->
            <div class="payment-proof-upload">
                <h4>อัปโหลดหลักฐานการชำระเงิน</h4>
                <p class="payment-instructions">กรุณาอัปโหลดสลิปการชำระเงินเพื่อยืนยันว่าคุณได้ชำระเงินตามใบแจ้งหนี้นี้แล้ว สถานะใบแจ้งหนี้ของคุณจะถูกอัปเดตเมื่อหลักฐานการชำระเงินของคุณถูกส่งและตรวจสอบแล้ว</p>
                <form action="/invoice/upload-payment-proof?lang={{.Lang}}" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="invoice_id" value="{{.Invoice.ID}}">
                    <div class="form-group">
                        <label for="payment_slip">สลิปการชำระเงิน (รูปภาพหรือ PDF):</label>
                        <input type="file" id="payment_slip" name="payment_slip" accept="image/*,.pdf" required>
                    </div>
                    <button type="submit" class="action-button">ส่งหลักฐานการชำระเงิน</button>
                </form>
            </div>
            {{end}}
            {{if .Invoice.PaymentSlipSeller.Valid}}
            <div class="payment-proof-other">
                <p>หลักฐานการชำระเงินถูกส่งโดยผู้ขาย กรุณารอการยืนยัน</p>
            </div>
            {{end}}
        {{else if eq .UserID .Invoice.SellerID}}
            {{if .Invoice.PaymentSlipSeller.Valid}}
            <div class="payment-proof-submitted">
                <h4>หลักฐานการชำระเงินของคุณที่ส่งแล้ว</h4>
                <p>คุณได้อัปโหลดหลักฐานการชำระเงินแล้ว ทีมงานของเราจะตรวจสอบในไม่ช้า</p>
                <a href="/{{.Invoice.PaymentSlipSeller.String}}" target="_blank" class="view-proof-button">ดูหลักฐานที่คุณส่ง</a>
            </div>
            {{else}}
            <!-- Upload form for Seller of product/service -->
            <div class="payment-proof-upload">
                <h4>อัปโหลดหลักฐานการชำระเงิน</h4>
                <p class="payment-instructions">หากคุณเป็นผู้รับผิดชอบในการอัปโหลดหลักฐานสำหรับธุรกรรมนี้ (เช่น การยืนยันการรับเงินหรือขั้นตอนการชำระเงินที่เฉพาะเจาะจง) โปรดดำเนินการที่นี่</p>
                <form action="/invoice/upload-payment-proof?lang={{.Lang}}" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="invoice_id" value="{{.Invoice.ID}}">
                    <div class="form-group">
                        <label for="payment_slip">สลิปการชำระเงิน (รูปภาพหรือ PDF):</label>
                        <input type="file" id="payment_slip" name="payment_slip" accept="image/*,.pdf" required>
                    </div>
                    <button type="submit" class="action-button">ส่งหลักฐานการชำระเงิน</button>
                </form>
            </div>
            {{end}}
            {{if .Invoice.PaymentSlipBuyer.Valid}}
            <div class="payment-proof-other">
                <p>หลักฐานการชำระเงินถูกส่งโดยผู้ซื้อ กรุณารอการยืนยัน</p>
            </div>
            {{end}}
        {{end}}
    {{end}}

    <div class="payment-instructions">
        <h3>คำแนะนำการชำระเงิน</h3>
        <p>กรุณาดาวน์โหลด PDF ใบแจ้งหนี้และรอทีมงานของเราติดต่อเกี่ยวกับตัวเลือกการชำระเงิน หากมีคำถามใด ๆ โปรดติดต่อเราที่ <a href="mailto:<EMAIL>"><EMAIL></a> หรือโทร +66 818073767</p>
        <p>หากคุณมีข้อกังวลเกี่ยวกับใบแจ้งหนี้นี้ โปรดติดต่อทีมสนับสนุนของเรา</p>
    </div>
    {{end}}

    <!-- ปุ่มยกเลิกถูกลบออกเนื่องจากสถานะการชำระเงินจะถูกจัดการโดยผู้ดูแลระบบ -->

    <div class="invoice-actions">
        <a href="/invoice/{{.Invoice.ID}}/download?lang={{.Lang}}" class="action-button download-button">ดาวน์โหลด PDF</a>
    </div>

    <a href="/invoice?lang={{.Lang}}" class="back-button">กลับไปยังใบแจ้งหนี้</a>
</div>
{{end}}
