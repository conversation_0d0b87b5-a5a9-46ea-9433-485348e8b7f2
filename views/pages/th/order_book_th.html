{{define "content"}}
<nav class="dash-nav">
    <div class="dash-nav-container">
        <a href="/?lang={{ .Lang }}" class="dash-nav-brand">BiomassX</a>
        <div class="dash-nav-links">
            <a href="/dashboard?lang={{ .Lang }}">หน้าหลักของฉัน</a>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">เสนอซื้อ/เสนอขาย</a>
                <div class="dropdown-content">
                    <a href="/order?lang={{ .Lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">สมุดคำสั่งซื้อ/ขาย</a>
                <div class="dropdown-content">
                    <a href="/order_book?lang={{ .Lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">สัญญาซื้อขาย</a>
                <div class="dropdown-content">
                    <a href="/contract?lang={{ .Lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">ตั้งค่าใช้งาน</a>
                <div class="dropdown-content">
                    <a href="/profile?lang={{ .Lang }}">ข้อมูลผู้ใช้</a>
                </div>
            </div>

            <a href="/logout?lang={{ .Lang }}" hx-post="/logout?lang={{ .Lang }}" hx-redirect="/login?lang={{ .Lang }}">ออกจากระบบ</a>

            <!-- <div class="language-selector">
                <form hx-get="/order_book" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">เลือกภาษา:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                    </select>
                </form>
            </div> -->
        </div>
        <!-- <div class="desktop-lang-selector">
            <form hx-get="/order_book" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                    <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                    <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                </select>
            </form>
        </div> -->
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="main-dashboard">
    <h3>ประวัติคำสั่งซื้อขาย</h3>
    <p>ดูและจัดการคำสั่งซื้อขายที่คุณส่ง</p>

    <div class="order-book-container">
        {{if .Orders}}
        <div class="order-filter-container">
            <span class="order-filter-label">กรองตามประเภทคำสั่ง:</span>
            <select id="order-type-filter" class="order-filter-select">
                <option value="all">ทั้งหมด</option>
                <option value="buy">คำสั่งซื้อ</option>
                <option value="sell">คำสั่งขาย</option>
            </select>
        </div>
        <!-- Mobile Card View (hidden on desktop) -->
        <div class="order-cards">
            {{range .Orders}}
            <div class="order-card" data-order-id="{{.ID}}" data-order-type="{{.OrderTypeName}}">
                <div class="order-card-header">
                    <div class="order-card-title">{{.ProductName}}</div>
                    {{if eq .StatusID 13}}
                    <div class="order-card-status available">เปิดประมูล</div>
                    {{else if eq .StatusID 14}}
                    <div class="order-card-status partial">จับคู่แล้วบางส่วน</div>
                    {{else if eq .StatusID 15}}
                    <div class="order-card-status matched">จับคู่แล้วแล้วทั้งหมด</div>
                    {{end}}
                </div>
                <div class="order-card-detail">
                    <div class="order-card-label">ประเภท:</div>
                    <div class="order-card-value">{{.OrderTypeName}}</div>
                </div>
                <div class="order-card-detail">
                    <div class="order-card-label">ราคา:</div>
                    <div class="order-card-value">{{.Price}} {{.CurrencyName}}</div>
                </div>
                <div class="order-card-detail">
                    <div class="order-card-label">ปริมาณเริ่มต้น:</div>
                    <div class="order-card-value">{{.OriginalQuantity}} {{.UOMName}}</div>
                </div>
                <div class="order-card-detail">
                    <div class="order-card-label">ปริมาณคงเหลือ:</div>
                    <div class="order-card-value">{{.AvailableQuantity}} {{.UOMName}}</div>
                </div>
                <div class="order-card-detail">
                    <div class="order-card-label">วันที่สร้าง:</div>
                    <div class="order-card-value">{{.CreatedAt.Format "02 Jan 2006"}}</div>
                </div>
                <div class="order-card-actions">
                    {{if eq .StatusID 13}}
                    <button class="edit-btn" onclick="openEditModal('{{.ID}}', '{{.Price}}', '{{.AvailableQuantity}}', '999999', '{{.Remark.String}}', '{{.StatusID}}')">แก้ไข</button>
                    <button class="delete-btn" onclick="openDeleteModal('{{.ID}}')">ลบ</button>
                    {{else if eq .StatusID 14}}
                    <button class="edit-btn" id="mobile-edit-btn-{{.ID}}" data-order-id="{{.ID}}" style="display: none;" onclick="checkContractStatusBeforeEdit('{{.ID}}', '{{.Price}}', '{{.AvailableQuantity}}', '{{.AvailableQuantity}}', '{{.Remark.String}}', '{{.StatusID}}')">แก้ไข</button>
                    <button class="delete-btn" id="mobile-delete-btn-{{.ID}}" data-order-id="{{.ID}}" style="display: none;" onclick="checkContractStatusBeforeDelete('{{.ID}}')">ลบ</button>
                    {{else if eq .StatusID 15}}
                    <!-- Placeholder for fully matched orders to maintain consistent height -->
                    <div style="height: 36px; width: 1px; visibility: hidden; display: inline-block;"></div>
                    {{end}}
                </div>
            </div>
            {{end}}
        </div>
        <!-- Desktop Table View (hidden on mobile) -->
        <table class="order-book-table">
            <thead>
                <tr>
                    <th>ประเภทคำสั่ง</th>
                    <th>สินค้า</th>
                    <th>ปริมาณเดิม</th>
                    <th>ปริมาณที่เหลือ</th>
                    <th>ราคา</th>
                    <th>สถานะ</th>
                    <th>วันที่สร้าง</th>
                    <th>การดำเนินการ</th>
                </tr>
            </thead>
            <tbody>
                {{range .Orders}}
                <tr data-order-id="{{.ID}}">
                    <td>{{.OrderTypeName}}</td>
                    <td>{{.ProductName}}</td>
                    <td>{{.OriginalQuantity}} {{.UOMName}}</td>
                    <td class="quantity-cell">{{.AvailableQuantity}} {{.UOMName}}</td>
                    <td>{{.Price}} {{.CurrencyName}}</td>
                    <td class="status-cell" data-status-id="{{.StatusID}}">
                        {{if eq .StatusID 13}}
                        <span class="status-available"><span class="status-icon"></span> เปิดประมูล</span>
                        {{else if eq .StatusID 14}}
                        <span class="status-partial"><span class="status-icon"></span> จับคู่แล้วบางส่วน</span>
                        {{else if eq .StatusID 15}}
                        <span class="status-matched"><span class="status-icon"></span> จับคู่แล้วแล้วทั้งหมด</span>
                        {{else}}
                        {{.StatusName}}
                        {{end}}
                    </td>
                    <td>{{.CreatedAt.Format "02 Jan 2006"}}</td>
                    <td class="action-buttons">
                        {{if eq .StatusID 13}}
                        <button class="edit-btn" onclick="openEditModal('{{.ID}}', '{{.Price}}', '{{.AvailableQuantity}}', '999999', '{{.Remark.String}}', '{{.StatusID}}')">แก้ไข</button>
                        <button class="delete-btn" onclick="openDeleteModal('{{.ID}}')">ลบ</button>
                        {{else if eq .StatusID 14}}
                        <button class="edit-btn" id="edit-btn-{{.ID}}" data-order-id="{{.ID}}" style="display: none;" onclick="checkContractStatusBeforeEdit('{{.ID}}', '{{.Price}}', '{{.AvailableQuantity}}', '{{.AvailableQuantity}}', '{{.Remark.String}}', '{{.StatusID}}')">แก้ไข</button>
                        <button class="delete-btn" id="delete-btn-{{.ID}}" data-order-id="{{.ID}}" style="display: none;" onclick="checkContractStatusBeforeDelete('{{.ID}}')">ลบ</button>
                        {{else if eq .StatusID 15}}
                        <!-- Placeholder for fully matched orders to maintain consistent row height -->
                        <div style="height: 32px; width: 1px; visibility: hidden; display: inline-block;"></div>
                        {{end}}
                    </td>
                </tr>
                {{end}}
            </tbody>
        </table>
        {{else}}
        <p>คุณยังไม่มีคำสั่งซื้อขายที่ส่ง</p>
        <p>ไปที่หน้า <a href="/order?lang={{ .Lang }}">คำสั่งซื้อขาย</a> เพื่อส่งคำสั่งใหม่</p>
        {{end}}
    </div>
</main>

<footer class="footer">
    <br>
    <a href="/about?lang={{ .Lang }}">เกี่ยวกับเรา</a> | <a href="/faqs?lang={{ .Lang }}">คำถามที่พบบ่อย</a> | <a href="/contact?lang={{ .Lang }}">ติดต่อเรา</a><br>
    <p>บริษัท ไบโอแมส เอ็กซ์เชนจ์ จำกัด</p>
    <br>
</footer>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.dash-nav-links');
        const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
        const dropdownContents = document.querySelectorAll('.dropdown-content');
        const body = document.body;

        // Mobile detection
        const isMobile = () => window.innerWidth <= 768;

        // Toggle hamburger menu
        if (hamburger && navLinks) {
            hamburger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle active classes
                hamburger.classList.toggle('active');
                navLinks.classList.toggle('active');

                // Manage body scroll
                if (navLinks.classList.contains('active')) {
                    body.style.overflow = 'hidden';

                    // Show all dropdown contents in mobile view
                    if (isMobile()) {
                        dropdownContents.forEach(content => {
                            content.style.display = 'block';
                        });
                    }
                } else {
                    body.style.overflow = '';

                    // Hide all dropdown contents when closing menu
                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });
        }

        // Handle dropdown triggers in mobile view
        dropdownTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                if (isMobile()) {
                    e.preventDefault();
                    e.stopPropagation();

                    const content = this.nextElementSibling;
                    const isVisible = content.style.display === 'block';

                    // Hide all dropdowns first
                    dropdownContents.forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });

                    // Toggle current dropdown
                    if (!isVisible) {
                        content.style.display = 'block';
                    }
                }
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!isMobile()) return;

            const isClickInsideNav = navLinks && navLinks.contains(event.target);
            const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

            if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Hide all dropdown contents
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                // Reset styles for desktop view
                if (navLinks) navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset dropdown display
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });
    });
</script>
{{end}}
