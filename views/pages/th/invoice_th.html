{{define "content"}}
<div class="page-header">
    <h1>ใบแจ้งหนี้ของฉัน</h1>
</div>

<div class="order-filter-container">
    <span class="order-filter-label">กรองใบแจ้งหนี้:</span>
    <div class="filter-buttons">
        <button class="filter-button active" data-filter="all">ทั้งหมด</button>
        <button class="filter-button" data-filter="as-buyer">ในฐานะผู้ซื้อ</button>
        <button class="filter-button" data-filter="as-seller">ในฐานะผู้ขาย</button>
        <button class="filter-button" data-filter="platform-fees">ค่าธรรมเนียมแพลตฟอร์ม</button>
    </div>
</div>

<div class="invoice-container">
    {{if .Invoices}}
    <table class="invoice-list">
        <thead>
            <tr>
                <th>เลขที่ใบแจ้งหนี้</th>
                <th>วันที่</th>
                <th>วันครบกำหนด</th>
                <th>ประเภท</th>
                <th>เกี่ยวข้องกับ</th>
                <th>จำนวนเงิน</th>
                <th>สถานะ</th>
                <th>การดำเนินการ</th>
            </tr>
        </thead>
        <tbody>
            {{range .Invoices}}
            <tr class="invoice-row {{if eq .ItemID 1}}type-product{{else}}type-fee{{end}} {{if eq .SellerID $.UserID}}as-seller{{else}}as-buyer{{end}}">
                <td>{{.InvoiceNumber}}</td>
                <td>{{.InvoiceDate.Format "02/01/2006"}}</td>
                <td>{{.DueDate.Format "02/01/2006"}}</td>
                <td>{{.ItemThName}}</td>
                <td>{{.ContractNumber}}</td>
                <td>{{.ContractValue}} {{.CurrencyCode}}</td>
                <td>
                    <span class="invoice-status status-{{.Status}}">
                        {{if eq .Status "draft"}}ร่าง{{end}}
                        {{if eq .Status "sent"}}รอการชำระเงิน{{end}}
                        {{if eq .Status "viewed"}}รอการชำระเงิน{{end}}
                        {{if eq .Status "paid"}}✓ ชำระเงินแล้ว{{end}}
                        {{if eq .Status "overdue"}}⚠️ เกินกำหนดชำระ{{end}}
                        {{if eq .Status "canceled"}}ยกเลิก{{end}}
                        {{if eq .Status "disputed"}}อยู่ระหว่างการตรวจสอบ{{end}}
                    </span>
                </td>
                <td>
                    <a href="/invoice/{{.ID}}?lang={{$.Lang}}" class="action-button view-button">ดู</a>
                    <a href="/invoice/{{.ID}}/download?lang={{$.Lang}}" class="action-button download-button">ดาวน์โหลด</a>
                </td>
            </tr>
            {{end}}
        </tbody>
    </table>
    {{else}}
    <div class="no-invoices">
        <p>คุณยังไม่มีใบแจ้งหนี้</p>
    </div>
    {{end}}
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set up invoice filter buttons
        const filterButtons = document.querySelectorAll('.filter-button');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                const filter = this.getAttribute('data-filter');

                // Show/hide rows based on filter
                const rows = document.querySelectorAll('.invoice-row');
                rows.forEach(row => {
                    switch(filter) {
                        case 'all':
                            row.style.display = '';
                            break;
                        case 'as-buyer':
                            row.style.display = row.classList.contains('as-buyer') ? '' : 'none';
                            break;
                        case 'as-seller':
                            row.style.display = row.classList.contains('as-seller') ? '' : 'none';
                            break;
                        case 'platform-fees':
                            row.style.display = row.classList.contains('type-fee') ? '' : 'none';
                            break;
                    }
                });

                // Save filter preference
                localStorage.setItem('invoiceFilter', filter);
            });
        });

        // Apply saved filter if exists
        const savedFilter = localStorage.getItem('invoiceFilter');
        if (savedFilter) {
            const buttonToActivate = document.querySelector(`.filter-button[data-filter="${savedFilter}"]`);
            if (buttonToActivate) {
                // Simulate click to apply the filter
                buttonToActivate.click();
            }
        }
    });
</script>
{{end}}
