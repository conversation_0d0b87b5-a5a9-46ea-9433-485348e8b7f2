{{define "content"}}
<nav class="dash-nav">
    <div class="dash-nav-container">
        <a href="/?lang={{ .Lang }}" class="dash-nav-brand">BiomassX</a>
        <div class="dash-nav-links">
            <a href="/dashboard?lang={{ .Lang }}">หน้าหลักของฉัน</a>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">เสนอซื้อ/เสนอขาย</a>
                <div class="dropdown-content">
                    <a href="/order?lang={{ .Lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">สมุดคำสั่งซื้อ/ขาย</a>
                <div class="dropdown-content">
                    <a href="/order_book?lang={{ .Lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">สัญญาซื้อขาย</a>
                <div class="dropdown-content">
                    <a href="/contract?lang={{ .Lang }}">สินค้า</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">ตั้งค่าใช้งาน</a>
                <div class="dropdown-content">
                    <a href="/profile?lang={{ .Lang }}">ข้อมูลผู้ใช้</a>
                </div>
            </div>

            <a href="/logout?lang={{ .Lang }}" hx-post="/logout?lang={{ .Lang }}" hx-redirect="/login?lang={{ .Lang }}">ออกจากระบบ</a>

            <!-- <div class="language-selector">
                <form hx-get="/contract" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">เลือกภาษา:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                    </select>
                </form>
            </div> -->
        </div>
        <div class="desktop-lang-selector">
            <form hx-get="/contract" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                    <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                    <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                </select>
            </form>
        </div>
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="main-dashboard">
<h3>สัญญาซื้อขาย</h3>
<div class="container">
    {{if .SellContracts}}
    <h3>Sell contract(s)</h3>
    <table border="1">
        <thead>
            <tr>
                <th>#</th>
                <th>วันที่สัญญา</th>
                <th>ผู้ขาย</th>
                <th>องค์กร</th>
                <th>ผู้ซื้อ     </th>
                <th>องค์กร</th>
                <th>คุณภาพ</th>
                <th>ราคา</th>
                <th>จำนวน</th>
                <th>บรรจุภัณฑ์</th>
                <th>เงื่อนไขการส่งมอบ</th>
                <th>สถานะการส่งมอบ</th>
                <th>เงื่อนไขการชำระเงิน</th>
                <th>สถานะการชำระเงิน</th>
                <th>การยืนยันจากผู้ขาย</th>
                <th>การยืนยันจากผู้ซื้อ</th>
                <th>สถานะสัญญา</th>
                <th>ดำเนินการ</th>
            </tr>
        </thead>
        <tbody>
            {{range .SellContracts}}
            <tr>
                <td>{{.ID}}</td>
                <td>{{if .ContractDate.IsZero}}N/A{{else}}{{.ContractDate.Format "02-01-2006"}}{{end}}</td>
                <td>{{if .SellerFirstName.Valid}}{{.SellerFirstName.String}} {{.SellerLastName.String}}{{else}}N/A{{end}}</td>
                <td>{{if .SellerOrgName.Valid}}{{.SellerOrgName.String}}{{else}}-{{end}}</td>
                <td>{{if .BuyerFirstName.Valid}}{{.BuyerFirstName.String}} {{.BuyerLastName.String}}{{else}}N/A{{end}}</td>
                <td>{{if .BuyerOrgName.Valid}}{{.BuyerOrgName.String}}{{else}}-{{end}}</td>
                <td>
                    {{if .StandardThName.Valid}}Standard: {{.StandardThName.String}} - {{else}}Standard: N/A - {{end}}
                    {{if .GradeThName.Valid}}Grade: {{.GradeThName.String}}{{else}}Grade: N/A{{end}}
                </td>
                <td>{{.Price}} {{if .CurrencyThName.Valid}}{{.CurrencyThName.String}}{{else}}N/A{{end}}</td>
                <td>{{.Quantity}} {{if .UomTh.Valid}}{{.UomTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .PackingTh.Valid}}{{.PackingTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .DeliveryTermTh.Valid}}{{.DeliveryTermTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .DeliveryStatusTh.Valid}}{{.DeliveryStatusTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .PaymentTermTh.Valid}}{{.PaymentTermTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .PaymentStatusTh.Valid}}{{.PaymentStatusTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .SellerConfirmStatusTh.Valid}}{{.SellerConfirmStatusTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .BuyerConfirmStatusTh.Valid}}{{.BuyerConfirmStatusTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .ContractStatusTh.Valid}}{{.ContractStatusTh.String}}{{else}}N/A{{end}}</td>
                <td><a href="/contract/{{.ID}}?lang={{ $.Lang }}" class="btn">ดู</a></td>
            </tr>
            {{end}}
        </tbody>
    </table>
    {{end}}

    {{if .BuyContracts}}
    <h3>Buy contract(s)</h3>
    <table border="1">
        <thead>
            <tr>
                <th>#</th>
                <th>วันที่สัญญา</th>
                <th>ผู้ขาย</th>
                <th>องค์กร</th>
                <th>ผู้ซื้อ</th>
                <th>องค์กร</th>
                <th>คุณภาพ</th>
                <th>ราคา</th>
                <th>จำนวน</th>
                <th>บรรจุภัณฑ์</th>
                <th>เงื่อนไขการส่งมอบ</th>
                <th>สถานะการส่งมอบ</th>
                <th>เงื่อนไขการชำระเงิน</th>
                <th>สถานะการชำระเงิน</th>
                <th>การยืนยันจากผู้ขาย</th>
                <th>การยืนยันจากผู้ซื้อ</th>
                <th>สถานะสัญญา</th>
                <th>ดำเนินการ</th>
            </tr>
        </thead>
        <tbody>
            {{range .BuyContracts}}
            <tr>
                <td>{{.ID}}</td>
                <td>{{if .ContractDate.IsZero}}N/A{{else}}{{.ContractDate.Format "02-01-2006"}}{{end}}</td>
                <td>{{if .SellerFirstName.Valid}}{{.SellerFirstName.String}} {{.SellerLastName.String}}{{else}}N/A{{end}}</td>
                <td>{{if .SellerOrgName.Valid}}{{.SellerOrgName.String}}{{else}}-{{end}}</td>
                <td>{{if .BuyerFirstName.Valid}}{{.BuyerFirstName.String}} {{.BuyerLastName.String}}{{else}}N/A{{end}}</td>
                <td>{{if .BuyerOrgName.Valid}}{{.BuyerOrgName.String}}{{else}}-{{end}}</td>
                <td>
                    {{if .StandardThName.Valid}}Standard: {{.StandardThName.String}} - {{else}}Standard: N/A - {{end}}
                    {{if .GradeThName.Valid}}Grade: {{.GradeThName.String}}{{else}}Grade: N/A{{end}}
                </td>
                <td>{{.Price}} {{if .CurrencyThName.Valid}}{{.CurrencyThName.String}}{{else}}N/A{{end}}</td>
                <td>{{.Quantity}} {{if .UomTh.Valid}}{{.UomTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .PackingTh.Valid}}{{.PackingTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .DeliveryTermTh.Valid}}{{.DeliveryTermTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .DeliveryStatusTh.Valid}}{{.DeliveryStatusTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .PaymentTermTh.Valid}}{{.PaymentTermTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .PaymentStatusTh.Valid}}{{.PaymentStatusTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .SellerConfirmStatusTh.Valid}}{{.SellerConfirmStatusTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .BuyerConfirmStatusTh.Valid}}{{.BuyerConfirmStatusTh.String}}{{else}}N/A{{end}}</td>
                <td>{{if .ContractStatusTh.Valid}}{{.ContractStatusTh.String}}{{else}}N/A{{end}}</td>
                <td><a href="/contract/{{.ID}}?lang={{ $.Lang }}" class="btn">ดู</a></td>
            </tr>
            {{end}}
        </tbody>
    </table>
    {{end}}
</div>
</main>

<footer class="footer">
  <br>
  <a href="/about?lang={{ .Lang }}">เกี่ยวกับเรา</a> | <a href="/faqs?lang={{ .Lang }}">คำถามพบบ่อย</a> | <a href="/contact?lang={{ .Lang }}">ติดต่อเรา</a><br>
  <p>บริษัท ไบโอแมส เอ็กซ์เชนจ์ จำกัด</p>
  <br>
</footer>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.dash-nav-links');
        const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
        const dropdownContents = document.querySelectorAll('.dropdown-content');
        const body = document.body;

        // Mobile detection
        const isMobile = () => window.innerWidth <= 768;

        // Toggle hamburger menu
        if (hamburger && navLinks) {
            hamburger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle active classes
                hamburger.classList.toggle('active');
                navLinks.classList.toggle('active');

                // Manage body scroll
                if (navLinks.classList.contains('active')) {
                    body.style.overflow = 'hidden';

                    // Show all dropdown contents in mobile view
                    if (isMobile()) {
                        dropdownContents.forEach(content => {
                            content.style.display = 'block';
                        });
                    }
                } else {
                    body.style.overflow = '';

                    // Hide all dropdown contents when closing menu
                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });
        }

        // Handle dropdown triggers in mobile view
        dropdownTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                if (isMobile()) {
                    e.preventDefault();
                    e.stopPropagation();

                    const content = this.nextElementSibling;
                    const isVisible = content.style.display === 'block';

                    // Hide all dropdowns first
                    dropdownContents.forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });

                    // Toggle current dropdown
                    if (!isVisible) {
                        content.style.display = 'block';
                    }
                }
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!isMobile()) return;

            const isClickInsideNav = navLinks && navLinks.contains(event.target);
            const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

            if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Hide all dropdown contents
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                // Reset styles for desktop view
                if (navLinks) navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset dropdown display
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });
    });
</script>
{{end}}
