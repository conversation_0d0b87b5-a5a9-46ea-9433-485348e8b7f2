{{define "content"}}
<nav class="dash-nav">
    <div class="dash-nav-container">
        <a href="/?lang={{ .Lang }}" class="dash-nav-brand">BiomassX</a>
        <div class="dash-nav-links">
            <a href="/dashboard?lang={{ .Lang }}">Dashboard</a>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Submit orders</a>
                <div class="dropdown-content">
                    <a href="/order?lang={{ .Lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Order book</a>
                <div class="dropdown-content">
                    <a href="/order_book?lang={{ .Lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Matched orders</a>
                <div class="dropdown-content">
                    <a href="/contract?lang={{ .Lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Setting</a>
                <div class="dropdown-content">
                    <a href="/profile?lang={{ .Lang }}">Profile</a>
                </div>
            </div>

            <a href="/logout?lang={{ .Lang }}" hx-post="/logout?lang={{ .Lang }}" hx-redirect="/login?lang={{ .Lang }}">Logout</a>

            <!-- <div class="language-selector">
                <form hx-get="/order_book" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">Language:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                    </select>
                </form>
            </div> -->
        </div>
        <!-- <div class="desktop-lang-selector">
            <form hx-get="/order_book" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                    <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                    <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                </select>
            </form>
        </div> -->
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="main-dashboard">
    <h3>Order Book</h3>
    <p>View and manage your submitted orders</p>

    <div class="order-book-container">
        {{if .Orders}}
        <div class="order-filter-container">
            <span class="order-filter-label">Filter by Order Type:</span>
            <select id="order-type-filter" class="order-filter-select">
                <option value="all">All Orders</option>
                <option value="buy">Buy Orders</option>
                <option value="sell">Sell Orders</option>
            </select>
        </div>
        <!-- Mobile Card View (hidden on desktop) -->
        <div class="order-cards">
            {{range .Orders}}
            <div class="order-card" data-order-id="{{.ID}}" data-order-type="{{.OrderTypeName}}">
                <div class="order-card-header">
                    <div class="order-card-title">{{.ProductName}}</div>
                    {{if eq .StatusID 13}}
                    <div class="order-card-status available">Available</div>
                    {{else if eq .StatusID 14}}
                    <div class="order-card-status partial">Partially Matched</div>
                    {{else if eq .StatusID 15}}
                    <div class="order-card-status matched">Fully Matched</div>
                    {{end}}
                </div>
                <div class="order-card-detail">
                    <div class="order-card-label">Type:</div>
                    <div class="order-card-value">{{.OrderTypeName}}</div>
                </div>
                <div class="order-card-detail">
                    <div class="order-card-label">Price:</div>
                    <div class="order-card-value">{{.Price}} {{.CurrencyName}}</div>
                </div>
                <div class="order-card-detail">
                    <div class="order-card-label">Original Quantity:</div>
                    <div class="order-card-value">{{.OriginalQuantity}} {{.UOMName}}</div>
                </div>
                <div class="order-card-detail">
                    <div class="order-card-label">Available Quantity:</div>
                    <div class="order-card-value">{{.AvailableQuantity}} {{.UOMName}}</div>
                </div>
                <div class="order-card-detail">
                    <div class="order-card-label">Created:</div>
                    <div class="order-card-value">{{.CreatedAt.Format "Jan 02, 2006"}}</div>
                </div>
                <div class="order-card-actions">
                    {{if eq .StatusID 13}}
                    <button class="edit-btn" onclick="openEditModal('{{.ID}}', '{{.Price}}', '{{.AvailableQuantity}}', '999999', '{{.Remark.String}}', '{{.StatusID}}')">Edit</button>
                    <button class="delete-btn" onclick="openDeleteModal('{{.ID}}')">Delete</button>
                    {{else if eq .StatusID 14}}
                    <button class="edit-btn" id="mobile-edit-btn-{{.ID}}" data-order-id="{{.ID}}" style="display: none;" onclick="checkContractStatusBeforeEdit('{{.ID}}', '{{.Price}}', '{{.AvailableQuantity}}', '{{.AvailableQuantity}}', '{{.Remark.String}}', '{{.StatusID}}')">Edit</button>
                    <button class="delete-btn" id="mobile-delete-btn-{{.ID}}" data-order-id="{{.ID}}" style="display: none;" onclick="checkContractStatusBeforeDelete('{{.ID}}')">Delete</button>
                    {{else if eq .StatusID 15}}
                    <!-- Placeholder for fully matched orders to maintain consistent height -->
                    <div style="height: 36px; width: 1px; visibility: hidden; display: inline-block;"></div>
                    {{end}}
                </div>
            </div>
            {{end}}
        </div>
        <!-- Desktop Table View (hidden on mobile) -->
        <table class="order-book-table">
            <thead>
                <tr>
                    <th>Order Type</th>
                    <th>Product</th>
                    <th>Original Quantity</th>
                    <th>Available Quantity</th>
                    <th>Price</th>
                    <th>Status</th>
                    <th>Created Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {{range .Orders}}
                <tr data-order-id="{{.ID}}">
                    <td>{{.OrderTypeName}}</td>
                    <td>{{.ProductName}}</td>
                    <td>{{.OriginalQuantity}} {{.UOMName}}</td>
                    <td class="quantity-cell">{{.AvailableQuantity}} {{.UOMName}}</td>
                    <td>{{.Price}} {{.CurrencyName}}</td>
                    <td class="status-cell" data-status-id="{{.StatusID}}">
                        {{if eq .StatusID 13}}
                        <span class="status-available"><span class="status-icon"></span> Open</span>
                        {{else if eq .StatusID 14}}
                        <span class="status-partial"><span class="status-icon"></span> Partially Matched</span>
                        {{else if eq .StatusID 15}}
                        <span class="status-matched"><span class="status-icon"></span> Fully Matched</span>
                        {{else}}
                        {{.StatusName}}
                        {{end}}
                    </td>
                    <td>{{.CreatedAt.Format "Jan 02, 2006"}}</td>
                    <td class="action-buttons">
                        {{if eq .StatusID 13}}
                        <button class="edit-btn" onclick="openEditModal('{{.ID}}', '{{.Price}}', '{{.AvailableQuantity}}', '999999', '{{.Remark.String}}', '{{.StatusID}}')">Edit</button>
                        <button class="delete-btn" onclick="openDeleteModal('{{.ID}}')">Delete</button>
                        {{else if eq .StatusID 14}}
                        <button class="edit-btn" id="edit-btn-{{.ID}}" data-order-id="{{.ID}}" style="display: none;" onclick="checkContractStatusBeforeEdit('{{.ID}}', '{{.Price}}', '{{.AvailableQuantity}}', '{{.AvailableQuantity}}', '{{.Remark.String}}', '{{.StatusID}}')">Edit</button>
                        <button class="delete-btn" id="delete-btn-{{.ID}}" data-order-id="{{.ID}}" style="display: none;" onclick="checkContractStatusBeforeDelete('{{.ID}}')">Delete</button>
                        {{else if eq .StatusID 15}}
                        <!-- Placeholder for fully matched orders to maintain consistent row height -->
                        <div style="height: 32px; width: 1px; visibility: hidden; display: inline-block;"></div>
                        {{end}}
                    </td>
                </tr>
                {{end}}
            </tbody>
        </table>
        {{else}}
        <p>You haven't submitted any orders yet.</p>
        <p>Go to the <a href="/order?lang={{ .Lang }}">Order</a> page to submit a new order.</p>
        {{end}}
    </div>
</main>

<footer class="footer">
    <br>
    <a href="/about?lang={{ .Lang }}">About us</a> | <a href="/faqs?lang={{ .Lang }}">FAQs</a> | <a href="/contact?lang={{ .Lang }}">Contact us</a><br>
    <p>BIOMASS EXCHANGE CO., LTD.</p>
    <br>
</footer>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.dash-nav-links');
        const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
        const dropdownContents = document.querySelectorAll('.dropdown-content');
        const body = document.body;

        // Mobile detection
        const isMobile = () => window.innerWidth <= 768;

        // Toggle hamburger menu
        if (hamburger && navLinks) {
            hamburger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle active classes
                hamburger.classList.toggle('active');
                navLinks.classList.toggle('active');

                // Manage body scroll
                if (navLinks.classList.contains('active')) {
                    body.style.overflow = 'hidden';

                    // Show all dropdown contents in mobile view
                    if (isMobile()) {
                        dropdownContents.forEach(content => {
                            content.style.display = 'block';
                        });
                    }
                } else {
                    body.style.overflow = '';

                    // Hide all dropdown contents when closing menu
                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });
        }

        // Handle dropdown triggers in mobile view
        dropdownTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                if (isMobile()) {
                    e.preventDefault();
                    e.stopPropagation();

                    const content = this.nextElementSibling;
                    const isVisible = content.style.display === 'block';

                    // Hide all dropdowns first
                    dropdownContents.forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });

                    // Toggle current dropdown
                    if (!isVisible) {
                        content.style.display = 'block';
                    }
                }
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!isMobile()) return;

            const isClickInsideNav = navLinks && navLinks.contains(event.target);
            const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

            if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Hide all dropdown contents
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                // Reset styles for desktop view
                if (navLinks) navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset dropdown display
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });
    });
</script>
{{end}}
