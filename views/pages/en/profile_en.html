{{ define "content" }}
<style>
    .role-segment {
        margin-bottom: 1.5rem;
    }
    .role-segment h3 {
        font-size: 1.2rem;
        color: #333;
        margin-bottom: 0.5rem;
        border-bottom: 1px solid #ddd;
        padding-bottom: 0.3rem;
    }
    .role-segment label {
        display: block;
        margin: 0.3rem 0;
        font-size: 1rem;
    }
    .role-segment input[type="checkbox"] {
        margin-right: 0.5rem;
    }
    #roles-display ul {
        list-style: none;
        padding: 0;
    }
    #roles-display li {
        margin: 0.3rem 0;
        font-size: 0.95rem;
    }
    .success-message {
        color: green;
        font-weight: bold;
    }
    .error-message {
        color: red;
        font-weight: bold;
    }
    .address-types-summary {
        margin: 20px 0;
        padding: 15px;
        background-color: #f9f9f9;
        border-radius: 5px;
        border: 1px solid #eee;
    }
    .address-types-summary h3 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 1.1rem;
        color: #333;
    }
    .address-type-indicator {
        display: inline-block;
        margin-right: 15px;
        margin-bottom: 5px;
    }
    .address-type-indicator span {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.9rem;
        margin-right: 5px;
    }
    .address-type-indicator.has-address span {
        background-color: #e6f7ff;
        color: #0066cc;
        border: 1px solid #0066cc;
    }
    .address-type-indicator.no-address span {
        background-color: #f5f5f5;
        color: #999;
        border: 1px solid #ddd;
    }
    .address-type-status {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-left: 5px;
        vertical-align: middle;
    }
    .status-set {
        background-color: #52c41a;
    }
    .status-not-set {
        background-color: #f5222d;
    }
    .roles-section {
        background-color: #f8f8f8;
        border-radius: 4px;
        padding: 15px;
        margin: 20px 0;
        border: 1px solid #e0e0e0;
    }
    .roles-section h2 {
        margin-top: 0;
        color: #333;
        font-size: 1.2rem;
        margin-bottom: 15px;
    }
    .roles-section p {
        color: #555;
        margin-bottom: 15px;
        line-height: 1.4;
    }
    .roles-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }
    .role-segment {
        flex: 1;
        min-width: 200px;
        background-color: #fff;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 12px;
        margin-bottom: 10px;
    }
    .role-segment h3 {
        color: #333;
        font-size: 1rem;
        margin-top: 0;
        margin-bottom: 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #eee;
    }
    .role-segment ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .role-segment li {
        padding: 4px 0;
        font-size: 0.9rem;
        color: #444;
    }
    .role-tag {
        display: inline-block;
        background-color: #f0f0f0;
        color: #333;
        border-radius: 3px;
        padding: 3px 8px;
        margin: 2px 0;
        font-size: 0.85rem;
    }
    #roles-edit-modal .modal-content {
        max-width: 700px;
        width: 95%;
        border-radius: 3px;
        padding: 15px;
        overflow-y: auto;
        max-height: 85vh;
        margin: 5% auto;
    }
    .role-options-container {
        background-color: #fff;
        border-radius: 3px;
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid #e0e0e0;
    }
    .role-options-container h3 {
        color: #333;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 5px;
        margin-top: 0;
        font-size: 0.9rem;
        margin-bottom: 8px;
        font-weight: 600;
    }
    .role-options {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 6px;
    }
    .role-checkbox {
        display: flex;
        align-items: center;
        background-color: #f8f8f8;
        border-radius: 3px;
        padding: 4px 6px;
        transition: all 0.15s ease;
        cursor: pointer;
        border: 1px solid #e0e0e0;
        min-height: 30px; /* Smaller height */
    }
    .role-checkbox:hover {
        background-color: #f0f0f0;
        border-color: #ccc;
    }
    .role-checkbox input[type="checkbox"] {
        margin-right: 6px;
        min-width: 14px;
        min-height: 14px;
    }
    .role-label {
        font-size: 0.8rem;
        color: #444;
        line-height: 1.2;
    }
    .role-checkbox input[type="checkbox"]:checked + .role-label {
        font-weight: 500;
        color: #333;
    }
    .role-checkbox:has(input[type="checkbox"]:checked) {
        background-color: #f0f0f0;
        border-color: #ccc;
    }
    .no-roles-message {
        color: #555;
        font-size: 0.95rem;
        margin: 10px 0;
    }

    /* Mobile responsive styles for role edit */
    @media screen and (max-width: 480px) {
        .role-options {
            grid-template-columns: repeat(2, 1fr);
            gap: 5px;
        }

        .role-checkbox {
            padding: 4px 5px;
            min-height: 28px;
        }

        .role-checkbox input[type="checkbox"] {
            min-width: 14px;
            min-height: 14px;
            margin-right: 4px;
        }

        .role-label {
            font-size: 0.75rem;
        }
    }

    /* Small mobile responsive styles */
    @media screen and (max-width: 360px) {
        .role-options-container {
            padding: 8px;
            margin-bottom: 8px;
        }

        .role-options-container h3 {
            font-size: 0.85rem;
            padding-bottom: 4px;
            margin-bottom: 6px;
        }

        .role-options {
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
        }

        .role-checkbox {
            padding: 3px 4px;
            min-height: 26px;
        }

        .role-checkbox input[type="checkbox"] {
            min-width: 12px;
            min-height: 12px;
            margin-right: 3px;
        }

        .role-label {
            font-size: 0.7rem;
        }
    }
    #delete-address-modal .modal-content {
        max-width: 400px;
        border-radius: 4px;
    }
    #delete-address-modal .modal-content p {
        margin: 15px 0;
        font-size: 1rem;
        color: #333;
    }
    #delete-address-modal .button-group {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    #delete-address-modal .button-group button {
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 0.95rem;
        cursor: pointer;
        border: none;
    }
    #delete-address-modal .button-group .delete-btn {
        background-color: #f5222d;
        color: white;
    }
    #delete-address-modal .button-group .delete-btn:hover {
        background-color: #d61f26;
    }
    #delete-address-modal .button-group .cancel-btn {
        background-color: #f0f0f0;
        color: #333;
    }
    #delete-address-modal .button-group .cancel-btn:hover {
        background-color: #e0e0e0;
    }
</style>

<nav class="dash-nav">
    <div class="dash-nav-container">
        <a href="/?lang={{ .lang }}" class="dash-nav-brand">BiomassX</a>
        <div class="dash-nav-links">
            <a href="/dashboard?lang={{ .lang }}">Dashboard</a>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Submit orders</a>
                <div class="dropdown-content">
                    <a href="/order?lang={{ .lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Order book</a>
                <div class="dropdown-content">
                    <a href="/order_book?lang={{ .lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Matched orders</a>
                <div class="dropdown-content">
                    <a href="/contract?lang={{ .lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Setting</a>
                <div class="dropdown-content">
                    <a href="/profile?lang={{ .lang }}">Profile</a>
                </div>
            </div>

            <a href="/logout?lang={{ .lang }}" hx-post="/logout?lang={{ .lang }}" hx-redirect="/login?lang={{ .lang }}">Logout</a>

            <!-- <div class="language-selector">
                <form hx-get="/profile" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">Language:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>ไทย</option>
                    </select>
                </form>
            </div> -->
        </div>
        <!-- <div class="desktop-lang-selector">
            <form hx-get="/profile" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                    <option value="en" {{ if eq .lang "en" }}selected{{ end }}>EN</option>
                    <option value="th" {{ if eq .lang "th" }}selected{{ end }}>ไทย</option>
                </select>
            </form>
        </div> -->
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="main-dashboard profile-page">
    <div id="response"></div>
    <div class="profile-container">
        <h2>Profile Information</h2>

        <!-- User Information -->
        <div class="user-info">
            <p><strong>Name:</strong>
                <span id="first_name">{{if .user.FirstName.Valid}}{{.user.FirstName.String}}{{end}}</span>
                <span id="last_name">{{if .user.LastName.Valid}}{{.user.LastName.String}}{{end}}</span>
            </p>
            <p><strong>Email:</strong> <span id="email">{{if .user.Email.Valid}}{{.user.Email.String}}{{end}}</span></p>
            <p><strong>Phone:</strong> <span id="phone">{{if .user.Phone.Valid}}{{.user.Phone.String}}{{end}}</span></p>
            <button class="edit-btn" onclick="toggleEdit('user')">Edit</button>
        </div>
        <div class="address-types-summary">
          <h3>Address Types</h3>
          <div id="address-types-indicators">
              <p>Loading address types...</p>
          </div>
        </div>

        <!-- Role Selection -->
        <h2>User Roles</h2>
        <div class="roles-section">
            <p>Select your roles in the BiomassX ecosystem:</p>
            <button class="edit-btn" onclick="toggleEdit('roles')">Edit Roles</button>
            <div id="roles-display">
                <p>Loading roles...</p>
            </div>
        </div>

        <!-- Displaying Addresses -->
        {{if .user.Addresses}}
        <h2>Addresses</h2>
        <div class="address-table-container">
            <table class="address-table">
                <thead>
                    <tr>
                        <th>Address Type</th>
                        <th>Branch number</th>
                        <th>Tax ID</th>
                        <th>Address</th>
                        <th>Street</th>
                        <th>Subdistrict/Town</th>
                        <th>District/City</th>
                        <th>Province/State</th>
                        <th>Country</th>
                        <th>Postal Code</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {{range $index, $address := .user.Addresses}}
                    <tr data-address-id="{{$address.ID.Int64}}">
                        <td>{{if $address.AddressType.EnName.Valid}}{{$address.AddressType.EnName.String}}{{end}}</td>
                        <td>{{if $address.BranchNumber.Valid}}{{$address.BranchNumber.String}}{{end}}</td>
                        <td>{{if $address.Taxnumber.Valid}}{{$address.Taxnumber.String}}{{end}}</td>
                        <td>{{if $address.Address.Valid}}{{$address.Address.String}}{{end}}</td>
                        <td>{{if $address.Street.Valid}}{{$address.Street.String}}{{end}}</td>
                        <td>{{if $address.Subdistrict.EnName.Valid}}{{$address.Subdistrict.EnName.String}}{{end}}</td>
                        <td>{{if $address.District.EnName.Valid}}{{$address.District.EnName.String}}{{end}}</td>
                        <td>{{if $address.Province.EnName.Valid}}{{$address.Province.EnName.String}}{{end}}</td>
                        <td>{{if $address.Country.EnName.Valid}}{{$address.Country.EnName.String}}{{end}}</td>
                        <td>{{if $address.PostalCode.Valid}}{{$address.PostalCode.String}}{{end}}</td>
                        <td>
                            <button class="edit-btn" onclick="toggleEdit('address-{{$index}}')">Edit</button>
                            <button class="delete-btn" onclick="showDeleteConfirmation('{{$address.ID.Int64}}')">Delete</button>
                        </td>
                    </tr>
                    {{end}}
                </tbody>
            </table>
        </div>
        {{else}}
        <h3>No addresses available.</h3>
        {{end}}

        <!-- Add Address Button -->
        <button class="add-address-btn" onclick="toggleEdit('add-address')">Add Address</button>
    </div>

    <!-- User Edit Modal -->
    <div id="user-edit-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('user-edit-modal')">×</span>
            <h2>Edit Profile</h2>
            <form id="profile-form">
                <input type="hidden" name="_csrf" value="{{.csrfToken}}">
                <div class="form-group">
                    <label for="first_name">First name:</label>
                    <input type="text" id="edit_first_name" name="firstName" value="{{if .user.FirstName.Valid}}{{.user.FirstName.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="last_name">Last name:</label>
                    <input type="text" id="edit_last_name" name="lastName" value="{{if .user.LastName.Valid}}{{.user.LastName.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="organization_name">Organization name</label>
                    <input type="text" id="edit_organization_name" name="organizationName" value="{{if .user.Organization.Valid}}{{.user.Organization.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="username">User name:</label>
                    <input type="text" id="edit_username" name="username" value="{{if .user.Username.Valid}}{{.user.Username.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="edit_email" name="email" value="{{if .user.Email.Valid}}{{.user.Email.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="phone">Phone:</label>
                    <input type="text" id="edit_phone" name="phone" value="{{if .user.Phone.Valid}}{{.user.Phone.String}}{{end}}">
                </div>
                <div class="form-group">
                    <label for="password">New password:</label>
                    <input type="password" id="edit_password" name="password">
                    <small>Leave blank if you don't want to change password</small>
                </div>
                <div class="form-group">
                    <label for="confirm_password">Confirm New Password:</label>
                    <input type="password" id="edit_confirm_password" name="confirmPassword">
                </div>
                <div class="button-group">
                    <button type="button" onclick="saveUserProfile()">Save</button>
                    <button type="button" onclick="closeModal('user-edit-modal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Roles Edit Modal -->
    <div id="roles-edit-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('roles-edit-modal')">×</span>
            <h2>Edit Roles</h2>
            <form id="roles-form">
                <input type="hidden" name="_csrf" value="{{.csrfToken}}">
                <div id="roles-checkboxes">
                    <!-- Populated dynamically via JavaScript -->
                </div>
                <div class="button-group">
                    <button type="button" onclick="saveUserRoles()">Save</button>
                    <button type="button" onclick="closeModal('roles-edit-modal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Address Modal -->
    <div id="add-address-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('add-address-modal')">×</span>
            <h2>Add Address</h2>
            <form id="add-address-form">
                <input type="hidden" name="_csrf" value="{{.csrfToken}}">
                <div class="form-group">
                    <label for="address_type_id">Address Type:</label>
                    <select id="address_type_id" name="address_type_id" required>
                        <option value="">--Select Address Type--</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="branch_number">Branch:</label>
                    <input type="text" id="branch_number" name="branch_number" maxlength="5">
                </div>
                <div class="form-group">
                    <label for="tax_number">Tax ID:</label>
                    <input type="text" id="tax_number" name="tax_number" maxlength="13">
                </div>
                <div class="form-group">
                    <label for="country_id">Country:</label>
                    <select id="country_id" name="country_id" required>
                        <option value="">--Select Country--</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="province_id">Province:</label>
                    <select id="province_id" name="province_id" required>
                        <option value="">--Select Province--</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="district_id">District:</label>
                    <select id="district_id" name="district_id" required>
                        <option value="">--Select District--</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="subdistrict_id">Subdistrict:</label>
                    <select id="subdistrict_id" name="subdistrict_id" required>
                        <option value="">--Select Subdistrict--</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="street">Street:</label>
                    <input type="text" id="street" name="street">
                </div>
                <div class="form-group">
                    <label for="address">Address:</label>
                    <textarea id="address" name="address" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="postal_code">Postal Code:</label>
                    <input type="text" id="postal_code" name="postal_code" maxlength="5" required>
                </div>
                <div class="button-group">
                    <button type="button" onclick="saveAddress()">Save</button>
                    <button type="button" onclick="closeModal('add-address-modal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Address Modal -->
    <div id="edit-address-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('edit-address-modal')">×</span>
            <h2>Edit Address</h2>
            <form id="edit-address-form">
                <input type="hidden" name="_csrf" value="{{.csrfToken}}">
                <input type="hidden" id="edit_address_id" name="address_id">
                <div class="form-group">
                    <label for="edit_address_type_id">Address Type:</label>
                    <select id="edit_address_type_id" name="address_type_id" required>
                        <option value="">--Select Address Type--</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_branch_number">Branch:</label>
                    <input type="text" id="edit_branch_number" name="branch_number" maxlength="5">
                </div>
                <div class="form-group">
                    <label for="edit_taxnumber">Tax ID:</label>
                    <input type="text" id="edit_taxnumber" name="tax_number" maxlength="13">
                </div>
                <div class="form-group">
                    <label for="edit_country_id">Country:</label>
                    <select id="edit_country_id" name="country_id" required>
                        <option value="">--Select Country--</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_province_id">Province:</label>
                    <select id="edit_province_id" name="province_id" required>
                        <option value="">--Select Province--</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_district_id">District:</label>
                    <select id="edit_district_id" name="district_id" required>
                        <option value="">--Select District--</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_subdistrict_id">Subdistrict:</label>
                    <select id="edit_subdistrict_id" name="subdistrict_id" required>
                        <option value="">--Select Subdistrict--</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_street">Street:</label>
                    <input type="text" id="edit_street" name="street">
                </div>
                <div class="form-group">
                    <label for="edit_address">Address:</label>
                    <textarea id="edit_address" name="address" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label for="edit_postal_code">Postal Code:</label>
                    <input type="text" id="edit_postal_code" name="postal_code" maxlength="5" required>
                </div>
                <div class="button-group">
                    <button type="button" onclick="updateAddress()">Save</button>
                    <button type="button" onclick="closeModal('edit-address-modal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-address-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal('delete-address-modal')">×</span>
            <h2>BiomassX</h2>
            <p>Are you sure you want to delete this address?</p>
            <div class="button-group">
                <button type="button" class="delete-btn" onclick="confirmDeleteAddress()">Delete</button>
                <button type="button" class="cancel-btn" onclick="closeModal('delete-address-modal')">Cancel</button>
            </div>
        </div>
    </div>
</main>

<script>
// Toggle edit modals
window.toggleEdit = function(formType) {
    if (formType === 'user') {
        document.getElementById('user-edit-modal').style.display = 'block';
    } else if (formType === 'roles') {
        document.getElementById('roles-edit-modal').style.display = 'block';
        loadRoles();
    } else if (formType === 'add-address') {
        document.getElementById('add-address-modal').style.display = 'block';
        loadAddressTypes();
        loadCountries();
    } else if (formType.startsWith('address-')) {
        const index = formType.split('-')[1];
        const addressRow = document.querySelectorAll('.address-table tbody tr')[parseInt(index)];
        const addressId = addressRow.getAttribute('data-address-id');
        document.getElementById('edit-address-modal').style.display = 'block';
        loadAddressForEditing(addressId);
    }
};

// Close modals and reset forms
window.closeModal = function(modalId) {
    const modal = document.getElementById(modalId);
    modal.style.display = 'none';
    if (modalId === 'add-address-modal') {
        document.getElementById('add-address-form').reset();
    } else if (modalId === 'edit-address-modal') {
        document.getElementById('edit-address-form').reset();
    } else if (modalId === 'roles-edit-modal') {
        document.getElementById('roles-form').reset();
    } else if (modalId === 'user-edit-modal') {
        document.getElementById('profile-form').reset();
    }
};

// Show messages
function showMessage(message, className) {
    const responseDiv = document.getElementById('response');
    responseDiv.className = className;
    responseDiv.textContent = message;
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setTimeout(() => {
        responseDiv.textContent = '';
        responseDiv.className = '';
    }, 3000);
}

// Load roles and populate checkboxes
function loadRoles() {
    const rolesContainer = document.getElementById('roles-checkboxes');
    rolesContainer.innerHTML = '<p>Loading roles...</p>';

    fetch('/api/segments-subsegments')
        .then(response => {
            if (!response.ok) throw new Error('Failed to fetch segments and subsegments');
            return response.json();
        })
        .then(data => {
            if (data.success && data.segments) {
                // Override segment_id for specific subsegments
                const subsegmentOverrides = {
                    11: { segment_id: 11, segment_name: 'Producer (biomass/biofuels/bioenergy)' }, // Gaseous biofuel producer
                    29: { segment_id: 11, segment_name: 'Producer (biomass/biofuels/bioenergy)' }  // Solid biofuel producer
                };

                // Apply overrides to subsegments
                data.subsegments = data.subsegments.map(sub => ({
                    ...sub,
                    segment_id: subsegmentOverrides[sub.id]?.segment_id || sub.segment_id
                }));

                let html = '<div class="roles-grid">';

                data.segments.forEach(segment => {
                    const subsegments = data.subsegments.filter(sub => sub.segment_id === segment.id);
                    if (subsegments.length === 0) return; // Skip empty segments

                    html += `
                        <div class="role-segment">
                            <h3>${segment.en_name}</h3>
                            <div class="role-options">
                    `;

                    subsegments.forEach(subsegment => {
                        html += `
                            <label class="role-checkbox">
                                <input type="checkbox" name="subsegment_ids" value="${subsegment.id}"
                                        data-segment-id="${subsegment.segment_id}">
                                <span class="role-label">${subsegment.en_name}</span>
                            </label>
                        `;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                });

                html += '</div>';
                rolesContainer.innerHTML = html;

                // Fetch existing user roles
                fetch('/api/user-roles')
                    .then(response => {
                        if (!response.ok) throw new Error('Failed to fetch user roles');
                        return response.json();
                    })
                    .then(rolesData => {
                        if (rolesData.success && rolesData.roles) {
                            rolesData.roles.forEach(role => {
                                const checkbox = document.querySelector(`input[name="subsegment_ids"][value="${role.subsegment_id}"]`);
                                if (checkbox) checkbox.checked = true;
                            });
                        }
                        updateRolesDisplay();
                    })
                    .catch(error => {
                        showMessage('Error loading user roles: ' + error.message, 'error-message');
                    });
            }
        })
        .catch(error => {
            showMessage('Error loading roles: ' + error.message, 'error-message');
        });
}

// Update displayed roles
function updateRolesDisplay() {
    const rolesDisplay = document.getElementById('roles-display');
    fetch('/api/user-roles')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.roles) {
                if (data.roles.length === 0) {
                    rolesDisplay.innerHTML = '<p class="no-roles-message">You have not selected any roles yet. Click "Edit Roles" to add your roles.</p>';
                    return;
                }

                // Override segment names for specific subsegments
                const segmentOverrides = {
                    11: 'Producer (biomass/biofuels/bioenergy)', // Gaseous biofuel producer
                    29: 'Producer (biomass/biofuels/bioenergy)'  // Solid biofuel producer
                };

                // Group roles by segment
                const rolesBySegment = {};
                data.roles.forEach(role => {
                    const segmentName = segmentOverrides[role.subsegment_id] || role.segment_name || 'Other';
                    if (!rolesBySegment[segmentName]) {
                        rolesBySegment[segmentName] = [];
                    }
                    rolesBySegment[segmentName].push(role.subsegment_name || 'Unknown');
                });

                let html = '<div class="roles-list">';

                for (const segment in rolesBySegment) {
                    html += `
                        <div class="role-segment">
                            <h3>${segment}</h3>
                            <ul>
                                ${rolesBySegment[segment].map(subsegment =>
                                    `<li><span class="role-tag">${subsegment}</span></li>`
                                ).join('')}
                            </ul>
                        </div>
                    `;
                }

                html += '</div>';
                rolesDisplay.innerHTML = html;
            }
        })
        .catch(error => {
            console.error('Error updating roles display:', error);
            rolesDisplay.innerHTML = '<p class="error-message">Error loading roles. Please try again.</p>';
        });
}

// Save user roles
window.saveUserRoles = function() {
    const checkboxes = document.querySelectorAll('input[name="subsegment_ids"]:checked');
    const subsegmentIds = Array.from(checkboxes).map(cb => ({
        subsegment_id: parseInt(cb.value),
        segment_id: parseInt(cb.getAttribute('data-segment-id'))
    }));

    if (subsegmentIds.length === 0) {
        showMessage('Please select at least one role.', 'error-message');
        return;
    }

    const csrfToken = document.querySelector('#roles-form input[name="_csrf"]').value;
    const requestData = { roles: subsegmentIds, _csrf: csrfToken };

    fetch('/api/update-user-roles', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken
        },
        body: JSON.stringify(requestData),
        credentials: 'same-origin'
    })
        .then(response => {
            if (!response.ok) throw new Error('Failed to update roles: ' + response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('Roles updated successfully', 'success-message');
                closeModal('roles-edit-modal');
                updateRolesDisplay();
                triggerProfileCompletenessCheck();
            } else {
                throw new Error(data.message || 'Failed to update roles');
            }
        })
        .catch(error => {
            showMessage('Error updating roles: ' + error.message, 'error-message');
        });
};

// Trigger profile completeness check
function triggerProfileCompletenessCheck() {
    const csrfToken = document.querySelector('#profile-form input[name="_csrf"]').value;
    fetch('/api/check-profile-completeness', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken
        },
        credentials: 'same-origin'
    })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.warn('Profile completeness check failed:', data.message);
            }
        })
        .catch(error => {
            console.error('Error triggering profile completeness check:', error);
        });
}

// Validate profile form
function validateForm() {
    const password = document.getElementById('edit_password').value;
    const confirmPassword = document.getElementById('edit_confirm_password').value;
    if (password && password !== confirmPassword) {
        showMessage('Passwords do not match. Please check again.', 'error-message');
        return false;
    }
    return true;
}

// Save user profile
window.saveUserProfile = function() {
    if (!validateForm()) return;

    const userData = {
        firstName: document.getElementById('edit_first_name').value,
        lastName: document.getElementById('edit_last_name').value,
        organizationName: document.getElementById('edit_organization_name').value,
        username: document.getElementById('edit_username').value,
        email: document.getElementById('edit_email').value,
        phone: document.getElementById('edit_phone').value,
        password: document.getElementById('edit_password').value,
        _csrf: document.querySelector('#profile-form input[name="_csrf"]').value
    };

    Object.keys(userData).forEach(key => {
        if (!userData[key] && key !== '_csrf') delete userData[key];
    });

    fetch('/api/update-profile', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': userData._csrf
        },
        body: JSON.stringify(userData),
        credentials: 'same-origin'
    })
        .then(response => {
            if (!response.ok) throw new Error('Server responded with status: ' + response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('Profile information saved successfully', 'success-message');
                if (userData.firstName) document.getElementById('first_name').textContent = userData.firstName;
                if (userData.lastName) document.getElementById('last_name').textContent = userData.lastName;
                if (userData.email) document.getElementById('email').textContent = userData.email;
                if (userData.phone) document.getElementById('phone').textContent = userData.phone;
                closeModal('user-edit-modal');
                triggerProfileCompletenessCheck();
            } else {
                throw new Error(data.message || 'Failed to update profile');
            }
        })
        .catch(error => {
            showMessage('Error saving profile: ' + error.message, 'error-message');
        });
};

// Show delete confirmation modal
let addressIdToDelete = null;
window.showDeleteConfirmation = function(addressId) {
    addressIdToDelete = addressId;
    document.getElementById('delete-address-modal').style.display = 'block';
};

// Confirm address deletion
window.confirmDeleteAddress = function() {
    if (!addressIdToDelete) return;

    const requestData = {
        addressId: parseInt(addressIdToDelete),
        _csrf: document.querySelector('#profile-form input[name="_csrf"]').value
    };
    fetch('/api/delete-address', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': requestData._csrf
        },
        body: JSON.stringify(requestData),
        credentials: 'same-origin'
    })
        .then(response => {
            if (!response.ok) throw new Error('Server responded with status: ' + response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('Address deleted successfully', 'success-message');
                const addressRow = document.querySelector(`tr[data-address-id="${addressIdToDelete}"]`);
                if (addressRow) addressRow.remove();
                updateAddressTypesSummary();
                closeModal('delete-address-modal');
                addressIdToDelete = null;
                triggerProfileCompletenessCheck();
            } else {
                throw new Error(data.message || 'Failed to delete address');
            }
        })
        .catch(error => {
            showMessage('Error deleting address: ' + error.message, 'error-message');
            closeModal('delete-address-modal');
            addressIdToDelete = null;
        });
};

// Load address types
function loadAddressTypes() {
    const select = document.getElementById('address_type_id');
    select.innerHTML = '<option value="">--Select Address Type--</option>';
    select.disabled = true;

    fetch('/api/address-types')
        .then(response => {
            if (!response.ok) throw new Error('Error fetching address types');
            return response.json();
        })
        .then(data => {
            if (data.success && data.addressTypes) {
                data.addressTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.enName;
                    select.appendChild(option);
                });
                select.disabled = false;
            }
        })
        .catch(error => {
            showMessage('Unable to load address types: ' + error.message, 'error-message');
        });
}

// Load countries
function loadCountries() {
    const select = document.getElementById('country_id');
    select.innerHTML = '<option value="">--Select Country--</option>';
    select.disabled = true;

    fetch('/api/countries')
        .then(response => {
            if (!response.ok) throw new Error('Error fetching countries');
            return response.json();
        })
        .then(data => {
            if (data.success && data.countries) {
                data.countries.forEach(country => {
                    const option = document.createElement('option');
                    option.value = country.id;
                    option.textContent = country.enName;
                    select.appendChild(option);
                });
                select.disabled = false;
            }
        })
        .catch(error => {
            showMessage('Unable to load countries: ' + error.message, 'error-message');
        });
}

// Load provinces
function loadProvinces(countryId) {
    const provinceSelect = document.getElementById('province_id');
    const districtSelect = document.getElementById('district_id');
    const subdistrictSelect = document.getElementById('subdistrict_id');
    provinceSelect.innerHTML = '<option value="">--Select Province--</option>';
    districtSelect.innerHTML = '<option value="">--Select District--</option>';
    subdistrictSelect.innerHTML = '<option value="">--Select Subdistrict--</option>';
    provinceSelect.disabled = true;
    districtSelect.disabled = true;
    subdistrictSelect.disabled = true;

    if (!countryId) return;

    fetch(`/api/provinces?countryId=${countryId}`)
        .then(response => {
            if (!response.ok) throw new Error('Error fetching provinces');
            return response.json();
        })
        .then(data => {
            if (data.success && data.provinces) {
                data.provinces.forEach(province => {
                    const option = document.createElement('option');
                    option.value = province.id;
                    option.textContent = province.enName;
                    provinceSelect.appendChild(option);
                });
                provinceSelect.disabled = false;
            }
        })
        .catch(error => {
            showMessage('Unable to load provinces: ' + error.message, 'error-message');
        });
}

// Load districts
function loadDistricts(provinceId) {
    const districtSelect = document.getElementById('district_id');
    const subdistrictSelect = document.getElementById('subdistrict_id');
    districtSelect.innerHTML = '<option value="">--Select District--</option>';
    subdistrictSelect.innerHTML = '<option value="">--Select Subdistrict--</option>';
    districtSelect.disabled = true;
    subdistrictSelect.disabled = true;

    if (!provinceId) return;

    fetch(`/api/districts?provinceId=${provinceId}`)
        .then(response => {
            if (!response.ok) throw new Error('Error fetching districts');
            return response.json();
        })
        .then(data => {
            if (data.success && data.districts) {
                data.districts.forEach(district => {
                    const option = document.createElement('option');
                    option.value = district.id;
                    option.textContent = district.enName;
                    districtSelect.appendChild(option);
                });
                districtSelect.disabled = false;
            }
        })
        .catch(error => {
            showMessage('Unable to load districts: ' + error.message, 'error-message');
        });
}

// Load subdistricts
function loadSubdistricts(districtId) {
    const subdistrictSelect = document.getElementById('subdistrict_id');
    subdistrictSelect.innerHTML = '<option value="">--Select Subdistrict--</option>';
    subdistrictSelect.disabled = true;

    if (!districtId) return;

    fetch(`/api/subdistricts?districtId=${districtId}`)
        .then(response => {
            if (!response.ok) throw new Error('Error fetching subdistricts');
            return response.json();
        })
        .then(data => {
            if (data.success && data.subdistricts) {
                data.subdistricts.forEach(subdistrict => {
                    const option = document.createElement('option');
                    option.value = subdistrict.id;
                    option.textContent = subdistrict.enName;
                    subdistrictSelect.appendChild(option);
                });
                subdistrictSelect.disabled = false;
            }
        })
        .catch(error => {
            showMessage('Unable to load subdistricts: ' + error.message, 'error-message');
        });
}

// Save address
function saveAddress() {
    const form = document.getElementById('add-address-form');
    if (!form.checkValidity()) {
        showMessage('Please fill out all required fields.', 'error-message');
        return;
    }
    const addressData = {
        address_type_id: parseInt(document.getElementById('address_type_id').value),
        branch_number: document.getElementById('branch_number').value.trim(),
        tax_number: document.getElementById('tax_number').value.trim(),
        country_id: parseInt(document.getElementById('country_id').value),
        province_id: parseInt(document.getElementById('province_id').value),
        district_id: parseInt(document.getElementById('district_id').value),
        subdistrict_id: parseInt(document.getElementById('subdistrict_id').value),
        street: document.getElementById('street').value.trim(),
        address: document.getElementById('address').value.trim(),
        postal_code: document.getElementById('postal_code').value.trim(),
        _csrf: document.querySelector('#add-address-form input[name="_csrf"]').value
    };
    fetch('/api/add-address', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': addressData._csrf
        },
        body: JSON.stringify(addressData),
        credentials: 'same-origin'
    })
        .then(response => {
            if (!response.ok) throw new Error('Server responded with status: ' + response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('Address added successfully', 'success-message');
                closeModal('add-address-modal');
                setTimeout(() => window.location.reload(), 1500);
                triggerProfileCompletenessCheck();
            } else {
                throw new Error(data.message || 'Failed to add address');
            }
        })
        .catch(error => {
            showMessage('Error adding address: ' + error.message, 'error-message');
        });
}

// Load address types for edit form
function loadAddressTypesForEdit() {
    const select = document.getElementById('edit_address_type_id');
    select.innerHTML = '<option value="">--Select Address Type--</option>';
    select.disabled = true;

    fetch('/api/address-types')
        .then(response => {
            if (!response.ok) throw new Error('Error fetching address types');
            return response.json();
        })
        .then(data => {
            if (data.success && data.addressTypes) {
                data.addressTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.id;
                    option.textContent = type.enName;
                    select.appendChild(option);
                });
                select.disabled = false;
            }
        })
        .catch(error => {
            showMessage('Unable to load address types: ' + error.message, 'error-message');
        });
}

// Load countries for edit form
function loadCountriesForEdit() {
    const select = document.getElementById('edit_country_id');
    select.innerHTML = '<option value="">--Select Country--</option>';
    select.disabled = true;

    fetch('/api/countries')
        .then(response => {
            if (!response.ok) throw new Error('Error fetching countries');
            return response.json();
        })
        .then(data => {
            if (data.success && data.countries) {
                data.countries.forEach(country => {
                    const option = document.createElement('option');
                    option.value = country.id;
                    option.textContent = country.enName;
                    select.appendChild(option);
                });
                select.disabled = false;
            }
        })
        .catch(error => {
            showMessage('Unable to load countries: ' + error.message, 'error-message');
        });
}

// Load provinces for edit form
function loadProvincesForEdit(countryId) {
    return new Promise((resolve, reject) => {
        const provinceSelect = document.getElementById('edit_province_id');
        const districtSelect = document.getElementById('edit_district_id');
        const subdistrictSelect = document.getElementById('edit_subdistrict_id');
        provinceSelect.innerHTML = '<option value="">--Select Province--</option>';
        districtSelect.innerHTML = '<option value="">--Select District--</option>';
        subdistrictSelect.innerHTML = '<option value="">--Select Subdistrict--</option>';
        provinceSelect.disabled = true;
        districtSelect.disabled = true;
        subdistrictSelect.disabled = true;

        if (!countryId) {
            resolve();
            return;
        }

        fetch(`/api/provinces?countryId=${countryId}`)
            .then(response => {
                if (!response.ok) throw new Error('Error fetching provinces');
                return response.json();
            })
            .then(data => {
                if (data.success && data.provinces) {
                    data.provinces.forEach(province => {
                        const option = document.createElement('option');
                        option.value = province.id;
                        option.textContent = province.enName;
                        provinceSelect.appendChild(option);
                    });
                    provinceSelect.disabled = false;
                    resolve();
                }
            })
            .catch(error => {
                showMessage('Unable to load provinces: ' + error.message, 'error-message');
                reject(error);
            });
    });
}

// Load districts for edit form
function loadDistrictsForEdit(provinceId) {
    return new Promise((resolve, reject) => {
        const districtSelect = document.getElementById('edit_district_id');
        const subdistrictSelect = document.getElementById('edit_subdistrict_id');
        districtSelect.innerHTML = '<option value="">--Select District--</option>';
        subdistrictSelect.innerHTML = '<option value="">--Select Subdistrict--</option>';
        districtSelect.disabled = true;
        subdistrictSelect.disabled = true;

        if (!provinceId) {
            resolve();
            return;
        }

        fetch(`/api/districts?provinceId=${provinceId}`)
            .then(response => {
                if (!response.ok) throw new Error('Error fetching districts');
                return response.json();
            })
            .then(data => {
                if (data.success && data.districts) {
                    data.districts.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.id;
                        option.textContent = district.enName;
                        districtSelect.appendChild(option);
                    });
                    districtSelect.disabled = false;
                    resolve();
                }
            })
            .catch(error => {
                showMessage('Unable to load districts: ' + error.message, 'error-message');
                reject(error);
            });
    });
}

// Load subdistricts for edit form
function loadSubdistrictsForEdit(districtId) {
    return new Promise((resolve, reject) => {
        const subdistrictSelect = document.getElementById('edit_subdistrict_id');
        subdistrictSelect.innerHTML = '<option value="">--Select Subdistrict--</option>';
        subdistrictSelect.disabled = true;

        if (!districtId) {
            resolve();
            return;
        }

        fetch(`/api/subdistricts?districtId=${districtId}`)
            .then(response => {
                if (!response.ok) throw new Error('Error fetching subdistricts');
                return response.json();
            })
            .then(data => {
                if (data.success && data.subdistricts) {
                    data.subdistricts.forEach(subdistrict => {
                        const option = document.createElement('option');
                        option.value = subdistrict.id;
                        option.textContent = subdistrict.enName;
                        subdistrictSelect.appendChild(option);
                    });
                    subdistrictSelect.disabled = false;
                    resolve();
                }
            })
            .catch(error => {
                showMessage('Unable to load subdistricts: ' + error.message, 'error-message');
                reject(error);
            });
    });
}

// Load address for editing
function loadAddressForEditing(addressId) {
    document.getElementById('edit_address_id').value = addressId;

    const addressRow = document.querySelector(`tr[data-address-id="${addressId}"]`);
    if (!addressRow) {
        showMessage('Address not found', 'error-message');
        return;
    }

    const cells = addressRow.querySelectorAll('td');

    loadAddressTypesForEdit();
    loadCountriesForEdit();

    setTimeout(() => {
        const addressTypeText = cells[0].textContent.trim();
        const addressTypeSelect = document.getElementById('edit_address_type_id');
        for (let i = 0; i < addressTypeSelect.options.length; i++) {
            if (addressTypeSelect.options[i].text === addressTypeText) {
                addressTypeSelect.value = addressTypeSelect.options[i].value;
                break;
            }
        }

        document.getElementById('edit_branch_number').value = cells[1].textContent.trim();
        document.getElementById('edit_taxnumber').value = cells[2].textContent.trim();

        document.getElementById('edit_address').value = cells[3].textContent.trim();
        document.getElementById('edit_street').value = cells[4].textContent.trim();

        document.getElementById('edit_postal_code').value = cells[9].textContent.trim();

        const countryText = cells[8].textContent.trim();
        const provinceText = cells[7].textContent.trim();
        const districtText = cells[6].textContent.trim();
        const subdistrictText = cells[5].textContent.trim();

        const countrySelect = document.getElementById('edit_country_id');
        for (let i = 0; i < countrySelect.options.length; i++) {
            if (countrySelect.options[i].text === countryText) {
                countrySelect.value = countrySelect.options[i].value;
                break;
            }
        }

        if (countrySelect.value) {
            loadProvincesForEdit(countrySelect.value)
                .then(() => {
                    const provinceSelect = document.getElementById('edit_province_id');
                    for (let i = 0; i < provinceSelect.options.length; i++) {
                        if (provinceSelect.options[i].text === provinceText) {
                            provinceSelect.value = provinceSelect.options[i].value;
                            break;
                        }
                    }

                    if (provinceSelect.value) {
                        return loadDistrictsForEdit(provinceSelect.value);
                    }
                })
                .then(() => {
                    const districtSelect = document.getElementById('edit_district_id');
                    for (let i = 0; i < districtSelect.options.length; i++) {
                        if (districtSelect.options[i].text === districtText) {
                            districtSelect.value = districtSelect.options[i].value;
                            break;
                        }
                    }

                    if (districtSelect.value) {
                        return loadSubdistrictsForEdit(districtSelect.value);
                    }
                })
                .then(() => {
                    const subdistrictSelect = document.getElementById('edit_subdistrict_id');
                    for (let i = 0; i < subdistrictSelect.options.length; i++) {
                        if (subdistrictSelect.options[i].text === subdistrictText) {
                            subdistrictSelect.value = subdistrictSelect.options[i].value;
                            break;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error setting up location dropdowns:', error);
                });
        }
    }, 500);
}

// Update address
function updateAddress() {
    const form = document.getElementById('edit-address-form');
    if (!form.checkValidity()) {
        showMessage('Please fill out all required fields.', 'error-message');
        return;
    }
    const addressData = {
        address_id: parseInt(document.getElementById('edit_address_id').value),
        address_type_id: parseInt(document.getElementById('edit_address_type_id').value),
        branch_number: document.getElementById('edit_branch_number').value.trim(),
        tax_number: document.getElementById('edit_taxnumber').value.trim(),
        country_id: parseInt(document.getElementById('edit_country_id').value),
        province_id: parseInt(document.getElementById('edit_province_id').value),
        district_id: parseInt(document.getElementById('edit_district_id').value),
        subdistrict_id: parseInt(document.getElementById('edit_subdistrict_id').value),
        street: document.getElementById('edit_street').value.trim(),
        address: document.getElementById('edit_address').value.trim(),
        postal_code: document.getElementById('edit_postal_code').value.trim(),
        _csrf: document.querySelector('#edit-address-form input[name="_csrf"]').value
    };
    fetch('/api/update-address', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': addressData._csrf
        },
        body: JSON.stringify(addressData),
        credentials: 'same-origin'
    })
        .then(response => {
            if (!response.ok) throw new Error('Server responded with status: ' + response.status);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage('Address updated successfully', 'success-message');
                closeModal('edit-address-modal');
                setTimeout(() => window.location.reload(), 1500);
                triggerProfileCompletenessCheck();
            } else {
                throw new Error(data.message || 'Failed to update address');
            }
        })
        .catch(error => {
            showMessage('Error updating address: ' + error.message, 'error-message');
        });
}

// Event listeners for cascading dropdowns
document.addEventListener('DOMContentLoaded', function() {
    const countrySelect = document.getElementById('country_id');
    if (countrySelect) {
        countrySelect.addEventListener('change', function() {
            loadProvinces(this.value);
        });
    }

    const provinceSelect = document.getElementById('province_id');
    if (provinceSelect) {
        provinceSelect.addEventListener('change', function() {
            loadDistricts(this.value);
        });
    }

    const districtSelect = document.getElementById('district_id');
    if (districtSelect) {
        districtSelect.addEventListener('change', function() {
            loadSubdistricts(this.value);
        });
    }

    const editCountrySelect = document.getElementById('edit_country_id');
    if (editCountrySelect) {
        editCountrySelect.addEventListener('change', function() {
            loadProvincesForEdit(this.value);
        });
    }

    const editProvinceSelect = document.getElementById('edit_province_id');
    if (editProvinceSelect) {
        editProvinceSelect.addEventListener('change', function() {
            loadDistrictsForEdit(this.value);
        });
    }

    const editDistrictSelect = document.getElementById('edit_district_id');
    if (editDistrictSelect) {
        editDistrictSelect.addEventListener('change', function() {
            loadSubdistrictsForEdit(this.value);
        });
    }
});

// Function to update address types summary
function updateAddressTypesSummary() {
    const indicatorsContainer = document.getElementById('address-types-indicators');
    if (!indicatorsContainer) return;

    const addressTable = document.querySelector('.address-table');
    if (!addressTable) {
        let html = '';
        const addressTypes = {
            1: { singular: 'Billing address', plural: 'Billing addresses', count: 0 },
            2: { singular: 'Branch address', plural: 'Branch addresses', count: 0 },
            3: { singular: 'Shipping address', plural: 'Shipping addresses', count: 0 }
        };

        for (const typeId in addressTypes) {
            const type = addressTypes[typeId];
            html += `
                <div class="address-type-indicator no-address">
                    <span>${type.singular}</span>
                    <div class="address-type-status status-not-set" title="Not set up"></div>
                </div>
            `;
        }

        indicatorsContainer.innerHTML = html;
        return;
    }

    const addressRows = addressTable.querySelectorAll('tbody tr');
    const addressTypes = {
        1: { singular: 'Billing address', plural: 'Billing addresses', count: 0 },
        2: { singular: 'Branch address', plural: 'Branch addresses', count: 0 },
        3: { singular: 'Shipping address', plural: 'Shipping addresses', count: 0 }
    };

    addressRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 0) {
            const addressTypeText = cells[0].textContent.trim();

            if (addressTypeText.includes('billing') || addressTypeText.toLowerCase().includes('billing')) {
                addressTypes[1].count++;
            } else if (addressTypeText.includes('branch') || addressTypeText.toLowerCase().includes('branch')) {
                addressTypes[2].count++;
            } else if (addressTypeText.includes('shipping') || addressTypeText.toLowerCase().includes('shipping')) {
                addressTypes[3].count++;
            }
        }
    });

    let html = '';

    for (const typeId in addressTypes) {
        const type = addressTypes[typeId];
        const hasAddress = type.count > 0;
        const statusClass = hasAddress ? 'status-set' : 'status-not-set';
        const indicatorClass = hasAddress ? 'has-address' : 'no-address';
        const displayName = type.count > 1 ? type.plural : type.singular;

        html += `
            <div class="address-type-indicator ${indicatorClass}">
                <span>${displayName}</span>
                <div class="address-type-status ${statusClass}" title="${hasAddress ? 'Set up' : 'Not set up'}"></div>
                ${hasAddress ? ` (${type.count})` : ''}
            </div>
        `;
    }

    indicatorsContainer.innerHTML = html;
}

// Function to load and display user roles
function loadAndDisplayUserRoles() {
    const rolesDisplay = document.getElementById('roles-display');
    if (!rolesDisplay) return;

    fetch('/api/user-roles')
        .then(response => {
            if (!response.ok) throw new Error('Failed to fetch user roles');
            return response.json();
        })
        .then(data => {
            if (data.success) {
                if (!data.roles || data.roles.length === 0) {
                    rolesDisplay.innerHTML = '<p class="no-roles-message">You have not selected any roles yet. Click "Edit Roles" to add your roles.</p>';
                    return;
                }

                // Override segment names for specific subsegments
                const segmentOverrides = {
                    11: 'Producer (biomass/biofuels/bioenergy)', // Gaseous biofuel producer
                    29: 'Producer (biomass/biofuels/bioenergy)'  // Solid biofuel producer
                };

                // Group roles by segment
                const rolesBySegment = {};
                data.roles.forEach(role => {
                    const segmentName = segmentOverrides[role.subsegment_id] || role.segment_name || 'Other';
                    if (!rolesBySegment[segmentName]) {
                        rolesBySegment[segmentName] = [];
                    }
                    rolesBySegment[segmentName].push(role.subsegment_name || 'Unknown');
                });

                let html = '<div class="roles-list">';

                for (const segment in rolesBySegment) {
                    html += `
                        <div class="role-segment">
                            <h3>${segment}</h3>
                            <ul>
                                ${rolesBySegment[segment].map(subsegment =>
                                    `<li><span class="role-tag">${subsegment}</span></li>`
                                ).join('')}
                            </ul>
                        </div>
                    `;
                }

                html += '</div>';
                rolesDisplay.innerHTML = html;
            } else {
                rolesDisplay.innerHTML = '<p class="error-message">Error loading roles. Please try again.</p>';
            }
        })
        .catch(error => {
            console.error('Error loading user roles:', error);
            rolesDisplay.innerHTML = '<p class="error-message">Error loading roles. Please try again.</p>';
        });
}

// Call the function when the page loads
document.addEventListener('DOMContentLoaded', function() {
    loadAndDisplayUserRoles();
    updateAddressTypesSummary();

    // Elements
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.dash-nav-links');
    const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
    const dropdownContents = document.querySelectorAll('.dropdown-content');
    const body = document.body;

    // Mobile detection
    const isMobile = () => window.innerWidth <= 768;

    // Toggle hamburger menu
    if (hamburger && navLinks) {
        hamburger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Toggle active classes
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');

            // Manage body scroll
            if (navLinks.classList.contains('active')) {
                body.style.overflow = 'hidden';

                // Show all dropdown contents in mobile view
                if (isMobile()) {
                    dropdownContents.forEach(content => {
                        content.style.display = 'block';
                    });
                }
            } else {
                body.style.overflow = '';

                // Hide all dropdown contents when closing menu
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });
    }

    // Handle dropdown triggers in mobile view
    dropdownTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            if (isMobile()) {
                e.preventDefault();
                e.stopPropagation();

                const content = this.nextElementSibling;
                const isVisible = content.style.display === 'block';

                // Hide all dropdowns first
                dropdownContents.forEach(dropdown => {
                    dropdown.style.display = 'none';
                });

                // Toggle current dropdown
                if (!isVisible) {
                    content.style.display = 'block';
                }
            }
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        if (!isMobile()) return;

        const isClickInsideNav = navLinks && navLinks.contains(event.target);
        const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

        if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
            navLinks.classList.remove('active');
            if (hamburger) hamburger.classList.remove('active');
            body.style.overflow = '';

            // Hide all dropdown contents
            dropdownContents.forEach(content => {
                content.style.display = '';
            });
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            // Reset styles for desktop view
            if (navLinks) navLinks.classList.remove('active');
            if (hamburger) hamburger.classList.remove('active');
            body.style.overflow = '';

            // Reset dropdown display
            dropdownContents.forEach(content => {
                content.style.display = '';
            });
        }
    });
});

</script>
<div style="margin-bottom: 200px;"></div>
<footer class="footer">
    <br>
    <a href="/about?lang={{ .lang }}">About us</a> | <a href="/faqs?lang={{ .lang }}">FAQs</a> | <a href="/contact?lang={{ .lang }}">Contact us</a><br>
    <p>BIOMASS EXCHANGE CO., LTD.</p>
    <br>
</footer>
{{ end }}