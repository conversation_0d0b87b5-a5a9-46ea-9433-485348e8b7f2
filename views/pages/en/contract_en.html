
{{define "content"}}
<nav class="dash-nav">
    <div class="dash-nav-container">
        <a href="/?lang={{ .Lang }}" class="dash-nav-brand">BiomassX</a>
        <div class="dash-nav-links">
            <a href="/dashboard?lang={{ .Lang }}">Dashboard</a>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Submit orders</a>
                <div class="dropdown-content">
                    <a href="/order?lang={{ .Lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Order book</a>
                <div class="dropdown-content">
                    <a href="/order_book?lang={{ .Lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Matched orders</a>
                <div class="dropdown-content">
                    <a href="/contract?lang={{ .Lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Setting</a>
                <div class="dropdown-content">
                    <a href="/profile?lang={{ .Lang }}">Profile</a>
                </div>
            </div>

            <a href="/logout?lang={{ .Lang }}" hx-post="/logout?lang={{ .Lang }}" hx-redirect="/login?lang={{ .Lang }}">Logout</a>

            <!-- <div class="language-selector">
                <form hx-get="/contract" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">Language:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                    </select>
                </form>
            </div> -->
        </div>
        <!-- <div class="desktop-lang-selector">
            <form hx-get="/contract" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                    <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                    <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                </select>
            </form>
        </div> -->
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="main-dashboard">
<h3>My contract(s)</h3>
<div class="container">
    {{if .SellContracts}}
    <h3>Sell contract(s)</h3>
    <table border="1">
        <thead>
            <tr>
                <th>#</th>
                <th>Contract date</th>
                <th>Seller</th>
                <th>Organization</th>
                <th>Buyer</th>
                <th>Organization</th>
                <th>Quality</th>
                <th>Price</th>
                <th>Qty.</th>
                <th>Packing name</th>
                <th>Delivery term</th>
                <th>Delivery status</th>
                <th>Payment term</th>
                <th>Payment status</th>
                <th>Seller confirmation</th>
                <th>Buyer confirmation</th>
                <th>Contract status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            {{range .SellContracts}}
            <tr>
                <td>{{.ID}}</td>
                <td>{{if .ContractDate.IsZero}}N/A{{else}}{{.ContractDate.Format "02-01-2006"}}{{end}}</td>
                <td>{{if .SellerFirstName.Valid}}{{.SellerFirstName.String}} {{.SellerLastName.String}}{{else}}N/A{{end}}</td>
                <td>{{if .SellerOrgName.Valid}}{{.SellerOrgName.String}}{{else}}-{{end}}</td>
                <td>{{if .BuyerFirstName.Valid}}{{.BuyerFirstName.String}} {{.BuyerLastName.String}}{{else}}N/A{{end}}</td>
                <td>{{if .BuyerOrgName.Valid}}{{.BuyerOrgName.String}}{{else}}-{{end}}</td>
                <td>
                    {{if .StandardEnName.Valid}}Standard: {{.StandardEnName.String}} - {{else}}Standard: N/A - {{end}}
                    {{if .GradeEnName.Valid}}Grade: {{.GradeEnName.String}}{{else}}Grade: N/A{{end}}
                </td>
                <td>{{.Price}} {{if .CurrencyEnName.Valid}}{{.CurrencyEnName.String}}{{else}}N/A{{end}}</td>
                <td>{{.Quantity}} {{if .UomEn.Valid}}{{.UomEn.String}}{{else}}N/A{{end}}</td>
                <td>{{if .PackingEn.Valid}}{{.PackingEn.String}}{{else}}N/A{{end}}</td>
                <td>{{if .DeliveryTermEn.Valid}}{{.DeliveryTermEn.String}}{{else}}N/A{{end}}</td>
                <td>{{if .DeliveryStatusEn.Valid}}{{.DeliveryStatusEn.String}}{{else}}N/A{{end}}</td>
                <td>{{if .PaymentTermEn.Valid}}{{.PaymentTermEn.String}}{{else}}N/A{{end}}</td>
                <td>{{if .PaymentStatusEn.Valid}}{{.PaymentStatusEn.String}}{{else}}N/A{{end}}</td>
                <td>{{if .SellerConfirmStatusEn.Valid}}{{.SellerConfirmStatusEn.String}}{{else}}N/A{{end}}</td>
                <td>{{if .BuyerConfirmStatusEn.Valid}}{{.BuyerConfirmStatusEn.String}}{{else}}N/A{{end}}</td>
                <td>{{if .ContractStatusEn.Valid}}{{.ContractStatusEn.String}}{{else}}N/A{{end}}</td>
                <td><a href="/contract/{{.ID}}?lang={{ $.Lang }}" class="btn">View</a></td>
            </tr>
            {{end}}
        </tbody>
    </table>
    {{end}}

    {{if .BuyContracts}}
    <h3>Buy contract(s)</h3>
    <table border="1">
        <thead>
            <tr>
                <th>#</th>
                <th>Contract date</th>
                <th>Seller</th>
                <th>Organization</th>
                <th>Buyer</th>
                <th>Organization</th>
                <th>Quality</th>
                <th>Price</th>
                <th>Qty.</th>
                <th>Packing name</th>
                <th>Delivery term</th>
                <th>Delivery status</th>
                <th>Payment term</th>
                <th>Payment status</th>
                <th>Seller confirmation</th>
                <th>Buyer confirmation</th>
                <th>Contract status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            {{range .BuyContracts}}
            <tr>
                <tr>
                    <td>{{.ID}}</td>
                    <td>{{if .ContractDate.IsZero}}N/A{{else}}{{.ContractDate.Format "02-01-2006"}}{{end}}</td>
                    <td>{{if .SellerFirstName.Valid}}{{.SellerFirstName.String}} {{.SellerLastName.String}}{{else}}N/A{{end}}</td>
                    <td>{{if .SellerOrgName.Valid}}{{.SellerOrgName.String}}{{else}}-{{end}}</td>
                    <td>{{if .BuyerFirstName.Valid}}{{.BuyerFirstName.String}} {{.BuyerLastName.String}}{{else}}N/A{{end}}</td>
                    <td>{{if .BuyerOrgName.Valid}}{{.BuyerOrgName.String}}{{else}}-{{end}}</td>
                    <td>
                        {{if .StandardEnName.Valid}}Standard: {{.StandardEnName.String}} - {{else}}Standard: N/A - {{end}}
                        {{if .GradeEnName.Valid}}Grade: {{.GradeEnName.String}}{{else}}Grade: N/A{{end}}
                    </td>
                    <td>{{.Price}} {{if .CurrencyEnName.Valid}}{{.CurrencyEnName.String}}{{else}}N/A{{end}}</td>
                    <td>{{.Quantity}} {{if .UomEn.Valid}}{{.UomEn.String}}{{else}}N/A{{end}}</td>
                    <td>{{if .PackingEn.Valid}}{{.PackingEn.String}}{{else}}N/A{{end}}</td>
                    <td>{{if .DeliveryTermEn.Valid}}{{.DeliveryTermEn.String}}{{else}}N/A{{end}}</td>
                    <td>{{if .DeliveryStatusEn.Valid}}{{.DeliveryStatusEn.String}}{{else}}N/A{{end}}</td>
                    <td>{{if .PaymentTermEn.Valid}}{{.PaymentTermEn.String}}{{else}}N/A{{end}}</td>
                    <td>{{if .PaymentStatusEn.Valid}}{{.PaymentStatusEn.String}}{{else}}N/A{{end}}</td>
                    <td>{{if .SellerConfirmStatusEn.Valid}}{{.SellerConfirmStatusEn.String}}{{else}}N/A{{end}}</td>
                    <td>{{if .BuyerConfirmStatusEn.Valid}}{{.BuyerConfirmStatusEn.String}}{{else}}N/A{{end}}</td>
                    <td>{{if .ContractStatusEn.Valid}}{{.ContractStatusEn.String}}{{else}}N/A{{end}}</td>
                <td><a href="/contract/{{.ID}}?lang={{ $.Lang }}" class="btn">View</a></td>
            </tr>
            {{end}}
        </tbody>
    </table>
    {{end}}
</div>
</main>
<footer class="footer">
    <br>
    <a href="/about?lang={{ .Lang }}">About</a> | <a href="/faqs?lang={{ .Lang }}">FAQs</a> | <a href="/contact?lang={{ .Lang }}">Contact</a><br>
    <p> &copy; 2024 BIOMASS EXCHANGE CO., LTD.</p>
    <br>
</footer>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.dash-nav-links');
        const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
        const dropdownContents = document.querySelectorAll('.dropdown-content');
        const body = document.body;

        // Mobile detection
        const isMobile = () => window.innerWidth <= 768;

        // Toggle hamburger menu
        if (hamburger && navLinks) {
            hamburger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle active classes
                hamburger.classList.toggle('active');
                navLinks.classList.toggle('active');

                // Manage body scroll
                if (navLinks.classList.contains('active')) {
                    body.style.overflow = 'hidden';

                    // Show all dropdown contents in mobile view
                    if (isMobile()) {
                        dropdownContents.forEach(content => {
                            content.style.display = 'block';
                        });
                    }
                } else {
                    body.style.overflow = '';

                    // Hide all dropdown contents when closing menu
                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });
        }

        // Handle dropdown triggers in mobile view
        dropdownTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                if (isMobile()) {
                    e.preventDefault();
                    e.stopPropagation();

                    const content = this.nextElementSibling;
                    const isVisible = content.style.display === 'block';

                    // Hide all dropdowns first
                    dropdownContents.forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });

                    // Toggle current dropdown
                    if (!isVisible) {
                        content.style.display = 'block';
                    }
                }
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!isMobile()) return;

            const isClickInsideNav = navLinks && navLinks.contains(event.target);
            const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

            if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Hide all dropdown contents
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                // Reset styles for desktop view
                if (navLinks) navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset dropdown display
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });
    });
</script>
{{end}}
