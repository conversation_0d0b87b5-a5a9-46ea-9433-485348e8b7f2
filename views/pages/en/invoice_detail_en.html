{{define "content"}}
<div class="page-header">
    <h1>Invoice Details</h1>
</div>

<div class="invoice-detail">
    <!-- Status message section removed to fix template error -->
    <div class="invoice-header">
        <div class="invoice-title">INVOICE</div>
        <div class="invoice-number">{{.Invoice.InvoiceNumber}}</div>
    </div>

    <div class="invoice-dates">
        <div class="invoice-date">
            <strong>Invoice Date:</strong> {{.InvoiceDate}}
        </div>
        <div class="due-date">
            <strong>Due Date:</strong> {{.DueDate}}
        </div>
    </div>

    <div class="invoice-parties">
        <div class="seller-info">
            <h3>From</h3>
            <p>
                {{.Invoice.SellerName}}
            </p>
        </div>
        <div class="buyer-info">
            <h3>To</h3>
            <p>
                {{.Invoice.BuyerName}}
            </p>
        </div>
    </div>

    <div class="invoice-container">
        <table class="invoice-items">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                {{if eq .Invoice.ItemID 1}}
                <tr>
                    <td>Brokerage Fee (0.5%)</td>
                    <td>1</td>
                    <td>{{printf "%.2f" .Invoice.ContractValue}} {{.Invoice.CurrencyCode}}</td>
                    <td>{{printf "%.2f" .Invoice.ContractValue}} {{.Invoice.CurrencyCode}}</td>
                </tr>
                {{if .Invoice.ContractFee.Valid}}
                <tr class="fee-calculation">
                    <td colspan="4" class="fee-note">
                        <small>* This fee ({{printf "%.2f" .Invoice.ContractValue}} {{.Invoice.CurrencyCode}}) is calculated as 0.5% of the contract value ({{printf "%.2f" (multiply .Invoice.ContractValue 200)}} {{.Invoice.CurrencyCode}})</small>
                    </td>
                </tr>
                {{end}}
                {{else}}
                <tr>
                    <td>Product: {{.Invoice.ProductName}}</td>
                    <td>{{.Invoice.Quantity}} {{.Invoice.UnitOfMeasure}}</td>
                    <td>{{printf "%.2f" .UnitPrice}} {{.Invoice.CurrencyCode}}</td>
                    <td>{{printf "%.2f" .Invoice.ContractValue}} {{.Invoice.CurrencyCode}}</td>
                </tr>
                {{end}}
            </tbody>
        </table>
    </div>

    <div class="invoice-total">
        <p>Total: {{printf "%.2f" .Invoice.ContractValue}} {{.Invoice.CurrencyCode}}</p>
    </div>

    <div class="invoice-status-display prominent">
        <strong>Payment Status:</strong>
        <span class="invoice-status status-{{.Invoice.Status}}">
            {{if eq .Invoice.Status "draft"}}Draft{{end}}
            {{if eq .Invoice.Status "sent"}}Payment Pending{{end}}
            {{if eq .Invoice.Status "viewed"}}Payment Pending{{end}}
            {{if eq .Invoice.Status "paid"}}✓ Payment Received{{end}}
            {{if eq .Invoice.Status "overdue"}}⚠️ Payment Overdue{{end}}
            {{if eq .Invoice.Status "canceled"}}Canceled{{end}}
            {{if eq .Invoice.Status "disputed"}}Under Review{{end}}
        </span>
    </div>

    {{if eq .Invoice.ItemID 1}} <!-- Platform Fee Invoice Specific Section -->
    <div class="platform-fee-summary">
        <h4>Your Platform Fee Payment Status (Contract: {{.Invoice.ContractNumber}})</h4>
        <div class="fee-status-item">
            <strong>Your Platform Fee (0.5%):</strong>
            {{if eq .MyFeeStatus "paid"}}
                <span class="status-paid">Paid</span>
                {{if .MyFeeProofPath.Valid}}
                    (<a href="/{{.MyFeeProofPath.String}}" target="_blank">View Your Proof</a>)
                {{end}}
            {{else if .MyFeeProofPath.Valid}}
                <span class="status-viewed">Proof Submitted</span>
                 (<a href="/{{.MyFeeProofPath.String}}" target="_blank">View Your Proof</a>)
            {{else if .MyFeeStatus}} {{/* Only show pending if MyFeeStatus is actually set (meaning it's their fee context) */}}
                <span class="status-sent">Payment Pending</span>
            {{end}}
        </div>
        <div class="overall-fee-status">
            <strong>Overall Platform Fee Status:</strong>
            {{if and (eq .MyFeeStatus "paid") (eq .Invoice.OtherPartyFeeStatus.String "paid")}}
                <span class="status-paid">All Platform Fees Paid</span>
            {{else}}
                <span class="status-sent">Platform Fees Partially Paid or Pending</span>
            {{end}}
        </div>
    </div>
    {{end}}

    {{if eq .Invoice.Status "paid"}}
    <div class="payment-history">
        <h3>Payment Information</h3>
        {{if .Invoice.DatePaid.Valid}}
        <p>Payment date: {{.Invoice.DatePaid.Time.Format "Jan 02, 2006"}}</p>
        {{end}}

        {{if eq .Invoice.ItemID 1}} <!-- Platform Fee Invoice - Proof for THIS invoice -->
            {{if .Invoice.PaymentSlipBuyer.Valid}} 
            <div class="payment-proof">
                <h4>Payment Proof for this Invoice (paid by {{.Invoice.BuyerName}})</h4>
                <a href="/{{.Invoice.PaymentSlipBuyer.String}}" target="_blank" class="view-proof-button">View Payment Proof</a>
            </div>
            {{end}}
        {{else}} <!-- Product Invoice (between actual buyer and seller) or other types -->
            {{if eq .UserID .Invoice.BuyerID}}
                {{if .Invoice.PaymentSlipBuyer.Valid}}
                <div class="payment-proof">
                    <h4>Payment Proof</h4>
                    <a href="/{{.Invoice.PaymentSlipBuyer.String}}" target="_blank" class="view-proof-button">View Your Payment Proof</a>
                </div>
                {{end}}
                {{if .Invoice.PaymentSlipSeller.Valid}}
                <div class="payment-proof-other">
                    <p>Payment proof has been submitted by the seller.</p>
                </div>
                {{end}}
            {{else if eq .UserID .Invoice.SellerID}}
                {{if .Invoice.PaymentSlipSeller.Valid}}
                <div class="payment-proof">
                    <h4>Payment Proof</h4>
                    <a href="/{{.Invoice.PaymentSlipSeller.String}}" target="_blank" class="view-proof-button">View Your Payment Proof</a>
                </div>
                {{end}}
                {{if .Invoice.PaymentSlipBuyer.Valid}}
                <div class="payment-proof-other">
                    <p>Payment proof has been submitted by the buyer.</p>
                </div>
                {{end}}
            {{end}}
        {{end}}
        <p>Thank you for your payment!</p>
    </div>
    {{else if or (eq .Invoice.Status "sent") (eq .Invoice.Status "viewed") (eq .Invoice.Status "overdue")}}
    <!-- This section is for invoices that are NOT YET PAID -->
    <!-- We will show payment instructions and upload form -->

    {{if eq .Invoice.ItemID 1}} <!-- Fee Invoice (Platform Fee) - Upload form for THIS invoice -->
        {{if and (eq .UserID .Invoice.BuyerID) .Invoice.PaymentSlipBuyer.Valid}} <!-- Show if current user is buyer of THIS invoice and proof submitted -->
        <div class="payment-proof-submitted">
            <h4>Your Payment Proof Submitted</h4>
            <p>You have already uploaded a payment proof for this fee. Our team will verify it shortly.</p>
            <a href="/{{.Invoice.PaymentSlipBuyer.String}}" target="_blank" class="view-proof-button">View Your Submitted Proof</a>
            <!-- <p>If you need to upload a new proof, please contact support.</p> -->
        </div>
        {{else if and (eq .UserID .Invoice.BuyerID) (not .Invoice.PaymentSlipBuyer.Valid)}} <!-- Show if current user is buyer of THIS invoice and NO proof submitted -->
        <!-- Upload form for Fee Invoice (current user is the buyer of the fee) -->
        <div class="payment-proof-upload">
            <h4>Upload Payment Proof</h4>
            <p class="payment-instructions">Please upload your payment slip to confirm that you have paid this platform fee. Your invoice status will be updated once your payment proof is submitted and verified.</p>
            <form action="/invoice/upload-payment-proof?lang={{.Lang}}" method="post" enctype="multipart/form-data">
                <input type="hidden" name="invoice_id" value="{{.Invoice.ID}}">
                <div class="form-group">
                    <label for="payment_slip">Payment Slip (Image or PDF):</label>
                    <input type="file" id="payment_slip" name="payment_slip" accept="image/*,.pdf" required>
                </div>
                <button type="submit" class="action-button">Submit Payment Proof</button>
            </form>
        </div>
        {{end}}
    {{else}} <!-- Product Invoice (or other types not ItemID 1) -->
        {{if eq .UserID .Invoice.BuyerID}}
            {{if .Invoice.PaymentSlipBuyer.Valid}}
            <div class="payment-proof-submitted">
                <h4>Your Payment Proof Submitted</h4>
                <p>You have already uploaded a payment proof. Our team will verify it shortly.</p>
                <a href="/{{.Invoice.PaymentSlipBuyer.String}}" target="_blank" class="view-proof-button">View Your Submitted Proof</a>
            </div>
            {{else}}
            <!-- Upload form for Buyer of product/service -->
            <div class="payment-proof-upload">
                <h4>Upload Payment Proof</h4>
                 <p class="payment-instructions">Please upload your payment slip to confirm that you have paid this invoice. Your invoice status will be updated once your payment proof is submitted and verified.</p>
                <form action="/invoice/upload-payment-proof?lang={{.Lang}}" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="invoice_id" value="{{.Invoice.ID}}">
                    <div class="form-group">
                        <label for="payment_slip">Payment Slip (Image or PDF):</label>
                        <input type="file" id="payment_slip" name="payment_slip" accept="image/*,.pdf" required>
                    </div>
                    <button type="submit" class="action-button">Submit Payment Proof</button>
                </form>
            </div>
            {{end}}
            {{if .Invoice.PaymentSlipSeller.Valid}}
            <div class="payment-proof-other">
                <p>Payment proof has been submitted by the seller. Please wait for confirmation.</p>
            </div>
            {{end}}
        {{else if eq .UserID .Invoice.SellerID}}
            {{if .Invoice.PaymentSlipSeller.Valid}}
            <div class="payment-proof-submitted">
                <h4>Your Payment Proof Submitted</h4>
                <p>You have already uploaded a payment proof. Our team will verify it shortly.</p>
                <a href="/{{.Invoice.PaymentSlipSeller.String}}" target="_blank" class="view-proof-button">View Your Submitted Proof</a>
            </div>
            {{else}}
            <!-- Upload form for Seller of product/service -->
            <div class="payment-proof-upload">
                <h4>Upload Payment Proof</h4>
                <p class="payment-instructions">If you are responsible for uploading proof for this transaction (e.g., confirming receipt or a specific payment step), please do so here.</p>
                <form action="/invoice/upload-payment-proof?lang={{.Lang}}" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="invoice_id" value="{{.Invoice.ID}}">
                    <div class="form-group">
                        <label for="payment_slip">Payment Slip (Image or PDF):</label>
                        <input type="file" id="payment_slip" name="payment_slip" accept="image/*,.pdf" required>
                    </div>
                    <button type="submit" class="action-button">Submit Payment Proof</button>
                </form>
            </div>
            {{end}}
            {{if .Invoice.PaymentSlipBuyer.Valid}}
            <div class="payment-proof-other">
                <p>Payment proof has been submitted by the buyer. Please wait for confirmation.</p>
            </div>
            {{end}}
        {{end}}
    {{end}}

    <div class="payment-instructions">
        <h3>Payment Instructions</h3>
        <p>Please download the invoice PDF and wait for our team to contact you regarding payment options. For any questions, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a> or call +66 818073767.</p>
        <p>If you have any concerns about this invoice, please contact our support team.</p>
    </div>
    {{end}}

    <!-- Cancel button removed as payment status will be managed by admin -->

    <div class="invoice-actions">
        <a href="/invoice/{{.Invoice.ID}}/download?lang={{.Lang}}" class="action-button download-button">Download PDF</a>
    </div>

    <a href="/invoice?lang={{.Lang}}" class="back-button">Back to Invoices</a>
</div>
{{end}}
