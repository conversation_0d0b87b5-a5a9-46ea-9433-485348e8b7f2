{{ define "content" }}
<nav class="navigator">
  <a href="/?lang={{ .lang }}" class="nav-brand">BiomassX</a>
  <a href="/order?lang={{ .lang }}">Trade</a>
  <a href="/markets?lang={{ .lang }}">Markets</a>
  <a href="/productspage?lang={{ .lang }}">Products</a>
  <a href="/services?lang={{ .lang }}">Services</a>
  <a href="/login?lang={{ .lang }}">Login</a>
</nav>

<main class="main-dashboard">
  <h2>Markets</h2>
  
  <!-- Add search bar -->
  <div class="search-container">
    <input type="text" id="productSearch" placeholder="Search products...">
     <div class="advanced-search">
      <div class="filter-row">
        <select class="filter-select" id="qualityFilter">
          <option value="">Quality</option>
          <option value="premium">Premium</option>
          <option value="standard">Standard</option>
          <option value="economy">Economy</option>
        </select>
        
        <select class="filter-select" id="deliveryTermFilter">
          <option value="">Delivery Term</option>
          <option value="immediate">Immediate</option>
          <option value="30days">30 Days</option>
          <option value="60days">60 Days</option>
        </select>
        
        <select class="filter-select" id="paymentTermFilter">
          <option value="">Payment Term</option>
          <option value="advance">Advance</option>
          <option value="loc">Letter of Credit</option>
          <option value="cod">Cash on Delivery</option>
        </select>
        
        <select class="filter-select" id="timeFilter">
          <option value="">Last Month</option>
          <option value="1month">1 Month</option>
          <option value="3months">3 Months</option>
          <option value="6months">6 Months</option>
        </select>
      </div>
      
      <div class="filter-row">
        <select class="filter-select" id="localFilter">
          <option value="">Local</option>
          <option value="domestic">Domestic</option>
          <option value="international">International</option>
        </select>
        
        <select class="filter-select" id="marketFilter">
          <option value="">Market</option>
          <option value="spot">Spot</option>
          <option value="forward">Forward</option>
        </select>
        
        <select class="filter-select" id="contractFilter">
          <option value="">Contract type</option>
          <option value="fixed">Fixed Price</option>
          <option value="floating">Floating Price</option>
        </select>
        

      </div>
    </div>
    <button class="search-btn">
      <i class="fas fa-search"></i>Search
    </button>
  </div>
    <div class="analytics-card">
      <h3>End-of-day Quote</h3>
      <h5>as of Wednesday, 21 May 2025</h5>
      <div class="futures-table-wrapper">
        <table class="futures-table">
          <thead>
            <tr>
              <th>Contract Period</th>
              <th>Open</th>
              <th>High</th>
              <th>Low</th>
              <th>Bid Vol.</th>
              <th>Bid</th>
              <th>Offer</th>
              <th>Offer Vol.</th>
              <th>Last</th>
              <th>Last Vol.</th>
              <th>Chg.</th>
              <th colspan="3">Settle Price</th>
              <th colspan="3">Open Interest</th>
            </tr>
            <tr>
              <th colspan="11"></th>
              <th>Prev.</th>
              <th>New</th>
              <th>Chg.</th>
              <th>Total</th>
              <th>EFP</th>
              <th>Prev.</th>
              <th>Curr.</th>
              <th>Chg.</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>JUN-25</td>
              <td>120.50</td>
              <td>121.25</td>
              <td>119.75</td>
              <td>150</td>
              <td class="bid">120.25</td>
              <td class="offer">120.75</td>
              <td>200</td>
              <td>120.50</td>
              <td>50</td>
              <td class="positive">+0.25</td>
              <td>120.25</td>
              <td>120.50</td>
              <td class="positive">+0.25</td>
              <td>1,500</td>
              <td>100</td>
              <td>1,400</td>
              <td>1,500</td>
              <td class="positive">+100</td>
            </tr>
            <tr>
              <td>JUL-25</td>
              <td>118.75</td>
              <td>119.50</td>
              <td>118.25</td>
              <td>120</td>
              <td class="bid">118.50</td>
              <td class="offer">119.00</td>
              <td>180</td>
              <td>118.75</td>
              <td>40</td>
              <td class="negative">-0.15</td>
              <td>118.90</td>
              <td>118.75</td>
              <td class="negative">-0.15</td>
              <td>1,200</td>
              <td>80</td>
              <td>1,150</td>
              <td>1,200</td>
              <td class="positive">+50</td>
            </tr>
            <tr>
              <td>AUG-25</td>
              <td>117.25</td>
              <td>118.00</td>
              <td>117.00</td>
              <td>100</td>
              <td class="bid">117.25</td>
              <td class="offer">117.75</td>
              <td>150</td>
              <td>117.50</td>
              <td>35</td>
              <td class="positive">+0.20</td>
              <td>117.30</td>
              <td>117.50</td>
              <td class="positive">+0.20</td>
              <td>1,000</td>
              <td>60</td>
              <td>980</td>
              <td>1,000</td>
              <td class="positive">+20</td>
            </tr>
            <tr class="total-row">
              <td colspan="14" style="text-align: right;"><strong>Total</strong></td>
              <td>610</td>
              <td>0</td>
              <td>1597</td>
              <td></td>
              <td class="negative">-26</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>


    <div class="analytics-card">
      <h3>Top 5 Trading Products</h3>
      <table class="products-table">
        <thead>
          <tr>
            <th>Product</th>
            <th>Volume (tons)</th>
            <th>Price Change</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Wood Pellets</td>
            <td>1,200</td>
            <td class="positive">+2.5%</td>
          </tr>
          <tr>
            <td>Rice Husks</td>
            <td>850</td>
            <td class="negative">-1.2%</td>
          </tr>
          <tr>
            <td>Palm Kernel</td>
            <td>650</td>
            <td class="positive">+0.8%</td>
          </tr>
        </tbody>
      </table>
    </div>

  <div class="analytics-container">
    <div class="analytics-card">
      <h3>Market Wood Pellets Overview</h3>
      <div class="chart-container">
        <canvas id="marketTrendsChart"></canvas>
      </div>
      <div class="market-stats">
        <div class="stat-item">
          <span class="stat-label">Daily Trading Volume</span>
          <span class="stat-value">$2.5M</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Active Traders</span>
          <span class="stat-value">245</span>
        </div>
      </div>
    </div>



    <div class="analytics-card">
      <h3>Price History Analysis</h3>
      <div class="chart-container">
        <canvas id="priceHistoryChart"></canvas>
      </div>
      <div class="time-filter">
        <button class="time-btn active">1D</button>
        <button class="time-btn">1W</button>
        <button class="time-btn">1M</button>
        <button class="time-btn">1Y</button>
      </div>
    </div>
    <div class="analytics-card">
      <h3>Live Bid-Offer Spreads</h3>
      <div class="bid-offer-table">
        <table>
          <thead>
            <tr>
              <th>Product</th>
              <th>Bid</th>
              <th>Offer</th>
              <th>Spread</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Wood Pellets</td>
              <td class="bid">$120.50</td>
              <td class="offer">$121.25</td>
              <td class="spread">$0.75</td>
            </tr>
            <tr>
              <td>Rice Husks</td>
              <td class="bid">$85.25</td>
              <td class="offer">$86.00</td>
              <td class="spread">$0.75</td>
            </tr>
            <tr>
              <td>Palm Kernel</td>
              <td class="bid">$92.75</td>
              <td class="offer">$93.25</td>
              <td class="spread">$0.50</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>


    <!-- Add price and volume index charts -->
    <div class="analytics-card full-width">
      <div class="chart-grid">
        <div class="chart-container">
          <h3>Price Index of "<span class="product-name">Wood Pellets</span>"</h3>
          <canvas id="priceIndexChart"></canvas>
        </div>
        <div class="chart-container">
          <h3>Volume Index of "<span class="product-name">Wood Pellets</span>"</h3>
          <canvas id="volumeIndexChart"></canvas>
        </div>
      </div>
    </div>
  
  </div>
</main>

<footer class="footer">
  <br>
  <a href="/about?lang={{ .lang }}">About us</a> | <a href="/faqs?lang={{ .lang }}">FAQs</a> | <a href="/contact?lang={{ .lang }}">Contact us</a><br>
  <p>BIOMASS EXCHANGE CO., LTD.</p>
  <br>
</footer>

<!-- Add Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Add custom charts script -->
<script>
// Market Trends Chart
const marketTrendsChart = new Chart(
  document.getElementById('marketTrendsChart'),
  {
    type: 'line',
    data: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [{
        label: 'Trading Volume',
        data: [30, 45, 35, 50, 40, 60],
        borderColor: '#3498db',
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: true,
          text: 'Monthly Trading Volume'
        }
      }
    }
  }
);

// Price History Chart
const priceHistoryChart = new Chart(
  document.getElementById('priceHistoryChart'),
  {
    type: 'line',
    data: {
      labels: ['9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'],
      datasets: [{
        label: 'Wood Pellets',
        data: [100, 120, 115, 134, 168, 132, 150, 160],
        borderColor: '#2ecc71',
        tension: 0.4
      },]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: true,
          text: 'Price Trends (USD/ton)'
        }
      }
    }
  }
);

// Price Index Chart
const priceIndexChart = new Chart(
  document.getElementById('priceIndexChart'),
  {
    type: 'bar',
    data: {
      labels: ['Jan', 'Feb', 'Mar'],
      datasets: [
        {
          label: 'Buy',
          data: [3, 8, 15],
          backgroundColor: '#ff4757',
        },
        {
          label: 'Sell',
          data: [5, 12, 18],
          backgroundColor: '#2ed573',
        }
      ]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Price (USD/ton)'
          }
        }
      },
      plugins: {
        legend: {
          position: 'top',
        }
      }
    }
  }
);

// Volume Index Chart
const volumeIndexChart = new Chart(
  document.getElementById('volumeIndexChart'),
  {
    type: 'bar',
    data: {
      labels: ['Jan', 'Feb', 'Mar'],
      datasets: [
        {
          label: 'Buy',
          data: [3, 8, 15],
          backgroundColor: '#ff4757',
        },
        {
          label: 'Sell',
          data: [5, 12, 18],
          backgroundColor: '#2ed573',
        }
      ]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Volume (K tons)'
          }
        }
      },
      plugins: {
        legend: {
          position: 'top',
        }
      }
    }
  }
);

// Time filter button functionality
document.querySelectorAll('.time-btn').forEach(button => {
  button.addEventListener('click', () => {
    // Remove active class from all buttons
    document.querySelectorAll('.time-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    // Add active class to clicked button
    button.classList.add('active');
    
    // Update chart data based on timeframe
    // This is where you would normally fetch new data
    // For demo, we'll just update with random data
    const newData = Array(8).fill(0).map(() => Math.random() * 100 + 50);
    priceHistoryChart.data.datasets[0].data = newData;
    priceHistoryChart.update();
  });
});

// Add to your existing script section
function updateBidOfferPrices() {
  // Simulate real-time price updates
  setInterval(() => {
    document.querySelectorAll('.bid-offer-table tbody tr').forEach(row => {
      const bid = parseFloat(row.querySelector('.bid').textContent.replace('$', ''));
      const offer = parseFloat(row.querySelector('.offer').textContent.replace('$', ''));
      
      // Random small price changes
      const newBid = (bid + (Math.random() - 0.5) * 0.2).toFixed(2);
      const newOffer = (offer + (Math.random() - 0.5) * 0.2).toFixed(2);
      const newSpread = (newOffer - newBid).toFixed(2);
      
      row.querySelector('.bid').textContent = `$${newBid}`;
      row.querySelector('.offer').textContent = `$${newOffer}`;
      row.querySelector('.spread').textContent = `$${newSpread}`;
    });
  }, 3000); // Update every 3 seconds
}

// Initialize bid-offer updates
updateBidOfferPrices();

function updateFuturesData() {
  setInterval(() => {
    document.querySelectorAll('.futures-table tbody tr').forEach(row => {
      // Update bid/offer prices with small random changes
      const bid = parseFloat(row.querySelector('.bid').textContent);
      const offer = parseFloat(row.querySelector('.offer').textContent);
      
      const newBid = (bid + (Math.random() - 0.5) * 0.1).toFixed(2);
      const newOffer = (offer + (Math.random() - 0.5) * 0.1).toFixed(2);
      
      row.querySelector('.bid').textContent = newBid;
      row.querySelector('.offer').textContent = newOffer;
    });
  }, 5000); // Update every 5 seconds
}

// Initialize futures data updates
updateFuturesData();

// Add after your existing Chart.js initialization

// Product data (example)
const productData = {
  'Wood Pellets': {
    prices: [100, 120, 115, 134, 168, 132, 150, 160],
    color: '#2ecc71'
  },
  'Rice Husks': {
    prices: [80, 95, 87, 110, 105, 90, 92, 88],
    color: '#e74c3c'
  },
  'Palm Kernel': {
    prices: [90, 88, 92, 95, 98, 96, 99, 102],
    color: '#3498db'
  }
};

// Search functionality
document.getElementById('productSearch').addEventListener('input', function(e) {
  const searchTerm = e.target.value.toLowerCase();
  
  // Filter products table
  document.querySelectorAll('.products-table tbody tr').forEach(row => {
    const productName = row.querySelector('td:first-child').textContent.toLowerCase();
    row.style.display = productName.includes(searchTerm) ? '' : 'none';
  });
  
  // Update price history chart
  const matchedProducts = Object.keys(productData).filter(product => 
    product.toLowerCase().includes(searchTerm)
  );
  
  priceHistoryChart.data.datasets = matchedProducts.map(product => ({
    label: product,
    data: productData[product].prices,
    borderColor: productData[product].color,
    tension: 0.4
  }));
  
  priceHistoryChart.update();
});

// Add click handler for search button
document.querySelector('.search-btn').addEventListener('click', function() {
  const searchTerm = document.getElementById('productSearch').value;
  // Trigger the search
  document.getElementById('productSearch').dispatchEvent(new Event('input'));
});
</script>
{{ end }}

<style>
/* Add before other styles */
.search-container {
  margin: 20px 0;
  display: flex;
  gap: 10px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

#productSearch {
  flex: 1;
  padding: 12px 20px;
  border: 2px solid #eee;
  border-radius: 25px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

#productSearch:focus {
  outline: none;
  border-color: #3498db;
}

.search-btn {
  padding: 12px 24px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}

.search-btn:hover {
  background: #2980b9;
}

/* Update analytics container layout */
.analytics-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* Change to 2 columns */
  gap: 24px;
  padding: 24px;
  max-width: 1440px;
  margin: 0 auto;
}

/* Make certain cards span full width */
.analytics-card:nth-child(1), /* Market Overview */
.analytics-card:nth-child(5), /* Live Bid-Offer Spreads */
.analytics-card:nth-child(6) { /* Futures Contracts */
  grid-column: span 2;
}

/* Update card styling */
.analytics-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

/* Chart container improvements */
.chart-container {
  height: 300px;
  margin: 24px 0;
  position: relative;
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

/* Table improvements */
.products-table,
.bid-offer-table table,
.futures-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 16px 0;
}

.products-table th,
.bid-offer-table th,
.futures-table th {
  background: #f8f9fa;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  border-bottom: 2px solid #eee;
}

.products-table td,
.bid-offer-table td,
.futures-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

/* Futures table specific improvements */
.futures-table-wrapper {
  margin: 16px -24px;
  padding: 0 24px;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.futures-table {
  min-width: 1200px;
}

.futures-table th,
.futures-table td {
  text-align: right;
  padding: 12px 16px;
}

.futures-table thead tr:first-child th {
  background: #e9ecef;
  text-align: center;
}

/* Stats improvements */
.market-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 24px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* Button improvements */
.time-filter {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 8px;
  justify-content: center;
}

.time-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  background: white;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.time-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

/* Advanced search styles */
.advanced-search {
  background: #fff;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  margin: 20px 0;
}

.filter-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-select {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
}

/* Chart grid for price and volume index */
.chart-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-top: 24px;
}

.full-width {
  grid-column: 1 / -1;
}

/* Responsive improvements */
@media (max-width: 1200px) {
  .analytics-container {
    grid-template-columns: 1fr;
  }
  
  .analytics-card:nth-child(1),
  .analytics-card:nth-child(5),
  .analytics-card:nth-child(6) {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .analytics-container {
    padding: 16px;
    gap: 16px;
  }
  
  .analytics-card {
    padding: 16px;
  }
  
  .chart-container {
    height: 250px;
  }
  
  .filter-row {
    flex-direction: column;
  }
  
  .chart-grid {
    grid-template-columns: 1fr;
  }
}
</style>