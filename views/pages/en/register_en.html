{{ define "content" }}
<nav class="index-nav">
    <div class="index-nav-container">
        <a href="/?lang={{ .lang }}" class="index-nav-brand">BiomassX</a>
        <div class="index-nav-links">
            <a href="/order?lang={{ .lang }}">Trade</a>
            <a href="/markets?lang={{ .lang }}">Markets</a>
            <a href="/productspage?lang={{ .lang }}">Products</a>
            <a href="/services?lang={{ .lang }}">Services</a>
            <div class="language-selector">
                <form hx-get="/register" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">Language:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>TH</option>
                        <option value="en" {{ if eq .lang "kr" }}selected{{ end }}>KR</option>
                        <option value="en" {{ if eq .lang "jp" }}selected{{ end }}>JP</option>
                        <option value="en" {{ if eq .lang "vi" }}selected{{ end }}>VI</option>
                    </select>
                </form>
            </div>
        </div>
        <div class="desktop-lang-selector index-lang-selector">
            <form hx-get="/register" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>TH</option>
                        <option value="en" {{ if eq .lang "kr" }}selected{{ end }}>KR</option>
                        <option value="en" {{ if eq .lang "jp" }}selected{{ end }}>JP</option>
                        <option value="en" {{ if eq .lang "vi" }}selected{{ end }}>VI</option>
                </select>
            </form>
        </div>
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="content">
<form hx-post="/register?lang={{ .lang }}" hx-redirect="/login?lang={{ .lang }}" hx-target="#message">
    <h2>Register</h2>
    <div class="registerform-group">
    <label for="firstName">First name :</label>
    <input type="text" id="firstName" name="firstName">
    </div>

    <div class="registerform-group">
    <label for="lastName">Lastname :</label>
    <input type="text" id="lastName" name="lastName" required>
    </div>

    <div class="registerform-group">
    <label for="organizationName">Organization name : <br>(if any) </label>
    <input type="text" id="organizationName" name="organizationName">
    </div>

    <div class="registerform-group">
    <label for="username">Username :</label>
    <input type="username" id="username" name="username" required>
    </div>

    <div class="registerform-group">
    <label for="password">Password :</label>
    <input type="password" id="password" name="password" required>
    </div>

    <div class="registerform-group">
    <label for="phone">Phone : </label>
    <input type="tel" id="phone" name="phone" required>
    </div>

    <div class="registerform-group">
    <label for="email">Email : </label>
    <input type="email" id="email" name="email" required>
    </div>
    <button type="submit">Register</button>
    <p>Already have an account? <a href="/login?lang={{ .lang }}">Login</a></p>
</form>
<div id="message"></div>
</main>

<footer class="footer">
  <br>
  <a href="/about?lang={{ .lang }}">About us</a> | <a href="/faqs?lang={{ .lang }}">FAQs</a> | <a href="/contact?lang={{ .lang }}">Contact us</a><br>
  <p>BIOMASS EXCHANGE CO., LTD.</p>
  <br>
</footer>

<script>
    // Elements
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('.index-nav-links');
    const body = document.body;

    // Mobile detection
    const isMobile = () => window.innerWidth <= 768;

    // Toggle hamburger menu
    if (hamburger && navLinks) {
        hamburger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Toggle active classes
            hamburger.classList.toggle('active');
            navLinks.classList.toggle('active');

            // Manage body scroll
            if (navLinks.classList.contains('active')) {
                body.style.overflow = 'hidden';
            } else {
                body.style.overflow = '';
            }
        });
    }

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        if (!isMobile()) return;

        const isClickInsideNav = navLinks && navLinks.contains(event.target);
        const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

        if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
            navLinks.classList.remove('active');
            if (hamburger) hamburger.classList.remove('active');
            body.style.overflow = '';
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            // Reset styles for desktop view
            if (navLinks) navLinks.classList.remove('active');
            if (hamburger) hamburger.classList.remove('active');
            body.style.overflow = '';
        }
    });
</script>
{{ end }}