{{ define "content" }}
<nav class="dash-nav">
    <div class="dash-nav-container">
        <a href="/?lang={{ .Lang }}" class="dash-nav-brand">BiomassX</a>
        <div class="dash-nav-links">
            <a href="/dashboard?lang={{ .Lang }}">Dashboard</a>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Submit orders</a>
                <div class="dropdown-content">
                    <a href="/order?lang={{ .Lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Order book</a>
                <div class="dropdown-content">
                    <a href="/order_book?lang={{ .Lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Matched orders</a>
                <div class="dropdown-content">
                    <a href="/contract?lang={{ .Lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Setting</a>
                <div class="dropdown-content">
                    <a href="/profile?lang={{ .Lang }}">Profile</a>
                </div>
            </div>

            <a href="/logout?lang={{ .Lang }}" hx-post="/logout?lang={{ .Lang }}" hx-redirect="/login?lang={{ .Lang }}">Logout</a>

            <!-- <div class="language-selector">
                <form hx-get="/contract/{{ .Contract.ID }}" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">Language:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                    </select>
                </form>
            </div> -->
        </div>
        <!-- <div class="desktop-lang-selector">
            <form hx-get="/contract/{{ .Contract.ID }}" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                    <option value="en" {{ if eq .Lang "en" }}selected{{ end }}>EN</option>
                    <option value="th" {{ if eq .Lang "th" }}selected{{ end }}>ไทย</option>
                </select>
            </form>
        </div> -->
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>


<main class="main-dashboard">
    <div class="dashboard-content">
        <h2 class="contract-title">Contract Number: {{.Contract.ID}}</h2>
        <p class="contract-date">Contract date: {{if .Contract.ContractDate.IsZero}}N/A{{else}}{{.Contract.ContractDate.Format "02-01-2006"}}{{end}}</p>

        <section class="contract-section">
            <h2>Parties</h2>
            <p>Seller: {{.Contract.SellerFirstName.String}} {{.Contract.SellerLastName.String}}</p>
            <p>Organization name: {{if .Contract.SellerOrgName.Valid}}{{.Contract.SellerOrgName.String}}{{else}}-{{end}}</p>
            <p>Buyer: {{.Contract.BuyerFirstName.String}} {{.Contract.BuyerLastName.String}}</p>
            <p>Organization name: {{if .Contract.BuyerOrgName.Valid}}{{.Contract.BuyerOrgName.String}}{{else}}-{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>Product details</h2>
            <p>Product: {{if .Contract.ProductEnName.Valid}}{{.Contract.ProductEnName.String}}{{else}}N/A{{end}}</p>
            <p>Standard: {{if .Contract.StandardEnName.Valid}}{{.Contract.StandardEnName.String}}{{else}}N/A{{end}}</p>
            <p>Grade: {{if .Contract.GradeEnName.Valid}}{{.Contract.GradeEnName.String}}{{else}}N/A{{end}}</p>
            <p>Quantity: {{.Contract.Quantity}} {{if .Contract.UomEn.Valid}}{{.Contract.UomEn.String}}{{else}}N/A{{end}}</p>
            <p>Packing: {{if .Contract.PackingEn.Valid}}{{.Contract.PackingEn.String}}{{else}}N/A{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>Financial terms</h2>
            <p>Price: {{.Contract.Price}} {{if .Contract.CurrencyEnName.Valid}}{{.Contract.CurrencyEnName.String}}{{else}}N/A{{end}}</p>
            <p>Payment term: {{if .Contract.PaymentTermEn.Valid}}{{.Contract.PaymentTermEn.String}}{{else}}N/A{{end}}</p>
            <p>Payment status: {{if .Contract.PaymentStatusEn.Valid}}{{.Contract.PaymentStatusEn.String}}{{else}}N/A{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>Delivery terms</h2>
            <p>Delivery term: {{if .Contract.DeliveryTermEn.Valid}}{{.Contract.DeliveryTermEn.String}}{{else}}N/A{{end}}</p>
            <p>Start delivery: {{if .Contract.StartDelivery.Valid}}{{.Contract.StartDelivery.Time.Format "02-01-2006"}}{{else}}N/A{{end}}</p>
            <p>Finish delivery: {{if .Contract.FinishDelivery.Valid}}{{.Contract.FinishDelivery.Time.Format "02-01-2006"}}{{else}}N/A{{end}}</p>
            <p><strong>Place of delivery:</strong></p>
            <p>Seller Country: {{.Contract.SellerCountryEn.String}}</p>
            <p>Buyer Country: {{.Contract.BuyerCountryEn.String}}</p>
            <p>Province: {{if .Contract.PodProvinceEn.Valid}}{{.Contract.PodProvinceEn.String}}{{else}}N/A{{end}}</p>
            <p>District: {{if .Contract.PodDistrictEn.Valid}}{{.Contract.PodDistrictEn.String}}{{else}}N/A{{end}}</p>
            <p>Subdistrict: {{if .Contract.PodSubdistrictEn.Valid}}{{.Contract.PodSubdistrictEn.String}}{{else}}N/A{{end}}</p>
            <p><strong>Ports of delivery:</strong></p>
            <p>Seller port of loading: {{if .Contract.SellerPolEn.Valid}}{{.Contract.SellerPolEn.String}}{{else}}N/A{{end}}</p>
            <p>Seller port of discharge: {{if .Contract.SellerPodEn.Valid}}{{.Contract.SellerPodEn.String}}{{else}}N/A{{end}}</p>
            <p>Buyer port of loading: {{if .Contract.BuyerPolEn.Valid}}{{.Contract.BuyerPolEn.String}}{{else}}N/A{{end}}</p>
            <p>Buyer port of discharge: {{if .Contract.BuyerPodEn.Valid}}{{.Contract.BuyerPodEn.String}}{{else}}N/A{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>Contract status</h2>
            <p>Seller signatory: {{if .Contract.SellerConfirmStatusEn.Valid}}{{.Contract.SellerConfirmStatusEn.String}}{{else}}N/A{{end}}</p>
            <p>Buyer signatory: {{if .Contract.BuyerConfirmStatusEn.Valid}}{{.Contract.BuyerConfirmStatusEn.String}}{{else}}N/A{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>Remarks</h2>
            <p>Seller: {{if .Contract.SellerRemark.Valid}}{{.Contract.SellerRemark.String}}{{else}}N/A{{end}}</p>
            <p>Buyer: {{if .Contract.BuyerRemark.Valid}}{{.Contract.BuyerRemark.String}}{{else}}N/A{{end}}</p>
        </section>

        <section class="contract-section">
            <h2>Contract actions</h2>
            <div class="button-stack">
                <button
                    class="sign-button"
                    hx-post="/sign"
                    hx-vals='{"contract_id": "{{.Contract.ID}}"}'
                >
                    Sign contract
                </button>

                <button
                    class="sign-button reject-button"
                    hx-post="/reject"
                    hx-vals='{"contract_id": "{{.Contract.ID}}"}'
                    style="background-color: #f44336; margin-left: 10px;"
                >
                    Reject contract
                </button>

                {{if and (eq .Contract.SellerConfirmStatusEn.String "signed") (eq .Contract.BuyerConfirmStatusEn.String "signed") (eq .Contract.ContractStatusEn.String "signed")}}
                <a href="/download-pdfEn/{{.Contract.ID}}" class="sign-button download-btn">
                    Download PDF
                </a>
                {{end}}
            </div>
        </section>

        {{if and (eq .Contract.SellerConfirmStatusEn.String "signed") (eq .Contract.BuyerConfirmStatusEn.String "signed")}}
        <section class="contract-section">
            <h2>Invoices</h2>
            {{if .Invoices}}
            <div class="invoice-container">
                <table class="invoice-list">
                    <thead>
                        <tr>
                            <th>Invoice #</th>
                            <th>Type</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{range .Invoices}}
                        <tr class="invoice-row {{if eq .ItemID 1}}type-product{{else}}type-fee{{end}} {{if eq .SellerID $.UserID}}as-seller{{else}}as-buyer{{end}}">
                            <td>{{.InvoiceNumber}}</td>
                            <td>{{.ItemEnName}}</td>
                            <td>{{.InvoiceDate.Format "Jan 02, 2006"}}</td>
                            <td>{{printf "%.2f" .ContractValue}} {{.CurrencyCode}}</td>
                            <td>
                                <span class="invoice-status status-{{.Status}}">
                                    {{if eq .Status "draft"}}Draft{{end}}
                                    {{if eq .Status "sent"}}Payment Pending{{end}}
                                    {{if eq .Status "viewed"}}Payment Pending{{end}}
                                    {{if eq .Status "paid"}}✓ Payment Received{{end}}
                                    {{if eq .Status "overdue"}}⚠️ Payment Overdue{{end}}
                                    {{if eq .Status "canceled"}}Canceled{{end}}
                                    {{if eq .Status "disputed"}}Under Review{{end}}
                                </span>
                            </td>
                            <td>
                                <a href="/invoice/{{.ID}}?lang=en" class="action-button view-button">View</a>
                                <a href="/invoice/{{.ID}}/download?lang=en" class="action-button download-button">Download</a>
                            </td>
                        </tr>
                        {{end}}
                    </tbody>
                </table>
            </div>
            {{else}}
            <p>No invoices found for this contract.</p>
            <button
                class="action-button"
                hx-post="/api/invoice/generate"
                hx-vals='{"contract_id": {{.Contract.ID}}}'
                hx-swap="outerHTML"
                hx-target="closest section"
            >
                Generate Invoices
            </button>
            {{end}}
            <div class="view-all-link">
                <a href="/invoice?lang=en">View all invoices</a>
            </div>
        </section>
        {{end}}
    </div>
</main>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.dash-nav-links');
        const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
        const dropdownContents = document.querySelectorAll('.dropdown-content');
        const body = document.body;

        // Mobile detection
        const isMobile = () => window.innerWidth <= 768;

        // Toggle hamburger menu
        if (hamburger && navLinks) {
            hamburger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle active classes
                hamburger.classList.toggle('active');
                navLinks.classList.toggle('active');

                // Manage body scroll
                if (navLinks.classList.contains('active')) {
                    body.style.overflow = 'hidden';

                    // Show all dropdown contents in mobile view
                    if (isMobile()) {
                        dropdownContents.forEach(content => {
                            content.style.display = 'block';
                        });
                    }
                } else {
                    body.style.overflow = '';

                    // Hide all dropdown contents when closing menu
                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });
        }

        // Handle dropdown triggers in mobile view
        dropdownTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                if (isMobile()) {
                    e.preventDefault();
                    e.stopPropagation();

                    const content = this.nextElementSibling;
                    const isVisible = content.style.display === 'block';

                    // Hide all dropdowns first
                    dropdownContents.forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });

                    // Toggle current dropdown
                    if (!isVisible) {
                        content.style.display = 'block';
                    }
                }
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!isMobile()) return;

            const isClickInsideNav = navLinks && navLinks.contains(event.target);
            const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

            if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Hide all dropdown contents
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                // Reset styles for desktop view
                if (navLinks) navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset dropdown display
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });
    });
</script>
{{end}}