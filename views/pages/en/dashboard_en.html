{{ define "content" }}
<nav class="dash-nav">
    <div class="dash-nav-container">
        <a href="/?lang={{ .lang }}" class="dash-nav-brand">BiomassX</a>
        <div class="dash-nav-links">
            <a href="/dashboard?lang={{ .lang }}">Dashboard</a>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Submit orders</a>
                <div class="dropdown-content">
                    <a href="/order?lang={{ .lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Order book</a>
                <div class="dropdown-content">
                    <a href="/order_book?lang={{ .lang }}">Products</a>
                </div>
            </div>

            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Matched orders</a>
                <div class="dropdown-content">
                    <a href="/contract?lang={{ .lang }}">Products</a>
                </div>
            </div>



            <div class="dropdown-container">
                <a href="#" class="dropdown-trigger">Setting</a>
                <div class="dropdown-content">
                    <a href="/profile?lang={{ .lang }}">Profile</a>
                </div>
            </div>

            <a href="/logout?lang={{ .lang }}" hx-post="/logout?lang={{ .lang }}" hx-redirect="/login?lang={{ .lang }}">Logout</a>

            <!-- <div class="language-selector">
                <form hx-get="/dashboard" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">Language:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>ไทย</option>
                    </select>
                </form>
            </div> -->
        </div>
        <!-- <div class="desktop-lang-selector">
            <form hx-get="/dashboard" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                    <option value="en" {{ if eq .lang "en" }}selected{{ end }}>EN</option>
                    <option value="th" {{ if eq .lang "th" }}selected{{ end }}>ไทย</option>
                </select>
            </form>
        </div> -->
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="main-dashboard">
  <h3>My dashboard</h3>
  <p>You have logged in to BiomassX.</p>

  <div class="dashboard-section">
    <h4>Recent Invoices</h4>
    {{if .RecentInvoices}}
    <div class="invoice-container">
      <table class="invoice-list">
        <thead>
          <tr>
            <th>Invoice</th>
            <th>Date</th>
            <th>Type</th>
            <th>Amount</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {{range .RecentInvoices}}
          <tr class="invoice-row {{if eq .ItemID 1}}type-product{{else}}type-fee{{end}} {{if eq .SellerID $.UserID}}as-seller{{else}}as-buyer{{end}}">
            <td>{{.InvoiceNumber}}</td>
            <td>{{.InvoiceDate.Format "Jan 02, 2006"}}</td>
            <td>{{.ItemEnName}}</td>
            <td>{{printf "%.2f" .ContractValue}} {{.CurrencyCode}}</td>
            <td>
              <span class="invoice-status status-{{.Status}}">
                {{if eq .Status "draft"}}Draft{{end}}
                {{if eq .Status "sent"}}Payment Pending{{end}}
                {{if eq .Status "viewed"}}Payment Pending{{end}}
                {{if eq .Status "paid"}}✓ Payment Received{{end}}
                {{if eq .Status "overdue"}}⚠️ Payment Overdue{{end}}
                {{if eq .Status "canceled"}}Canceled{{end}}
                {{if eq .Status "disputed"}}Under Review{{end}}
              </span>
            </td>
            <td>
              <a href="/invoice/{{.ID}}?lang={{$.lang}}" class="action-button view-button">View</a>
            </td>
          </tr>
          {{end}}
        </tbody>
      </table>
      <div class="view-all-link">
        <a href="/invoice?lang={{.lang}}">View all invoices</a>
      </div>
    </div>
    {{else}}
    <p>You don't have any invoices yet.</p>
    <button
      class="action-button"
      hx-post="/api/invoice/generate-all"
      hx-swap="outerHTML"
      hx-target="closest div"
    >
      Generate Invoices for Signed Contracts
    </button>
    {{end}}
  </div>
</main>

<style>
  .dashboard-section {
    margin-top: 30px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
  }

  .dashboard-section h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
  }
</style>

<footer class="footer">
  <br>
  <a href="/about?lang={{ .lang }}">About us</a> | <a href="/faqs?lang={{ .lang }}">FAQs</a> | <a href="/contact?lang={{ .lang }}">Contact us</a><br>
  <p>BIOMASS EXCHANGE CO., LTD.</p>
  <br>
</footer>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.dash-nav-links');
        const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
        const dropdownContents = document.querySelectorAll('.dropdown-content');
        const body = document.body;

        // Mobile detection
        const isMobile = () => window.innerWidth <= 768;

        // Toggle hamburger menu
        if (hamburger && navLinks) {
            hamburger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle active classes
                hamburger.classList.toggle('active');
                navLinks.classList.toggle('active');

                // Manage body scroll
                if (navLinks.classList.contains('active')) {
                    body.style.overflow = 'hidden';

                    // Show all dropdown contents in mobile view
                    if (isMobile()) {
                        dropdownContents.forEach(content => {
                            content.style.display = 'block';
                        });
                    }
                } else {
                    body.style.overflow = '';

                    // Hide all dropdown contents when closing menu
                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });
        }

        // Handle dropdown triggers in mobile view
        dropdownTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                if (isMobile()) {
                    e.preventDefault();
                    e.stopPropagation();

                    const content = this.nextElementSibling;
                    const isVisible = content.style.display === 'block';

                    // Hide all dropdowns first
                    dropdownContents.forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });

                    // Toggle current dropdown
                    if (!isVisible) {
                        content.style.display = 'block';
                    }
                }
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!isMobile()) return;

            const isClickInsideNav = navLinks && navLinks.contains(event.target);
            const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

            if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Hide all dropdown contents
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                // Reset styles for desktop view
                if (navLinks) navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset dropdown display
                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });
    });
</script>
{{ end }}