{{ define "content" }}
<nav class="index-nav">
    <div class="index-nav-container">
        <a href="/?lang={{ .lang }}" class="index-nav-brand">BiomassX</a>
        <div class="index-nav-links">
            <a href="/order?lang={{ .lang }}">Trade</a>
            <a href="/markets?lang={{ .lang }}">Markets</a>
            <a href="/productspage?lang={{ .lang }}">Products</a>
            <a href="services?lang={{ .lang }}">Services</a>
            <a href="/login?lang={{ .lang }}">Login</a>
            <div class="language-selector">
                <form hx-get="/" hx-target="body" hx-push-url="true">
                    <label for="lang-select-mobile">Language:</label>
                    <select id="lang-select-mobile" name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>TH</option>
                        <option value="en" {{ if eq .lang "kr" }}selected{{ end }}>KR</option>
                        <option value="en" {{ if eq .lang "jp" }}selected{{ end }}>JP</option>
                        <option value="en" {{ if eq .lang "vi" }}selected{{ end }}>VI</option>
                    </select>
                </form>
            </div>
        </div>
        <div class="desktop-lang-selector index-lang-selector">
            <form hx-get="/" hx-target="body" hx-push-url="true">
                <select name="lang" onchange="this.form.submit()">
                        <option value="en" {{ if eq .lang "en" }}selected{{ end }}>EN</option>
                        <option value="th" {{ if eq .lang "th" }}selected{{ end }}>TH</option>
                        <option value="en" {{ if eq .lang "kr" }}selected{{ end }}>KR</option>
                        <option value="en" {{ if eq .lang "jp" }}selected{{ end }}>JP</option>
                        <option value="en" {{ if eq .lang "vi" }}selected{{ end }}>VI</option>
                </select>
            </form>
        </div>
        <button class="hamburger" aria-label="Toggle menu">
            <span></span>
            <span></span>
            <span></span>
        </button>
    </div>
</nav>

<main class="main-logo">
    <a>bioenergy exchange platform</a>
</main>

<div class="content-wrapper">
    <main class="content">
        <div class="container">
            <div class="grid">
                <div class="card">
                    <h2 id="top-product-container" hx-get="/api/top-product-name?lang=en" hx-trigger="load, every 5s"
                        hx-swap="innerHTML">
                        {{ .TopProduct.ProductName }} price index
                    </h2>

                    <table class="biomass-price">
                        <thead>
                            <tr>
                                <th>Delivery term</th>
                                <th>Buy(USD)</th>
                                <th>Sell(USD)</th>
                            </tr>
                        </thead>
                        <tbody hx-get="/api/product-prices?lang=en" hx-trigger="load, every 5s" hx-swap="innerHTML">
                            <tr>
                                <td colspan="3" class="loading-text">Loading prices...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="card">
                    <h2>Market volume</h2>
                    <div id="biomass-price">
                        <table class="biomass-price">
                            <thead>
                                <tr>
                                    <th>Delivery term</th>
                                    <th>Buy(USD)</th>
                                    <th>Sell(USD)</th>
                                </tr>
                            </thead>
                            <tbody hx-get="/api/market-volume?lang=en" hx-trigger="load, every 5s" hx-swap="innerHTML">
                                <tr>
                                    <td colspan="3" class="loading-text">Loading market volume data...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card">
                    <h2>Open orders</h2>
                    <table class="biomass-price">
                        <thead>
                            <tr>
                                <th>Delivery term</th>
                                <th>Buy(Orders)</th>
                                <th>Sell(Orders)</th>
                                <th>Marketspace</th>
                                <th>Market</th>
                            </tr>
                        </thead>
                        <tbody hx-get="/api/active-orders?lang={{ .lang }}" hx-trigger="load, every 5s" hx-swap="innerHTML">
                            <tr>
                                <td colspan="5" class="loading-text">Loading active orders data...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="card">
                    <h2>CO2 saved</h2>
                    <div class="co2-stats" hx-get="/api/co2-saved?lang=en" hx-trigger="load, every 5s" hx-swap="innerHTML">
                        <div class="co2-stat">
                            <div class="co2-label">Monthly:</div>
                            <div class="co2-value">Loading... TCO2eq</div>
                        </div>
                        <div class="co2-stat">
                            <div class="co2-label">Yearly:</div>
                            <div class="co2-value">Loading... TCO2eq</div>
                        </div>
                        <div class="co2-stat">
                            <div class="co2-label">Total:</div>
                            <div class="co2-value">Loading... TCO2eq</div>
                        </div>
                    </div>
                    <div class="co2-description">Emissions saved since platform launch</div>
                </div>
            </div>

            <div class="card" style="margin-top: 20px; padding-bottom: 50px;">
                <h2>Recent local market orders</h2>
                <table id="transactions-table">
                    <thead>
                        <tr>
                            <th>Order</th>
                            <th>Product</th>
                            <th>Quantity</th>
                            <th>Quality</th>
                            <th>Packing</th>
                            <th>Market</th>
                            <th>Submarket</th>
                            <th>Contract type</th>
                            <th>Delivery term</th>
                            <th>Country</th>
                            <th>Province</th>
                            <th>Payment term</th>
                        </tr>
                    </thead>
                    <tbody hx-get="/api/recent-market-orders?lang=en" hx-trigger="every 5s" hx-swap="innerHTML">
                        {{ range .orders }}
                        <tr>
                            <td>{{ .Order }}</td>
                            <td>{{ .Product }}</td>
                            <td>{{ .QuantityUnit }}</td>
                            <td>{{ .Quality }}</td>
                            <td>{{ .Packaging }}</td>
                            <td>{{ .Market }}</td>
                            <td>{{ .SubMarket }}</td>
                            <td>{{ .ContractType }}</td>
                            <td>{{ .DeliveryTerm }}</td>
                            <td>{{ .Country }}</td>
                            <td>{{ .ProvinceState }}</td>
                            <td>{{ .PaymentTerm }}</td>
                        </tr>
                        {{ end }}
                    </tbody>
                </table>
            </div>

            <div class="card" style="margin-top: 20px; padding-bottom: 50px;">
                <h2>Recent global market orders</h2>
                <table id="transactions-table-global">
                    <thead>
                        <tr>
                            <th>Order</th>
                            <th>Product</th>
                            <th>Quantity</th>
                            <th>Quality</th>
                            <th>Packing</th>
                            <th>Market</th>
                            <th>Submarket</th>
                            <th>Contract type</th>
                            <th>Delivery term</th>
                            <th>Region</th>
                            <th>Country</th>
                            <th>Port of loading</th>
                            <th>Port of discharge</th>
                            <th>Payment term</th>
                        </tr>
                    </thead>
                    <tbody hx-get="/api/recent-market-orders-global?lang=en"
                           hx-trigger="every 5s"
                           hx-swap="innerHTML">
                        {{ range .globalOrders }}
                        <tr>
                            <td>{{ .Order }}</td>
                            <td>{{ .Product }}</td>
                            <td>{{ .QuantityUnit }}</td>
                            <td>{{ .Quality }}</td>
                            <td>{{ .Packaging }}</td>
                            <td>{{ .Market }}</td>
                            <td>{{ .SubMarket }}</td>
                            <td>{{ .ContractType }}</td>
                            <td>{{ .DeliveryTerm }}</td>
                            <td>{{ .Region }}</td>
                            <td>{{ .Country }}</td>
                            <td>{{ .PortOfLoading }}</td>
                            <td>{{ .PortOfDischarge }}</td>
                            <td>{{ .PaymentTerm }}</td>
                        </tr>
                        {{ end }}
                    </tbody>
                </table>
            </div>

        </div>

        <script>
            // Initialize price chart
            const ctx = document.getElementById('priceChart').getContext('2d');
            const priceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['7 days ago', '6 days ago', '5 days ago', '4 days ago', '3 days ago', '2 days ago', 'Yesterday', 'Today'],
                    datasets: [{
                        label: 'Woodchip price (USD/MT)',
                        data: [43.21, 44.52, 45.16, 44.89, 45.67, 46.02, 45.78, 45.67],
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });
        </script>
</div>
</main>

<footer class="footer">
    <br>
    <a href="/about?lang={{ .lang }}">About us</a> | <a href="/faqs?lang={{ .lang }}">FAQs</a> | <a
        href="/contact?lang={{ .lang }}">Contact us</a><br>
    <p>BIOMASS EXCHANGE CO., LTD.</p>
    <br>
</footer>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.index-nav-links');
        const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
        const dropdownContents = document.querySelectorAll('.dropdown-content');
        const body = document.body;

        // Mobile detection
        const isMobile = () => window.innerWidth <= 768;

        // Toggle hamburger menu
        if (hamburger && navLinks) {
            hamburger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Toggle active classes
                hamburger.classList.toggle('active');
                navLinks.classList.toggle('active');

                // Manage body scroll
                if (navLinks.classList.contains('active')) {
                    body.style.overflow = 'hidden';
                } else {
                    body.style.overflow = '';

                    // Reset all dropdown triggers and contents
                    dropdownTriggers.forEach(trigger => {
                        trigger.classList.remove('active');
                    });

                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });
        }

        // Handle dropdown triggers in mobile view
        dropdownTriggers.forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                if (isMobile()) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Remove active class from all triggers
                    dropdownTriggers.forEach(t => {
                        if (t !== this) {
                            t.classList.remove('active');
                        }
                    });

                    // Hide all dropdowns first
                    dropdownContents.forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });

                    // Toggle active class on current trigger
                    const wasActive = this.classList.contains('active');
                    this.classList.toggle('active');

                    // Show dropdown content if trigger is active
                    const content = this.nextElementSibling;
                    if (!wasActive) {
                        content.style.display = 'block';
                    }
                }
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!isMobile()) return;

            const isClickInsideNav = navLinks && navLinks.contains(event.target);
            const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

            if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
                navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset all dropdown triggers and contents
                dropdownTriggers.forEach(trigger => {
                    trigger.classList.remove('active');
                });

                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                // Reset styles for desktop view
                if (navLinks) navLinks.classList.remove('active');
                if (hamburger) hamburger.classList.remove('active');
                body.style.overflow = '';

                // Reset all dropdown triggers and contents
                dropdownTriggers.forEach(trigger => {
                    trigger.classList.remove('active');
                });

                dropdownContents.forEach(content => {
                    content.style.display = '';
                });
            }
        });

        // Add click event to regular nav links to close menu when clicked
        const regularLinks = navLinks.querySelectorAll('a:not(.dropdown-trigger)');
        regularLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (isMobile()) {
                    navLinks.classList.remove('active');
                    hamburger.classList.remove('active');
                    body.style.overflow = '';
                }
            });
        });
    });
</script>
{{ end }}
