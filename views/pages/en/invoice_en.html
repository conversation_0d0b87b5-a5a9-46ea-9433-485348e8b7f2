{{define "content"}}
<div class="page-header">
    <h1>My Invoices</h1>
</div>

<div class="order-filter-container">
    <span class="order-filter-label">Filter Invoices:</span>
    <div class="filter-buttons">
        <button class="filter-button active" data-filter="all">All Invoices</button>
        <button class="filter-button" data-filter="as-buyer">As Buyer</button>
        <button class="filter-button" data-filter="as-seller">As Seller</button>
        <button class="filter-button" data-filter="platform-fees">Platform Fees</button>
    </div>
</div>

<div class="invoice-container">
    {{if .Invoices}}
    <table class="invoice-list">
        <thead>
            <tr>
                <th>Invoice #</th>
                <th>Date</th>
                <th>Due Date</th>
                <th>Type</th>
                <th>Related To</th>
                <th>Amount</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {{range .Invoices}}
            <tr class="invoice-row {{if eq .ItemID 1}}type-product{{else}}type-fee{{end}} {{if eq .SellerID $.UserID}}as-seller{{else}}as-buyer{{end}}">
                <td>{{.InvoiceNumber}}</td>
                <td>{{.InvoiceDate.Format "Jan 02, 2006"}}</td>
                <td>{{.DueDate.Format "Jan 02, 2006"}}</td>
                <td>{{.ItemEnName}}</td>
                <td>{{.ContractNumber}}</td>
                <td>{{.ContractValue}} {{.CurrencyCode}}</td>
                <td>
                    <span class="invoice-status status-{{.Status}}">
                        {{if eq .Status "draft"}}Draft{{end}}
                        {{if eq .Status "sent"}}Payment Pending{{end}}
                        {{if eq .Status "viewed"}}Payment Pending{{end}}
                        {{if eq .Status "paid"}}✓ Payment Received{{end}}
                        {{if eq .Status "overdue"}}⚠️ Payment Overdue{{end}}
                        {{if eq .Status "canceled"}}Canceled{{end}}
                        {{if eq .Status "disputed"}}Under Review{{end}}
                    </span>
                </td>
                <td>
                    <a href="/invoice/{{.ID}}?lang={{$.Lang}}" class="action-button view-button">View</a>
                    <a href="/invoice/{{.ID}}/download?lang={{$.Lang}}" class="action-button download-button">Download</a>
                </td>
            </tr>
            {{end}}
        </tbody>
    </table>
    {{else}}
    <div class="no-invoices">
        <p>You don't have any invoices yet.</p>
    </div>
    {{end}}
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set up invoice filter buttons
        const filterButtons = document.querySelectorAll('.filter-button');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));

                // Add active class to clicked button
                this.classList.add('active');

                const filter = this.getAttribute('data-filter');

                // Show/hide rows based on filter
                const rows = document.querySelectorAll('.invoice-row');
                rows.forEach(row => {
                    switch(filter) {
                        case 'all':
                            row.style.display = '';
                            break;
                        case 'as-buyer':
                            row.style.display = row.classList.contains('as-buyer') ? '' : 'none';
                            break;
                        case 'as-seller':
                            row.style.display = row.classList.contains('as-seller') ? '' : 'none';
                            break;
                        case 'platform-fees':
                            row.style.display = row.classList.contains('type-fee') ? '' : 'none';
                            break;
                    }
                });

                // Save filter preference
                localStorage.setItem('invoiceFilter', filter);
            });
        });

        // Apply saved filter if exists
        const savedFilter = localStorage.getItem('invoiceFilter');
        if (savedFilter) {
            const buttonToActivate = document.querySelector(`.filter-button[data-filter="${savedFilter}"]`);
            if (buttonToActivate) {
                // Simulate click to apply the filter
                buttonToActivate.click();
            }
        }
    });
</script>
{{end}}
