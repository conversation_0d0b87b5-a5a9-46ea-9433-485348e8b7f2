{{ define "base" }}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">
    <link rel="stylesheet" href="/static/css/styles.css">
    <script src="https://unpkg.com/htmx.org@2.0.3"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
</head>
<script>
    function toggleEdit(id) {
        document.getElementById(id).classList.toggle("hidden");
    }

    </script>

    <style>
    .hidden { display: none; }
    .edit-btn { margin-left: 10px; }
    .address-container { display: flex; flex-wrap: wrap; gap: 10px; }
    .address-card { border: 1px solid #ddd; padding: 10px; width: 48%; }

    /* Dropdown menu styles */
    .dropdown-container {
        position: relative;
        display: inline-block;
    }

    .dropdown-trigger {
        cursor: pointer;
        color: #333;
        margin: 0 15px;
        text-decoration: none;
        font-size: 18px;
        padding: 5px 0;
        transition: color 0.3s ease;
    }

    .dropdown-trigger:hover {
        color: #ff6d00;
    }

    .dropdown-content {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        background-color: white;
        min-width: 160px;
        box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
        z-index: 1000;
        border-radius: 4px;
        overflow: hidden;
    }

    .dropdown-content a {
        color: #333;
        padding: 12px 16px;
        text-decoration: none;
        display: block;
        font-size: 16px;
        text-align: left;
        margin: 0;
        white-space: nowrap;
    }

    .dropdown-content a:hover {
        background-color: rgba(178, 255, 89, 0.2);
        color: #ff6d00;
    }

    .dropdown-content.show {
        display: block;
        animation: fadeIn 0.3s;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Mobile responsive styles for dropdown */
    @media screen and (max-width: 768px) {
        .dropdown-container {
            display: block;
            width: 100%;
        }

        .dropdown-trigger {
            display: block;
            width: 100%;
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            margin: 0;
        }

        .dropdown-content {
            position: static;
            box-shadow: none;
            width: 100%;
            background-color: rgba(178, 255, 89, 0.1);
        }

        .dropdown-content a {
            text-align: center;
            padding: 12px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        .dropdown-content.show {
            display: block;
        }
    }
        .address-table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .address-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .address-table th,
        .address-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .address-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .address-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .address-table tr:hover {
            background-color: #f1f1f1;
        }

        .edit-btn {
            background-color: #ff9800;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            margin-right: 5px;
        }

        .delete-btn {
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
        }

        .add-address-btn {
            background-color: #2196F3;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 20px;
        }

        .success-message {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
        }

        .error-message {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
        }

        .info-message {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            text-align: center;
        }

        .modal {
          position: fixed;
          z-index: 1000;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0,0,0,0.4);
          overflow: auto;
          margin-bottom: 150px
        }

        .modal-content {
          background-color: #fefefe;
          margin: 5% auto;
          padding: 20px;
          border: 1px solid #888;
          width: 80%;
          max-width: 600px;
          border-radius: 8px;
          margin-bottom: 150px
        }

        .close-btn {
          color: #aaa;
          float: right;
          font-size: 28px;
          font-weight: bold;
          cursor: pointer;
        }

        .form-group {
          margin-bottom: 15px;
        }

        .form-group label {
          display: block;
          margin-bottom: 5px;
          font-weight: bold;
        }

        .form-group input {
          width: 100%;
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }

        .button-group {
          margin-top: 20px;
          text-align: right;
        }

        .button-group button {
          padding: 8px 16px;
          margin-left: 10px;
          border-radius: 4px;
          border: none;
          cursor: pointer;
        }

        .button-group button:first-child {
          background-color: #4CAF50;
          color: white;
        }

        .button-group button:last-child {
          background-color: #f44336;
          color: white;
        }

        small {
          color: #666;
          font-size: 0.8em;
        }

        textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
          }

          select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
          }

          .loading {
            color: #666;
            font-style: italic;
          }
        </style>
<body>
    {{ template "content" . }}
</body>
</html>
{{ end }}