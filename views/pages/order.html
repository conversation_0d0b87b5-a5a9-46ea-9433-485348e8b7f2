{{ define "base" }}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order</title>
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">
    <link rel="stylesheet" href="/static/css/styles.css">
    <script src="https://unpkg.com/htmx.org@2.0.3"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
    <style>
        #port-fields, #location-fields {
            display: none; /* ซ่อนทั้งสองส่วนตอนเริ่มต้น */
        }

        /* Dropdown menu styles */
        .dropdown-container {
            position: relative;
            display: inline-block;
        }

        .dropdown-trigger {
            cursor: pointer;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 4px;
            overflow: hidden;
        }

        .dropdown-content a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            font-size: 16px;
            text-align: left;
            margin: 0;
        }

        .dropdown-content a:hover {
            background-color: rgba(178, 255, 89, 0.2);
            color: #ff6d00;
        }

        .dropdown-content.show {
            display: block;
            animation: fadeIn 0.3s;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Adjust main content for top navbar */
        .main-dashboard {
            margin-top: 80px;
            padding: 20px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Mobile responsive styles for dropdown */
        @media screen and (max-width: 768px) {
            .dropdown-container {
                display: block;
                width: 100%;
            }

            .dropdown-trigger {
                display: block;
                width: 100%;
                padding: 15px;
                text-align: center;
                border-bottom: 1px solid rgba(0,0,0,0.1);
            }

            .dropdown-content {
                position: static;
                box-shadow: none;
                width: 100%;
                background-color: rgba(178, 255, 89, 0.1);
            }

            .dropdown-content a {
                text-align: center;
                padding: 12px;
                border-bottom: 1px solid rgba(0,0,0,0.05);
            }

            .dropdown-content.show {
                display: block;
            }
        }
    </style>
</head>
<body>
    {{ template "content" . }}{{ end }}
</body>
</html>
