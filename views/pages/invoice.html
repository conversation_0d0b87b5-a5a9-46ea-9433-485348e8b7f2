{{ define "base" }}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BiomassX - Invoices</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <link rel="stylesheet" href="/static/css/responsive.css">
    <link rel="stylesheet" href="/static/css/invoice_styles.css">
    <script src="https://unpkg.com/htmx.org@1.9.2"></script>
    <style>
        /* Invoice list styling */
        .invoice-list {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            color: #333;
        }
        .invoice-list th, .invoice-list td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .invoice-list th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .invoice-list tr:hover {
            background-color: #f5f5f5;
        }

        /* Invoice status styling */
        .invoice-status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            display: inline-block;
        }
        .status-draft {
            background-color: #e0e0e0;
            color: #333;
        }
        .status-sent {
            background-color: #cce5ff;
            color: #004085;
        }
        .status-viewed {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }
        .status-overdue {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-canceled {
            background-color: #e2e3e5;
            color: #383d41;
        }
        .status-disputed {
            background-color: #fff3cd;
            color: #856404;
        }

        /* Action buttons */
        .action-button {
            padding: 5px 10px;
            margin-right: 5px;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
        }
        .view-button {
            background-color: #0096FE;
            color: white;
        }
        .download-button {
            background-color: #28a745;
            color: white;
        }

        /* Invoice detail page styling */
        .invoice-detail {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: 20px;
            color: #333;
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
        }
        .invoice-number {
            font-size: 18px;
            color: #666;
        }
        .invoice-dates {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .invoice-date, .due-date {
            flex: 1;
        }
        .invoice-parties {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .seller-info, .buyer-info {
            flex: 1;
        }
        .invoice-items {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .invoice-items th, .invoice-items td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .invoice-items th {
            background-color: #f2f2f2;
        }

        .fee-calculation {
            background-color: #f9f9f9;
        }

        .fee-note {
            text-align: right;
            font-style: italic;
            color: #666;
            padding-top: 0 !important;
        }
        .invoice-total {
            text-align: right;
            font-size: 18px;
            font-weight: bold;
            margin-top: 20px;
        }
        .invoice-actions {
            margin-top: 20px;
            text-align: right;
        }

        /* Invoice action buttons */
        .pay-button {
            background-color: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .cancel-button {
            background-color: #dc3545;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        .dispute-button {
            background-color: #ffc107;
            color: #212529;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        /* Status message styling */
        .status-message {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message-icon {
            font-size: 20px;
            margin-right: 10px;
        }

        .message-text {
            font-size: 16px;
        }

        .status-message.status-marked-as-paid {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-message.status-disputed {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }

        .status-message.status-canceled {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Loading spinner styles */
        .loading-spinner {
            display: none;
            margin-left: 8px;
        }

        .htmx-request .loading-spinner {
            display: inline-block;
        }

        .htmx-request .button-text {
            opacity: 0.7;
        }

        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 0.8s linear infinite;
            vertical-align: middle;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .status-message.status-updated {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .back-button {
            background-color: #6c757d;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }

        /* Filter styling to match order book */
        .order-filter-container {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .order-filter-label {
            margin-right: 10px;
            font-weight: bold;
            color: #333;
        }

        .order-filter-select {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ccc;
            background-color: white;
            font-size: 14px;
            min-width: 150px;
        }

        /* Responsive styling */
        @media (max-width: 768px) {
            .invoice-header, .invoice-dates, .invoice-parties {
                flex-direction: column;
            }
            .seller-info, .buyer-info, .invoice-date, .due-date {
                margin-bottom: 10px;
            }
            .invoice-actions {
                display: flex;
                flex-direction: column;
            }
            .pay-button, .cancel-button, .dispute-button {
                margin: 5px 0;
                width: 100%;
            }
            .order-filter-container {
                flex-direction: column;
                align-items: flex-start;
            }
            .order-filter-label {
                margin-bottom: 5px;
            }
            .order-filter-select {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="dash-nav">
            <div class="dash-nav-container">
                <a href="/?lang={{ .Lang }}" class="dash-nav-brand">BiomassX</a>
                <div class="dash-nav-links">
                    <a href="/dashboard?lang={{ .Lang }}">Dashboard</a>

                    <div class="dropdown-container">
                        <a href="#" class="dropdown-trigger">Submit orders</a>
                        <div class="dropdown-content">
                            <a href="/order?lang={{ .Lang }}">Products</a>
                        </div>
                    </div>

                    <div class="dropdown-container">
                        <a href="#" class="dropdown-trigger">Order book</a>
                        <div class="dropdown-content">
                            <a href="/order_book?lang={{ .Lang }}">Products</a>
                        </div>
                    </div>

                    <div class="dropdown-container">
                        <a href="#" class="dropdown-trigger">Matched orders</a>
                        <div class="dropdown-content">
                            <a href="/contract?lang={{ .Lang }}">Products</a>
                        </div>
                    </div>

                    <div class="dropdown-container">
                        <a href="#" class="dropdown-trigger">Setting</a>
                        <div class="dropdown-content">
                            <a href="/profile?lang={{ .Lang }}">Profile</a>
                        </div>
                    </div>

                    <a href="/logout?lang={{ .Lang }}" hx-post="/logout?lang={{ .Lang }}" hx-redirect="/login?lang={{ .Lang }}">Logout</a>
                </div>
                <button class="hamburger" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </nav>
    </header>

    <main class="main-dashboard">
        {{template "content" .}}
    </main>

    <footer class="footer">
        <br>
        <a href="/about?lang={{ .Lang }}">About us</a> | <a href="/faqs?lang={{ .Lang }}">FAQs</a> | <a href="/contact?lang={{ .Lang }}">Contact us</a><br>
        <p>BIOMASS EXCHANGE CO., LTD.</p>
        <br>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const hamburger = document.querySelector('.hamburger');
            const navLinks = document.querySelector('.dash-nav-links');
            const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');
            const dropdownContents = document.querySelectorAll('.dropdown-content');
            const body = document.body;

            // Mobile detection
            const isMobile = () => window.innerWidth <= 768;

            // Toggle hamburger menu
            if (hamburger && navLinks) {
                hamburger.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Toggle active classes
                    hamburger.classList.toggle('active');
                    navLinks.classList.toggle('active');

                    // Manage body scroll
                    if (navLinks.classList.contains('active')) {
                        body.style.overflow = 'hidden';

                        // Show all dropdown contents in mobile view
                        if (isMobile()) {
                            dropdownContents.forEach(content => {
                                content.style.display = 'block';
                            });
                        }
                    } else {
                        body.style.overflow = '';

                        // Hide all dropdown contents when closing menu
                        dropdownContents.forEach(content => {
                            content.style.display = '';
                        });
                    }
                });
            }

            // Handle dropdown triggers in mobile view
            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    if (isMobile()) {
                        e.preventDefault();
                        e.stopPropagation();

                        const content = this.nextElementSibling;
                        const isVisible = content.style.display === 'block';

                        // Hide all dropdowns first
                        dropdownContents.forEach(dropdown => {
                            dropdown.style.display = 'none';
                        });

                        // Toggle current dropdown
                        if (!isVisible) {
                            content.style.display = 'block';
                        }
                    }
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!isMobile()) return;

                const isClickInsideNav = navLinks && navLinks.contains(event.target);
                const isClickInsideHamburger = hamburger && hamburger.contains(event.target);

                if (!isClickInsideNav && !isClickInsideHamburger && navLinks && navLinks.classList.contains('active')) {
                    navLinks.classList.remove('active');
                    if (hamburger) hamburger.classList.remove('active');
                    body.style.overflow = '';

                    // Hide all dropdown contents
                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    // Reset styles for desktop view
                    if (navLinks) navLinks.classList.remove('active');
                    if (hamburger) hamburger.classList.remove('active');
                    body.style.overflow = '';

                    // Reset dropdown display
                    dropdownContents.forEach(content => {
                        content.style.display = '';
                    });
                }
            });
        });

        // HTMX is now used for invoice status updates
    </script>
</body>
</html>
{{ end }}
