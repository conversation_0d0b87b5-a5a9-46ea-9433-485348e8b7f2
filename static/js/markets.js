// Markets page JavaScript functionality

document.addEventListener('DOMContentLoaded', function () {
  // Price History Chart initialization
  const priceHistoryChart = new Chart(
    document.getElementById('priceHistoryChart'),
    {
      type: 'line',
      data: {
        labels: ['Today'],
        datasets: [{
          label: 'Bid Price',
          data: [null],
          borderColor: '#96FE00', // Green for bid price
          backgroundColor: 'rgba(150, 254, 0, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Offer Price',
          data: [null],
          borderColor: '#e74c3c', // Red for offer price
          backgroundColor: 'rgba(231, 76, 60, 0.1)', // Light red background
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'Price History',
            font: {
              size: 16,
              weight: 'bold'
            }
          },
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 15,
              font: {
                weight: 'bold'
              },
              generateLabels: function (chart) {
                const datasets = chart.data.datasets;
                return datasets.map((dataset, i) => {
                  return {
                    text: dataset.label,
                    fillStyle: dataset.borderColor,
                    strokeStyle: dataset.borderColor,
                    lineWidth: 2,
                    hidden: !chart.isDatasetVisible(i),
                    index: i,
                    fontColor: i === 0 ? '#96FE00' : '#e74c3c' // Set text color based on dataset index
                  };
                });
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: false,
            title: {
              display: true,
              text: 'Price (USD)',
              font: {
                weight: 'bold'
              }
            },
            grid: {
              color: 'rgba(0, 0, 0, 0.05)'
            }
          },
          x: {
            grid: {
              color: 'rgba(0, 0, 0, 0.05)'
            }
          }
        },
        backgroundColor: 'white',
        elements: {
          point: {
            radius: 3,
            hoverRadius: 5
          },
          line: {
            borderWidth: 2
          }
        }
      }
    }
  );

  // Variables for real-time updates
  let realtimeUpdateInterval = null;
  const REALTIME_UPDATE_FREQUENCY = 1000; // Update every 1 second

  // Function to fetch price history data
  async function fetchPriceHistory(productId, days) {
    if (!productId) {
      console.log('No product ID provided to fetchPriceHistory');
      document.getElementById('priceHistoryNoData').style.display = 'flex';
      return null;
    }

    document.getElementById('priceHistoryLoading').style.display = 'flex';
    document.getElementById('priceHistoryNoData').style.display = 'none';

    try {
      const url = `/api/price-history?` + new URLSearchParams({
        product_id: productId,
        days: days,
        market_id: document.getElementById('marketFilter').value || '0',
        marketspace_id: document.getElementById('localFilter').value || '0',
        quality_id: document.getElementById('qualityFilter').value || '0',
        delivery_term_id: document.getElementById('deliveryTermFilter').value || '0',
        payment_term_id: document.getElementById('paymentTermFilter').value || '0',
        contract_type_id: document.getElementById('contractFilter').value || '0'
      });

      console.log('Fetching price history with URL:', url);

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch price history: ${response.status}`);
      }

      const data = await response.json();
      console.log('Received price history data:', data);

      return data;
    } catch (error) {
      console.error('Error fetching price history:', error);
      return null;
    } finally {
      document.getElementById('priceHistoryLoading').style.display = 'none';
    }
  }

  // Function to fetch real-time price history data
  async function fetchPriceHistoryRealtime(productId, days) {
    if (!productId) {
      console.log('No product ID provided for real-time updates');
      document.getElementById('priceHistoryNoData').style.display = 'flex';
      return null;
    }

    try {
      const url = `/api/price-history-realtime?` + new URLSearchParams({
        product_id: productId,
        days: days,
        market_id: document.getElementById('marketFilter').value || '0',
        marketspace_id: document.getElementById('localFilter').value || '0',
        quality_id: document.getElementById('qualityFilter').value || '0',
        delivery_term_id: document.getElementById('deliveryTermFilter').value || '0',
        payment_term_id: document.getElementById('paymentTermFilter').value || '0',
        contract_type_id: document.getElementById('contractFilter').value || '0',
        timestamp: new Date().getTime() // Prevent caching
      });

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch real-time price history: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching real-time price history:', error);
      return null;
    }
  }

  // Function to update the price history chart with new data
  function updatePriceHistoryChart(data) {
    if (!data || data.length === 0) {
      // No data available
      document.getElementById('priceHistoryNoData').style.display = 'flex';
      priceHistoryChart.data.labels = ['Today'];
      priceHistoryChart.data.datasets[0].data = [null];
      priceHistoryChart.data.datasets[1].data = [null];
      priceHistoryChart.update();
      return;
    }

    // Hide no data message
    document.getElementById('priceHistoryNoData').style.display = 'none';

    // Extract dates, bid prices, and offer prices
    const labels = data.map(item => item.date);
    const bidPrices = data.map(item => item.bid_price);
    const offerPrices = data.map(item => item.offer_price);

    // Update chart data
    priceHistoryChart.data.labels = labels;
    priceHistoryChart.data.datasets[0].data = bidPrices;
    priceHistoryChart.data.datasets[1].data = offerPrices;

    // Update chart title with product name if available
    const productName = document.getElementById('productSearch').value;
    priceHistoryChart.options.plugins.title.text = productName ?
      `Price History: ${productName}` : 'Price History';

    // Update dataset labels with product name
    if (productName) {
      priceHistoryChart.data.datasets[0].label = `${productName} - Bid Price`;
      priceHistoryChart.data.datasets[1].label = `${productName} - Offer Price`;
    } else {
      priceHistoryChart.data.datasets[0].label = 'Bid Price';
      priceHistoryChart.data.datasets[1].label = 'Offer Price';
    }

    priceHistoryChart.update();
  }

  // Function to start real-time updates
  function startRealtimeUpdates() {
    // Clear any existing interval
    if (realtimeUpdateInterval) {
      clearInterval(realtimeUpdateInterval);
    }

    // Get the current product ID and time period
    const productId = window.selectedProductId;
    if (!productId) {
      console.log('No product selected, not starting real-time updates');
      return;
    }

    // Get the currently selected time period
    const activeTimeBtn = document.querySelector('.time-btn.active');
    const days = activeTimeBtn ? activeTimeBtn.getAttribute('data-days') : '1';

    console.log(`Starting real-time updates for product ${productId} with ${days} day(s) period`);

    // Show real-time indicator
    const chartContainer = document.querySelector('.chart-container');
    let indicator = document.getElementById('realtime-indicator');

    if (!indicator) {
      indicator = document.createElement('div');
      indicator.id = 'realtime-indicator';
      indicator.style.position = 'absolute';
      indicator.style.top = '10px';
      indicator.style.right = '10px';
      indicator.style.fontSize = '12px';
      indicator.style.color = '#e74c3c';
      indicator.style.padding = '3px 6px';
      indicator.style.borderRadius = '3px';
      indicator.style.backgroundColor = 'rgba(231, 76, 60, 0.1)';
      indicator.style.border = '1px solid rgba(231, 76, 60, 0.3)';
      indicator.style.zIndex = '100';
      chartContainer.appendChild(indicator);
    }

    // Update indicator text
    indicator.innerHTML = 'LIVE';
    indicator.style.display = 'block';

    // Add pulse animation
    indicator.style.animation = 'pulse 1s infinite';

    // Add the pulse animation if it doesn't exist
    if (!document.getElementById('pulse-animation')) {
      const style = document.createElement('style');
      style.id = 'pulse-animation';
      style.textContent = `
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `;
      document.head.appendChild(style);
    }

    // Set up interval for real-time updates
    realtimeUpdateInterval = setInterval(async () => {
      // Get the current product ID and time period (in case they changed)
      const currentProductId = window.selectedProductId;
      const currentActiveTimeBtn = document.querySelector('.time-btn.active');
      const currentDays = currentActiveTimeBtn ? currentActiveTimeBtn.getAttribute('data-days') : '1';

      if (currentProductId) {
        const data = await fetchPriceHistoryRealtime(currentProductId, currentDays);
        if (data) {
          updatePriceHistoryChart(data);

          // Flash effect to indicate new data
          const canvas = document.getElementById('priceHistoryChart');
          canvas.style.transition = 'background-color 0.3s';
          canvas.style.backgroundColor = 'rgba(52, 152, 219, 0.1)';
          setTimeout(() => {
            canvas.style.backgroundColor = 'transparent';
          }, 300);
        }
      }
    }, REALTIME_UPDATE_FREQUENCY);
  }

  // Function to stop real-time updates
  function stopRealtimeUpdates() {
    if (realtimeUpdateInterval) {
      clearInterval(realtimeUpdateInterval);
      realtimeUpdateInterval = null;

      // Hide the indicator
      const indicator = document.getElementById('realtime-indicator');
      if (indicator) {
        indicator.style.display = 'none';
      }
    }
  }

  // Connect time filter buttons to the price history chart
  document.querySelectorAll('.time-btn').forEach(btn => {
    btn.addEventListener('click', function () {
      // Remove active class from all buttons
      document.querySelectorAll('.time-btn').forEach(b => b.classList.remove('active'));
      // Add active class to clicked button
      this.classList.add('active');

      // Get the selected days value
      const days = this.getAttribute('data-days');

      // Only fetch new data if a product is selected
      const productId = window.selectedProductId;
      if (productId) {
        fetchPriceHistory(productId, days).then(updatePriceHistoryChart);
      } else {
        document.getElementById('priceHistoryNoData').style.display = 'flex';
      }

      // Don't restart real-time updates - they're already running
      // and will pick up the new time period on the next interval
    });
  });

  // Update the search button click handler to also update the price history chart
  document.getElementById('searchSubmitBtn').addEventListener('click', function () {
    if (!selectedProductId && !window.selectedProductId) {
      document.getElementById('priceHistoryNoData').style.display = 'flex';
      stopRealtimeUpdates();
      return;
    }

    const productId = selectedProductId || window.selectedProductId;
    const activeTimeBtn = document.querySelector('.time-btn.active');
    const days = activeTimeBtn ? activeTimeBtn.getAttribute('data-days') : '1';

    // Fetch price history data and update chart
    fetchPriceHistory(productId, days).then(updatePriceHistoryChart);

    // Start real-time updates if not already running
    if (!realtimeUpdateInterval) {
      startRealtimeUpdates();
    }
  });

  // Hook into product selection to start real-time updates
  // This assumes there's a global function that handles product selection
  const originalHandleProductSelect = window.handleProductSelect;
  window.handleProductSelect = function (product) {
    // Call the original handler if it exists
    if (typeof originalHandleProductSelect === 'function') {
      originalHandleProductSelect(product);
    } else {
      // If no original handler, implement basic functionality
      console.log('Selected product:', product);
      window.selectedProductId = product.id;
      document.getElementById('productSearch').value = product.name;
    }

    // Start real-time updates when a product is selected
    startRealtimeUpdates();
  };

  // Start real-time updates immediately if a product is already selected
  if (window.selectedProductId) {
    startRealtimeUpdates();
  }

  // Stop real-time updates when the user navigates away from the page
  window.addEventListener('beforeunload', stopRealtimeUpdates);

  // Pause updates when tab is not visible to save resources
  document.addEventListener('visibilitychange', function () {
    if (document.hidden) {
      // Pause updates when tab is not visible
      if (realtimeUpdateInterval) {
        clearInterval(realtimeUpdateInterval);
        realtimeUpdateInterval = null;

        // Hide the indicator but keep its element
        const indicator = document.getElementById('realtime-indicator');
        if (indicator) {
          indicator.style.display = 'none';
        }
      }
    } else {
      // Resume updates when tab becomes visible again
      if (window.selectedProductId) {
        startRealtimeUpdates();
      }
    }
  });

  // Make the chart visible with placeholder data initially
  document.getElementById('priceHistoryNoData').style.display = 'flex';
});

// Time filter button functionality
document.querySelectorAll('.time-btn').forEach(button => {
  button.addEventListener('click', () => {
    // Remove active class from all buttons
    document.querySelectorAll('.time-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    // Add active class to clicked button
    button.classList.add('active');

    // Update chart data based on timeframe
    // This is where you would normally fetch new data
    // For demo, we'll just update with random data
    const newData = Array(8).fill(0).map(() => Math.random() * 100 + 50);
    priceHistoryChart.data.datasets[0].data = newData;
    priceHistoryChart.update();
  });
});

// Add to your existing script section
document.addEventListener('DOMContentLoaded', function () {
  const searchInput = document.getElementById('productSearch');
  const qualityFilter = document.getElementById('qualityFilter');

  // Function to update quality options
  async function updateQualityOptions(productId) {
    const qualityFilter = document.getElementById('qualityFilter');
    try {
      const response = await fetch(`/api/products/${productId}/qualities?lang=${document.documentElement.lang}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const qualities = await response.json();
      console.log('Fetched qualities:', qualities); // Debug log

      // Clear existing options except the first one
      qualityFilter.innerHTML = '<option value="">Quality</option>';

      // Add new options
      qualities.forEach(quality => {
        const option = document.createElement('option');
        option.value = quality.quality_id;

        // Create option text with combined information
        const qualityName = document.documentElement.lang === 'th' ?
          quality.quality_name_th : quality.quality_name_en;
        const standardName = document.documentElement.lang === 'th' ?
          quality.standard_name_th : quality.standard_name_en;
        const gradeName = document.documentElement.lang === 'th' ?
          quality.grade_name_th : quality.grade_name_en;

        option.textContent = `${qualityName} - ${standardName} ${gradeName}`;
        qualityFilter.appendChild(option);
      });

      // Enable the select
      qualityFilter.disabled = false;

    } catch (error) {
      console.error('Error loading quality options:', error);
      qualityFilter.innerHTML = '<option value="">Error loading qualities</option>';
      qualityFilter.disabled = true;
    }
  }

  // Update quality options when a product is selected
  searchInput.addEventListener('change', function () {
    const selectedProduct = this.getAttribute('data-selected-id');
    if (selectedProduct) {
      updateQualityOptions(selectedProduct);
    } else {
      qualityFilter.innerHTML = '<option value="">Select a product first</option>';
      qualityFilter.disabled = true;
    }
  });
});

// Search functionality - improved autocomplete with better user feedback
document.getElementById('productSearch').addEventListener('input', function (e) {
  const searchTerm = e.target.value.toLowerCase();

  // Clear product selection if input is cleared
  if (searchTerm.length === 0) {
    // Reset product selection
    window.selectedProductId = null;
    this.removeAttribute('data-selected-id');
    this.classList.remove('product-selected');
    document.getElementById('selectedProductIndicator').style.display = 'none';

    // Disable quality filter
    document.getElementById('qualityFilter').disabled = true;
    document.getElementById('qualityFilter').innerHTML = '<option value="">All Qualities</option>';

    return;
  }

  // Only show search results if we have at least 2 characters
  if (searchTerm.length < 2) {
    document.getElementById('searchResults').style.display = 'none';
    return;
  }
});

// Add click handler for search button - now acts as a submit button
document.querySelector('.search-btn').addEventListener('click', function () {
  const searchTerm = document.getElementById('productSearch').value;
  // Don't trigger input event - we'll handle search in the main search function
  // This will be handled by the searchSubmitBtn click handler in the main script
});

// Product search and selection functionality
document.addEventListener('DOMContentLoaded', function () {
  const searchInput = document.getElementById('productSearch');
  const searchResults = document.getElementById('searchResults');
  const marketFilter = document.getElementById('marketFilter');
  const qualityFilter = document.getElementById('qualityFilter');

  // Handle product search and selection
  searchInput.addEventListener('input', async function () {
    const query = this.value.trim();
    if (query.length < 2) {
      searchResults.style.display = 'none';
      return;
    }

    try {
      const response = await fetch(`/api/search-products?q=${encodeURIComponent(query)}`);
      if (!response.ok) throw new Error('Search failed');

      const products = await response.json();
      displaySearchResults(products);
    } catch (error) {
      console.error('Search error:', error);
      searchResults.innerHTML = '<div class="error">Error searching products</div>';
    }
  });

  // Display search results with enhanced market information
  function displaySearchResults(products) {
    searchResults.innerHTML = '';
    if (products.length === 0) {
      const noResultsText = document.documentElement.lang === 'th' ?
        'ไม่พบสินค้า' : 'No products found';
      searchResults.innerHTML = `<div class="no-results">${noResultsText}</div>`;
      return;
    }

    const ul = document.createElement('ul');
    products.forEach(product => {
      const li = document.createElement('li');
      li.setAttribute('data-product-id', product.id);
      li.setAttribute('data-market-id', product.market_id);
      li.innerHTML = `
              <div class="product-info">
                  <strong>${product.name}</strong>
                  <span class="th-name">${product.th_name || ''}</span>
                  <small class="market-name">${product.market_name || ''}</small>
              </div>
          `;

      li.addEventListener('click', () => handleProductSelect(product));
      ul.appendChild(li);
    });

    searchResults.appendChild(ul);
    searchResults.style.display = 'block';
  }

  // Handle product selection with market auto-selection
  async function handleProductSelect(product) {
    console.log('Selected product:', product);

    // Update search input
    searchInput.value = product.name;
    searchResults.style.display = 'none';

    // Auto-select the market
    if (product.market_id) {
      console.log('Setting market to:', product.market_id);
      marketFilter.value = product.market_id;
      // Trigger change event on market filter
      marketFilter.dispatchEvent(new Event('change'));
    }

    try {
      // Fetch and update quality options
      const qualityResponse = await fetch(`/api/products/${product.id}/qualities`);
      if (!qualityResponse.ok) throw new Error('Failed to fetch qualities');

      const qualities = await qualityResponse.json();
      updateQualityOptions(qualities);
      qualityFilter.disabled = false;
    } catch (error) {
      console.error('Error fetching qualities:', error);
      qualityFilter.innerHTML = '<option value="">Error loading qualities</option>';
      qualityFilter.disabled = true;
    }
  }

  // Update quality options
  function updateQualityOptions(qualities) {
    qualityFilter.innerHTML = '<option value="">Select Quality</option>';
    qualities.forEach(quality => {
      const option = document.createElement('option');
      option.value = quality.id;
      option.textContent = `${quality.standard_en_name} - ${quality.grade_en_name}`;
      qualityFilter.appendChild(option);
    });
  }

  // Close search results when clicking outside
  document.addEventListener('click', function (e) {
    if (!searchResults.contains(e.target) && e.target !== searchInput) {
      searchResults.style.display = 'none';
    }
  });
});

// Marketspaces and terms loading functionality
document.addEventListener('DOMContentLoaded', function () {
  const localFilter = document.getElementById('localFilter');
  const deliveryTermFilter = document.getElementById('deliveryTermFilter');
  const paymentTermFilter = document.getElementById('paymentTermFilter');

  async function loadMarketspaces() {
    try {
      const response = await fetch('/api/marketspaces');
      if (!response.ok) throw new Error('Failed to fetch marketspaces');

      const marketspaces = await response.json();
      console.log('Fetched marketspaces:', marketspaces); // Debug log

      // Clear existing options
      const selectMarketspaceText = document.documentElement.lang === 'th' ?
        'พื้นที่ตลาดทั้งหมด' : 'All Marketspaces';
      localFilter.innerHTML = `<option value="">${selectMarketspaceText}</option>`;

      // Add marketspaces from database
      marketspaces.forEach(marketspace => {
        const option = document.createElement('option');
        option.value = marketspace.id;
        option.textContent = document.documentElement.lang === 'th' ?
          marketspace.th_name : marketspace.en_name;
        localFilter.appendChild(option);
      });
    } catch (error) {
      console.error('Error loading marketspaces:', error);
      localFilter.innerHTML = '<option value="">Error loading marketspaces</option>';
    }
  }

  // Load marketspaces when page loads
  loadMarketspaces();

  // Update the change event listener for marketspace selection
  localFilter.addEventListener('change', async function () {
    const selectedValue = this.value;
    console.log('Selected marketspace:', selectedValue);

    // Disable filters if no selection
    if (!selectedValue) {
      deliveryTermFilter.disabled = true;
      paymentTermFilter.disabled = true;
      return;
    }

    try {
      // Get local status based on the selected marketspace
      // Assuming id=1 is local/domestic and id=2 is international/global
      const isLocal = selectedValue === "1";

      const [deliveryResponse, paymentResponse] = await Promise.all([
        fetch(`/api/delivery-terms?local=${isLocal}`),
        fetch(`/api/payment-terms?local=${isLocal}`)
      ]);

      if (!deliveryResponse.ok || !paymentResponse.ok) {
        throw new Error('Failed to fetch terms');
      }

      const [deliveryTerms, paymentTerms] = await Promise.all([
        deliveryResponse.json(),
        paymentResponse.json()
      ]);

      // Update the filters with fetched data
      updateDeliveryTerms(deliveryTerms);
      updatePaymentTerms(paymentTerms);

      // Enable the filters
      deliveryTermFilter.disabled = false;
      paymentTermFilter.disabled = false;

    } catch (error) {
      console.error('Error fetching terms:', error);
      deliveryTermFilter.disabled = true;
      paymentTermFilter.disabled = true;
    }
  });

  function updateDeliveryTerms(terms) {
    if (!Array.isArray(terms)) {
      console.error('Invalid delivery terms data:', terms);
      return;
    }

    const selectDeliveryText = document.documentElement.lang === 'th' ?
      'เงื่อนไขการส่งมอบทั้งหมด' : 'All Delivery Terms';
    deliveryTermFilter.innerHTML = `<option value="">${selectDeliveryText}</option>`;
    terms.forEach(term => {
      const option = document.createElement('option');
      option.value = term.id;
      option.textContent = document.documentElement.lang === 'th' ?
        term.th_name : term.en_name;
      deliveryTermFilter.appendChild(option);
    });
  }

  function updatePaymentTerms(terms) {
    if (!Array.isArray(terms)) {
      console.error('Invalid payment terms data:', terms);
      return;
    }

    const selectPaymentText = document.documentElement.lang === 'th' ?
      'เงื่อนไขการชำระเงินทั้งหมด' : 'All Payment Terms';
    paymentTermFilter.innerHTML = `<option value="">${selectPaymentText}</option>`;
    terms.forEach(term => {
      const option = document.createElement('option');
      option.value = term.id;
      option.textContent = document.documentElement.lang === 'th' ?
        term.th_name : term.en_name;
      paymentTermFilter.appendChild(option);
    });
  }
});

// Markets and contract types loading functionality
document.addEventListener('DOMContentLoaded', function () {
  const marketFilter = document.getElementById('marketFilter');
  const contractFilter = document.getElementById('contractFilter');

  // Function to load market options
  async function loadMarkets() {
    try {
      const response = await fetch('/api/markets');
      if (!response.ok) throw new Error('Failed to fetch markets');

      const markets = await response.json();

      // Clear existing options except the first one
      const selectMarketText = document.documentElement.lang === 'th' ?
        'ตลาดทั้งหมด' : 'All Markets';
      marketFilter.innerHTML = `<option value="">${selectMarketText}</option>`;

      markets.forEach(market => {
        const option = document.createElement('option');
        option.value = market.id;
        option.textContent = document.documentElement.lang === 'th' ?
          market.th_name : market.en_name;
        marketFilter.appendChild(option);
      });
    } catch (error) {
      console.error('Error loading markets:', error);
      marketFilter.innerHTML = '<option value="">Error loading markets</option>';
    }
  }

  // Function to load contract type options
  async function loadContractTypes() {
    try {
      const response = await fetch('/api/contract-types');
      if (!response.ok) throw new Error('Failed to fetch contract types');

      const contractTypes = await response.json();

      // Clear existing options except the first one
      contractFilter.innerHTML = '<option value="">Select Contract Type</option>';

      contractTypes.forEach(contractType => {
        const option = document.createElement('option');
        option.value = contractType.id;
        option.textContent = document.documentElement.lang === 'th' ?
          contractType.th_name : contractType.en_name;
        contractFilter.appendChild(option);
      });
    } catch (error) {
      console.error('Error loading contract types:', error);
      contractFilter.innerHTML = '<option value="">Error loading contract types</option>';
    }
  }

  // Load both markets and contract types when the page loads
  loadMarkets();
  loadContractTypes();
});

// Global variable to track the selected product ID across all scripts
let selectedProductId = null;

// Debug logging function for use across all scripts
function debugLog(...args) {
  console.log('[Markets Debug]', ...args);
}

// Helper functions for formatting data
function formatPrice(value) {
  if (!value && value !== 0) return '-';
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
}

function formatNumber(value) {
  if (!value && value !== 0) return '-';
  return new Intl.NumberFormat('en-US').format(value);
}

function formatChange(value) {
  if (!value && value !== 0) return '-';
  return (value > 0 ? '+' : '') + formatPrice(value);
}

// Function to get the selected days from time filter
function getSelectedDays() {
  const activeTimeBtn = document.querySelector('.time-filter-btn.active');
  return activeTimeBtn ? activeTimeBtn.getAttribute('data-days') : '1';
}

// Function to fetch futures market data using the real database query
async function fetchFuturesData(productId, days) {
  const tableBody = document.querySelector('.futures-table tbody');

  if (!productId) {
    debugLog('No product ID provided to fetchFuturesData');
    tableBody.innerHTML = '<tr><td colspan="15">Please select a product first</td></tr>';
    return;
  }

  // Show loading state
  tableBody.innerHTML = '<tr><td colspan="15"><div class="loading-spinner"></div> Loading market data...</td></tr>';

  debugLog('Fetching futures data with parameters:', {
    product_id: productId,
    days: days,
    market_id: document.getElementById('marketFilter').value || '0',
    marketspace_id: document.getElementById('localFilter').value || '0',
    quality_id: document.getElementById('qualityFilter').value || '0',
    delivery_term_id: document.getElementById('deliveryTermFilter').value || '0',
    payment_term_id: document.getElementById('paymentTermFilter').value || '0',
    contract_type_id: document.getElementById('contractFilter').value || '0'
  });

  try {
    // Use the market-futures endpoint which uses the exact SQL query requested
    const url = `/api/market-futures?` + new URLSearchParams({
      product_id: productId,
      days: days,
      market_id: document.getElementById('marketFilter').value || '0',
      marketspace_id: document.getElementById('localFilter').value || '0',
      quality_id: document.getElementById('qualityFilter').value || '0',
      delivery_term_id: document.getElementById('deliveryTermFilter').value || '0',
      payment_term_id: document.getElementById('paymentTermFilter').value || '0',
      contract_type_id: document.getElementById('contractFilter').value || '0'
    });

    debugLog('Sending request to:', url);
    const response = await fetch(url);
    debugLog('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      debugLog('Error response:', errorText);
      throw new Error(`Failed to fetch market data: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    debugLog('Received futures data:', data);

    // Check if we have data
    if (data && data.length > 0) {
      debugLog('Sample data row:', data[0]);
      updateFuturesTable(data);
    } else {
      debugLog('No data returned from API');
      tableBody.innerHTML = '<tr><td colspan="15">No data available for the selected criteria</td></tr>';
    }
  } catch (error) {
    debugLog('Error fetching futures data:', error);
    tableBody.innerHTML = `<tr><td colspan="15">Error loading market data: ${error.message}</td></tr>`;
  }
}

// Function to update the futures table with the data from the API
function updateFuturesTable(data) {
  const tableBody = document.querySelector('.futures-table tbody');

  if (!data || data.length === 0) {
    tableBody.innerHTML = '<tr><td colspan="15">No data available for the selected criteria</td></tr>';
    return;
  }

  // Check if all values are zero (which might happen with COALESCE)
  const hasRealData = data.some(row =>
    row.open_price > 0 || row.high_price > 0 || row.low_price > 0 ||
    row.bid_volume > 0 || row.bid_price > 0 ||
    row.offer_volume > 0 || row.offer_price > 0
  );

  if (!hasRealData) {
    tableBody.innerHTML = '<tr><td colspan="15">No meaningful data available for the selected criteria</td></tr>';
    return;
  }

  tableBody.innerHTML = data.map(row => {
    const bidChangeClass = row.bid_change > 0 ? 'price-up' : row.bid_change < 0 ? 'price-down' : '';
    const offerChangeClass = row.offer_change > 0 ? 'price-up' : row.offer_change < 0 ? 'price-down' : '';

    return `
          <tr>
              <td>${row.contract_period}</td>
              <td>${formatPrice(row.open_price)}</td>
              <td>${formatPrice(row.high_price)}</td>
              <td>${formatPrice(row.low_price)}</td>
              <td>${formatNumber(row.bid_volume)}</td>
              <td>${formatPrice(row.bid_price)}</td>
              <td class="${bidChangeClass}">${formatChange(row.bid_change)}</td>
              <td>${formatPrice(row.price_spread)}</td>
              <td>${formatPrice(row.offer_open)}</td>
              <td>${formatPrice(row.offer_high)}</td>
              <td>${formatPrice(row.offer_low)}</td>
              <td>${formatNumber(row.offer_volume)}</td>
              <td>${formatPrice(row.offer_price)}</td>
              <td class="${offerChangeClass}">${formatChange(row.offer_change)}</td>
              <td>${formatPrice(row.offer_spread)}</td>
          </tr>
      `;
  }).join('');
}

// Main search and table functionality
document.addEventListener('DOMContentLoaded', function () {
  const searchInput = document.getElementById('productSearch');
  const searchResults = document.getElementById('searchResults');
  const marketFilter = document.getElementById('marketFilter');
  const localFilter = document.getElementById('localFilter');
  const qualityFilter = document.getElementById('qualityFilter');
  const deliveryTermFilter = document.getElementById('deliveryTermFilter');
  const paymentTermFilter = document.getElementById('paymentTermFilter');
  const contractFilter = document.getElementById('contractFilter');
  const searchSubmitBtn = document.getElementById('searchSubmitBtn');
  const tableBody = document.querySelector('.futures-table tbody');
  const timeFilterBtns = document.querySelectorAll('.time-filter-btn');

  // Using the global selectedProductId variable and debugLog function
  let searchTimeout;

  // Handle product search with debounce
  searchInput.addEventListener('input', function () {
    const query = this.value.trim();

    // If the input is cleared, reset the product selection
    if (query.length === 0) {
      selectedProductId = null;
      window.selectedProductId = null;
      this.removeAttribute('data-selected-id');
      this.classList.remove('product-selected');
      document.getElementById('selectedProductIndicator').style.display = 'none';
      qualityFilter.disabled = true;
      qualityFilter.innerHTML = '<option value="">All Qualities</option>';
      tableBody.innerHTML = '<tr><td colspan="15">Please select a product first</td></tr>';
      searchResults.style.display = 'none';
      return;
    }

    if (query.length < 2) {
      searchResults.style.display = 'none';
      return;
    }

    // Clear previous timeout
    clearTimeout(searchTimeout);

    // Set new timeout for debounce (300ms)
    searchTimeout = setTimeout(async function () {
      try {
        debugLog('Searching for products with query:', query);
        const response = await fetch(`/api/search-products?q=${encodeURIComponent(query)}`);
        if (!response.ok) throw new Error('Search failed');

        const products = await response.json();
        debugLog('Found products:', products.length);
        displaySearchResults(products);
      } catch (error) {
        debugLog('Search error:', error);
        searchResults.innerHTML = '<div class="error">Error searching products</div>';
        searchResults.style.display = 'block';
      }
    }, 300);
  });

  // Display search results with enhanced market information
  function displaySearchResults(products) {
    searchResults.innerHTML = '';

    if (products.length === 0) {
      searchResults.innerHTML = '<div class="no-results">No products found</div>';
      searchResults.style.display = 'block';
      return;
    }

    const ul = document.createElement('ul');
    products.forEach(product => {
      const li = document.createElement('li');
      li.setAttribute('data-product-id', product.id);
      li.setAttribute('data-market-id', product.market_id);
      li.innerHTML = `
        <div class="product-info">
          <strong>${product.name}</strong>
          <span class="th-name">${product.th_name || ''}</span>
          <small class="market-name">${product.market_name || ''}</small>
        </div>
      `;

      li.addEventListener('click', () => handleProductSelect(product));
      ul.appendChild(li);
    });

    searchResults.appendChild(ul);
    searchResults.style.display = 'block';
  }

  // Handle product selection with market auto-selection
  async function handleProductSelect(product) {
    debugLog('Selected product:', product);

    // Update search input and store product ID
    searchInput.value = product.name;
    searchInput.setAttribute('data-selected-id', product.id);

    // Set the global selectedProductId variable
    window.selectedProductId = product.id;
    selectedProductId = product.id;

    debugLog('Set selectedProductId to:', selectedProductId);

    searchResults.style.display = 'none';

    // Show the selected product indicator
    const indicator = document.getElementById('selectedProductIndicator');
    indicator.style.display = 'flex';

    // Add a selected class to the search input
    searchInput.classList.add('product-selected');

    // Auto-select the market
    if (product.market_id) {
      debugLog('Setting market to:', product.market_id);
      marketFilter.value = product.market_id;
      // Trigger change event on market filter
      marketFilter.dispatchEvent(new Event('change'));
    }

    try {
      // Fetch and update quality options
      const qualityResponse = await fetch(`/api/products/${product.id}/qualities`);
      if (!qualityResponse.ok) throw new Error('Failed to fetch qualities');

      const qualities = await qualityResponse.json();
      updateQualityOptions(qualities);
      qualityFilter.disabled = false;

      // Update table to show that a product is selected but search needs to be clicked
      tableBody.innerHTML = '<tr><td colspan="15">Product selected. Click the Search button to view data.</td></tr>';

      // Highlight the search button to indicate it should be clicked
      searchSubmitBtn.classList.add('pulse-animation');
      setTimeout(() => {
        searchSubmitBtn.classList.remove('pulse-animation');
      }, 2000);
    } catch (error) {
      debugLog('Error fetching qualities:', error);
      qualityFilter.innerHTML = '<option value="">Error loading qualities</option>';
      qualityFilter.disabled = true;
    }
  }

  // Update quality options
  function updateQualityOptions(qualities) {
    qualityFilter.innerHTML = '<option value="">All Qualities</option>';
    qualities.forEach(quality => {
      const option = document.createElement('option');
      option.value = quality.id;
      option.textContent = `${quality.standard_en_name} - ${quality.grade_en_name}`;
      qualityFilter.appendChild(option);
    });
  }

  // Handle filter changes to enable/disable dependent filters
  localFilter.addEventListener('change', function () {
    const hasValue = this.value !== '';
    deliveryTermFilter.disabled = !hasValue;
    paymentTermFilter.disabled = !hasValue;

    if (!hasValue) {
      deliveryTermFilter.innerHTML = '<option value="">All Delivery Terms</option>';
      paymentTermFilter.innerHTML = '<option value="">All Payment Terms</option>';
    }
  });

  // Handle search button click - using the global fetchFuturesData function
  searchSubmitBtn.addEventListener('click', function () {
    if (!selectedProductId && !window.selectedProductId) {
      tableBody.innerHTML = '<tr><td colspan="15">Please select a product first</td></tr>';
      searchInput.focus();
      return;
    }

    const productId = selectedProductId || window.selectedProductId;
    const days = getSelectedDays();

    // Fetch data using the global function
    fetchFuturesData(productId, days);
  });

  // Handle time filter clicks - using the global fetchFuturesData function
  timeFilterBtns.forEach(btn => {
    btn.addEventListener('click', function () {
      // Update active state
      timeFilterBtns.forEach(b => b.classList.remove('active'));
      this.classList.add('active');

      // Get the selected days value
      const days = this.getAttribute('data-days');

      // Only fetch new data if a product is selected
      const productId = window.selectedProductId;
      if (productId) {
        fetchFuturesData(productId, days);
      } else {
        // If no product is selected, show a message
        tableBody.innerHTML = '<tr><td colspan="15">Please select a product first</td></tr>';
      }
    });
  });

  // Close search results when clicking outside
  document.addEventListener('click', function (e) {
    if (!searchResults.contains(e.target) && e.target !== searchInput) {
      searchResults.style.display = 'none';
    }
  });

  // Set initial active state for time filter
  document.querySelector('.time-filter-btn[data-days="1"]').classList.add('active');
});


