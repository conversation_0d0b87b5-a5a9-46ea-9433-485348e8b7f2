/* Additional styles for invoice pages */

/* Status display styling */
.invoice-status-display.prominent {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
    text-align: center;
}

.invoice-status-display.prominent strong {
    font-size: 18px;
    margin-right: 10px;
}

.invoice-status {
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 16px;
}

/* Status colors */
.status-draft {
    background-color: #e9ecef;
    color: #495057;
}

.status-sent, .status-viewed {
    background-color: #cce5ff;
    color: #004085;
}

.status-paid {
    background-color: #d4edda;
    color: #155724;
}

.status-overdue {
    background-color: #f8d7da;
    color: #721c24;
}

.status-canceled {
    background-color: #e2e3e5;
    color: #383d41;
}

.status-disputed {
    background-color: #fff3cd;
    color: #856404;
}

/* Payment instructions and history */
.payment-instructions, .payment-history {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
}

.payment-instructions h3, .payment-history h3 {
    color: #0096FE;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 18px;
}

.payment-instructions p, .payment-history p {
    margin-bottom: 10px;
    line-height: 1.5;
    color: #333;
}

.payment-proof-upload {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f8ff;
    border: 1px solid #b8daff;
    border-radius: 5px;
}

.payment-proof-upload h4 {
    color: #0056b3;
    margin-top: 0;
    margin-bottom: 10px;
}

.payment-proof-upload .payment-instructions {
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0 0 15px 0;
    font-style: italic;
    color: #555;
}

.payment-instructions a {
    color: #0096FE;
    text-decoration: none;
}

.payment-instructions a:hover {
    text-decoration: underline;
}

.payment-history {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.fee-calculation {
    background-color: #f8f9fa;
}

.fee-note {
    padding: 8px;
    color: #666;
    font-style: italic;
}

/* Make the download button more prominent */
.download-button {
    background-color: #0096FE;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    display: inline-block;
    font-weight: bold;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s;
}

.download-button:hover {
    background-color: #0078cc;
}

/* Back button styling */
.back-button {
    display: inline-block;
    margin-top: 20px;
    color: #0096FE;
    text-decoration: none;
}

.back-button:hover {
    text-decoration: underline;
}
