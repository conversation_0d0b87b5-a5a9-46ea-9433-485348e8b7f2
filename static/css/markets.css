
    .product-overview-container {
      margin: 20px 0;
      width: 100%;
    }

    .product-header {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-bottom: 15px;
    }

    .product-market-tag {
      background-color: #96FE00;
      color: rgb(0, 0, 0);
      padding: 4px 10px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }

    .product-info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      margin-bottom: 15px;
    }

    .product-info-item {
      padding: 10px;
      background-color: rgba(52, 152, 219, 0.05);
      border-radius: 4px;
    }

    .info-label {
      font-size: 13px;
      color: #7f8c8d;
      display: block;
      margin-bottom: 5px;
    }

    .info-value {
      font-size: 15px;
      color: #2c3e50;
      font-weight: 500;
    }

    .product-description {
      margin-top: 15px;
      padding: 15px;
      border-radius: 4px;
      background-color: rgba(52, 152, 219, 0.05);
      color: #34495e;
      font-size: 14px;
      line-height: 1.5;
    }

    /* Responsive styles for smaller screens */
    @media screen and (max-width: 768px) {
      .product-info-grid {
        grid-template-columns: 1fr;
      }
    }

    .chart-container {
      position: relative;
      height: 300px;
      margin-bottom: 10px;
    }

    .chart-loading,
    .chart-no-data {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.8);
      z-index: 10;
    }

    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .time-filter {
      display: flex;
      justify-content: center;
      gap: 10px;
      margin-top: 10px;
    }

    .time-btn {
      padding: 5px 15px;
      border: 1px solid #ddd;
      background-color: #f8f8f8;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .time-btn.active {
      background-color: #3498db;
      color: white;
      border-color: #3498db;
    }
  

  /* Main dashboard styling */
  .main-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .main-dashboard h2 {
    color: #333;
    margin-bottom: 25px;
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    position: relative;
  }

  .main-dashboard h2:after {
    content: '';
    display: block;
    width: 50px;
    height: 3px;
    background: #96FE00;
    margin: 10px auto;
  }

  /* Search container base styles */
  .search-container {
    position: relative;
    margin: 20px auto 30px;
    width: 100%;
    max-width: 1200px;
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  /* Search header styles */
  .search-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
  }

  .search-header h3 {
    margin: 0 0 8px;
    color: #2c3e50;
    font-size: 20px;
  }

  .search-header p {
    margin: 0;
    color: #7f8c8d;
    font-size: 14px;
  }

  /* Search main content */
  .search-main {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* Input group for search and indicator */
  .input-group {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .input-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 14px;
  }

  .search-input-wrapper {
    position: relative;
    width: 100%;
  }

  /* Product search input */
  #productSearch {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  #productSearch:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    outline: none;
  }

  /* Style for when a product is selected */
  #productSearch.product-selected {
    border-color: #28a745;
    background-color: #f8fff8;
  }

  /* Selected product indicator */
  .selected-product-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #28a745;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }

  /* Filter section */
  .filter-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  /* Filter rows */
  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
  }

  /* Filter group */
  .filter-group {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .filter-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
  }

  /* Filter select boxes */
  .filter-select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    transition: all 0.3s ease;
  }

  /* Search action section */
  .search-action {
    display: flex;
    justify-content: flex-end;
  }

  /* Search button */
  .search-btn {
    padding: 14px 28px;
    background: #96FE00; /* Your specified green color */
    color: #333; /* Dark text for better contrast on light green */
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(150, 254, 0, 0.3);
    position: relative;
    overflow: hidden;
  }

  .search-btn:hover {
    background: #86E400; /* Slightly darker on hover */
    box-shadow: 0 4px 8px rgba(150, 254, 0, 0.4);
    transform: translateY(-1px);
  }

  /* Update time filter buttons */
  .time-filter-btn.active,
  .time-btn.active {
    background: #96FE00;
    color: #333;
    border-color: #96FE00;
    font-weight: bold;
  }

  .time-filter-btn:hover,
  .time-btn:hover {
    background: #f0f9e0;
    border-color: #96FE00;
  }

  /* Update LIVE indicator to match the theme */
  #realtime-indicator {
    background-color: #96FE00 !important;
    color: #333 !important;
    border: 1px solid rgba(150, 254, 0, 0.5) !important;
    font-weight: bold;
    padding: 4px 8px !important;
    border-radius: 4px !important;
  }

  /* Style the chart container with subtle green accents */
  .chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 10px;
    background-color: white;
    border: 1px solid #f0f9e0;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 15px;
  }

  /* Style the analytics cards with green accents */
  .analytics-card {
    border-left: 4px solid #96FE00;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .analytics-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .analytics-card h3 {
    color: #333;
    border-bottom: 1px solid #f0f9e0;
    padding-bottom: 10px;
    margin-top: 0;
  }

  /* Style the time filter buttons */
  .time-filter {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
  }

  .time-btn {
    padding: 6px 16px;
    border: 1px solid #e0e0e0;
    background-color: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
  }

  .time-btn.active {
    background-color: #96FE00;
    color: #333;
    border-color: #96FE00;
    font-weight: bold;
  }

  /* Style the product search input */
  #productSearch {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  #productSearch:focus {
    border-color: #96FE00;
    box-shadow: 0 0 0 3px rgba(150, 254, 0, 0.25);
    outline: none;
  }

  /* Style for when a product is selected */
  #productSearch.product-selected {
    border-color: #96FE00;
    background-color: #f9fff0;
  }

  /* Style the filter selects */
  .filter-select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    transition: all 0.3s ease;
  }

  .filter-select:focus {
    border-color: #96FE00;
    box-shadow: 0 0 0 2px rgba(150, 254, 0, 0.25);
    outline: none;
  }

  /* Add a subtle green background to the search container */
  .search-container {
    background: linear-gradient(to bottom, white, #f9fff0);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  }

  /* Style the search header */
  .search-header {
    border-bottom: 2px solid #f0f9e0;
  }

  .search-header h3 {
    color: #333;
    font-weight: bold;
  }

  /* Add a subtle animation to the search button */
  @keyframes gentle-pulse {
    0% { box-shadow: 0 2px 4px rgba(150, 254, 0, 0.3); }
    50% { box-shadow: 0 4px 8px rgba(150, 254, 0, 0.5); }
    100% { box-shadow: 0 2px 4px rgba(150, 254, 0, 0.3); }
  }

  .search-btn.pulse-animation {
    animation: gentle-pulse 2s infinite;
  }

  /* Time filter container */
  .time-filter-container {
    margin: 20px 0;
    display: flex;
    gap: 10px;
  }

  .time-filter-btn {
    padding: 8px 16px;
    border: 1px solid #e9ecef;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
  }

  .time-filter-btn:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
  }

  .time-filter-btn.active {
    background: #96FE00;
    color: rgb(0, 0, 0);
    border-color: #96FE00;
  }

  /* Futures table wrapper */
  .futures-table-wrapper {
    position: relative;
  }

  .futures-table-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
  }

  /* Loading and error states */
  .futures-table tr.loading td {
    text-align: center;
    padding: 2em;
    background: rgba(255, 255, 255, 0.8);
  }

  .futures-table tr.error td {
    text-align: center;
    padding: 2em;
    color: #dc3545;
    background: #fff;
  }

  /* Price changes */
  .price-up {
    color: #28a745;
  }

  .price-down {
    color: #dc3545;
  }

  /* Loading spinner */
  .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  /* Add these to your existing <style> section */
  .futures-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
  }

  .futures-table th,
  .futures-table td {
    padding: 10px;
    text-align: right;
    border: 1px solid #ddd;
    font-size: 14px;
  }

  .futures-table th {
    background-color: #f8f9fa;
    font-weight: bold;
  }

  .price-up {
    color: #28a745;
  }

  .price-down {
    color: #dc3545;
  }

  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  /* Responsive Design */
  @media screen and (max-width: 1200px) {
    .search-container {
      max-width: 95%;
      padding: 20px;
    }

    .filter-select {
      min-width: 180px;
    }
  }

  @media screen and (max-width: 992px) {
    .filter-row {
      gap: 12px;
    }

    .filter-select {
      min-width: calc(33.333% - 12px);
      flex: 0 0 calc(33.333% - 12px);
    }

    #productSearch {
      padding: 12px 16px;
      font-size: 15px;
    }
  }

  @media screen and (max-width: 768px) {
    .search-container {
      padding: 15px;
      margin: 15px auto;
    }

    .filter-select {
      min-width: calc(50% - 8px);
      flex: 0 0 calc(50% - 8px);
      padding: 10px;
      font-size: 13px;
    }

    .advanced-search {
      padding: 15px;
      margin-top: 12px;
    }

    .search-btn {
      padding: 12px;
      font-size: 14px;
    }
  }

  @media screen and (max-width: 480px) {
    .search-container {
      padding: 12px;
      margin: 10px auto;
    }

    .filter-select {
      min-width: 100%;
      flex: 0 0 100%;
      margin-bottom: 8px;
    }

    #productSearch {
      padding: 10px;
      font-size: 14px;
      margin-bottom: 10px;
    }

    .advanced-search {
      padding: 12px;
      margin-top: 10px;
    }

    .filter-row {
      gap: 8px;
      margin-bottom: 8px;
    }

    .search-btn {
      padding: 10px;
      font-size: 13px;
    }
  }

  /* Hover and focus states */
  .filter-select:hover:not(:disabled) {
    border-color: #6ee663;
  }

  .filter-select:focus:not(:disabled) {
    outline: none;
    border-color: #42b635;
    box-shadow: 0 0 0 2px rgba(111, 235, 130, 0.25);
  }

  .search-btn:hover {
    background: #42b635;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
  }

  .search-btn:active {
    background: #59c464;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    transform: translateY(1px);
  }

  /* Disabled state */
  .filter-select:disabled {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.8;
  }

  /* Search results dropdown */
  .search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    margin-top: 5px;
    display: none;
  }

  .search-results ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .search-results li {
    padding: 12px 15px;
    border-bottom: 1px solid #f1f1f1;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .search-results li:last-child {
    border-bottom: none;
  }

  .search-results li:hover {
    background-color: #f8f9fa;
  }

  .search-results .product-info {
    display: flex;
    flex-direction: column;
  }

  .search-results .product-info strong {
    color: #2c3e50;
    margin-bottom: 2px;
  }

  .search-results .product-info .th-name {
    color: #7f8c8d;
    font-size: 12px;
    margin-bottom: 2px;
  }

  .search-results .product-info .market-name {
    color: #3498db;
    font-size: 12px;
  }

  .search-results .no-results,
  .search-results .error {
    padding: 15px;
    text-align: center;
    color: #7f8c8d;
  }

  .search-results .error {
    color: #e74c3c;
  }
