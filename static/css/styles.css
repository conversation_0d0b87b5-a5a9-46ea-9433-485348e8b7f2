/* Color variables */
:root {
    --seller-color: #FE9600;     /* Producer (Seller) */
    --buyer-color: #96FE00;      /* Consumer (Buyer) */
    --service-color: #1ab3f7;    /* Service Provider */
    --text-color: #ffffff;       /* Text color */
    --footer-color: #0096FE;     /* Footer color */
}

/* General body styling */
body {
    background-color: #ffffff;
    font-family: 'Verdana', 'Geneva', sans-serif;
    color: #333;
    margin: 0px; /*0*/
    padding: 0px; /*0*/
    min-height: 100vh; /* Ensures content takes full height of the viewport */
    display: flex;
    flex-direction: column;
}

/* Header styling */
header {
    background-color: var(--seller-color);
    color: white;
    padding: 20px -20px; /*20px 0px*/
    margin-top: -10px; /*0px*/
    text-align: center;
    font-size: 24px;
    font-weight: bold;
}

/* Common Navigation bar styling */
nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: var(--buyer-color);
    text-align: center;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    height: 65px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
}

/* Index page navigation */
.index-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: var(--buyer-color);
    text-align: center;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    height: 65px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
}

/* Dashboard page navigation */
.dash-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: var(--buyer-color);
    text-align: center;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    height: 65px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
}

/* Common container for navigation elements */
.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    width: 100%;
    padding: 0 15px;
    position: relative;
    box-sizing: border-box;
    overflow: hidden; /* Changed from overflow-x to overflow */
}

/* Index page container for navigation elements */
.index-nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    width: 100%;
    padding: 0 15px;
    position: relative;
    box-sizing: border-box;
    overflow: hidden; /* Changed from overflow-x to overflow */
}

/* Dashboard page container for navigation elements */
.dash-nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    width: 100%;
    padding: 0 15px;
    position: relative;
    box-sizing: border-box;
    overflow: hidden; /* Changed from overflow-x to overflow */
}

/* Add language selector to desktop nav */
.desktop-lang-selector {
    display: none; /* Hide by default on all pages */
    margin-left: 20px;
    margin-right: 10px;
}

/* Only show language selector on index page */
.index-lang-selector {
    display: block; /* Show only on index page */
}

.desktop-lang-selector select {
    padding: 5px 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: white;
    font-size: 14px;
    cursor: pointer;
}

/**/
nav a {
    color: #333;
    margin: 0 15px; /*0 15px*/
    text-decoration: none;
    font-size: 18px;
}


/* Hover styles for navigation links */
nav a:hover {
    color: #ffffff; /* White text on hover */
}

/* Hamburger menu icon */
.hamburger {
    display: none;
    cursor: pointer;
    z-index: 1001;
    background-color: transparent;
    border: none;
    outline: none;
    padding: 6px; /* Reduced from 10px to make the button smaller */
    margin-right: 15px; /* Increased right margin for better spacing */
}

.hamburger span {
    display: block;
    width: 35px; /* Reduced from 35px to make it smaller */
    height: 4px; /* Reduced from 4px to make it smaller */
    margin: 5px -7px; /* Reduced from 7px to make it more compact */
    background-color: var(--text-color);
    transition: all 0.3s ease;
    border-radius: 2px;
}

.hamburger.active {
    transform: rotate(0deg);
}

/* Common Navigation links container */
.nav-links {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    min-width: auto; /* Changed from 700px to auto */
    max-width: 100%; /* Added max-width */
    flex-wrap: nowrap;
}

/* Index page Navigation links container */
.index-nav-links {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    min-width: auto; /* Changed from 700px to auto */
    max-width: 100%; /* Added max-width */
    flex-wrap: nowrap;
}

/* Dashboard page Navigation links container */
.dash-nav-links {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    min-width: auto; /* Changed from 700px to auto */
    max-width: 100%; /* Added max-width */
    flex-wrap: nowrap;
}

/* Common navigation links */
.nav-links a {
    margin: 0 12px;
    font-size: 20px;
    text-decoration: none;
    color: #333;
    transition: color 0.3s ease;
    padding: 5px 0;
    font-weight: 500;
    white-space: nowrap;
}

/* Index page navigation links */
.index-nav-links a {
    margin: 0 12px;
    font-size: 20px;
    text-decoration: none;
    color: #333;
    transition: color 0.3s ease;
    padding: 5px 0;
    font-weight: 500;
    white-space: nowrap;
}

/* Dashboard page navigation links */
.dash-nav-links a {
    margin: 0 12px;
    font-size: 20px;
    text-decoration: none;
    color: #333;
    transition: color 0.3s ease;
    padding: 5px 0;
    font-weight: 500;
    white-space: nowrap;
}

/* Dashboard page navigation links hover */
.dash-nav-links a:hover,
.dash-nav-links .dropdown-trigger:hover {
    color: #ffffff !important; /* White text on hover */
}

/* Hide language selector in desktop view */
.language-selector {
    display: none; /* Hide by default on all pages */
}

/* Only show mobile language selector on index page */
.index-page .language-selector {
    display: block; /* Show only on index page */
}

/* Responsive styles */

@media screen and (max-width: 768px) {
    /* Common styles for all navigation types */
    nav, .index-nav, .dash-nav {
        padding: 0 15px;
        height: 65px;
        justify-content: space-between; /* Use space-between in mobile view */
    }

    .desktop-lang-selector {
        display: none;
    }

    /* Common brand styles for mobile */
    .nav-brand,
    .index-nav-brand,
    .dash-nav-brand {
        font-size: 15px;
        margin: 0 0 0 15px; /* Add left margin for better spacing */
    }

    .hamburger {
        display: block !important;
        margin-right: 20px;
        position: relative;
    }

    /* Special margin for order page */
    .order-page .hamburger {
        margin-right: 50px !important;
    }

    /* Common mobile styles */
    .nav-links {
        position: fixed;
        top: 65px;
        left: 0;
        width: 100%;
        background-color: var(--buyer-color);
        flex-direction: column;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        padding: 0;
        z-index: 999;
        display: none;
        margin: 0;
        transform: none;
        max-height: calc(100vh - 65px);
        overflow-y: auto;
    }

    /* Index page mobile styles */
    .index-nav-links {
        position: fixed;
        top: 65px;
        left: 0;
        width: 100%;
        background-color: var(--buyer-color);
        flex-direction: column;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        padding: 0;
        z-index: 999;
        display: none;
        margin: 0;
        transform: none;
        max-height: calc(100vh - 65px);
        overflow-y: auto;
    }

    /* Ensure dropdown containers are properly displayed in mobile view */
    .index-nav-links .dropdown-container {
        width: 100%;
        display: block;
        text-align: center;
        margin: 0 auto;
    }

    /* Ensure all links are centered in mobile view */
    .index-nav-links a,
    .index-nav-links .dropdown-trigger,
    .index-nav-links .dropdown-content a {
        margin: 0 auto;
        width: 100%;
        box-sizing: border-box;
    }

    /* Dashboard page mobile styles */
    .dash-nav-links {
        position: fixed;
        top: 65px;
        left: 0;
        width: 100%;
        background-color: var(--buyer-color);
        flex-direction: column;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        padding: 0;
        z-index: 999;
        display: none;
        margin: 0;
        transform: none;
        max-height: calc(100vh - 65px);
        overflow-y: auto;
    }

    /* Ensure dropdown containers are properly displayed in mobile view */
    .dash-nav-links .dropdown-container {
        width: 100%;
        display: block;
    }

    /* Active states for all navigation types */
    .nav-links.active,
    .index-nav-links.active,
    .dash-nav-links.active {
        display: flex;
        animation: slideDown 0.3s ease forwards;
    }

    @keyframes slideDown {
        0% {
            opacity: 0;
            transform: translateY(-10px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Common link styles for all navigation types */
    .nav-links a,
    .index-nav-links a,
    .dash-nav-links a {
        display: block;
        margin: 0 auto;
        padding: 18px;
        width: 100%;
        border-bottom: 1px solid rgba(0,0,0,0.1);
        font-size: 20px;
        transition: background-color 0.3s ease, color 0.3s ease;
        font-weight: 500;
        text-align: center;
        box-sizing: border-box;
    }

    /* Common hover styles for all navigation types */
    .nav-links a:hover,
    .index-nav-links a:hover,
    .dash-nav-links a:hover {
        background-color: rgba(239, 150, 0, 0.2);
        color: #ffffff; /* White text on hover */
        transform: scale(1.02);
        transition: all 0.2s ease;
    }

    /* Last item in each navigation type */
    .nav-links a:last-of-type,
    .index-nav-links a:last-of-type,
    .dash-nav-links a:last-of-type {
        border-bottom: none;
    }

    .language-selector {
        display: block; /* Show by default in mobile view */
        padding: 10px;
        border-top: 1px solid rgba(0,0,0,0.1);
        margin-top: 0;
        width: 100%;
        background-color: var(--buyer-color); /* Match the green background */
        order: 999; /* Make it appear at the bottom of the flex container */
        text-align: center;
        box-sizing: border-box;
    }

    .language-selector form {
        margin: 0 auto;
        padding: 0;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .language-selector label {
        display: block;
        margin-bottom: 5px;
        font-weight: normal;
        font-size: 16px;
        color: #333;
        text-align: center;
        padding: 0;
        width: auto;
    }

    .language-selector select {
        width: 80%;
        padding: 8px;
        border-radius: 0;
        border: 1px solid #ccc;
        background-color: white;
        font-size: 16px;
        cursor: pointer;
        box-shadow: none;
        margin: 0 auto;
        height: 40px; /* Fixed height to ensure consistency */
        text-align: center;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        text-indent: 0;
    }
    /* Fix for Products text alignment in dashboard page */
    .dash-nav-links .dropdown-content a {
        text-align: center !important;
        margin: 0 auto !important;
        width: 100% !important;
        box-sizing: border-box !important;
        position: relative !important;
        left: 0 !important;
        right: 0 !important;
        float: none !important;
        display: block !important;
    }

    /* Ensure dropdown content is properly displayed */
    .dash-nav-links.active .dropdown-content {
        width: 40% !important;
        margin-left: 250px;
        box-sizing: border-box !important;
    }
}

/* Media query for Surface Duo and similar folding/tablet devices (540-600px width) */
@media screen and (min-width: 540px) and (max-width: 600px),
       screen and (min-width: 720px) and (max-width: 740px) and (max-height: 540px) {
    /* Adjust link styles for all navigation types */
    .nav-links a,
    .index-nav-links a,
    .dash-nav-links a {
        font-size: 16px;
        padding: 15px;
        margin-left: 0;
        width: 100%;
        box-sizing: border-box;
    }

    /* Ensure dropdown triggers are properly centered */
    .dropdown-trigger {
        text-align: center;
        margin: 0 auto;
        width: 100%;
        padding: 15px;
    }

    /* Ensure dropdown content links are properly centered */
    .dropdown-content a {
        text-align: center;
        margin: 0 auto;
        width: 100%;
        padding: 12px;
    }

    /* Adjust language selector for Surface Duo */
    .language-selector {
        padding: 10px;
        text-align: center;
        width: 100%;
    }

    .language-selector label {
        font-size: 15px;
        padding: 0;
        margin: 0 auto 5px auto;
        text-align: center;
        width: auto;
    }

    .language-selector select {
        width: 80%;
        margin: 0 auto;
        font-size: 15px;
        height: 35px;
        border-radius: 0;
        text-align: center;
    }

    .language-selector form {
        width: 100%;
        margin-left: 0;
    }

    /* Adjust hamburger menu position */
    .hamburger {
        display: block !important;
        margin-right: 20px;
        position: relative;
    }

    /* Ensure index-nav-links are properly displayed */
    .index-nav-links.active {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    /* Ensure dropdown containers are properly centered */
    .index-nav-links .dropdown-container,
    .dash-nav-links .dropdown-container {
        width: 100%;
        display: block;
        text-align: center;
        margin: 0 auto;
    }
    /* Fix for Products text alignment in dashboard page */
    .dash-nav-links .dropdown-content a {
        text-align: center !important;
        margin: 0 auto !important;
        width: 100% !important;
        box-sizing: border-box !important;
        position: relative !important;
        left: 0 !important;
        right: 0 !important;
        float: none !important;
        display: block !important;
    }

    /* Ensure dropdown content is properly displayed */
    .dash-nav-links.active .dropdown-content {
        width: 50% !important;
        margin-left: 140px !important;
        box-sizing: border-box !important;
    }

    /* Login and Register form styles for tablet devices */
    .loginform-group, .registerform-group {
        margin-bottom: 15px;
    }

    /* Enhanced styles for login and register forms on Surface Duo and similar devices */
    .main-dashboard form, .content form {
        padding: 0 15px;
        margin: 0 auto;
        max-width: 500px;
    }

    .main-dashboard form h2, .content form h2 {
        text-align: left; /* Left-align the title */
        margin-bottom: 20px;
        margin-left: 5px; /* Add a small left margin */
    }

    .loginform-group{
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start;
        margin-left: -20px;
    }
    .registerform-group {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start;
        margin-left: -70px;
    }

    .loginform-group label, .registerform-group label {
        width: 25%;
        text-align: right;
        margin-right: 15px;
        font-size: 15px;
    }

    .loginform-group input, .registerform-group input {
        width: 65%;
        max-width: 300px;
    }

    .main-dashboard form button, .content form button {
        margin: 15px auto;
        display: block;
        width: 50%;
        max-width: 180px;
    }

    .main-dashboard form p, .content form p {
        text-align: center;
        margin: 10px 0;
    }
}

/* Media query for screens between 480px and 540px */
@media screen and (min-width: 481px) and (max-width: 539px) {
    /* Adjust link styles for all navigation types */
    .nav-links a,
    .index-nav-links a,
    .dash-nav-links a {
        font-size: 16px;
        padding: 15px;
        margin-left: 0;
        width: 100%;
        box-sizing: border-box;
    }

    /* Login and register form styles for this screen size */
    .main-dashboard form, .content form {
        padding: 0 15px;
        margin: 0 auto;
        max-width: 450px;
    }

    .main-dashboard form h2, .content form h2 {
        text-align: left; /* Left-align the title */
        margin-bottom: 20px;
        font-size: 22px;
        margin-left: 5px; /* Add a small left margin */
    }

    .loginform-group, .registerform-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start; /* Align to the left */
    }

    .loginform-group label, .registerform-group label {
        width: 35%; /* Fixed width for labels */
        text-align: right;
        margin-right: 15px;
        font-size: 15px;
    }

    .loginform-group input, .registerform-group input {
        width: 60%; /* Narrower inputs */
        max-width: 180px; /* Limit maximum width */
    }

    .main-dashboard form button, .content form button {
        margin: 15px auto;
        display: block;
        width: 60%;
        max-width: 180px;
    }

    .main-dashboard form p, .content form p {
        text-align: left; /* Left-align text and links */
        margin: 10px 0;
    }

    /* Submit order page specific styles for this screen size */
    .order-page .form-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start; /* Align to the left */
        width: 100%;
    }

    .order-page label {
        width: 30%; /* Fixed width for labels */
        text-align: left; /* Left-align the labels */
        margin-right: 15px;
        font-size: 15px;
        white-space: nowrap; /* Prevent label text from wrapping */
    }

    .order-page input,
    .order-page select {
        width: 65%; /* Wider inputs */
        max-width: none; /* Remove max-width limitation */
        margin-right: 0; /* Remove right margin */
    }

    .order-page button[type="button"] {
        width: 100%; /* Full width button */
        padding: 12px;
        font-size: 16px;
        margin: 15px 0; /* Align button to the left */
        border-radius: 4px;
        max-width: none; /* Remove max-width limitation */
    }

    .order-page h2 {
        font-size: 22px;
        text-align: left; /* Left-align the title */
        margin-bottom: 20px;
        margin-left: 5px; /* Add a small left margin */
    }

    /* Ensure dropdown triggers are properly centered */
    .dropdown-trigger {
        text-align: center;
        margin: 0 auto;
        width: 100%;
        padding: 15px;
    }

    /* Ensure dropdown content links are properly centered */
    .dropdown-content a {
        text-align: center;
        margin: 0 auto;
        width: 100%;
        padding: 12px;
    }

    /* Adjust language selector for this screen size */
    .language-selector {
        padding: 10px;
        text-align: center;
        width: 100%;
    }

    .language-selector label {
        font-size: 15px;
        padding: 0;
        margin: 0 auto 5px auto;
        text-align: center;
        width: auto;
    }

    .language-selector select {
        width: 70%;
        margin: 0 auto;
        font-size: 15px;
        height: 35px;
        border-radius: 0;
        text-align: center;
    }

    .language-selector form {
        width: 100%;
        margin-left: 0;
    }

    /* Adjust hamburger menu position */
    .hamburger {
        display: block !important;
        margin-right: 20px;
        position: relative;
    }

    /* Ensure index-nav-links are properly displayed */
    .index-nav-links.active {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    /* Ensure dropdown containers are properly centered */
    .index-nav-links .dropdown-container,
    .dash-nav-links .dropdown-container {
        width: 100%;
        display: block;
        text-align: center;
        margin: 0 auto;
    }
        /* Fix for Products text alignment in dashboard page */
    .dash-nav-links .dropdown-content a {
        text-align: center !important;
        margin: 0 auto !important;
        width: 100% !important;
        box-sizing: border-box !important;
        position: relative !important;
        left: 0 !important;
        right: 0 !important;
        float: none !important;
        display: block !important;
    }

    /* Ensure dropdown content is properly displayed */
    .dash-nav-links.active .dropdown-content {
        width: 50% !important;
        margin-left: 130px !important;
        box-sizing: border-box !important;
    }
}

/* Additional media queries for smaller screens */
@media screen and (max-width: 480px) {
    /* Adjust link styles for all navigation types */
    .nav-links a,
    .index-nav-links a,
    .dash-nav-links a {
        font-size: 15px;
        padding: 15px;
        margin-left: 0;
        width: 100%;
    }

    /* Adjust spacing for login and register pages */
    .main-dashboard, .content {
        padding: 15px 10px;
    }

    .language-selector label {
        font-size: 15px;
        margin: 0 auto 5px auto;
        padding: 0;
        text-align: center;
        width: auto;
    }

    /* Common brand styles for smaller screens */
    .nav-brand,
    .index-nav-brand,
    .dash-nav-brand {
        font-size: 14px;
    }

    .language-selector select {
        width: 90%;
        padding: 8px;
        border-radius: 0;
        border: 1px solid #ccc;
        background-color: white;
        font-size: 14px;
        cursor: pointer;
        box-shadow: none;
        margin: 0 auto;
        height: 30px;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        text-align: center;
        text-indent: 0;
    }

    .language-selector form {
        width: 100%;
        margin-left: 0;
    }
    .hamburger {
        display: block !important;
        margin-right: 20px;
        position: relative;
    }
    /* Ensure dropdown content is properly displayed */
    .dash-nav-links.active .dropdown-content {
        width: 50% !important;
        margin-left: 110px !important;
        box-sizing: border-box !important;
    }
}

/* Specific styling for iPhone 12 Pro and similar mobile phones (390px width) */
@media screen and (max-width: 390px) {
    /* Adjust link styles for all navigation types */
    .nav-links a,
    .index-nav-links a,
    .dash-nav-links a {
        font-size: 16px;
        padding: 12px;
        margin-left: 0;
        width: 100%;
        box-sizing: border-box;
    }

    /* Ensure dropdown triggers are properly centered */
    .dropdown-trigger {
        text-align: center;
        margin: 0 auto;
        width: 100%;
    }

    /* Ensure dropdown content links are properly centered */
    .dropdown-content a {
        text-align: center;
        margin: 0 auto;
        width: 100%;
    }

    /* Adjust padding for language selector in very small screens */
    .language-selector {
        padding: 10px;
        text-align: center;
    }

    .language-selector label {
        font-size: 14px;
        padding: 0;
        margin: 0 auto 5px auto;
        text-align: center;
        width: auto;
    }

    .language-selector select {
        width: 100%;
        margin-left: 25px;
        font-size: 14px;
        height: 10px;
        border-radius: 0;
        text-align: center;
    }

    .language-selector form {
        width: 90%;
        margin-left: 0;
    }
    .hamburger {
        display: block !important;
        margin-right: 20px;
        position: relative;
    }
    .dash-nav-links.active .dropdown-content {
        width: 50% !important;
        margin-left: 100px !important;
        box-sizing: border-box !important;
    }

    /* Submit order page specific styles for 360-390px */
    .order-page .form-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start; /* Align to the left */
        width: 100%;
    }

    .order-page label {
        width: 30%; /* Fixed width for labels */
        text-align: left; /* Left-align the labels */
        margin-right: 15px;
        font-size: 13px;
        white-space: nowrap; /* Prevent label text from wrapping */
    }

    .order-page input,
    .order-page select {
        width: 65%; /* Wider inputs */
        max-width: none; /* Remove max-width limitation */
        margin-right: 0; /* Remove right margin */
        font-size: 13px;
    }

    .order-page button[type="button"] {
        width: 100%; /* Full width button */
        padding: 10px;
        font-size: 15px;
        margin: 15px 0; /* Align button to the left */
        border-radius: 4px;
    }

    .order-page h2 {
        font-size: 20px;
        text-align: left; /* Left-align the title */
        margin-bottom: 15px;
        margin-left: 5px; /* Add a small left margin */
    }
}

/* Very small screens */
@media screen and (max-width: 320px) {
    /* Adjust link styles for all navigation types */
    .nav-links a,
    .index-nav-links a,
    .dash-nav-links a {
        font-size: 16px;
        padding: 12px;
        align-items: center;
    }

    /* Adjust padding for language selector in very small screens */
    .language-selector {
        padding: 10px;
        text-align: center;
    }

    .language-selector label {
        font-size: 14px;
        padding: 0;
        margin: 0 auto 5px auto;
        text-align: center;
        width: auto;
    }

    .language-selector select {
        width: 80%;
        margin: 0 auto;
        font-size: 14px;
        height: 30px;
        border-radius: 0;
        text-align: center;
    }

    .language-selector form {
        width: 100%;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .hamburger span {
        width: 22px;
        height: 3px;
    }
    .dash-nav-links.active .dropdown-content {
        width: 45% !important;
        margin-left: 5% !important;
        box-sizing: border-box !important;
    }

    /* Login and Register form styles for very small screens */
    .loginform-group, .registerform-group {
        margin-bottom: 10px;
    }

    /* Enhanced login and register form styles for very small screens */
    .main-dashboard form, .content form {
        padding: 0 10px;
    }

    .main-dashboard form h2, .content form h2 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .loginform-group, .registerform-group {
        margin-bottom: 12px;
    }

    .loginform-group label, .registerform-group label {
        font-size: 14px;
    }

    .loginform-group input, .registerform-group input {
        max-width: 200px;
        height: 34px;
        font-size: 14px;
    }

    .main-dashboard form button, .content form button {
        width: 100%;
        max-width: 180px;
        font-size: 15px;
        padding: 8px 15px;
    }

    .main-dashboard form p, .content form p {
        font-size: 14px;
    }
}

    /* Adjust main content for mobile */
    main {
        margin-top: 80px;
        padding: 0 20px;
    }

    /* Adjust footer for mobile */
    footer {
        padding: 5px 0;
        height: auto;
        min-height: 80px;
    }

    footer a {
        display: inline-block;
        margin: 5px;
    }

    /* Adjust grid for mobile */
    .grid {
        grid-template-columns: 1fr;
    }

    /* Make tables responsive on mobile */
    table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
        font-size: 14px;
        width: 100%;
        border: none;
        box-shadow: none;
    }

    /* Adjust table cells for mobile */
    table th, table td {
        padding: 8px 5px;
        font-size: 13px;
    }

    /* Specific styling for matched orders page on mobile */
    .main-dashboard h3 {
        font-size: 18px;
        text-align: left; /* Left-align all dashboard titles */
        margin-bottom: 15px;
        margin-left: 5px; /* Add a small left margin */
    }

    .main-dashboard .container {
        padding: 0;
        margin: 0;
        width: 100%;
    }

    /* Adjust specific tables for mobile */
    #transactions-table, #transactions-table-global {
        font-size: 12px;
    }

    /* Adjust card layout for mobile */
    .card {
        padding: 15px 10px;
    }

    /* Adjust font sizes for mobile */
    h2 {
        font-size: 18px;
    }

    /* Adjust main logo for mobile */
    .main-logo {
        padding: 30px 0;
    }

    /* Adjust CO2 stats for mobile */
    .co2-stats {
        flex-direction: column;
    }

    .co2-stat {
        margin-bottom: 15px;
    }

    /* Hamburger menu animation */
    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
        background-color: white; /* Change color to white when active */
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(5px, -5px);
        background-color: white; /* Change color to white when active */
    }


/* Main content styling */
main {
    margin-top: 0; /* Remove top margin as it's handled by main-logo */
    padding: 20px 50px;
    background-color: white;
    flex: 1; /* Ensures the main content area grows to fill remaining space */
    box-sizing: border-box;
}

/* Specific styling for login and register pages */
.main-dashboard, .content {
    margin-top: 80px; /* Proper spacing below navigation bar */
    padding: 20px;
    box-sizing: border-box;
}

/* Dashboard title styling */
.main-dashboard h2 {
    text-align: left; /* Left-align all dashboard titles */
    margin-bottom: 20px;
    color: #333;
    font-weight: 600;
}

/* Enhanced table styling for matched orders page */
table {
    border-collapse: collapse;
    margin: 20px 0;
    box-shadow: 0 2px 3px rgba(0,0,0,0.1);
    border-radius: 4px;
    background-color: #fff;
    width: 100%;
}

/* Table header styling */


table th {
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    color: black; /* White text for better contrast */
    border-bottom: 2px solid #ddd;
    position: sticky;
    top: 0;
}

/* Table cell styling */
table td {
    padding: 10px 8px;
    border-bottom: 1px solid #eee;
    color: #333;
}

/* Table row hover effect */
table tbody tr:hover {
    background-color: rgba(0, 150, 254, 0.1); /* Light blue background on hover */
}

/* Alternating row colors for better readability */
table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* Last row styling */
table tbody tr:last-child td {
    border-bottom: none;
}

/* View button styling in tables */
table .btn {
    display: inline-block;
    padding: 6px 12px;
    background-color: var(--buyer-color);
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;
}

table .btn:hover {
    background-color: var(--footer-color);
    color: var(--text-color);
}

/* Container styling for matched orders page */
.main-dashboard .container {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    padding: 10px 0;
}

/* Specific styling for matched orders tables */
.main-dashboard h3 {
    margin-top: 25px;
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
    text-align: left; /* Left-align all dashboard titles */
}

/* Responsive styles for matched orders page */
@media screen and (min-width: 769px) and (max-width: 1200px) {
    .main-dashboard .container {
        padding: 0;
    }

    table th, table td {
        padding: 10px 6px;
        font-size: 14px;
    }
}

/* Contract detail page styling */
.contract-title {
    font-size: 24px;
    margin-bottom: 10px;
    color: #333;
    text-align: left; /* Left-align contract title */
}

.contract-date {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
    text-align: left; /* Left-align contract date */
}

.contract-section {
    margin-bottom: 25px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.contract-section h2 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
}

.contract-section p {
    margin-bottom: 8px;
    line-height: 1.5;
}

.button-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.sign-button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    background-color: var(--buyer-color);
    color: #333;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sign-button:hover {
    background-color: var(--footer-color);
    color: var(--text-color);
}

.reject-button {
    background-color: #f44336;
}

.reject-button:hover {
    background-color: #d32f2f;
}

.download-btn {
    display: inline-block;
    text-decoration: none;
    text-align: center;
}

/* Invoice and Order Book filter styling is now unified */
.order-filter-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
}

.order-filter-label {
    margin-right: 10px;
    font-weight: bold;
    color: #333;
    white-space: nowrap;
}

.order-filter-select {
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: white;
    font-size: 14px;
    min-width: 150px;
    width: 100%;
}

/* Filter buttons styling */
.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.filter-button {
    padding: 8px 16px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-button:hover {
    background-color: rgba(0, 150, 254, 0.1);
}

.filter-button.active {
    background-color: var(--footer-color);
    color: var(--text-color);
    border-color: var(--footer-color);
}

/* Invoice container styling - matched with order book container */
.invoice-container {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    box-sizing: border-box;
    display: block;
}

/* Invoice table styling - matched with order book table */
.invoice-list {
    width: 100%;
    min-width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    box-shadow: 0 2px 3px rgba(0,0,0,0.1);
    border-radius: 4px;
    background-color: #fff;
    table-layout: auto;
}

.invoice-list th {
    padding: 12px 15px;
    text-align: left;
    font-weight: bold;
    color: black;
    border-bottom: 2px solid #ddd;
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    height: 60px;
    vertical-align: middle;
    box-sizing: border-box;
    line-height: 1.5;
}

.invoice-list td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    color: #333;
    height: 60px;
    vertical-align: middle;
    box-sizing: border-box;
    line-height: 1.5;
}

.invoice-list tr {
    height: 60px; /* Ensure consistent row height */
}

.invoice-list tbody tr:hover {
    background-color: #f5f5f5;
}

/* Alternating row colors for better readability */
.invoice-list tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* Last row styling */
.invoice-list tbody tr:last-child td {
    border-bottom: none;
}

/* No invoices message styling */
.no-invoices {
    padding: 20px;
    text-align: center;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-top: 20px;
}

.no-invoices p {
    color: #666;
    font-size: 16px;
}

/* Invoice detail page styling */
.invoice-detail {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 30px;
    color: #333;
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.invoice-title {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.invoice-number {
    font-size: 16px;
    color: #555;
}

.invoice-dates {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
}

.invoice-date, .due-date {
    flex: 1;
}

.invoice-parties {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
}

.seller-info, .buyer-info {
    flex: 1;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.seller-info h3, .buyer-info h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
    font-size: 16px;
}

.invoice-items {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 25px;
}

.invoice-items th {
    background-color: #f5f5f5;
    padding: 12px 10px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
    color: #333;
}

.invoice-items td {
    padding: 12px 10px;
    border-bottom: 1px solid #eee;
    color: #333;
}

.invoice-total {
    text-align: right;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 25px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.invoice-status-display {
    margin-bottom: 25px;
}

.invoice-actions {
    margin-bottom: 15px;
}

.pay-button, .dispute-button, .cancel-button {
    padding: 8px 16px;
    margin-right: 10px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
}

.pay-button {
    background-color: var(--buyer-color);
    color: #333;
}

.pay-button:hover {
    background-color: var(--footer-color);
    color: var(--text-color);
}

.dispute-button {
    background-color: #ffb74d;
    color: #333;
}

.dispute-button:hover {
    background-color: #ff9800;
    color: #333;
}

.cancel-button {
    background-color: #ef5350;
    color: white;
}

.cancel-button:hover {
    background-color: #e53935;
    color: white;
}

.back-button {
    display: inline-block;
    padding: 8px 16px;
    background-color: #757575;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.back-button:hover {
    background-color: #616161;
}

.invoice-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.status-draft {
    background-color: #e0e0e0;
    color: #333;
}

.status-sent {
    background-color: #bbdefb;
    color: #0d47a1;
}

.status-viewed {
    background-color: #c8e6c9;
    color: #1b5e20;
}

.status-paid {
    background-color: #a5d6a7;
    color: #1b5e20;
}

.status-overdue {
    background-color: #ffcdd2;
    color: #b71c1c;
}

.status-canceled {
    background-color: #d7ccc8;
    color: #3e2723;
}

.status-disputed {
    background-color: #ffe0b2;
    color: #e65100;
}

.action-button {
    display: inline-block;
    padding: 6px 12px;
    margin-right: 5px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.view-button {
    background-color: var(--buyer-color);
    color: #333;
}

.view-button:hover {
    background-color: var(--footer-color);
    color: var(--text-color);
}

.download-button {
    background-color: var(--buyer-color);
    color: #333;
}

.download-button:hover {
    background-color: var(--footer-color);
    color: var(--text-color);
}

.view-all-link {
    margin-top: 15px;
    text-align: right;
}

.view-all-link a {
    color: var(--footer-color);
    text-decoration: none;
    font-weight: 600;
}

.view-all-link a:hover {
    text-decoration: underline;
}

/* Responsive styles for contract detail page */
@media screen and (max-width: 768px) {
    .contract-title {
        font-size: 20px;
        text-align: left; /* Left-align contract title */
    }

    .contract-date {
        font-size: 14px;
        text-align: left; /* Left-align contract date */
    }

    .contract-section {
        padding: 12px;
        margin-bottom: 15px;
    }

    .contract-section h2 {
        font-size: 18px;
    }

    .button-stack {
        flex-direction: column;
        gap: 8px;
    }

    .sign-button {
        width: 100%;
        padding: 12px;
    }

    /* Responsive styles for invoice page */
    .page-header h1 {
        font-size: 24px;
    }

    .order-filter-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .order-filter-label {
        margin-bottom: 8px;
    }

    .order-filter-select {
        width: 100%;
    }

    .filter-buttons {
        width: 100%;
        justify-content: flex-start;
    }

    .filter-button {
        font-size: 13px;
        padding: 6px 12px;
        flex-grow: 1;
        text-align: center;
    }

    .invoice-list th,
    .invoice-list td {
        padding: 10px 8px;
        font-size: 14px;
    }

    /* Make invoice container full width on mobile */
    .invoice-container {
        width: 100%;
        margin: 0;
        padding: 0;
    }

    /* Make invoice table full width on mobile */
    .invoice-list {
        width: 100%;
        margin: 10px 0;
    }

    .action-button {
        padding: 6px;
        font-size: 11px;
        margin-right: 2px;
    }
}

/* Additional responsive styles for very small screens */
@media screen and (max-width: 480px) {
    .invoice-list th:nth-child(5), /* Related To */
    .invoice-list td:nth-child(5) {
        display: none;
    }

    .invoice-list th,
    .invoice-list td {
        padding: 8px 6px;
        font-size: 13px;
    }

    .invoice-status {
        padding: 3px 6px;
        font-size: 11px;
    }

    .action-button {
        display: block;
        margin-bottom: 5px;
        text-align: center;
    }

    /* Invoice detail responsive styling */
    .invoice-detail {
        padding: 15px;
    }

    .invoice-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .invoice-title {
        margin-bottom: 10px;
    }

    .invoice-dates {
        flex-direction: column;
    }

    .invoice-date {
        margin-bottom: 10px;
    }

    .invoice-parties {
        flex-direction: column;
    }

    .seller-info {
        margin-bottom: 15px;
    }

    .invoice-items th:nth-child(2), /* Quantity */
    .invoice-items td:nth-child(2) {
        display: none;
    }

    .invoice-items th,
    .invoice-items td {
        padding: 8px 6px;
        font-size: 13px;
    }

    .pay-button,
    .dispute-button,
    .cancel-button {
        display: block;
        width: 100%;
        margin-bottom: 10px;
        margin-right: 0;
    }
}


h1 {
    color: #333;
}

h4 {
    color: whitesmoke;
}

/* Page header styling */
.page-header {
    margin-bottom: 25px;
}

.page-header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

/**/
h2, h3 {
    color: black;
}

/**/
p {
    line-height: 1.6;
}

/**/
.main-logo {
    background-color: var(--seller-color);
    color: white;
    text-align: center;
    font-weight: bold;
    font-size: 22px;
    box-sizing: border-box;
    padding: 40px 0;
    margin-top: 50px; /* Match the height of the nav bar */
    width: 100%;
}

/**/
.main-dashboard {
    margin-top: 30px;
    padding: 20px;
    left: 5px;
}

/* Button styling with improved appearance */
button {
    background-color: var(--buyer-color);
    color: #333;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
}

/* Button hover effect */
button:hover {
    background-color: var(--seller-color);
    color: #333;
}

/* Specific styling for order form submit button */
.order-page button[type="button"] {
    background-color: var(--buyer-color);
    color: #333;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 10px;
}

/* Specific hover style for order form submit button */
.order-page button[type="button"]:hover {
    background-color: var(--footer-color);
    color: var(--text-color);
}

/* Special hover effect for submit, edit, and delete buttons */
button[type="submit"]:hover,
button.edit-btn:hover,
button.delete-btn:hover,
.edit-btn:hover,
.delete-btn:hover,
.submit-btn:hover {
    background-color: var(--footer-color);
    color: var(--text-color);
}

/* Base styles for action buttons */
.submit-btn, .edit-btn, .delete-btn {
    cursor: pointer;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
}

/**/
footer {
    position: fixed;
    bottom: 0;
    left: 0;
    text-align: center;
    height: 100px;
    box-sizing: border-box;
    z-index: 1000; /* bring to front */
    bottom: 0px; /* ยึดติดกับขอบด้านล่าง */
    background-color: var(--footer-color);
    color: white;
    text-align: center;
    padding: 0px 0px;
    margin-bottom: 0px;
    margin-top: 0px;
    width: 100%;
}
/* Prevent footer from overlapping content */
body {
    margin-bottom: 100px; /* Same as footer height */
}

footer a {
    color: var(--text-color);
    text-decoration: none;
}


footer a:hover {
    color: var(--seller-color);
}

/* */
#login-page label {
    margin-right: 10px;
    text-align: right;
    width: 100px;
}

/* */
#login-page input {
    margin-bottom: 10px;
}

/* Label styling with improved responsiveness */
label {
    width: 150px; /* กำหนดความกว้างของ label ให้เท่ากัน */
    text-align: left; /* จัดข้อความ label ให้อยู่ชิดซ้าย */
    margin-right: 10px; /* ระยะห่างระหว่าง label และ input */
    line-height: 1.5; /* ปรับความสูงของบรรทัดให้สมดุลกับ input */
    font-weight: 500; /* Make labels slightly bolder */
}

/* Form styling with improved responsiveness */
form {
    display: flex;
    flex-direction: column; /* จัดให้แต่ละฟิลด์เรียงลงมาในแนวตั้ง */
    max-width: 600px; /* กำหนดความกว้างสูงสุดของฟอร์ม */
    margin: 0 auto; /* จัดฟอร์มให้อยู่กึ่งกลาง */
    width: 100%; /* Ensure form takes full width of its container */
    box-sizing: border-box; /* Include padding in width calculation */
    padding: 0 10px; /* Add some padding on small screens */
}

/* Input and select styling with improved responsiveness */
input, select {
    flex: 1; /* ให้ input และ select ขยายเต็มพื้นที่ที่เหลือ */
    padding: 8px 10px; /* เพิ่ม padding เพื่อให้ input ดูสมดุล */
    box-sizing: border-box; /* ป้องกัน input ล้นขอบ */
    height: 38px; /* กำหนดความสูงของ input ให้เท่ากัน */
    border: 1px solid #ccc; /* Add a light border */
    border-radius: 4px; /* Add rounded corners */
    font-size: 15px; /* Consistent font size */
    min-width: 200px; /* Minimum width for better mobile display */
}

/*error message*/
.message {
    margin: 10px 0;
    padding: 10px;
}
/*...*/
.success {
    color: white;
    background-color: var(--buyer-color);
}
/*...*/
.error {
    color: white;
    background-color: var(--seller-color);
}
/*...*/
.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}
/*...*/
.card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 20px;
}
/*...*/
.value {
    font-size: 24px;
    font-weight: bold;
    color: var(--service-color);
}

/* Role-based styling */
.seller-bg {
    background-color: var(--seller-color);
    color: var(--text-color);
}

/* Elements with seller color background should have white text on hover */
.seller-bg:hover,
[style*="background-color: #ef9600"]:hover,
[style*="background-color:#ef9600"]:hover,
[style*="background: #ef9600"]:hover,
[style*="background:#ef9600"]:hover {
    color: var(--text-color) !important;
}

.buyer-bg {
    background-color: var(--buyer-color);
    color: var(--text-color);
}

.service-bg {
    background-color: var(--service-color);
    color: var(--text-color);
}

/* CO2 stats styling */
.co2-stats {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
}

.co2-stat {
    text-align: center;
    padding: 10px;
}

.co2-label {
    font-weight: bold;
    margin-bottom: 5px;
}

.co2-value {
    font-size: 18px;
    color: var(--service-color);
}

.co2-description {
    text-align: center;
    font-style: italic;
    margin-top: 10px;
    color: #666;
}
/* Form group styling with improved responsiveness */
.form-group {
    display: flex;
    align-items: center; /* จัด label และ input ให้อยู่กึ่งกลางแนวตั้ง */
    margin-bottom: 15px; /* เพิ่มช่องว่างระหว่างแถว */
    width: 100%; /* Ensure full width */
    flex-wrap: wrap; /* Allow wrapping on small screens */
}
/*...*/
.loginform-group {
    display: flex;
    align-items: center; /* จัด label และ input ให้อยู่กึ่งกลางแนวตั้ง */
    margin-bottom: 5px; /* เพิ่มช่องว่างระหว่างแถว */
    margin-right: 5px;
    width: 100%;
    margin-top: 20px;
    box-sizing: border-box;
}
/*...*/
.content-wrapper {
    padding-bottom: 120px;
}
/*...*/
.registerform-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    margin-top: 20px;
    box-sizing: border-box;
}
/*...*/
.registerform-group label {
    width: 200px; /* ปรับความกว้างของ label ตามต้องการ */
    margin-right: 5px; /* เพิ่มช่องว่างระหว่าง label และ input */
    align-items: center;
}
/*...*/
.registerform-group input {
    flex: 1; /* ให้ input ขยายเต็มพื้นที่ที่เหลือ */
    min-width: 250px;
}

/* Basic responsive styles for login and register forms */
@media screen and (max-width: 768px) {
    .loginform-group, .registerform-group {
        width: 100%;
        margin-top: 10px;
    }
}

/* Enhanced responsive styles for login and register forms */
@media screen and (max-width: 480px) {
    /* Login form specific styles */
    .main-dashboard form {
        padding: 0 15px;
        margin: 0 auto;
        max-width: 450px; /* Increase form width */
    }

    .loginform-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start; /* Align to the left */
        width: 100%;
        margin-left: 0; /* Remove negative margin */
    }

    .loginform-group label {
        width: 30%; /* Fixed width for labels */
        text-align: left; /* Left-align the labels */
        margin-right: 15px;
        font-size: 14px;
        white-space: nowrap; /* Prevent label text from wrapping */
    }

    .loginform-group input {
        width: 65%; /* Wider inputs */
        max-width: none; /* Remove max-width limitation */
        margin-right: 0; /* Remove right margin */
    }

    /* Register form specific styles */
    .content form {
        padding: 0 15px;
        margin: 0 auto;
        max-width: 450px; /* Increase form width */
    }

    .registerform-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start; /* Align to the left */
        width: 100%;
        margin-left: 0; /* Remove negative margin */
    }

    .registerform-group label {
        width: 30%; /* Fixed width for labels */
        text-align: left; /* Left-align the labels */
        margin-right: 15px;
        font-size: 14px;
        white-space: nowrap; /* Prevent label text from wrapping */
    }

    .registerform-group input {
        width: 65%; /* Wider inputs */
        max-width: none; /* Remove max-width limitation */
        margin-right: 0; /* Remove right margin */
    }

    /* Common form button styling */
    .main-dashboard form button,
    .content form button {
        margin: 15px 0; /* Align button to the left */
        display: block;
        width: 100%; /* Full width button */
        max-width: none; /* Remove max-width limitation */
    }

    /* Left-align text and links */
    .main-dashboard form p,
    .content form p {
        text-align: left;
        margin: 10px 0;
    }

    /* Left-align the form headings */
    .main-dashboard form h2,
    .content form h2 {
        text-align: left;
        margin-left: 5px;
    }
}

@media screen and (max-width: 390px) {
    /* Make form layout match the 769px layout but with adjusted sizes */
    .main-dashboard form,
    .content form {
        padding: 0 10px;
        width: 100%;
        max-width: 350px; /* Limit form width */
        margin: 0 auto;
    }

    .main-dashboard form h2,
    .content form h2 {
        font-size: 20px;
        text-align: left; /* Left-align the title */
        margin-bottom: 15px;
        margin-left: 5px; /* Add a small left margin */
    }

    .loginform-group,
    .registerform-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start; /* Align to the left */
        width: 100%;
        margin-left: 0; /* Remove negative margin */
    }

    .loginform-group label,
    .registerform-group label {
        width: 30%; /* Fixed width for labels */
        text-align: left; /* Left-align the labels */
        margin-right: 15px;
        font-size: 13px;
        white-space: nowrap; /* Prevent label text from wrapping */
    }

    .loginform-group input,
    .registerform-group input {
        width: 65%; /* Wider inputs */
        max-width: none; /* Remove max-width limitation */
        margin-right: 0; /* Remove right margin */
        font-size: 13px;
    }

    /* Common form button styling */
    .main-dashboard form button,
    .content form button {
        margin: 15px 0; /* Align button to the left */
        display: block;
        width: 100%; /* Full width button */
        max-width: none; /* Remove max-width limitation */
    }

    /* Left-align text and links */
    .main-dashboard form p,
    .content form p {
        text-align: left;
        margin: 10px 0;
        font-size: 13px;
    }

    /* Submit order page specific styles for 390px */
    .order-page .form-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start; /* Align to the left */
        width: 100%;
    }

    .order-page label {
        width: 30%; /* Fixed width for labels */
        text-align: left; /* Left-align the labels */
        margin-right: 15px;
        font-size: 13px;
        white-space: nowrap; /* Prevent label text from wrapping */
    }

    .order-page input,
    .order-page select {
        width: 65%; /* Wider inputs */
        max-width: none; /* Remove max-width limitation */
        margin-right: 0; /* Remove right margin */
        font-size: 13px;
        margin-left: 5px;
    }

    .order-page button[type="button"] {
        width: 100%; /* Full width button */
        padding: 10px;
        font-size: 15px;
        margin: 15px 0; /* Align button to the left */
        border-radius: 4px;
    }

    .order-page h2 {
        font-size: 20px;
        text-align: left; /* Left-align the title */
        margin-bottom: 15px;
        margin-left: 5px; /* Add a small left margin */
    }
}

@media screen and (max-width: 320px) {
    /* Make form layout match the 769px layout but with smaller sizes */
    .main-dashboard form,
    .content form {
        padding: 0 5px;
        max-width: 300px; /* Limit form width */
    }

    .main-dashboard form h2,
    .content form h2 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .loginform-group,
    .registerform-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 12px;
        justify-content: flex-start; /* Align to the left */
    }

    .loginform-group label,
    .registerform-group label {
        width: 30%; /* Fixed width for labels */
        text-align: left; /* Left-align the labels */
        margin-right: 10px;
        font-size: 12px;
        white-space: nowrap; /* Prevent label text from wrapping */
    }

    .loginform-group input,
    .registerform-group input {
        width: 65%; /* Wider inputs */
        font-size: 12px;
        height: 32px;
    }

    /* Common form button styling */
    .main-dashboard form button,
    .content form button {
        margin: 12px 0; /* Align button to the left */
        display: block;
        width: 100%; /* Full width button */
        font-size: 14px;
        padding: 8px 10px;
    }

    /* Left-align text and links */
    .main-dashboard form p,
    .content form p {
        font-size: 12px;
    }

    /* Submit order page specific styles for 320px */
    .order-page .form-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 12px;
        justify-content: flex-start; /* Align to the left */
    }

    .order-page label {
        width: 30%; /* Fixed width for labels */
        text-align: left; /* Left-align the labels */
        margin-right: 10px;
        font-size: 12px;
        white-space: nowrap; /* Prevent label text from wrapping */
    }

    .order-page input,
    .order-page select {
        width: 65%; /* Wider inputs */
        font-size: 12px;
        height: 32px;
    }

    .order-page button[type="button"] {
        margin: 12px 0; /* Align button to the left */
        display: block;
        width: 100%; /* Full width button */
        font-size: 14px;
        padding: 8px 10px;
    }

    .order-page h2 {
        font-size: 18px;
        text-align: left; /* Left-align the title */
        margin-bottom: 12px;
        margin-left: 5px; /* Add a small left margin */
    }
}
/*...*/
.language-switcher {
    position: absolute; /* ใช้ position เพื่อจัดวาง */
    top: 10px; /* ระยะห่างจากด้านบน */
    right: 60px; /* ระยะห่างจากด้านขวา */
    padding: 0; /* ลบ padding */
    margin: 0; /* ลบ margin */
    text-align: right; /* จัดข้อความให้ชิดขวา */
}
/*...*/
.lang-button {
    color: black;
    text-decoration: underline;
    cursor: pointer;
    padding: 0; /* ลบ padding ทั้งหมด */
    margin: 0 10px; /* เพิ่มระยะห่างเล็กน้อยระหว่างภาษา */
    border: none; /* ลบ border */
    background: none; /* ลบสีพื้นหลัง */
    font-size: inherit; /* ใช้ขนาดฟอนต์เดียวกับเนื้อหา */
}
/*...*/
.lang-button:hover {
    text-decoration: none; /* ลบขีดเส้นใต้เมื่อ hover */
}
/*...*/
.modal {
    display: none; /* ซ่อน modal โดยเริ่มต้น */
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}
/*...*/
.modal-content {
    background-color: #fff;
    margin: 5% auto; /* ปรับ margin-top เป็น 10% แทน 15% */
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 600px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow-y: auto; /* Add scrolling for tall modals */
    max-height: 90vh; /* Limit height to 90% of viewport height */
}
/*...*/
.modal-header {
    font-size: 20px;
    margin-bottom: 10px;
}
/*...*/
.modal-footer {
    text-align: right;
    margin-top: 20px;
}
/*...*/
.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}
/*...*/
.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styles for role edit modal */
@media screen and (max-width: 768px) {
    #roles-edit-modal .modal-content {
        width: 90%;
        padding: 15px;
        margin: 10% auto;
    }

    .role-options {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 6px;
    }

    .role-checkbox {
        padding: 5px 6px;
    }

    .role-label {
        font-size: 0.85rem;
    }
}

@media screen and (max-width: 480px) {
    #roles-edit-modal .modal-content {
        width: 95%;
        padding: 12px;
        margin: 15% auto;
    }

    .close-btn {
        color: #ffffff;
        float: right;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        background-color: transparent;
        border: none;
        padding: 0 5px;
        margin-top: -5px;
    }

    .role-options {
        grid-template-columns: 1fr;
        gap: 5px;
    }

    .role-checkbox {
        padding: 8px;
    }

    .role-options-container h3 {
        font-size: 0.95rem;
        padding-bottom: 6px;
        margin-bottom: 10px;
    }

    .button-group {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .button-group button {
        width: 100%;
        margin: 0;
    }
}
/*...*/
/* Common brand styling */
.nav-brand {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    text-decoration: none;
    margin: 0 20px 0 10px;
    display: inline-block;
    line-height: 1;
}

/* Index page brand styling */
.index-nav-brand {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    text-decoration: none;
    margin: 0 20px 0 10px;
    display: inline-block;
    line-height: 1;
}

/* Dashboard page brand styling */
.dash-nav-brand {
    font-size: 32px;
    font-weight: bold;
    color: #333;
    text-decoration: none;
    margin: 0 20px 0 10px;
    display: inline-block;
    line-height: 1;
}
/*...*/
/* Common brand hover */
.nav-brand:hover {
    color: #ffffff; /* White text on hover */
}

/* Index page brand hover */
.index-nav-brand:hover {
    color: #ffffff; /* White text on hover */
}

/* Dashboard page brand hover */
.dash-nav-brand:hover {
    color: #ffffff; /* White text on hover */
}
/*...*/
/* Nav right removed as language selector is now in the dropdown menu */
/*...*/
.main-dashboard {
    margin-top: 80px; /* Increased to account for fixed navigation bar */
    padding: 40px;
    left: 5px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    width: 90%;
    box-sizing: border-box; /* ป้องกันปัญหา padding ทำให้ความกว้างเกิน */
}
/*...*/
.dashboard-content {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    word-wrap: break-word;
    overflow-wrap: break-word;
    box-sizing: border-box;
}
/*...*/
.contract-title {
    text-align: left; /* Left-align contract title */
    margin-bottom: 10px;
    color: #333;
}
/*...*/
.contract-date {
    text-align: left; /* Left-align contract date */
    margin-bottom: 30px;
    font-style: italic;
}
/*...*/
.contract-section {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    width: 100%;
    box-sizing: border-box;
}
/*...*/
.contract-section h2 {
    text-align: center;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 15px;
    color: black;
}
/*...*/
.contract-section p {
    margin-bottom: 12px;
    line-height: 1.6;
    width: 100%;
    word-break: break-word;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    max-width: 100%;
    text-align: left;
}
/*...*/
.contract-section strong {
    display: block;
    margin: 15px 0 5px 0;
}
/*...*/
.button-stack {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 100%;
}
/*...*/
.sign-button {
    display: inline-block;
    background-color: var(--buyer-color);
    color: #333;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    min-width: 120px;
    max-width: 180px;
    width: 100%;
}
/*...*/
.sign-button:hover {
    background-color: var(--footer-color);
    color: var(--text-color);
}
/*...*/
.download-btn {
    background-color: var(--buyer-color);
}
/*...*/
.back-link {
    display: block;
    text-align: center;
    margin-top: 20px;
    color: #007bff;
    text-decoration: none;
}
/*...*/
.back-link:hover {
    text-decoration: underline;
}
/* เพิ่ม class ใหม่สำหรับ vertical navbar */
.vertical-nav {
    position: fixed; /* คงตำแหน่งไว้บนหน้าจอ */
    top: 0;
    left: 0;
    width: 200px; /* กำหนดความกว้างของ navbar */
    height: 100vh; /* สูงเท่ากับความสูงหน้าจอ */
    background-color: var(--buyer-color);
    padding: 80px 0 20px 0; /* padding ด้านบนมากกว่าเพื่อให้มีที่วาง logo */
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    z-index: 900; /* ต่ำกว่า z-index ของ nav-brand */
}
/* ปรับแต่ง logo ให้อยู่ด้านบนของ vertical navbar */
.vertical-nav-brand {
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 27px;
    font-weight: bold;
    color: #333;
    text-decoration: none;
    z-index: 950; /* มากกว่า z-index ของ vertical-nav */
}
.vertical-nav-brand:hover {
    color: #ffffff; /* White text on hover */
}
/* ปรับแต่งลิงก์ใน vertical navbar */
.vertical-nav a:not(.vertical-nav-brand) {
    color: #333;
    text-decoration: none;
    padding: 12px 20px;
    transition: background-color 0.3s;
    display: block;
    font-size: 16px;
}
.vertical-nav a:hover:not(.vertical-nav-brand) {
    background-color: rgba(239, 150, 0, 0.2);
    color: #ffffff; /* White text on hover */
}
/* ปรับ main content ให้ไม่ทับกับ navbar */
.main-with-vertical-nav {
    margin-left: 200px; /* เท่ากับความกว้างของ vertical-nav */
    padding: 20px;
    width: calc(100% - 200px); /* ลบความกว้าง navbar ออกจากความกว้างทั้งหมด */
    box-sizing: border-box;
}
.vertical-nav-menu {
    display: flex;
    flex-direction: column;
    width: 73%;
}
.vertical-nav-menu a {
    width: 100%;
    padding: 15px 20px; /* เพิ่ม padding ซ้ายเพื่อให้ข้อความชิดซ้าย */
    text-align: left; /* จัดข้อความชิดซ้าย */
    color: #333;
    text-decoration: none;
    font-size: 16px;
    transition: background-color 0.3s;
    display: flex; /* ใช้ flexbox สำหรับการจัดวางแนวตั้ง */
    align-items: center; /* จัดข้อความให้อยู่กึ่งกลางในแนวตั้ง */
    height: 30px; /* กำหนดความสูงของแต่ละเมนู */
}
.vertical-nav-menu {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding-left: 0;
}

.vertical-nav-menu a,
.vertical-nav-menu span {
    width: 100%;
    padding: 15px 20px;
    text-align: left;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    height: 40px;
}

.nav-category {
    font-weight: bold;
    color: #333;
    cursor: default;
}

.nav-subcategory {
    cursor: default;
    color: #333;
    text-decoration: none;
    font-size: 16px;
}

.nav-item {
    color: #333;
    text-decoration: none;
    font-size: 16px;
    cursor: pointer;
}

.nav-item:hover {
    background-color: rgba(239, 150, 0, 0.2);
    color: #ffffff; /* White text on hover */
}

/* Remove any potential margin or padding that might affect alignment */
.vertical-nav-menu a:not(.vertical-nav-brand),
.vertical-nav-menu span {
    margin: 0;
}

/* Styling for the navigator class used in other pages */
.navigator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: var(--buyer-color);
    text-align: center;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    height: 65px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 15px;
}

.navigator a {
    color: #333;
    margin: 0 15px;
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.navigator a:hover {
    color: #ffffff; /* White text on hover */
}

.navigator .nav-brand {
    font-size: 32px;
    font-weight: bold;
    margin-right: 20px;
}

/* Tablet-specific styles for dashboard */
@media screen and (min-width: 769px) and (max-width: 1180px) {
    html, body {
        overflow-x: hidden; /* Prevent horizontal scrollbar */
        width: 100%;
        position: relative;
    }

    /* Common styles for all navigation types */
    nav, .index-nav, .dash-nav {
        width: 100%;
        overflow-x: hidden;
    }

    /* Common container styles for all navigation types */
    .nav-container, .index-nav-container, .dash-nav-container {
        overflow-x: hidden;
        padding: 0 10px;
        width: 100%;
        box-sizing: border-box;
    }

    /* Common brand styles for all navigation types */
    .nav-brand, .index-nav-brand, .dash-nav-brand {
        margin-left: 15px;
        font-size: 24px; /* Smaller font size for tablet */
    }

    /* Common links container styles for all navigation types */
    .nav-links, .index-nav-links, .dash-nav-links {
        min-width: auto;
        width: auto;
        transform: none;
        position: relative;
        left: auto;
        margin-left: 20px;
    }

    /* Common link styles for all navigation types */
    .nav-links a, .index-nav-links a, .dash-nav-links a {
        font-size: 16px;
        margin: 0 5px;
        padding: 0 5px;
    }

    .dropdown-trigger {
        margin: 0 5px;
        font-size: 16px;
    }

    .index-lang-selector {
        display: block;
        margin-right: 15px;
    }

    .desktop-lang-selector select {
        padding: 5px;
        font-size: 14px;
    }
}

/* Responsive styles for navigator */
@media screen and (max-width: 768px) {
    .navigator {
        padding: 0 10px;
        justify-content: flex-start;
        overflow-x: hidden; /* Prevent horizontal scrollbar */
        width: 100%;
        box-sizing: border-box;
    }

    .navigator a {
        font-size: 16px;
        margin: 0 8px;
    }

    .navigator .nav-brand {
        font-size: 24px;
        margin-right: 10px;
        margin-left: 10px; /* Add left margin */
    }

    /* Login and Register form responsive styles */
    .loginform-group, .registerform-group {
        margin-bottom: 15px;
        width: 100%;
    }

    /* Center the form on mobile */
    main.main-dashboard form, main.content form {
        padding: 0 15px;
    }
}

@media screen and (max-width: 480px) {
    .navigator {
        padding: 0 5px;
        height: auto;
        min-height: 65px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .navigator a {
        font-size: 14px;
        margin: 5px;
    }

    .navigator .nav-brand {
        font-size: 20px;
        width: 100%;
        text-align: center;
        margin: 10px 0;
    }
}

/* Dropdown menu styles */
.dropdown-container {
    position: relative;
    display: inline-block;
    overflow: visible !important; /* Force no scrollbar */
    overflow-y: visible !important; /* Force no vertical scrollbar */
    overflow-x: visible !important; /* Force no horizontal scrollbar */
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.dropdown-container::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

.dropdown-trigger {
    cursor: pointer;
    color: #333;
    margin: 0 10px;
    text-decoration: none;
    font-size: 18px;
    padding: 5px 0;
    transition: color 0.3s ease;
    position: relative; /* For positioning the dropdown properly */
}

.dropdown-trigger:hover {
    color: #ffffff; /* White text on hover */
}

/* Specific hover style for Setting dropdown trigger */
.dash-nav-links .dropdown-container:nth-child(4) .dropdown-trigger:hover {
    color: #ffffff !important; /* White text on hover */
}

.dropdown-content {
    display: none;
    position: absolute;
    top: -20%; /* Position as preferred by user */
    left: 0;
    background-color: white; /* White background */
    min-width: 120px; /* Smaller width as shown in the image */
    box-shadow: 0px 4px 8px 0px rgba(0,0,0,0.1); /* Lighter shadow */
    z-index: 1000;
    border-radius: 4px;
    overflow: visible !important; /* Force no scrollbar */
    overflow-y: visible !important; /* Force no vertical scrollbar */
    overflow-x: visible !important; /* Force no horizontal scrollbar */
    margin-top: 5px; /* Add a small gap between trigger and dropdown */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    border: 1px solid #f0f0f0; /* Light border */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.dropdown-content::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
}

/* Desktop hover behavior */
@media screen and (min-width: 769px) {
    .dropdown-container:hover .dropdown-content {
        display: block;
        animation: fadeIn 0.3s;
    }

    /* Specific rules for all dropdown containers in dashboard navigation */
    .dash-nav-links .dropdown-container:hover .dropdown-content {
        display: block !important;
        animation: fadeIn 0.3s;
        top: -20% !important; /* Position as preferred by user */
        position: absolute !important;
    }

    /* Specific rule for Submit Order dropdown (1st dropdown in dashboard) */
    .dash-nav-links .dropdown-container:nth-child(1):hover .dropdown-content {
        display: block !important;
        animation: fadeIn 0.3s;
        top: -20% !important; /* Position as preferred by user */
        position: absolute !important;
    }

    /* Specific rule for Order Book dropdown (2nd dropdown in dashboard) */
    .dash-nav-links .dropdown-container:nth-child(2):hover .dropdown-content {
        display: block !important;
        animation: fadeIn 0.3s;
        top: -20% !important; /* Position as preferred by user */
        position: absolute !important;
    }

    /* Specific rule for Matched Orders dropdown (3rd dropdown in dashboard) */
    .dash-nav-links .dropdown-container:nth-child(3):hover .dropdown-content {
        display: block !important;
        animation: fadeIn 0.3s;
        top: -20% !important; /* Position as preferred by user */
        position: absolute !important;
    }

    /* Specific rule for Setting dropdown (4th dropdown in dashboard) */
    .dash-nav-links .dropdown-container:nth-child(4):hover .dropdown-content {
        display: block !important;
        animation: fadeIn 0.3s;
        top: -20% !important; /* Position as preferred by user */
        position: absolute !important;
    }

    /* Specific rules for profile page */
    .profile-page .dash-nav-links .dropdown-container:hover .dropdown-content {
        display: block !important;
        animation: fadeIn 0.3s;
        top: -20% !important; /* Position as preferred by user */
        position: absolute !important;
        background-color: white !important;
    }
}

.dropdown-content a {
    color: var(--seller-color); /* Orange text color */
    padding: 8px 12px; /* Add padding as shown in the image */
    text-decoration: none;
    display: block;
    font-size: 15px; /* Slightly smaller font */
    text-align: left; /* Left-aligned text as shown in the image */
    margin: 0;
    white-space: nowrap; /* Prevent text wrapping */
    width: auto; /* Allow content to determine width */
    min-width: 100%; /* Ensure dropdown is at least as wide as the trigger */
    overflow: visible !important; /* Force no scrollbar */
}

/* Specific styles for profile page dropdown content */
.profile-page .dropdown-content {
    background-color: white !important;
    width: auto !important;
    min-width: 50px;
}

.profile-page .dropdown-content a {
    color: var(--seller-color) !important;
    width: auto !important;
    min-width: 100% !important;
}

.dropdown-content a:hover {
    text-decoration: none; /* No underline on hover */
    background-color: var(--footer-color) !important; /* Blue background on hover */
    color: #ffffff !important; /* White text on hover */
    font-weight: 500; /* Make text slightly bolder on hover */
    border-radius: 5px; /* Slightly rounded corners */
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile responsive styles for dropdown */
@media screen and (max-width: 768px) {
    .dropdown-container {
        display: block;
        width: 100%;
        text-align: center;
        margin: 0 auto;
        box-sizing: border-box;
    }

    .dropdown-trigger {
        display: block;
        width: 100%;
        padding: 15px;
        text-align: center;
        border-bottom: 1px solid rgba(0,0,0,0.1);
        margin: 0 auto;
        color: #333; /* Ensure text color is visible */
        font-weight: 500; /* Make text slightly bolder */
        background-color: var(--buyer-color); /* Match the navigation background */
        box-sizing: border-box;
    }

    .dropdown-content {
        position: static;
        box-shadow: none;
        width: 100%;
        background-color: white; /* White background for mobile too */
        overflow: visible !important; /* Force no scrollbar */
        overflow-y: visible !important; /* Force no vertical scrollbar */
        overflow-x: visible !important; /* Force no horizontal scrollbar */
        max-height: none !important; /* Remove any max-height limitation */
        scrollbar-width: none !important; /* Firefox */
        -ms-overflow-style: none !important; /* IE and Edge */
        border: none; /* Remove border in mobile view */
        margin-top: 0; /* Remove margin in mobile view */
        display: none; /* Hide by default in mobile view */
    }

    /* Important: Make dropdown content visible in mobile when active */
    .dash-nav-links.active .dropdown-content,
    .index-nav-links.active .dropdown-content {
        display: block !important; /* Force display in mobile view */
    }

    /* Show dropdown content when dropdown trigger is clicked in mobile view */
    .dropdown-trigger.active + .dropdown-content {
        display: block !important; /* Force display when trigger is active */
    }

    /* Mobile dropdown styles */
    .nav-links.active .dropdown-container,
    .index-nav-links.active .dropdown-container,
    .dash-nav-links.active .dropdown-container {
        display: block;
        width: 100%;
    }

    /* Make sure dropdown triggers are visible in mobile view */
    .nav-links.active .dropdown-trigger,
    .index-nav-links.active .dropdown-trigger,
    .dash-nav-links.active .dropdown-trigger {
        display: block;
        width: 100%;
    }

    /* Hide scrollbar for Chrome, Safari and Opera in mobile */
    .dropdown-content::-webkit-scrollbar {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
    }

    .dropdown-content a {
        text-align: center;
        padding: 12px;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        color: var(--seller-color); /* Keep orange text in mobile view */
        margin: 0 auto; /* Center the text */
        width: 100%;
        display: block !important; /* Force display block */
        font-size: 15px; /* Slightly larger font size for better visibility */
        background-color: white; /* White background for contrast */
        box-sizing: border-box;
        position: relative; /* Add position relative */
        left: 0; /* Reset any left positioning */
        right: 0; /* Reset any right positioning */
    }

    /* Add hover effect for dropdown links in mobile */
    .dropdown-content a:hover {
        background-color: rgba(239, 150, 0, 0.1); /* Light orange background on hover */
        font-weight: 500; /* Make text slightly bolder on hover */
        color: #ffffff !important; /* White text on hover */
    }

    .dropdown-content.show {
        display: block;
    }
}

/* Responsive styles for submit order page */
@media screen and (max-width: 768px) {
    /* Add a specific class to the submit order page in the HTML */
    .order-page .hamburger {
        margin-right: 30px !important;
    }
}

@media screen and (max-width: 768px) {
    /* Make Products text less bold than main navigation items */
    .dash-nav-links .dropdown-content a {
        font-weight: normal !important;
        font-size: 15px !important;
        color: rgba(0, 0, 0, 0.6) !important;
    }

    /* Add hover effect for Products links */
    .dash-nav-links .dropdown-content a:hover {
        color: #ffffff !important; /* White text on hover */
        background-color: rgba(239, 150, 0, 0.2) !important;
    }

    /* Make main navigation items bold */
    .dash-nav-links a.dropdown-trigger,
    .dash-nav-links > a {
        font-weight: bold !important;
        font-size: 18px !important;
    }
    .index-nav-links a.dropdown-trigger,
    .index-nav-links > a {
        font-weight: bold !important;
        font-size: 18px !important;
    }
}

/* Responsive styles for the order form on tablets */
@media screen and (min-width: 481px) and (max-width: 768px) {
    /* Responsive form styling for tablets */
    .order-page .form-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start; /* Align to the left */
        width: 100%;
    }

    .order-page label {
        width: 30%; /* Fixed width for labels */
        text-align: left; /* Left-align the labels */
        margin-right: 15px;
        font-size: 15px;
        white-space: nowrap; /* Prevent label text from wrapping */
    }

    .order-page input,
    .order-page select {
        width: 65%; /* Wider inputs */
        max-width: none; /* Remove max-width limitation */
        margin-right: 0; /* Remove right margin */
    }

    .order-page button[type="button"] {
        width: 100%; /* Full width button */
        padding: 12px;
        font-size: 16px;
        margin: 15px 0; /* Align button to the left */
        border-radius: 4px;
        max-width: none; /* Remove max-width limitation */
    }

    .order-page .main-dashboard {
        padding: 25px 20px;
        width: 100%;
    }

    .order-page form {
        padding: 0 15px;
        max-width: 600px; /* Increase form width */
        margin: 0 auto;
    }

    .order-page h2 {
        font-size: 22px;
        text-align: left; /* Left-align the title */
        margin-bottom: 20px;
        margin-left: 5px; /* Add a small left margin */
    }
}

/* Responsive styles for the order form on mobile phones */
@media screen and (max-width: 480px) {
    /* Responsive form styling for mobile */
    .order-page .form-group {
        flex-direction: row; /* Keep horizontal layout */
        align-items: center;
        margin-bottom: 15px;
        justify-content: flex-start; /* Align to the left */
        width: 100%;
    }

    .order-page label {
        width: 30%; /* Fixed width for labels */
        text-align: left; /* Left-align the labels */
        margin-right: 15px;
        font-size: 14px;
        white-space: nowrap; /* Prevent label text from wrapping */
    }

    .order-page input,
    .order-page select {
        width: 65%; /* Wider inputs */
        max-width: none; /* Remove max-width limitation */
        margin-right: 0; /* Remove right margin */
        font-size: 14px;
    }

    .order-page button[type="button"] {
        width: 100%; /* Full width button */
        padding: 12px;
        font-size: 16px;
        margin: 15px 0; /* Align button to the left */
        border-radius: 4px;
        max-width: none; /* Remove max-width limitation */
    }

    .order-page .main-dashboard {
        padding: 20px 15px;
        width: 100%;
        margin-top: 70px;
    }

    .order-page form {
        padding: 0 15px;
        max-width: 450px; /* Increase form width */
        margin: 0 auto;
    }

    .order-page h2 {
        font-size: 20px;
        text-align: left; /* Left-align the title */
        margin-bottom: 20px;
        margin-left: 5px; /* Add a small left margin */
    }
}